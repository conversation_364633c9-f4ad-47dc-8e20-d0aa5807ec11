package com.jiuji.cloud.after.service;

import com.jiuji.cloud.after.service.fallback.OaAfterClientFallBackFactory;
import com.jiuji.cloud.after.vo.jiujiservice.ValidSaleJiujiServiceReqVo;
import com.jiuji.cloud.after.vo.jiujiservice.ValidSaleJiujiServiceResVo;
import com.jiuji.cloud.after.vo.refund.RecoverNoReasonReduceVo;
import com.jiuji.cloud.after.vo.refund.ThirdOriginRefundVo;
import com.jiuji.cloud.after.vo.req.*;
import com.jiuji.cloud.after.vo.res.PjtShouhouRes;
import com.jiuji.cloud.after.vo.res.SmallproLogRes;
import com.jiuji.oa.afterservice.shouhou.vo.ProductKcCountInfoRes;
import com.jiuji.oa.afterservice.shouhou.vo.ServiceInfoVO;
import com.jiuji.oa.afterservice.shouhou.vo.ServiceRecord;
import com.jiuji.oa.afterservice.shouhou.vo.ShouhouRepaireUserVo;
import com.jiuji.oa.afterservice.shouhou.vo.req.ExternalStatisticsReq;
import com.jiuji.oa.afterservice.shouhou.vo.req.ProductKcCountInfoQueryReq;
import com.jiuji.oa.afterservice.shouhou.vo.res.AfterSaleResultRes;
import com.jiuji.oa.afterservice.shouhou.vo.res.ExternalStatisticsRes;
import com.jiuji.oa.afterservice.shouhou.vo.res.ShouHouDetailRes;
import com.jiuji.oa.afterservice.shouhou.vo.res.ShouhouRepairInfoRes;
import com.jiuji.oa.afterservice.small.ExchangeProductListVO;
import com.jiuji.oa.afterservice.small.SmallYuyueOrderOnGoingVO;
import com.jiuji.oa.afterservice.yuyue.vo.req.ShouhouYuyueReq;
import com.jiuji.oa.afterservice.yuyue.vo.res.ShouHouYuyueRes;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.utils.enums.coupon.BusinessTypeEnum;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR> quan
 * @description
 * @since 2020/6/29
 */
@FeignClient(value = "AFTERSERVICE", path = "afterservice/api/wcf",
            fallbackFactory = OaAfterClientFallBackFactory.class)
public interface OaAfterClient {


    @PostMapping("/addSmallproLog")
    R<SmallproLogRes> addSmallproLog(@RequestBody SmallproAddLogReq smallproAddLogReq);
    /**
     * 查询 售后详情
     * @return
     * @see com.jiuji.oa.afterservice.api.controller.OaApiController#getShouHouDetail
     */
    @GetMapping(value = "getShouHouDetail")
    R<ShouHouDetailRes> getShouHouDetail(@RequestParam(value = "id",required = true) Integer id,
                                         @RequestParam(value = "userid") Integer userid,
                                         @RequestParam(value = "imei") String imei);

    /**
     * 查询预约详情
     *
     * @param
     * @return
     * @see com.jiuji.oa.afterservice.api.controller.OaApiController#getYuYueDetail
     */
    @GetMapping("getYuYueDetail")
    R<ShouHouYuyueRes> getYuYueDetail(@RequestParam(value = "id") Integer id,
                                      @RequestParam(value = "userid") Integer userid);

    /**
     * 新增售后预约
     *
     * @param req 售后预约
     * @return 执行结果
     * @see com.jiuji.oa.afterservice.api.controller.OaApiController#addShouhouYuyue
     */
    @PostMapping("addShouhouYuyue")
    R<Integer> addShouhouYuyue(@RequestBody ShouhouYuyueReq req);

    /**
     * 取消预约单
     *
     * @param yyid       预约单id
     * @param cancelType 取消类型
     * @param remark     备注
     * @return 执行结果
     * @see com.jiuji.oa.afterservice.api.controller.OaApiController#delYuyue
     */
    @GetMapping("delYuyue")
    @Deprecated
    R<Boolean> delYuyue(@RequestParam("yyid") Integer yyid,
                        @RequestParam("cancelType") String cancelType,
                        @RequestParam("remark") String remark);

    /**
     * 取消预约单
     *
     * @param yyid       预约单id
     * @param cancelType 取消类型
     * @param remark     备注
     * @return 执行结果
     * @see com.jiuji.oa.afterservice.api.controller.OaApiController#delYuyue
     */
    @GetMapping("delYuyue")
    R<Boolean> delYuyue(@RequestParam("yyid") Integer yyid,
                        @RequestParam("cancelType") String cancelType,
                        @RequestParam("remark") String remark,
                        @RequestParam(value = "userId", required = false) Integer userId
    );

    /**
     * 获取最新维修信息 维修专区调用
     *
     * @param rows 记录行数
     * @see com.jiuji.oa.afterservice.api.controller.OaApiController#getAfterSale
     */
    @GetMapping("getAfterSale")
    R<AfterSaleResultRes> getAfterSale(@RequestParam(required = false, defaultValue = "20") Integer rows);

    @ApiOperation(value = "根据会员ID查询会员是否修过手机", httpMethod = "POST")
    @RequestMapping("/getShouhouRepaireUserInfo")
    R<List<ShouhouRepaireUserVo>> getShouhouRepaireUserInfo(List<Long> userId);

    @ApiOperation(value = "查询商品库存信息,type值：1大件、2小件", httpMethod = "POST")
    @RequestMapping("/queryProductStockByPpid")
    R<ProductKcCountInfoRes> queryProductStockByPpid(@RequestBody ProductKcCountInfoQueryReq req);

    @ApiOperation(value = "查询商品库存信息V2,小件统计逻辑变更,统计库存+在途,type值：1大件、3小件", httpMethod = "POST")
    @RequestMapping("/queryProductStockByPpid/v2")
    R<ProductKcCountInfoRes> queryProductStockByPpidV2(@RequestBody ProductKcCountInfoQueryReq req);

    @ApiOperation(value = "查询商品库存信息V3,小件统计逻辑变更,统计九机库存,type值：1大件、3小件", httpMethod = "POST")
    @RequestMapping("/queryProductStockByPpid/v3")
    R<ProductKcCountInfoRes> queryProductStockByPpidV3(@RequestBody ProductKcCountInfoQueryReq req);


    @ApiOperation(value = "主站调用：根据串号查询维修记录")
    @GetMapping("/getRepairRecordByImei")
    R<ShouhouRepairInfoRes> getRepairRecordByImei(@RequestParam(value = "imei") String imei);

    /**
     * 九机服务
     * @param imei
     * @see com.jiuji.oa.afterservice.api.controller.OaApiController#getAfterSale
     * @return
     */
    @ApiOperation(value = "九机服务")
    @GetMapping("/getServiceInfo/v3")
    R<ServiceInfoVO> getServiceInfoV3(@RequestParam(value = "imei") String imei,
                                      @RequestParam(value = "userId", required = false) Integer userId,
                                      @RequestParam(value = "issave", defaultValue = "true") Boolean isSave);

    /**
     * 九机服务
     * @param imei
     * @see com.jiuji.oa.afterservice.api.controller.OaApiController#getAfterSale
     * @return
     */
    @ApiOperation(value = "九机服务")
    @GetMapping("/getServiceInfo/v3")
    R<ServiceInfoVO> getServiceInfoV3(@RequestParam(value = "imei") String imei,
                                      @RequestParam(value = "userId", required = false) Integer userId,
                                      @RequestParam(value = "subId", defaultValue = "0", required = false) Integer subId,
                                      @RequestParam(value = "issave", defaultValue = "true") Boolean isSave
    );


    /**九机服务*/
    @ApiOperation(value = "九机服务服务")
    @PostMapping("/listServiceInfo")
    R<List<ServiceInfoVO>> listServiceInfo(@RequestBody List<String> imeis,@RequestParam(value = "userId",required = false) Integer userId);
    /**
     * 人才盘点售后接口提供
     * @param req req
     * @return
     */
    @PostMapping("/getWeiXiuMaoLi")
    @ApiOperation(value = "计算维修毛利")
    R<List<ExternalStatisticsRes>> getWeiXiuMaoLi(@RequestBody ExternalStatisticsReq req);

    @PostMapping("/getWeiXiuMaoLvPeiBi")
    @ApiOperation(value = "计算维修毛利配比")
    R<List<ExternalStatisticsRes>> getWeiXiuMaoLvPeiBi(@RequestBody ExternalStatisticsReq req);

    @PostMapping("/getDianChiDaShouLv")
    @ApiOperation(value = "计算电池搭售率")
    R<List<ExternalStatisticsRes>> getDianChiDaShouLv(@RequestBody ExternalStatisticsReq req);

    @PostMapping("/getPingMuDaShouLv")
    @ApiOperation(value = "计算屏幕搭售率")
    R<List<ExternalStatisticsRes>> getPingMuDaShouLv(@RequestBody ExternalStatisticsReq req);

    @PostMapping("/getFlunkWeiXiuShouLv")
    @ApiOperation(value = "计算放弃维修率")
    R<List<ExternalStatisticsRes>> getFlunkWeiXiuShouLv(@RequestBody ExternalStatisticsReq req);

    @GetMapping("/getServiceRecordByImei")
    @ApiOperation(value = "售后购买的服务", httpMethod = "GET")
    R<List<ServiceRecord>> getServiceRecordByImei(@RequestParam(value = "imei") String imei);

    /**
     * 是否可以购买服务判断
     * @param saleJiujiServiceReq
     * @return
     */
    @PostMapping("/getValidSaleService")
    @ApiOperation(value = "是否可以购买服务判断", httpMethod = "POST")
    R<ValidSaleJiujiServiceResVo> getValidSaleService(@Valid @RequestBody ValidSaleJiujiServiceReqVo saleJiujiServiceReq);


    /**
     * 获取用户壳膜订单
     * @param userId
     * @return
     */
    @GetMapping("/getOrderClassByUserId")
    @ApiOperation(value = "获取用户壳膜订单", httpMethod = "GET")
    R<Integer> getOrderClassByUserId(@RequestParam(value = "userId") Integer userId);

    /**
     * 该串号下是否有贴膜信息
     * @param imei 串号
     * @return
     */
    @GetMapping("/getFilmByImei")
    @ApiOperation(value = "该串号下是否有贴膜信息", httpMethod = "GET")
    R<Boolean> getFilmByImei(@RequestParam("imei") String imei);

    @ApiOperation(value = "个人最近的业绩统计")
    @GetMapping("/person/recent/{ch999Id}")
    R<Map<String,Object>> personRecent(@PathVariable("ch999Id") Integer ch999Id,
                                       @ApiParam("最近的几个月内的数据") @RequestParam("months") Integer months);

    /**
     * 添加售后日志
     * @param req
     * @return
     */
    @PostMapping("/addShouHouLog")
    R<Boolean> addShouhouLog(@Valid @RequestBody ShouhouLogAddReq req);

    /**
     * 设置预约单来源类型
     */
    @PostMapping("/setShouhouyuyueFromSource")
    R<Boolean> setShouhouyuyueFromSource (@Valid @RequestBody ShouhouyuyueFromSource req);


    /**
     * 小件换货商品列表
     *
     * @param req 请求参数
     * @return
     */
    @PostMapping("/listSmallExchangeProduct")
    R<ExchangeProductListVO> listSmallExchangeProduct(@Valid @RequestBody SmallExchangeReq req);

    /**
     * 进行中的预约单和小件单
     *
     * @param basketIds
     * @return
     */
    @PostMapping("/listSmallYuyueOrderOnGoing")
    @Deprecated
    R<List<SmallYuyueOrderOnGoingVO>> listSmallYuyueOrderOnGoing(@Valid @RequestBody List<Integer> basketIds);

    /**
     * 进行中的预约单和小件单
     *
     * @param basketIds
     * @return
     */
    @PostMapping("/listSmallYuyueOrderOnGoing")
    R<List<SmallYuyueOrderOnGoingVO>> listSmallYuyueOrderOnGoing(@Valid @RequestBody List<Integer> basketIds,
                                                                 @RequestParam(value = "userId", required = false) Integer userId);



    /**
     * 拍机堂一键售后
     */
    @PostMapping("/addPjtShouhou")
    R<PjtShouhouRes> addPjtShouhou (@Valid @RequestBody PjtShouhouReq req);


    /**
     * 添加切膜成功日志
     * @param req
     * @return
     */
    @PostMapping("/addCutScreenReqLog")
    R<Boolean> addCutScreenReqLog(@Valid @RequestBody CutScreenReq req);

    /**
     * 添加切膜成功日志
     * @param req
     * @return
     */
    @PostMapping("/addCutScreenReqLogShouhou")
    R<Boolean> addCutScreenReqLogShouhou(@Valid @RequestBody CutScreenShouhouReq req);

    /**
     * 获取所有三方收银列表
     * @param orderId
     * @param tuihuanKind
     * @return
     */
    @GetMapping("/groupRefund/listAllThirdShouYing")
    @ApiOperation(value = "获取所有三方收银列表", notes = "获取所有三方收银列表")
    R<List<ThirdOriginRefundVo>> listAllThirdShouYing(@RequestParam(value = "orderId") Integer orderId,
                                                      @RequestParam(value = "tuihuanKind") Integer tuihuanKind);

    /**
     * 获取良品扣减服务费信息
     */
    @GetMapping("/groupRefundMachine/getRecoverNoReasonReduce")
    @ApiOperation(value = "获取良品扣减服务费信息", notes = "获取良品扣减服务费信息")
    R<RecoverNoReasonReduceVo> getRecoverNoReasonReduce(@RequestParam(value = "userId") Integer userId);



    /**
     * 是否用户单据
     * @see BusinessTypeEnum
     * @param orderId
     * @param userId
     * @param orderType
     * @return
     */
    @GetMapping("/isUserOrder")
    @ApiOperation(value = "是否用户单据", notes = "是否用户单据")
    R<Boolean> isUserOrder(@RequestParam(value = "orderId") Integer orderId,
                           @RequestParam(value = "userId", required = false) Integer userId,
                           @RequestParam(value = "orderType") Integer orderType
    );

}
