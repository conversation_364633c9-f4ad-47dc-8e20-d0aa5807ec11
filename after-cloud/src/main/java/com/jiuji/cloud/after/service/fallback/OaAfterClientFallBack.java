package com.jiuji.cloud.after.service.fallback;

import com.alibaba.fastjson.JSON;
import com.jiuji.cloud.after.service.OaAfterClient;
import com.jiuji.cloud.after.vo.jiujiservice.ValidSaleJiujiServiceReqVo;
import com.jiuji.cloud.after.vo.jiujiservice.ValidSaleJiujiServiceResVo;
import com.jiuji.cloud.after.vo.refund.RecoverNoReasonReduceVo;
import com.jiuji.cloud.after.vo.refund.ThirdOriginRefundVo;
import com.jiuji.cloud.after.vo.req.*;
import com.jiuji.cloud.after.vo.res.PjtShouhouRes;
import com.jiuji.cloud.after.vo.res.SmallproLogRes;
import com.jiuji.oa.afterservice.shouhou.vo.ProductKcCountInfoRes;
import com.jiuji.oa.afterservice.shouhou.vo.ServiceInfoVO;
import com.jiuji.oa.afterservice.shouhou.vo.ServiceRecord;
import com.jiuji.oa.afterservice.shouhou.vo.ShouhouRepaireUserVo;
import com.jiuji.oa.afterservice.shouhou.vo.req.ExternalStatisticsReq;
import com.jiuji.oa.afterservice.shouhou.vo.req.ProductKcCountInfoQueryReq;
import com.jiuji.oa.afterservice.shouhou.vo.res.AfterSaleResultRes;
import com.jiuji.oa.afterservice.shouhou.vo.res.ExternalStatisticsRes;
import com.jiuji.oa.afterservice.shouhou.vo.res.ShouHouDetailRes;
import com.jiuji.oa.afterservice.shouhou.vo.res.ShouhouRepairInfoRes;
import com.jiuji.oa.afterservice.small.ExchangeProductListVO;
import com.jiuji.oa.afterservice.small.SmallYuyueOrderOnGoingVO;
import com.jiuji.oa.afterservice.yuyue.vo.req.ShouhouYuyueReq;
import com.jiuji.oa.afterservice.yuyue.vo.res.ShouHouYuyueRes;
import com.jiuji.tc.common.vo.R;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.text.MessageFormat;
import java.util.List;
import java.util.Map;

/**
 * @Description:
 * @author: Li quan
 * @date: 2020/9/3 10:25
 */
@Component
public class OaAfterClientFallBack implements OaAfterClient {
    @Override
    public R<SmallproLogRes> addSmallproLog(SmallproAddLogReq smallproAddLogReq) {
        return R.error("远程调用/afterservice/api/wcf/addSmallproLog 失败,参数["+smallproAddLogReq.toString()+"]");
    }

    @Override
    public R<ShouHouDetailRes> getShouHouDetail(Integer id, Integer userid, String imei) {
        return R.error("远程调用/afterservice/api/wcf/getShouHouDetail 失败,参数[id:"+id+",userid:"+userid+",imei:"+imei+"]");
    }

    @Override
    public R<ShouHouYuyueRes> getYuYueDetail(Integer id, Integer userid) {
        return R.error("远程调用/afterservice/api/wcf/getYuYueDetail 失败,参数[id:"+id+",userid:"+userid+"]");
    }

    @Override
    public R<Integer> addShouhouYuyue(ShouhouYuyueReq req) {
        return R.error("远程调用/afterservice/api/wcf/addShouhouYuyue 失败,参数["+req+"]");
    }

    @Override
    public R<Boolean> delYuyue(Integer yyid, String cancelType, String remark) {
        return R.error("远程调用/afterservice/api/wcf/delYuyue 失败,参数[yyid:"+yyid+",cancelType" + cancelType + "remark:" +remark+ "]");
    }

    @Override
    public R<Boolean> delYuyue(Integer yyid, String cancelType, String remark, Integer userId) {
        return R.error("远程调用/afterservice/api/wcf/delYuyue 失败,参数[yyid:"+yyid+",cancelType" + cancelType + "remark:" +remark+ "+ userId:" +userId+ "]");
    }

    @Override
    public R<AfterSaleResultRes> getAfterSale(Integer rows) {
        return R.error( "远程调用/afterservice/api/wcf/getAfterSale 失败,参数[rows:"+rows+"]");
    }

    @Override
    public R<List<ShouhouRepaireUserVo>> getShouhouRepaireUserInfo(List<Long> userId) {
        return R.error("远程调用/afterservice/api/wcf/getShouhouRepaireUserInfo 失败,参数[userId:"+userId+"]");
    }

    @Override
    public R<ProductKcCountInfoRes> queryProductStockByPpid(ProductKcCountInfoQueryReq req) {
        return R.error("远程调用/afterservice/api/wcf/queryProductStockByPpid 失败,参数["+req+"]");
    }

    @Override
    public R<ProductKcCountInfoRes> queryProductStockByPpidV2(ProductKcCountInfoQueryReq req) {
        return R.error("远程调用/afterservice/api/wcf/queryProductStockByPpid/v2 失败,参数["+req+"]");
    }

    @Override
    public R<ProductKcCountInfoRes> queryProductStockByPpidV3(ProductKcCountInfoQueryReq req) {
        return R.error("远程调用/afterservice/api/wcf/queryProductStockByPpid/v3 失败,参数["+req+"]");
    }

    @Override
    public R<ShouhouRepairInfoRes> getRepairRecordByImei(String imei) {
        return R.error("远程调用/afterservice/api/wcf/getRepairRecordByImei 失败,参数["+imei+"]");
    }

    @Override
    public R<ServiceInfoVO> getServiceInfoV3(String imei, Integer userId, Boolean isSave) {
        return R.error(MessageFormat.format("远程调用/afterservice/api/wcf/getServiceInfo/v3 失败,参数[{0},{1,number,#},{2}]",imei,userId,isSave));
    }

    @Override
    public R<ServiceInfoVO> getServiceInfoV3(String imei, Integer userId, Integer subId, Boolean isSave) {
        return R.error(MessageFormat.format("远程调用/afterservice/api/wcf/getServiceInfo/v3 失败,参数[{0},{1,number,#},{3,number,#},{2}]",imei,userId,isSave, subId));
    }

    @Override
    public R<List<ServiceInfoVO>> listServiceInfo(List<String> imeis, Integer userId) {
        return R.error(MessageFormat.format("远程调用/afterservice/api/wcf/listServiceInfo 失败,参数[{0},{1,number,#},{2}]",imeis,userId));
    }

    @Override
    public R<List<ExternalStatisticsRes>> getWeiXiuMaoLi(@RequestBody ExternalStatisticsReq req){
        return R.error(MessageFormat.format("远程调用/afterservice/api/wcf/getWeiXiuMaoLi 失败,参数[{0},{1},{2}]",req.getStartTime(),req.getCh999IdList(),req.getEndTime()));
    }

    @Override
    public R<List<ExternalStatisticsRes>> getWeiXiuMaoLvPeiBi(@RequestBody ExternalStatisticsReq req){
        return R.error(MessageFormat.format("远程调用/afterservice/api/wcf/getWeiXiuMaoLvPeiBi 失败,参数[{0},{1},{2}]",req.getStartTime(),req.getCh999IdList(),req.getEndTime()));
    }

    @Override
    public R<List<ExternalStatisticsRes>> getDianChiDaShouLv(@RequestBody ExternalStatisticsReq req){
        return R.error(MessageFormat.format("远程调用/afterservice/api/wcf/getDianChiDaShouLv 失败,参数[{0},{1},{2}]",req.getStartTime(),req.getCh999IdList(),req.getEndTime()));
    }

    @Override
    public R<List<ExternalStatisticsRes>> getPingMuDaShouLv(@RequestBody ExternalStatisticsReq req){
        return R.error(MessageFormat.format("远程调用/afterservice/api/wcf/getPingMuDaShouLv 失败,参数[{0},{1},{2}]",req.getStartTime(),req.getCh999IdList(),req.getEndTime()));
    }

    @Override
    public R<List<ExternalStatisticsRes>> getFlunkWeiXiuShouLv(@RequestBody ExternalStatisticsReq req){
        return R.error(MessageFormat.format("远程调用/afterservice/api/wcf/getFlunkWeiXiuShouLv 失败,参数[{0},{1},{2}]",req.getStartTime(),req.getCh999IdList(),req.getEndTime()));
    }

    @Override
    public R<List<ServiceRecord>> getServiceRecordByImei(@RequestParam(value = "imei") String imei){
        return R.error(MessageFormat.format("远程调用/afterservice/api/wcf/getServiceRecordByImei 失败,参数[{0}]",imei));
    }

    @Override
    public R<ValidSaleJiujiServiceResVo> getValidSaleService(ValidSaleJiujiServiceReqVo saleJiujiServiceReq) {
        return R.error(MessageFormat.format("远程调用/afterservice/api/wcf/getValidSaleService 失败,参数[{0}]", JSON.toJSONString(saleJiujiServiceReq)));
    }

    @Override
    public R<Integer> getOrderClassByUserId(@RequestParam(value = "userId") Integer userId) {
        return R.error(MessageFormat.format("远程调用/afterservice/api/wcf/getOrderClassByUserId 失败,参数[{0}]", JSON.toJSONString(userId)));
    }

    @Override
    public R<Boolean> getFilmByImei(@RequestParam("imei") String imei) {
        return R.error(MessageFormat.format("远程调用/afterservice/api/wcf/getFilmByImei 失败,参数[{0}]", JSON.toJSONString(imei)));
    }

    @Override
    public R<Map<String, Object>> personRecent(Integer ch999Id, Integer months) {
        return R.error(MessageFormat.format("远程调用/afterservice/api/wcf/person/recent/{ch999Id} 失败,参数[{0},{1}]",ch999Id,months));
    }

    @Override
    public R<Boolean> addShouhouLog(@Valid ShouhouLogAddReq req) {
        return R.error(MessageFormat.format("远程调用/afterservice/api/wcf/addShouHouLog 失败,参数[{0}]", JSON.toJSONString(req)));
    }

    @Override
    public R<Boolean> setShouhouyuyueFromSource(ShouhouyuyueFromSource shouhouyuyueOriginTypeSetReq) {
        return R.error(MessageFormat.format("远程调用/afterservice/api/wcf/setShouhouyuyueFromSource 失败,参数[{0}]", JSON.toJSONString(shouhouyuyueOriginTypeSetReq)));
    }


    @Override
    public R<ExchangeProductListVO> listSmallExchangeProduct(SmallExchangeReq req) {
        return R.error(MessageFormat.format("远程调用/afterservice/api/wcf/listSmallExchangeProduct 失败,参数[{0}]", JSON.toJSONString(req)));
    }

    @Override
    public R<List<SmallYuyueOrderOnGoingVO>> listSmallYuyueOrderOnGoing(@Valid @RequestBody List<Integer> basketIds) {
        return R.error(MessageFormat.format("远程调用/afterservice/api/wcf/listSmallYuyueOrderOnGoing 失败,参数[{0}]", JSON.toJSONString(basketIds)));
    }

    @Override
    public R<List<SmallYuyueOrderOnGoingVO>> listSmallYuyueOrderOnGoing(List<Integer> basketIds, Integer userId) {
        return R.error(MessageFormat.format("远程调用/afterservice/api/wcf/listSmallYuyueOrderOnGoing 失败,参数[{0},{1}]",
                JSON.toJSONString(basketIds), JSON.toJSONString(userId)));
    }

    @Override
    public R<PjtShouhouRes> addPjtShouhou(@Valid PjtShouhouReq req) {
        return R.error(MessageFormat.format("远程调用/afterservice/api/wcf/addPjtShouhou 失败,参数[{0}]", JSON.toJSONString(req)));
    }

    @Override
    public R<Boolean> addCutScreenReqLog(@Valid CutScreenReq req) {
        return R.error(MessageFormat.format("远程调用/afterservice/api/wcf/addCutScreenReqLog 失败,参数[{0}]", JSON.toJSONString(req)));
    }

    @Override
    public R<Boolean> addCutScreenReqLogShouhou(@Valid CutScreenShouhouReq req) {
        return R.error(MessageFormat.format("远程调用/afterservice/api/wcf/addCutScreenReqLogShouhou 失败,参数[{0}]", JSON.toJSONString(req)));
    }

    @Override
    public R<List<ThirdOriginRefundVo>> listAllThirdShouYing(Integer orderId, Integer tuihuanKind) {
        return R.error(MessageFormat.format("远程调用/afterservice/api/wcf/groupRefund/listAllThirdShouYing 失败,参数[{0},{1}]", orderId, tuihuanKind));
    }

    @Override
    public R<RecoverNoReasonReduceVo> getRecoverNoReasonReduce(Integer userId) {
        return R.error(MessageFormat.format("远程调用/afterservice/api/wcf/groupRefundMachine/getRecoverNoReasonReduce 失败,参数[{0}]", userId));
    }

    @Override
    public R<Boolean> isUserOrder(Integer orderId, Integer userId, Integer orderType) {
        return R.error(MessageFormat.format("远程调用/afterservice/api/wcf/isUserOrder 失败,参数[{0}, {1}, {2}]", orderId, userId, orderType));
    }
}
