package com.jiuji.oa.afterservice.bigpro.service.impl;

import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.afterservice.bigpro.bo.HexiaoBo;
import com.jiuji.oa.afterservice.bigpro.bo.WxFeeBo;
import com.jiuji.oa.afterservice.bigpro.bo.WxPeiJianBo;
import com.jiuji.oa.afterservice.bigpro.bo.productkc.ProductAreaPrices;
import com.jiuji.oa.afterservice.bigpro.bo.wxpj.PjTransferBo;
import com.jiuji.oa.afterservice.bigpro.dao.ProductinfoMapper;
import com.jiuji.oa.afterservice.bigpro.dao.WxkcoutputMapper;
import com.jiuji.oa.afterservice.bigpro.entity.ProductSn;
import com.jiuji.oa.afterservice.bigpro.enums.ProductAttributeEnum;
import com.jiuji.oa.afterservice.bigpro.po.*;
import com.jiuji.oa.afterservice.bigpro.service.*;
import com.jiuji.oa.afterservice.bigpro.vo.req.ProductSnQueryReq;
import com.jiuji.oa.afterservice.bigpro.vo.res.ProductSimpleKcRes;
import com.jiuji.oa.afterservice.bigpro.vo.res.ShouhouCostPriceRes;
import com.jiuji.oa.afterservice.bigpro.vo.res.WeiXiuPjRes;
import com.jiuji.oa.afterservice.common.bo.OaUserBO;
import com.jiuji.oa.afterservice.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.afterservice.common.constant.RequestAttrKeys;
import com.jiuji.oa.afterservice.common.enums.MTableInfoEnum;
import com.jiuji.oa.afterservice.common.enums.XtenantEnum;
import com.jiuji.oa.afterservice.common.exception.CustomizeException;
import com.jiuji.oa.afterservice.common.exception.RRExceptionHandler;
import com.jiuji.oa.afterservice.common.util.CommenUtil;
import com.jiuji.oa.afterservice.common.util.SpringContextUtil;
import com.jiuji.oa.afterservice.other.enums.ProductLabelEnum;
import com.jiuji.oa.afterservice.sub.service.CategoryService;
import com.jiuji.oa.afterservice.sys.enums.ConfigEnum;
import com.jiuji.oa.afterservice.sys.po.SysConfig;
import com.jiuji.oa.afterservice.sys.service.SysConfigService;
import com.jiuji.oa.orginfo.areainfo.client.AreaInfoClient;
import com.jiuji.oa.orginfo.areainfo.vo.AreaInfo;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.common.vo.ResultCode;
import com.jiuji.tc.utils.common.build.LambdaBuild;
import com.jiuji.tc.utils.constants.NumberConstant;
import com.jiuji.tc.utils.enums.EnumUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.boot.logging.LogLevel;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-31
 */
@Service
@Slf4j
public class WxkcoutputServiceImpl extends ServiceImpl<WxkcoutputMapper, Wxkcoutput> implements WxkcoutputService {

    @Lazy
    @Resource
    private AreaInfoClient areaInfoClient;

    @Resource
    private ProductinfoService productinfoService;

    @Resource
    private ProductinfoMapper productinfoMapper;

    @Lazy
    @Resource
    private ShouhouService shouhouService;

    @Resource
    private ShouhouApplyService shouhouApplyService;

    @Resource
    private ShouhouTimerService shouhouTimerService;

    @Resource
    private DiaoboSubService diaoboSubService;

    @Resource
    private DiaobosubCommentService diaobosubCommentService;

    @Resource
    private AbstractCurrentRequestComponent currentRequestComponent;

    @Resource
    private SysConfigService sysConfigService;
    @Resource
    private CategoryService categoryService;
    @Resource
    private ProductSnService snService;

    private final static Integer KIND_VALUE = 2;
    @Override
    public List<Integer> selectRecoveryReturnCidList(Integer xTenant){
        List<Integer> recoveryReturnCidList = new ArrayList();
        if(XtenantEnum.isJiujiXtenant()){
            List<SysConfig> list = sysConfigService.lambdaQuery().eq(SysConfig::getCode, ConfigEnum.RECOVERY_RETURN_CID.getCode())
                    .eq(SysConfig::getXtenant, xTenant)
                    .orderByDesc(SysConfig::getId)
                    .list();
            if (CollectionUtils.isNotEmpty(list)) {
                SysConfig sysConfig = list.get(NumberConstant.ZERO);
                List<Integer> cidList = Arrays.asList(sysConfig.getValue().split(",")).stream().filter(NumberUtil::isNumber).map(Integer::new).collect(Collectors.toList());
                recoveryReturnCidList.addAll(categoryService.selectCategoryChildrenByCid(cidList));
            }
        }
        return recoveryReturnCidList;
    }

    @Override
    public List<HexiaoBo> getHexiao(Integer wxId) {
        if (wxId == null || wxId == 0) {
            return null;
        }
        OaUserBO oaUserBO = currentRequestComponent.getCurrentStaffId();
        Integer xTenant = oaUserBO.getXTenant();
        List<HexiaoBo> resList = CommenUtil.autoQueryHist(()->baseMapper.getHexiao(wxId,xTenant), MTableInfoEnum.WXKCOUTPUT_WXID,wxId);
        List<Integer> recoveryReturnCidList = selectRecoveryReturnCidList(xTenant);
        List<Integer> productLabelList = ProductLabelEnum.getProductLabelCodeByType("WX");
        if (CollectionUtils.isNotEmpty(resList)) {
            resList = resList.stream().map(e -> {
                if (e.getPrice() != null && e.getPrice1() != null) {
                    e.setPriceYh(e.getPrice1().subtract(e.getPrice()));
                }
                if (e.getPpid() != null && productLabelList.contains(e.getPLabel())) {
                    e.setPlabelText(EnumUtil.getMessageByCode(ProductLabelEnum.class, e.getPLabel()));
                }
                if(XtenantEnum.isJiujiXtenant()){
                    e.setIsRecoveryReturnCid(recoveryReturnCidList.contains(e.getCid()));
                    //判断如果是需要绑定sn的商品才进行查询sn
                    if(Boolean.TRUE.equals(e.getIsSn())){
                         Optional.ofNullable(snService.getProductSnByBasketIdAndPpid(new ProductSnQueryReq().setBasketId(e.getId()).setPpid(e.getPpid())))
                                 .ifPresent(productSn -> e.setSn(productSn.getSn()));
                    }
                }
                ProductAttributeEnum attributeEnum = Optional.ofNullable(EnumUtil.getEnumByCode(ProductAttributeEnum.class, e.getDianping())).orElse(ProductAttributeEnum.DEFAULT_NULL);
                e.setProductAttribute(attributeEnum.getMessage());
                return e;
            }).collect(Collectors.toList());
        }
        return resList;
    }

    @Override
    public Boolean checkExistShouhouService(Integer ppid, Integer shoushouId) {
        Integer count = CommenUtil.autoQueryHist(()->baseMapper.selectCount(new LambdaQueryWrapper<Wxkcoutput>().eq(Wxkcoutput::getPpriceid, ppid)
                .eq(Wxkcoutput::getWxid, shoushouId).ne(Wxkcoutput::getStats, 3)),  MTableInfoEnum.WXKCOUTPUT_WXID, shoushouId);
        if (count > 0) {
            return false;
        }
        return true;
    }

    @Override
    public Boolean saveWxkcoutput(Wxkcoutput wxkcoutput) {
        if (wxkcoutput == null) {
            return false;
        }
        return this.save(wxkcoutput);
    }

    @Override
    public R<List<WeiXiuPjRes>> getWeixiuPJ(Integer shouhouId) {
        List<WeiXiuPjRes> weiXiuPJList = CommenUtil.autoQueryHist(()->baseMapper.getWeixiuPJ(shouhouId), MTableInfoEnum.WXKCOUTPUT_WXID, shouhouId);
        if (CollectionUtils.isEmpty(weiXiuPJList)) {
            return R.error("无相关记录数");
        }
        return R.success(weiXiuPJList);
    }

    @Override
    public Integer getKcIdByWxId(Integer wxId) {
        return CommenUtil.autoQueryHist(()->baseMapper.getKcIdByWxId(wxId), MTableInfoEnum.WXKCOUTPUT_WXID, wxId);
    }

    @Override
    public Integer getWxKcCount(Integer shouhouId) {
        return CommenUtil.autoQueryHist(()->baseMapper.getWxKcCount(shouhouId), MTableInfoEnum.WXKCOUTPUT_WXID, shouhouId);
    }

    @Override
    public List<Integer> cidCheck(Integer shouhouId, List<Integer> limitIdList) {
        return CommenUtil.autoQueryHist(()->baseMapper.cidCheck(shouhouId, limitIdList), MTableInfoEnum.WXKCOUTPUT_WXID, shouhouId);
    }

    @Override
    public List<Integer> cidCheck1(Integer shouhouId, List<Integer> limitIdList) {
        return CommenUtil.autoQueryHist(()->baseMapper.cidCheck1(shouhouId, limitIdList), MTableInfoEnum.WXKCOUTPUT_WXID, shouhouId);
    }

    @Override
    public List<WxPeiJianBo> getWxPeijianByShouhouId(Integer shouhouId) {
        return CommenUtil.autoQueryHist(()->baseMapper.getWxPeijianByShouhouId(shouhouId), MTableInfoEnum.WXKCOUTPUT_WXID, shouhouId);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public R<Boolean> autoOutStockWhenCaiGouInStock(Integer transferId, Integer orderType) {
        List<PjTransferBo> transferSubInfoList = baseMapper.getTransferSubInfo(transferId, orderType);

        PjTransferBo.OrderTypeEnum orderTypeEnum = EnumUtil.getEnumByCode(PjTransferBo.OrderTypeEnum.class,
                ObjectUtil.defaultIfNull(orderType, 0));
        if(orderTypeEnum == null){
            return R.error("订单类型错误");
        }

        if (CollectionUtils.isEmpty(transferSubInfoList)) {
            return R.success("没有需要处理的数据");
        }

        List<Integer> wxkIds = transferSubInfoList.stream()
            .map(PjTransferBo::getWxId)
            .distinct()
            .collect(Collectors.toList());

        // 构建(wxid, ppriceid)元组映射
        Map<Pair<Integer, Integer>, Boolean> existMap = lambdaQuery()
            .in(Wxkcoutput::getWxid, wxkIds)
            .select(Wxkcoutput::getWxid, Wxkcoutput::getPpriceid)
            .list()
            .stream()
            .collect(Collectors.toMap(
                output -> Pair.of(output.getWxid(), output.getPpriceid()),
                output -> true,
                    (v1,v2) -> v1
            ));

        // 过滤掉已存在的记录
        transferSubInfoList = transferSubInfoList.stream()
            .filter(tsi -> !existMap.containsKey(Pair.of(tsi.getWxId(), tsi.getPpid())))
            .collect(Collectors.toList());

        log.info("{}处理开始, 单号: {}", orderTypeEnum.getMessage(), transferId);
        if (CollectionUtils.isEmpty(transferSubInfoList)) {
            return R.success("所有数据已经出库,请勿重复出库");
        }
        OaUserBO oaUser = currentRequestComponent.getCurrentStaffId();
        Optional<PjTransferBo> firstPjOpt = transferSubInfoList.stream().filter(tsi -> tsi.getToAreaId() != null).findFirst();
        if(oaUser == null){
            //模拟一个用户信息
            oaUser = LambdaBuild.create(new OaUserBO()).set(OaUserBO::setUserName, "系统").set(OaUserBO::setXTenant, XtenantEnum.getXtenant())
                    .set(OaUserBO::setAreaId, firstPjOpt.map(PjTransferBo::getToAreaId).orElse(null))
                    .build();
            OaUserBO finalOaUser = oaUser;
            SpringContextUtil.getRequest().ifPresent(req -> req.setAttribute(RequestAttrKeys.REQUEST_ATTR_OA_USER, finalOaUser));
        }
        transferSubInfoList.stream().forEach(item -> {
            WxFeeBo wxFeeBo = this.searchKcByPpid(item.getToAreaId(), item.getPpid(), item.getWxId());
            Shouhou shouhou = Optional.ofNullable(shouhouService.getById(item.getWxId())).orElse(null);
            if (wxFeeBo != null && shouhou != null && !CommenUtil.isCheckTrue(shouhou.getIsquji())) {
                R<ShouhouCostPriceRes> acpR = shouhouService.addCostPrice(wxFeeBo, Boolean.FALSE, Boolean.TRUE);
                log.warn("添加配件结果: {}, 业务日志: {}", JSON.toJSONString(acpR), SpringContextUtil.getRequestKeyListMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG));
                if(!acpR.isSuccess()){
                    //某一个配件出库失败,全部失败
                    CustomizeException ce = new CustomizeException(acpR.getUserMsg());
                    RRExceptionHandler.log(LogLevel.WARN,orderTypeEnum.getMessage(), Dict.create().set("transferId",transferId).set("result",acpR),ce,null);
                    throw ce;
                }
                List<ShouhouApply> list = shouhouApplyService.list(new LambdaQueryWrapper<ShouhouApply>().eq(ShouhouApply::getWxid, item.getWxId()).eq(ShouhouApply::getPpid, item.getPpid()));
                if (CollectionUtils.isNotEmpty(list)) {
                    ShouhouApply shouhouApply = list.get(0);
                    long timeout = Duration.between(shouhouApply.getAddTime(), LocalDateTime.now()).toMinutes();
                    ShouhouTimer timer = new ShouhouTimer();
                    timer.setShouhouid(item.getWxId()).setPjdh(Math.toIntExact(timeout));
                    shouhouTimerService.addShouhouTimer(timer);
                    // 更新出库id
                    Integer applyId = item.getApplyId() == null ? shouhouApply.getId() : item.getApplyId();
                    boolean outputIdUp = shouhouApplyService.lambdaUpdate().eq(ShouhouApply::getId, applyId)
                            .set(ShouhouApply::getOutputId, wxFeeBo.getId()).update();
                    if(!outputIdUp){
                        throw new CustomizeException(StrUtil.format("更新出库配件关联失败, 售后单[{}]ppid[{}]", item.getWxId(), item.getPpid()));
                    }
                }
            }
        });
        //同一个单里面的 diaobo_basket 都满足 lcount <= InStockCount 才能自动完成
        //记录日志信息
        if(PjTransferBo.OrderTypeEnum.TRANSFER.equals(orderTypeEnum)) {
            DiaobosubComment comment = new DiaobosubComment();
            comment.setComment("维修单自动出配件操作成功")
                    .setDbId(transferId)
                    .setDtime(LocalDateTime.now())
                    .setInuser("系统")
                    .setShowType(Boolean.TRUE);
            DiaoboBasketService diaoboBasketService = SpringUtil.getBean(DiaoboBasketService.class);
            boolean isAllInStock = diaoboBasketService.lambdaQuery().eq(DiaoboBasket::getSubId, transferId)
                    .apply("isnull(lcount,0)>isnull(InStockCount,0)").count() <= 0;
            if (isAllInStock) {
                //存在未到货的商品
                boolean isAutoComplete = diaoboSubService.update(new LambdaUpdateWrapper<DiaoboSub>()
                        .set(DiaoboSub::getStats, NumberConstant.FOUR)
                        .eq(DiaoboSub::getId, transferId)
                        .ne(DiaoboSub::getStats, NumberConstant.FOUR));
                if (isAutoComplete) {
                    comment.setComment(comment.getComment() + ",调拨单自动完成").setOperationKinds(NumberConstant.FOUR);
                }
            }
            diaobosubCommentService.save(comment);
        }
        return R.success("操作成功");
    }

    @Override
    public Wxkcoutput getByIdAndXtenantAndAuthPart(Integer id, Integer xTenant, Integer areaId, boolean hasAuthPart, Integer authorizeId) {
        return CommenUtil.autoQueryHist(()->baseMapper.getByIdAndXtenantAndAuthPart(id,xTenant,areaId,hasAuthPart,authorizeId), MTableInfoEnum.wxkcoutput, id);
    }

    @Override
    public List<Wxkcoutput> listYouhuiMaAllocation(Integer shouhouId, Set<Integer> wxkcIds, List<Integer> shouhouServicesPpriceids) {
        return CommenUtil.autoQueryHist(()->baseMapper.listYouhuiMaAllocation(shouhouId,wxkcIds,shouhouServicesPpriceids), MTableInfoEnum.WXKCOUTPUT_WXID, shouhouId);
    }

    @Override
    public int updateYouHuiFeiYongBatch(Integer shouhouId,String youHuiMa, List<Wxkcoutput> wxkcoutputs) {
        return CommenUtil.autoQueryHist(()->baseMapper.updateYouHuiFeiYongBatch(shouhouId,youHuiMa,wxkcoutputs));
    }

    @Override
    public BigDecimal sumCancelPjYouHuiFeiyong(Integer wxId) {
        return CommenUtil.autoQueryHist(()->baseMapper.sumCancelPjYouHuiFeiyong(wxId), MTableInfoEnum.WXKCOUTPUT_WXID, wxId);
    }

    @Override
    public int updateServiceType(List<Integer> servicePjIds, Integer serviceType) {
        return CommenUtil.autoQueryHist(()->baseMapper.updateServiceType(servicePjIds,serviceType));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int cancelServiceType(Integer wxId, Integer serviceType) {
        return CommenUtil.autoQueryHist(()->baseMapper.cancelServiceType(wxId,serviceType), MTableInfoEnum.WXKCOUTPUT_WXID, wxId);
    }

    private WxFeeBo searchKcByPpid(Integer areaId, Integer ppid, Integer wxId) {
        List<ProductSimpleKcRes> productKcList = productinfoMapper.searchProductKC(areaId, "", ppid, null, null);
        if (CollectionUtils.isEmpty(productKcList)) {
            return null;
        }

        R<AreaInfo> areaInfoR = areaInfoClient.getAreaInfoById(areaId);
        if (areaInfoR.getCode() != ResultCode.SUCCESS || areaInfoR.getData() == null) {
            return null;
        }
        ProductSimpleKcRes productKcInfo = productKcList.get(0);
        BigDecimal memberPrice = productKcInfo.getMemberPrice();
        Integer xTenant = areaInfoR.getData().getXtenant();
        if (xTenant > 0) {
            List<ProductAreaPrices> productAreaPricesInfo = productinfoService.getProductAreaPrices(0, ppid, xTenant);
            if (CollectionUtils.isNotEmpty(productAreaPricesInfo)) {
                ProductAreaPrices pricesInfo = productAreaPricesInfo.stream().filter(t -> Objects.equals(ppid, t.getPpid())).findFirst().orElse(null);
                if (pricesInfo != null && pricesInfo.getPrice() != null) {
                    memberPrice = pricesInfo.getPrice();
                }
            }
        }

        WxFeeBo result = new WxFeeBo();
        result.setPid(productKcInfo.getPid())
                .setPpid(ppid)
                .setCid(productKcInfo.getCid())
                .setProductName(productKcInfo.getProductName())
                .setColor(productKcInfo.getProductColor())
                .setPrice(memberPrice)
                .setInprice(productKcInfo.getInprice())
                .setShouhouId(wxId)
                .setUser("系统")
                .setKinds(KIND_VALUE)
                .setAreaId(areaId)
                .setPrice1(memberPrice)
                .setPriceGs(BigDecimal.ZERO);

        return result;
    }
}
