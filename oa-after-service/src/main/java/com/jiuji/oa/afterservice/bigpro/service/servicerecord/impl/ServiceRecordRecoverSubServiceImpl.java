package com.jiuji.oa.afterservice.bigpro.service.servicerecord.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.jiuji.oa.afterservice.bigpro.bo.externalrepair.SimpleServiceSubInfoBo;
import com.jiuji.oa.afterservice.bigpro.dao.servicerecord.ServiceRecordRecoverSubMapper;
import com.jiuji.oa.afterservice.bigpro.po.ServiceRecord;
import com.jiuji.oa.afterservice.bigpro.service.servicerecord.ServiceRecordRecoverSubService;
import com.jiuji.oa.afterservice.common.constant.DataSourceConstants;
import com.jiuji.oa.afterservice.common.enums.XtenantEnum;
import com.jiuji.oa.afterservice.shouhou.vo.ServiceInfoVO;
import com.jiuji.tc.foundation.db.annotation.DS;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * 外修服务购买
 * <AUTHOR>
 * @since 2022/1/11 10:17
 */
@Service
@Slf4j
public class ServiceRecordRecoverSubServiceImpl implements ServiceRecordRecoverSubService {
    @Autowired
    private ServiceRecordRecoverSubMapper recordRecoverSubMapper;

    /**
     * 获取简单的订单信息
     */
    @Override
    public SimpleServiceSubInfoBo getSimpleSubInfo(String imei, Integer userId, Integer xTenant, Integer subId){
        ServiceRecordRecoverSubService recoverSubService = SpringUtil.getBean(ServiceRecordRecoverSubService.class);
        //输出查询生效的就行,九机只查询已完成的订单
        boolean isSaas = !XtenantEnum.isJiujiXtenant(xTenant);
        return Optional.ofNullable(recoverSubService.getSimpleNormalSub(imei,userId, isSaas, subId)).map(Optional::of)
                //目前良品单不查询历史库
                .orElseGet(()->Optional.of(XtenantEnum.isJiujiXtenant()).filter(Boolean::booleanValue)
                        .map(isHist->recoverSubService.getSimpleHistorySub(imei,userId,isSaas, subId)).map(ssi->ssi.setIsHistory(Boolean.TRUE)))
                .map(ssi-> ssi.setOrderType(SimpleServiceSubInfoBo.OrderTypeEnum.LP_ORDER)
                        .setIsHistory(ObjectUtil.defaultIfNull(ssi.getIsHistory(),Boolean.FALSE)))
                .orElse(null);
    }

    @Override
    public ServiceInfoVO getServiceInfo(Integer subId, String imei, boolean isHistory){
        //通过订单号获取订单信息
        ServiceRecordRecoverSubService recoverSubService = SpringUtil.getBean(ServiceRecordRecoverSubService.class);
        ServiceInfoVO result;
        if(isHistory){
            result = recoverSubService.getHistorySub(subId,imei);
        }else{
            result = recoverSubService.getNormalSub(subId,imei);
        }
        return Optional.ofNullable(result)
                .map(h->h.setOrderType(SimpleServiceSubInfoBo.OrderTypeEnum.LP_ORDER.getCode()).setIsHistory(isHistory).setHuishou(Boolean.TRUE))
                .orElse(null);
    }

    @Override
    public List<ServiceRecord> list9jiServiceRecord(Integer subId, String imei, boolean isHistory) {
        //通过商品id获取九机服务
        ServiceRecordRecoverSubService recoverSubService = SpringUtil.getBean(ServiceRecordRecoverSubService.class);
        return recoverSubService.listNormal9jiServiceRecord(subId,imei);
    }

    @Override
    @DS(DataSourceConstants.CH999_OA_NEW)
    public SimpleServiceSubInfoBo getSimpleNormalSub(String imei, Integer userId, boolean isEffect, Integer subId) {
        return recordRecoverSubMapper.getSimpleSub(imei,userId,isEffect, subId);
    }

    @Override
    @DS(DataSourceConstants.OA_NEW_HIS)
    public SimpleServiceSubInfoBo getSimpleHistorySub(String imei, Integer userId, boolean isEffect, Integer subId) {
        return recordRecoverSubMapper.getSimpleSub(imei,userId,isEffect, subId);
    }

    @Override
    @DS(DataSourceConstants.CH999_OA_NEW)
    public ServiceInfoVO getNormalSub(Integer subId, String imei) {
        return recordRecoverSubMapper.getSubById(subId, imei);
    }

    @Override
    @DS(DataSourceConstants.OA_NEW_HIS)
    public ServiceInfoVO getHistorySub(Integer subId, String imei) {
        return recordRecoverSubMapper.getSubById(subId,imei);
    }

    @Override
    @DS(DataSourceConstants.CH999_OA_NEW)
    public List<ServiceRecord> listNormal9jiServiceRecord(Integer basketId, String imei) {
        return recordRecoverSubMapper.list9jiServiceRecord(basketId,imei);
    }

    @Override
    @DS(DataSourceConstants.OA_NEW_HIS)
    public List<ServiceRecord> listHistory9jiServiceRecord(Integer basketId, String imei) {
        return recordRecoverSubMapper.list9jiServiceRecord(basketId,imei);
    }
}
