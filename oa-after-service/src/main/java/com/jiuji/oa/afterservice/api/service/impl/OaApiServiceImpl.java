package com.jiuji.oa.afterservice.api.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import cn.hutool.log.StaticLog;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.jiuji.cloud.after.enums.ProductTypeEnum;
import com.jiuji.oa.afterservice.EvaluateJobEnum;
import com.jiuji.oa.afterservice.api.bo.*;
import com.jiuji.oa.afterservice.api.dao.OaApiMapper;
import com.jiuji.oa.afterservice.api.service.OaApiService;
import com.jiuji.oa.afterservice.apollo.ApolloEntity;
import com.jiuji.oa.afterservice.bigpro.enums.*;
import com.jiuji.oa.afterservice.bigpro.po.*;
import com.jiuji.oa.afterservice.bigpro.repository.ShouhouLogNewRepository;
import com.jiuji.oa.afterservice.bigpro.repository.document.ShouhouLogNew;
import com.jiuji.oa.afterservice.bigpro.service.*;
import com.jiuji.oa.afterservice.bigpro.vo.req.FileReq;
import com.jiuji.oa.afterservice.bigpro.vo.req.ProductSnQueryReq;
import com.jiuji.oa.afterservice.bigpro.vo.req.ProductSnUpdateBindReq;
import com.jiuji.oa.afterservice.bigpro.vo.res.ExpressDetailResVo;
import com.jiuji.oa.afterservice.bigpro.vo.res.ShouhouWxzqRes;
import com.jiuji.oa.afterservice.common.config.properties.JiujiSystemProperties;
import com.jiuji.oa.afterservice.common.constant.ConfigConsts;
import com.jiuji.oa.afterservice.common.enums.BusinessTypeEnum;
import com.jiuji.oa.afterservice.common.enums.ESmsChannelTypeEnum;
import com.jiuji.oa.afterservice.common.enums.MTableInfoEnum;
import com.jiuji.oa.afterservice.common.enums.XtenantEnum;
import com.jiuji.oa.afterservice.common.exception.CustomizeException;
import com.jiuji.oa.afterservice.common.exception.RRExceptionHandler;
import com.jiuji.oa.afterservice.common.source.MoaUrlSource;
import com.jiuji.oa.afterservice.common.source.OaWcfUrlSource;
import com.jiuji.oa.afterservice.common.util.CommenUtil;
import com.jiuji.oa.afterservice.common.util.DateUtil;
import com.jiuji.oa.afterservice.electronicTicket.mapper.AfterElectronicTicketMapper;
import com.jiuji.oa.afterservice.express.WuliuInfo;
import com.jiuji.oa.afterservice.shouhou.vo.*;
import com.jiuji.oa.afterservice.shouhou.vo.req.ShouhouDaiYongjiAttachmentsAddReq;
import com.jiuji.oa.afterservice.shouhou.vo.res.AfterSaleResultRes;
import com.jiuji.oa.afterservice.shouhou.vo.res.ServiceIntroduce;
import com.jiuji.oa.afterservice.shouhou.vo.res.ShouHouDetailRes;
import com.jiuji.oa.afterservice.smallpro.bo.WxBO;
import com.jiuji.oa.afterservice.smallpro.dao.SmallproMapper;
import com.jiuji.oa.afterservice.smallpro.enums.JiujiNumberCardTypeEnum;
import com.jiuji.oa.afterservice.smallpro.po.Smallpro;
import com.jiuji.oa.afterservice.smallpro.service.SmallproService;
import com.jiuji.oa.afterservice.sub.po.Sub;
import com.jiuji.oa.afterservice.sub.service.CategoryService;
import com.jiuji.oa.afterservice.sub.service.SubService;
import com.jiuji.oa.afterservice.sys.service.SysConfigService;
import com.jiuji.oa.afterservice.yuyue.vo.*;
import com.jiuji.oa.afterservice.yuyue.vo.req.ShouhouYuyueReq;
import com.jiuji.oa.afterservice.yuyue.vo.req.ShouhouYuyueReqVo;
import com.jiuji.oa.afterservice.yuyue.vo.req.YuyueTuiDataReqVo;
import com.jiuji.oa.afterservice.yuyue.vo.res.ShouHouYuyueRes;
import com.jiuji.oa.nc.SmsConfigCloud;
import com.jiuji.oa.nc.sms.SmsConfigCodeEnum;
import com.jiuji.oa.nc.sms.SmsConfigVO;
import com.jiuji.oa.nc.sms.SmsPushMethodEnum;
import com.jiuji.oa.orginfo.areainfo.client.AreaInfoClient;
import com.jiuji.oa.orginfo.areainfo.vo.AreaInfo;
import com.jiuji.oa.orginfo.member.client.MemberClient;
import com.jiuji.oa.orginfo.sysconfig.client.SysConfigClient;
import com.jiuji.oa.orginfo.userinfo.client.UserInfoClient;
import com.jiuji.oa.orginfo.userinfo.vo.Ch999UserVo;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.common.vo.ResultCode;
import com.jiuji.tc.utils.common.CommonUtils;
import com.jiuji.tc.utils.constants.NumberConstant;
import com.jiuji.tc.utils.constants.SysConfigConstant;
import com.jiuji.tc.utils.enums.EnumUtil;
import com.jiuji.tc.utils.xtenant.Namespaces;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description:
 * @author: Li quan
 * @date: 2020/6/28 14:20
 */
@Slf4j
@Service
public class OaApiServiceImpl implements OaApiService {

    @Autowired
    private OaApiMapper oaApiMapper;
    @Resource
    private ShouhouLogNewRepository shouhouLogNewRepository;
    @Resource
    private AreaInfoClient areaInfoClient;
    @Autowired
    private ShouhouExService shouhouExService;
    @Resource
    private UserInfoClient userInfoClient;
    @Resource
    private MemberClient memberClient;
    @Autowired
    private ShouhouService shouhouService;
    @Autowired
    private AddinfopsService addinfopsService;
    @Autowired
    private ShouhouCommentService shouhouCommentService;
    @Autowired
    private AttachmentsService attachmentsService;
    @Autowired
    private WxkcoutputService wxkcoutputService;
    @Autowired
    private ShouhouYuyueService shouhouYuyueService;
    @Autowired
    private ShouhouYuyueproductinfoService shouhouYuyueproductinfoService;
    @Autowired
    private YuyueLogsService yuyueLogsService;
    @Autowired
    private ShouhouSendaddressService shouhouSendaddressService;
    @Resource
    private OaWcfUrlSource oaWcfUrlSource;
    @Autowired
    private InstallServicesRecordService installServicesRecordService;
    @Autowired
    private NumberCardService numberCardService;
    @Autowired
    private SmsService smsService;
    @Autowired
    private TroubleService troubleService;
    @Autowired
    private ShouhouYuyueTroubleService shouhouYuyueTroubleService;
    @Resource
    @Qualifier("oaRabbitTemplate")
    private RabbitTemplate rabbitTemplate;
    @Autowired
    private SmallproMapper smallproMapper;
    @Autowired
    private ShouhouMsgService shouhouMsgService;
    @Resource
    private MoaUrlSource moaUrlSource;
    @Value("${imei.url}")
    private String imeiQueryApiUrl;
    @Autowired
    private WeixinUserService weixinUserService;
    @Resource
    private JiujiSystemProperties jiujiSystemProperties;
    @Autowired
    private SysConfigService sysConfigService;
    @Resource
    private CategoryService categoryService;
    @Resource
    private SysConfigClient sysConfigClient;
    @Resource
    private AfterElectronicTicketMapper ticketMapper;
    @Resource
    private SmsConfigCloud smsConfigCloud;
    @Resource
    private ApolloEntity apolloEntity;


    @Override
    public R<ShouHouDetailRes> getShouHouDetail(Integer id, Integer userId, String imei) {
        if (imei == null) {
            imei = "";
        }
        Boolean isMobile = false;
        if (StringUtils.isNotEmpty(imei) && imei.length() == 11 && imei.startsWith("1")) {
            isMobile = true;
        }

        ShouHouDetailRes res = new ShouHouDetailRes();

        ShouHouDetailVo shouHouDetailVo = this.getShouhouInfoByIdAndUserIdAndImei(id, userId, imei, isMobile);
        //发票信息赋值
        if(id!=null){
            Optional.ofNullable(ticketMapper.selectpiaoProductInfo(id)).ifPresent(item->{
                shouHouDetailVo.setPiaoid(item.getPiaoid());
                Integer flag = item.getFlag();
                Integer piaoStatus;
                if(NumberConstant.THREE.equals(flag) || NumberConstant.FOUR.equals(flag)){
                    piaoStatus=NumberConstant.TWO;
                } else {
                    piaoStatus=NumberConstant.ONE;
                }
                shouHouDetailVo.setPiaoStatus(piaoStatus);
            });
        }
        if (shouHouDetailVo == null) {
            return R.error("查无相关记录");
        }
        Optional<ShouhouLogNew> shouhouLogNewOpt = shouhouLogNewRepository.findById(id);
        List<ShouHouDetailVo.ShouhouLogs> logs = new LinkedList<>();
        if (shouhouLogNewOpt.isPresent()) {
            List<ShouhouLogNew.Conts> m = shouhouLogNewOpt.get().getCons().stream().filter(t -> t.getIsWeb() == true).collect(Collectors.toList());
            List<String> comments = m.stream().map(e -> {
                String comment = StringUtils.isNotEmpty(e.getComment()) && e.getComment().contains("after-service/risk-notice?") ? e.getComment() : e.getComment().replace("<a ", "<span").replace("</a>", "</span>");
                comment = comment
                        + "【" + e.getInUser() + "】" + DateUtil.localDateTimeToString(e.getDTime());
                return comment;
            }).collect(Collectors.toList());
            String result = org.apache.commons.lang3.StringUtils.join(comments, "<br>");
            shouHouDetailVo.setResult(result);

            ShouhouLogNew logNew = shouhouLogNewOpt.get();
            if (logNew != null && CollectionUtils.isNotEmpty(logNew.getCons())) {
                logs = logNew.getCons().stream().filter(t -> t.getIsWeb() != null && t.getIsWeb()).map(e -> {
                    ShouHouDetailVo.ShouhouLogs logsVo = new ShouHouDetailVo.ShouhouLogs();
                    logsVo.setContent(StringUtils.isNotEmpty(e.getComment()) && e.getComment().contains("after-service/risk-notice?") ? e.getComment() : e.getComment().replace("<a ", "<span").replace("</a>", "</span>").trim());
                    logsVo.setDtime(e.getDTime());
                    logsVo.setShouhouId(id);
                    logsVo.setId(id);
                    logsVo.setInuser(e.getInUser());
                    return logsVo;

                }).collect(Collectors.toList());
            }
        }

        shouHouDetailVo.setStateName(EnumUtil.getMessageByCode(WxStatusEnum.class, shouHouDetailVo.getStats()));
        setArea(shouHouDetailVo);

        // 设置subType
        Integer subType = CommenUtil.isNullOrZero(shouHouDetailVo.getSub_id()) ? 0 :
                Optional.ofNullable(shouHouDetailVo.getIshuishou()).orElse(0) > 0 ? 2 : 1;
        shouHouDetailVo.setSubType(subType);

        // 维修倒计时
        ShouhouWxzqRes wxzqRes = shouhouExService.getShouHouWeiXiuZqByShId(id);
        if (wxzqRes != null) {
            String text;
            if (shouHouDetailVo.getIsquji()) {
                text = StringUtils.isNotEmpty(wxzqRes.getTimeTextSum()) ? "维修时长：" + wxzqRes.getTimeTextSum() : "";
            } else {
                text = StringUtils.isNotEmpty(wxzqRes.getTimeTextDjs()) ? "维修倒计时：" + wxzqRes.getTimeTextDjs() : "";
            }
            shouHouDetailVo.setWxDjs(text);
        }

        //获取最后一条测试数据信息
        ShouhoutestInfo testInfo = shouhouExService.getLastTestInfoByShId(id);
        if (testInfo != null) {
            shouHouDetailVo.setIsPassTest(testInfo.getTestResult() != null && testInfo.getTestResult());
        }

        if (StringUtils.isNotEmpty(shouHouDetailVo.getInuser())) {

            R<Ch999UserVo> ch999UserVoR = userInfoClient.getCh999UserByUserName(shouHouDetailVo.getInuser());
            if (ch999UserVoR.getCode() == ResultCode.SUCCESS && ch999UserVoR.getData() != null) {
                Ch999UserVo ch999UserVo = ch999UserVoR.getData();
                SubCh999UserVo subCh999UserVo = new SubCh999UserVo();
                subCh999UserVo.setCh999_id(ch999UserVo.getCh999Id());
                subCh999UserVo.setCh999_name(ch999UserVo.getCh999Name());
                subCh999UserVo.setHeadImg(ch999UserVo.getAvatar());
                subCh999UserVo.setHeadImg(ch999UserVo.getAvatar()
                        .replace("https://moa.ch999.com/static", "https://img2.ch999img.com/newstatic")
                        .replace(moaUrlSource.getBasicUrl() + "/static", "https://img2.ch999img.com/newstatic"));
                subCh999UserVo.setJob(EvaluateJobEnum.ShouhouKefu.getCode());
                res.setSubCh999user(Collections.singletonList(subCh999UserVo));
            }
        }
        Shouhou sh = CommenUtil.autoQueryHist(() ->shouhouService.getById(id), MTableInfoEnum.SHOUHOU, id);
        //Shouhou sh = shouhouService.getById(id);
        if (sh != null && sh.getQujitongzhitime() != null) {
            shouHouDetailVo.setIsSendQuji(true);
        }

        shouHouDetailVo.setLogs(logs);

        //收件地址
        List<Addinfops> addinfopsList = addinfopsService.list(new LambdaQueryWrapper<Addinfops>().eq(Addinfops::getBindId, id).in(Addinfops::getType,
                Arrays.asList(1, 2)));
        if (CollectionUtils.isNotEmpty(addinfopsList)) {
            Optional<Addinfops> optional1 = addinfopsList.stream().filter(e -> e.getType() == 1).findAny();
            if (optional1.isPresent()) {
                AddinfopsVo addinfopsVo = new AddinfopsVo();
                addinfopsVo.setReciver(optional1.get().getReciver());
                addinfopsVo.setAddress(optional1.get().getAddress());
                addinfopsVo.setMobile(optional1.get().getMobile());
                addinfopsVo.setCityid(optional1.get().getCityid());
                addinfopsVo.setType(1);
                res.setShouhouAddinfo1(addinfopsVo);
            }
            Optional<Addinfops> optional2 = addinfopsList.stream().filter(e -> e.getType() == 2).findAny();
            if (optional2.isPresent()) {
                AddinfopsVo addinfopsVo = new AddinfopsVo();
                addinfopsVo.setReciver(optional2.get().getReciver());
                addinfopsVo.setAddress(optional2.get().getAddress());
                addinfopsVo.setMobile(optional2.get().getMobile());
                addinfopsVo.setCityid(optional2.get().getCityid());
                addinfopsVo.setType(2);
                res.setShouhouAddinfo2(addinfopsVo);
            }
        }

        //售后维修进程和留言
        R<List<ShouhouComment>> shouhouComments = shouhouCommentService.getShouhouComment(id);
        if (shouhouComments.getCode() == ResultCode.SUCCESS && CollectionUtils.isNotEmpty(shouhouComments.getData())) {
            List<ShouhouCommentVo> shouhou_commentList = shouhouComments.getData().stream().map(e -> {
                ShouhouCommentVo shouhouCommentVo = new ShouhouCommentVo();
                shouhouCommentVo.setDtime(DateUtil.localDateTime2Date(e.getDtime()));
                shouhouCommentVo.setComment(e.getComment());
                shouhouCommentVo.setReply(e.getReply());
                shouhouCommentVo.setReplytime(DateUtil.localDateTime2Date(e.getReplytime()));
                return shouhouCommentVo;

            }).collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(shouhou_commentList)) {
                res.setShouhou_commentList(shouhou_commentList);
            }
        }

        //售后附件
        List<Attachments> attachmentsList = attachmentsService.newList(new LambdaQueryWrapper<Attachments>().eq(Attachments::getType, AttachmentsEnum.SHOUHOU.getCode()).eq(Attachments::getLinkedID, id)
                .and(bo -> bo.eq(Attachments::getKind, 0).or().isNull(Attachments::getKind)).orderByAsc(Attachments::getId), id, AttachmentsEnum.SHOUHOU.getCode(), sh.getModidate());
        if (CollectionUtils.isNotEmpty(attachmentsList)) {
            List<AttachmentsVo> shouhou_attachList = attachmentsList.stream().filter(e -> !Objects.equals(e.getKind1(), 7)).map(e -> {
                AttachmentsVo attachmentsVo = new AttachmentsVo();
                attachmentsVo.setId(e.getId());
                attachmentsVo.setLinkedID(e.getLinkedID());
                attachmentsVo.setFilename(e.getFilename());
                attachmentsVo.setFilepath(e.getFilepath());
                return attachmentsVo;

            }).collect(Collectors.toList());
            res.setShouhou_attachList(shouhou_attachList);

            List<ShouhouDaiYongjiAttachmentsAddReq.FileItem> daiYongjiAttachemntsList = attachmentsList.stream().filter(e -> Objects.equals(e.getKind1(), 7)).map(e -> {
                ShouhouDaiYongjiAttachmentsAddReq.FileItem attachmentsVo = new ShouhouDaiYongjiAttachmentsAddReq.FileItem();
                attachmentsVo.setFid(e.getFid());
                attachmentsVo.setFileName(e.getFilename());
                attachmentsVo.setFilePath(e.getFilepath());
                return attachmentsVo;
            }).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(daiYongjiAttachemntsList) && CommenUtil.isNotNullZero(shouHouDetailVo.getDyjid())) {
                ShouhouDaiYongjiAttachmentsAddReq daiYongjiInfo = new ShouhouDaiYongjiAttachmentsAddReq();
                daiYongjiInfo.setShouhouId(id)
                        .setAttachmentsList(daiYongjiAttachemntsList)
                        .setDyjId(shouHouDetailVo.getDyjid())
                        .setProductName(shouHouDetailVo.getDyj_name());
                res.setDaiYongjiXieyiAttachments(daiYongjiInfo);
            }

        }

        //获取快递单号
        WuliuInfo wuliuInfo = oaApiMapper.getWuliuInfoByShouhouId(id);
        if (wuliuInfo != null) {
            res.setWuliuinfo(wuliuInfo);
            //如果是第三方快递，获取快递进程
            if (StringUtils.isNotEmpty(wuliuInfo.getCom()) && StringUtils.isNotEmpty(wuliuInfo.getNu())) {
                R<List<ExpressDetailResVo>> expressInfoR = shouhouExService.queryExpress(wuliuInfo.getCom(), wuliuInfo.getNu());

                ExpressResultVo expressResult = new ExpressResultVo();
                expressResult.setIsQuerySuccess(expressInfoR.getCode() == ResultCode.SUCCESS);
                List<ExpressResultVo.ExpressDetailResult> expressLogs = Optional.ofNullable(expressInfoR.getData())
                        .orElse(Collections.emptyList()).stream()
                        .map(e -> {
                            ExpressResultVo.ExpressDetailResult detail = new ExpressResultVo.ExpressDetailResult();
                            detail.setCityName(e.getCityName());
                            detail.setContent(e.getContent());
                            detail.setTime(e.getTime());
                            return detail;
                        }).collect(Collectors.toList());
                expressResult.setLogs(expressLogs);
                res.setExpressInfo(expressResult);
            }
        }

        //获取备用机
        ShouhouDaiyongjiVo shouhouDaiyongji = oaApiMapper.getShouhouDaiyongjiInfoByShouhouId(id);
        if (shouhouDaiyongji != null) {
            shouHouDetailVo.setDaiyongji(shouhouDaiyongji);
        }

        try {
            Integer shouHouId = shouHouDetailVo.getId();
            shouHouDetailVo.setIsDj(SpringUtil.getBean(ShouhouExtendService.class).isDJIRepairOrder(null, shouHouId));
            ProductSnQueryReq productSnQueryReq = new ProductSnQueryReq().setBasketId(shouHouDetailVo.getBasket_id()).setPpid(shouHouDetailVo.getPpriceid());
            ProductSnService snService = SpringUtil.getBean(ProductSnService.class);
            Optional.ofNullable(snService.getProductSnByBasketIdAndPpid(productSnQueryReq)).ifPresent(productSn -> {
                shouHouDetailVo.setSn(productSn.getSn());
            });
            //判断给网站判断是否存在大疆维修方案
            List<RepairPlanMaster> repairPlanList = SpringUtil.getBean(RepairPlanMasterService.class).lambdaQuery()
                    .eq(RepairPlanMaster::getShouHouId, shouHouId).list();
            if(CollUtil.isNotEmpty(repairPlanList)){
                Integer repairPlanMasterId = repairPlanList.get(0).getId();
                shouHouDetailVo.setRepairPlanMasterId(repairPlanMasterId);
            }
        } catch (Exception e){
            RRExceptionHandler.logError("提供给主站接口判断是否大疆维修单异常", shouHouDetailVo.getId(), e, SpringUtil.getBean(SmsService.class)::sendOaMsgTo9JiMan);
        }


        List<WxkcInfoVo> wxkcOutputList = getWxkcOutputList(id);
        res.setWxkcList(wxkcOutputList);

        List<ShouhouPriceBo> priceDataList = oaApiMapper.getShouhouPriceByShouhouId(id);
        if (CollectionUtils.isNotEmpty(priceDataList)) {
            BigDecimal servicePrice = priceDataList.stream().filter(e -> e.getKinds() == 3).map(e -> e.getPrice()).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal youhuiPrice = priceDataList.stream().filter(e -> Arrays.asList(1, 2, 4).contains(e.getKinds())).map(e -> e.getPrice()).reduce(BigDecimal.ZERO, BigDecimal::add);
            youhuiPrice = youhuiPrice.subtract(shouHouDetailVo.getServicePrice() == null ? BigDecimal.ZERO : shouHouDetailVo.getServicePrice());
            BigDecimal shouhouServicePrice = shouHouDetailVo.getServicePrice() == null ? BigDecimal.ZERO : shouHouDetailVo.getServicePrice();
            BigDecimal totalPrice = shouhouServicePrice.add(youhuiPrice).add(shouHouDetailVo.getPrice() == null ? BigDecimal.ZERO : shouHouDetailVo.getPrice());

            shouHouDetailVo.setServicePrice(servicePrice);
            shouHouDetailVo.setYouhuiPrice(youhuiPrice);
            shouHouDetailVo.setTotalPrice(totalPrice);
        }

        //售后服务介绍
        Integer serviceType = sh.getServiceType();
        if (CommenUtil.isNotNullZero(serviceType) && Arrays.stream(ServiceTypeEnum.values()).map(ServiceTypeEnum::getCode).anyMatch(st -> Objects.equals(st,serviceType))) {
            ServiceIntroduce serviceIntroduce = sysConfigService.getServiceIntroduce(serviceType);
            res.setServiceIntroduce(serviceIntroduce);
        }
        ProductTypeEnum productTypeEnum = shouhouService.getProductTypeEnum(sh);
        if(productTypeEnum != null){
            shouHouDetailVo.setProductType(productTypeEnum.getCode());
        }

        res.setShouhouDetail(shouHouDetailVo);
        return R.success(res);
    }

    @Override
    public R<ShouHouYuyueRes> getYuYueDetail(Integer id, Integer userid) {
        ShouhouYuyue yuyueInfo = shouhouYuyueService.getById(id);
        if (yuyueInfo == null) {
            return R.error("查无相关记录");
        }
        if (!yuyueInfo.getUserid().equals(userid)) {
            yuyueInfo = null;
            //兼容预约单和售后单userid不一致问题
            List<Shouhou> shouhouList = CommenUtil.autoQueryHist(()->shouhouService.list(new LambdaQueryWrapper<Shouhou>().select(Shouhou::getId).eq(Shouhou::getYuyueid, id).eq(Shouhou::getUserid, userid)));
           // List<Shouhou> shouhouList = shouhouService.list(new LambdaQueryWrapper<Shouhou>().select(Shouhou::getId).eq(Shouhou::getYuyueid, id).eq(Shouhou::getUserid, userid));
            if (CollectionUtils.isNotEmpty(shouhouList)) {
                yuyueInfo = shouhouYuyueService.getById(id);
            }
        }
        if (yuyueInfo == null) {
            return R.error("查无相关记录");
        }

        String kuaidigongsiName = EnumUtil.getMessageByCode(WuLiuCompanyTypeEnum.class, yuyueInfo.getKuaidigongsi());
        kuaidigongsiName = StringUtils.isEmpty(kuaidigongsiName) ? "其他" : kuaidigongsiName;
        ShouHouYuyueRes res = new ShouHouYuyueRes();

        LocalDateTime dTime = yuyueInfo.getDtime();
        LocalDateTime sTime = yuyueInfo.getStime();
        LocalDateTime eTime = yuyueInfo.getEtime();
        yuyueInfo.setDtime(null);
        yuyueInfo.setStime(null);
        yuyueInfo.setEtime(null);

        BeanUtils.copyProperties(yuyueInfo, res);

        if (dTime != null) {
            Date dtime = DateUtil.localDateTime2Date(dTime);
            res.setDtime(dtime);
        }
        if (sTime != null) {
            Date stime = DateUtil.localDateTime2Date(sTime);
            res.setStime(stime);
        }
        if (eTime != null) {
//            Long etime = yuyueInfo.getEtime().toInstant(ZoneOffset.of("+8")).toEpochMilli();
            Date etime = DateUtil.localDateTime2Date(eTime);
            res.setEtime(etime);
        }

        res.setKdgongsi(kuaidigongsiName);
        res.setSub_id(yuyueInfo.getSubId());
        res.setBasket_id(yuyueInfo.getBasketId());
        res.setShouhou_id(yuyueInfo.getShouhouId());
        //查询预约优惠码金额 默认为0
        res.setDiscountAmount(Optional.ofNullable(yuyueInfo.getDiscountAmount()).orElse(BigDecimal.ZERO));
        if (CommenUtil.isNotNullZero(yuyueInfo.getAreaid())) {
            R<AreaInfo> areaInfoR = areaInfoClient.getAreaInfoById(yuyueInfo.getAreaid());
            if (areaInfoR.getCode() == ResultCode.SUCCESS && areaInfoR.getData() != null) {
                res.setArea(areaInfoR.getData().getArea());
            }
        }
        if (yuyueInfo.getYMkcareaid() != null) {
            R<AreaInfo> areaInfoR = areaInfoClient.getAreaInfoById(yuyueInfo.getYMkcareaid());
            if (areaInfoR.getCode() == ResultCode.SUCCESS && areaInfoR.getData() != null) {
                res.setY_mkcArea(areaInfoR.getData().getArea());
            }
        }
        res.setY_ppriceid(yuyueInfo.getYPpriceid());
        res.setYy_ppids(yuyueInfo.getYuyuePPids());
        res.setProduct_id(yuyueInfo.getProductId());
        Integer status = yuyueInfo.getIsdel() && yuyueInfo.getStats() != null && yuyueInfo.getStats() != 5 ? 10 : yuyueInfo.getStats();
        res.setStats(status);

        List<AttachmentsVo> yuyuePics = this.getYuyuePics(yuyueInfo.getId());
        res.setPics(yuyuePics);

        List<ShouhouYuyueproductinfoVo> yyproducts = this.getYuyueProducts(yuyueInfo.getId());
        res.setYyproducts(yyproducts);

        res.setShouhouids(yuyueInfo.getZyShouhouIDs());

        List<YuyueLogs> yuyueLogsList = yuyueLogsService.list(new LambdaQueryWrapper<YuyueLogs>().eq(YuyueLogs::getYuyueId, id)
                .and(bo -> bo.eq(YuyueLogs::getViewType, 0).or().isNull(YuyueLogs::getViewType)));
        if (CollectionUtils.isNotEmpty(yuyueLogsList)) {
            List<YuYueLogs> logs = yuyueLogsList.stream().map(e -> {
                YuYueLogs yuYueLogsVo = new YuYueLogs();
                yuYueLogsVo.setId(e.getId());
                yuYueLogsVo.setContent(e.getComment().replace("收件人：严修修，电话：严修修", "收件人：严修修，电话：15608807767"));
                yuYueLogsVo.setDtime(DateUtil.localDateTime2Date(e.getDtime()));
                yuYueLogsVo.setInuser(e.getInuser());
                yuYueLogsVo.setYuyueid(e.getYuyueId());
                return yuYueLogsVo;
            }).collect(Collectors.toList());
            res.setLogs(logs);
        }

        List<YouhuiMaInfo> youhuimaList = oaApiMapper.getYouhuiMaList(id);
        res = calcYouhuimaInfo(youhuimaList, res);
        res.setShouhouAddress(this.getShouhouAddressInfo(id, 1));

        List<SubCh999UserVo> subCh999UserList = new LinkedList<>();
        if (StringUtils.isNotEmpty(yuyueInfo.getCheckUser())) {

            R<Ch999UserVo> ch999UserVoR = userInfoClient.getCh999UserByUserName(yuyueInfo.getCheckUser());
            if (ch999UserVoR.getCode() == ResultCode.SUCCESS && ch999UserVoR.getData() != null) {
                Ch999UserVo ch999UserVo = ch999UserVoR.getData();
                SubCh999UserVo subCh999UserVo = new SubCh999UserVo();
                subCh999UserVo.setCh999_id(ch999UserVo.getCh999Id());
                subCh999UserVo.setCh999_name(ch999UserVo.getCh999Name());
                subCh999UserVo.setHeadImg(ch999UserVo.getAvatar()
                        .replace("https://moa.ch999.com/static", "https://img2.ch999img.com/newstatic")
                        .replace(moaUrlSource.getBasicUrl() + "/static", "https://img2.ch999img.com/newstatic")
                );
                subCh999UserVo.setJob(EvaluateJobEnum.ShouhouKefu.getCode());
                subCh999UserList.add(subCh999UserVo);
                res.setSubCh999user(subCh999UserList);
            }
        }
        if (StringUtils.isNotEmpty(res.getTuidata())) {
            YuyueTuiDataVo tuiData2 = JSONObject.parseObject(res.getTuidata(), YuyueTuiDataVo.class);
            res.setTuiData2(tuiData2);
        }

        if (CommenUtil.isNotNullZero(res.getAreaid())) {
            R<AreaInfo> areaInfoR = areaInfoClient.getAreaInfoById(res.getAreaid());
            if (areaInfoR.getCode() == ResultCode.SUCCESS && areaInfoR.getData() != null) {
                AreaInfo areaInfo = areaInfoR.getData();
                ShopVo shopVo = new ShopVo();
                shopVo.setArea(areaInfo.getArea());
                shopVo.setAreaName(areaInfo.getAreaName());
                shopVo.setId(areaInfo.getId());
                shopVo.setShopAddress(areaInfo.getCompanyAddress());
                shopVo.setSaleTel(areaInfo.getCompanyTel1());
                shopVo.setHours(areaInfo.getHours());

                res.setShop(shopVo);
            }
        }
        if (1 != res.getStype()) {
            LambdaQueryWrapper<Addinfops> queryWrapper = new LambdaQueryWrapper();
            if (res.getShouhou_id() == null || res.getShouhou_id() == 0) {
                queryWrapper.in(Addinfops::getType, Arrays.asList(3, 4)).eq(Addinfops::getBindId, res.getId());
            } else {
                queryWrapper.in(Addinfops::getType, Arrays.asList(1, 2)).eq(Addinfops::getBindId, res.getShouhou_id());
            }

            List<Addinfops> addinfopsList = addinfopsService.list(queryWrapper);
            if (CollectionUtils.isNotEmpty(addinfopsList)) {
                for (Addinfops addinfops : addinfopsList) {
                    Integer type = addinfops.getType();
                    if (1 == type || 3 == type) {
                        res.setReciver1(addinfops.getReciver());
                        res.setCityid1(addinfops.getCityid());
                        res.setAddress1(addinfops.getAddress());
                        res.setConsignee1(addinfops.getConsignee());
                    } else if (4 == type || 2 == type) {
                        res.setReciver2(addinfops.getReciver());
                        res.setCityid2(addinfops.getCityid());
                        res.setAddress2(addinfops.getAddress());
                        res.setConsignee2(addinfops.getConsignee());
                    }
                }
            }
        }

        res.setSubType(0);
        if (CommenUtil.isNotNullZero(res.getSub_id()) && StringUtils.isNotEmpty(res.getImei())) {
            if (StringUtils.isNotEmpty(res.getArea())) {
                R<AreaInfo> areaInfoR = areaInfoClient.getAreaInfoByArea(res.getArea());
                if (areaInfoR.getCode() == ResultCode.SUCCESS && areaInfoR.getData() != null) {
                    AreaInfo areaInfo = areaInfoR.getData();
                    String url = oaWcfUrlSource.getRecordInfo(res.getImei(), areaInfo.getXtenant().toString());
                    String json = HttpUtil.get(url);
                    if (StringUtils.isNotEmpty(json) && json.indexOf("ishuishou: 1") == -1) {
                        res.setSubType(1);
                    } else {
                        res.setSubType(2);
                    }
                }
            }
        }

        return R.success(res);
    }

    @Override
    public ShouHouDetailVo getShouhouInfoByIdAndUserIdAndImei(Integer id, Integer userId, String imei, Boolean isMobile) {
        ShouHouDetailVo shouHouDetailVo = CommenUtil.autoQueryHist(() -> oaApiMapper.getShouhouInfoByIdAndUserIdAndImei(id, userId, imei, isMobile));
        if(ObjectUtil.isNotNull(shouHouDetailVo)){
            Integer shouHouId = shouHouDetailVo.getId();
            Boolean isPj = Optional.ofNullable(oaApiMapper.checkShouhouEvaluate(shouHouId, jiujiSystemProperties.getOfficeName())).orElse(Boolean.FALSE);
            shouHouDetailVo.setIspj(isPj);
        }
        return shouHouDetailVo;
    }

    @Override
    public List<WxkcInfoVo> getWxkcOutputList(Integer shouhouId) {
        List<WxkcInfoVo> res = new LinkedList<>();
        //List<Wxkcoutput> wxkcoutputList = wxkcoutputService.list(new LambdaQueryWrapper<Wxkcoutput>().ne(Wxkcoutput::getStats, 3).eq(Wxkcoutput::getWxid, shouhouId));
        List<Wxkcoutput> wxkcoutputList =CommenUtil.autoQueryHist(()->wxkcoutputService.list(new LambdaQueryWrapper<Wxkcoutput>().ne(Wxkcoutput::getStats, 3).eq(Wxkcoutput::getWxid, shouhouId)),MTableInfoEnum.WXKCOUTPUT_WXID,shouhouId);
        if (CollectionUtils.isNotEmpty(wxkcoutputList)) {
            res = wxkcoutputList.stream().map(e -> {
                WxkcInfoVo wxkcInfoVo = new WxkcInfoVo();
                wxkcInfoVo.setId(e.getId());
                wxkcInfoVo.setName(e.getName());
                wxkcInfoVo.setPpriceid(e.getPpriceid());
                wxkcInfoVo.setPrice(e.getPrice1());
                return wxkcInfoVo;
            }).collect(Collectors.toList());
        }

        return res;
    }

    @Override
    public List<ShouhouYuyueproductinfoVo> getYuyueProducts(Integer yuyueId) {
        List<ShouhouYuyueproductinfo> yuyueproductinfoList = shouhouYuyueproductinfoService.list(new LambdaQueryWrapper<ShouhouYuyueproductinfo>().eq(ShouhouYuyueproductinfo::getYuyueid, yuyueId));
        if (CollectionUtils.isEmpty(yuyueproductinfoList)) {
            return Collections.emptyList();
        }

        List<ShouhouYuyueproductinfoVo> res = yuyueproductinfoList.stream().map(e -> {
            ShouhouYuyueproductinfoVo yuyueproductinfoVo = new ShouhouYuyueproductinfoVo();
            BeanUtils.copyProperties(e, yuyueproductinfoVo);
            return yuyueproductinfoVo;

        }).collect(Collectors.toList());
        return res;
    }

    @Override
    public List<AttachmentsVo> getYuyuePics(Integer yuyueId) {
        List<Attachments> attachmentsList = attachmentsService.newList(new LambdaQueryWrapper<Attachments>().eq(Attachments::getType, AttachmentsEnum.YUYUE.getCode())
                .eq(Attachments::getLinkedID, yuyueId), yuyueId, AttachmentsEnum.YUYUE.getCode(), null);
        if (CollectionUtils.isEmpty(attachmentsList)) {
            return Collections.emptyList();
        }
        List<AttachmentsVo> res = attachmentsList.stream().map(e -> {
            AttachmentsVo attachmentsVo = new AttachmentsVo();
            attachmentsVo.setFid(e.getFid());
            attachmentsVo.setExtension(e.getExtension());
            attachmentsVo.setFilename(e.getFilename());
            return attachmentsVo;
        }).collect(Collectors.toList());
        return res;
    }

    @Override
    public ShouhouAddressInfoVo getShouhouAddressInfo(Integer linkid, Integer kind) {

        List<ShouhouSendaddress> shouhouSendaddressList = shouhouSendaddressService.list(new LambdaQueryWrapper<ShouhouSendaddress>().eq(ShouhouSendaddress::getLinkid, linkid).eq(ShouhouSendaddress::getKind, kind).orderByDesc(ShouhouSendaddress::getId));
        if (CollectionUtils.isEmpty(shouhouSendaddressList)) {
            return null;
        }

        return shouhouSendaddressList.stream().map(e -> {
            ShouhouAddressInfoVo shouhouAddressInfoVo = new ShouhouAddressInfoVo();
            BeanUtils.copyProperties(e, shouhouAddressInfoVo);
            return shouhouAddressInfoVo;
        }).findFirst().orElse(null);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<Integer> addShouhouYuyue(ShouhouYuyueReq req) {
        ShouhouYuyueReqVo yuyue = req.getYuyue();
        YuyueTuiDataReqVo tui = req.getTui();
        List<String> troubles = req.getTroubles();
        if (StringUtils.isNotEmpty(yuyue.getMobile()) && yuyue.getMobile().length() > 11) {
            return R.error("手机号码格式有误");
        }

        if (req.getTui() != null && CollectionUtils.isNotEmpty(req.getTui().getBasket())) {
            yuyue.setTuiData(JSONObject.toJSONString(tui));
        }

        //网站提交为未确认状态
        yuyue.setStats(YuyueStatusEnum.WQR.getCode());
        if (yuyue.getIszy() != null && yuyue.getIszy()) {
            yuyue.setStats(YuyueStatusEnum.WQJ.getCode());
        }

        Integer romUpgradeType = yuyue.getId();
        //iphone 容量升级，邮寄方式
        if (romUpgradeType != null && romUpgradeType.equals(-2)) {
            yuyue.setArea(AreaEnum.AREA_DC.getCode().toString());
        }
        //iphone容量升级，到店，只能是KM，帮助KM拉客流量
        else if (romUpgradeType != null && romUpgradeType.equals(-3)) {
            R<AreaInfo> areaInfoR = areaInfoClient.getAreaInfoByArea(yuyue.getArea());
            if (areaInfoR.getCode() == ResultCode.SUCCESS && areaInfoR.getData() != null) {
                yuyue.setArea(areaInfoR.getData().getArea());
            }
        }
        //iphone容量升级，上门取件
        else if (romUpgradeType != null && romUpgradeType.equals(-4)) {
            yuyue.setArea(AreaEnum.AREA_DC.toString());
            Integer yuyueSmqjCount = oaApiMapper.checkYuyueSmqjCount();
            if (yuyueSmqjCount >= 20) {
                return R.error("今日上门取件预约名额已满，您可以选择其他时间预约上门取件升级容量，也可以选择邮寄方式进行容量升级");
            }
        }

        if (CommenUtil.isNotNullZero(yuyue.getStaffId())) {
            Integer count = oaApiMapper.checkStaffInfo(yuyue.getStaffId());
            if (CommenUtil.isNullOrZero(count)) {
                return R.error("无效的导购员工号");
            }
        }

        Boolean hasFuwuma = false;
        if (StringUtils.isNotEmpty(yuyue.getFuwuma())) {
            List<InstallServicesRecord> installServicesRecordList = installServicesRecordService.list(new LambdaQueryWrapper<InstallServicesRecord>().select(InstallServicesRecord::getStats).eq(InstallServicesRecord::getServiceCode, yuyue.getFuwuma()));
            if (CollectionUtils.isNotEmpty(installServicesRecordList)) {
                Integer fuwumaStats = installServicesRecordList.get(0).getStats();
                if (fuwumaStats == 0) {
                    hasFuwuma = true;
                } else {
                    if (fuwumaStats == 1) {
                        return R.error("服务码(" + yuyue.getFuwuma() + ")已使用过");
                    } else {
                        return R.error("服务码(" + yuyue.getFuwuma() + ")无效");
                    }
                }
            }
        }
        if (yuyue.getPpriceid() == 77990) {
            DateTimeFormatter df = DateTimeFormatter.ofPattern("MMdd");
            yuyue.setImei(String.format("%s%s", yuyue.getMobile(), df.format(LocalDateTime.now())));
        }

        ShouhouYuyue shouhouYuyue = new ShouhouYuyue();
        buidShouhouYuyueEntity(shouhouYuyue, yuyue);
        Integer yuyueAreaId = null;
        R<AreaInfo> areaInfoR = areaInfoClient.getAreaInfoByArea(yuyue.getArea());
        if (areaInfoR.getCode() == ResultCode.SUCCESS && areaInfoR.getData() != null) {
            yuyueAreaId = areaInfoR.getData().getId();
            shouhouYuyue.setAreaid(yuyueAreaId);
        }
        shouhouYuyue.setArea(null);
        Boolean flag = shouhouYuyueService.save(shouhouYuyue);
        if (!flag) {
            return R.error("预约提交失败！");
        }
        //添加日志记录
        yuyueLogsService.yuyueLogsAdd(shouhouYuyue.getId(), "添加预约单", shouhouYuyue.getUsername(), YuyueLogViewTypeEnum.WZ_NO_SHOUW.getCode());

        Integer yuyueId = shouhouYuyue.getId();
        yuyue.setId(shouhouYuyue.getId());
        if (hasFuwuma) {
            installServicesRecordService.update(new LambdaUpdateWrapper<InstallServicesRecord>()
                    .set(InstallServicesRecord::getStats, 1).set(InstallServicesRecord::getUseDate, LocalDateTime.now())
                    .set(InstallServicesRecord::getUserName, "系统").eq(InstallServicesRecord::getServiceCode, yuyue.getFuwuma()));
            yuyueLogsService.yuyueLogsAdd(yuyue.getId(), "添加预约服务码使用：" + yuyue.getFuwuma(), "系统", 0);
        }

        //如果有优惠码，并且是[上门快修][上门安装] 使用优惠码
        if (StringUtils.isNotEmpty(yuyue.getFuwuma()) && (ServicesWayEnum.SMWX.getCode().equals(yuyue.getStype()) || ServicesWayEnum.SMAZ.getCode().equals(yuyue.getStype()))) {
            numberCardService.useYuyueCoupon(yuyue.getYouhuima(), yuyue.getId(), "系统", yuyue.getStype(), shouhouYuyue.getAreaid());
        }

        //中邮逻辑
        if (yuyue.getIszy() != null && yuyue.getIszy()) {
            if (CollectionUtils.isNotEmpty(yuyue.getYyproducts())) {
                List<ShouhouYuyueproductinfo> yuyueproductinfoList = yuyue.getYyproducts().stream().map(e -> {
                    ShouhouYuyueproductinfo productinfo = new ShouhouYuyueproductinfo();
                    productinfo.setYuyueid(shouhouYuyue.getId());
                    productinfo.setPpriceid(e.getPpriceid());
                    productinfo.setProductname(e.getProductname());
                    productinfo.setProductcolor(e.getProductcolor());
                    productinfo.setProductid(e.getProductid());
                    productinfo.setRemark(e.getRemark());
                    return productinfo;
                }).collect(Collectors.toList());

                shouhouYuyueproductinfoService.saveBatch(yuyueproductinfoList);
            }

            if (1 == yuyue.getZyptype()) {
                if (yuyue.getZypnum() == null || yuyue.getZypnum() <= 0) {
                    return R.error("数量应大于0！");
                }
            }

            //图片附件
            if (CollectionUtils.isNotEmpty(yuyue.getPics())) {
                List<FileReq> attchmentList = yuyue.getPics().stream().map(e -> {
                    FileReq fileReq = new FileReq();
                    fileReq.setFileName(e.getFilename());
                    fileReq.setFid(e.getFid());
                    fileReq.setFilePath(e.getFilepath());
                    fileReq.setSuffix(e.getExtension());
                    return fileReq;
                }).collect(Collectors.toList());
                if(CollUtil.isNotEmpty(attchmentList)){
                    attachmentsService.attachmentsAdd(attchmentList, shouhouYuyue.getId(), AttachmentsEnum.YUYUE.getCode(), 0, null,null);
                }
            }
            AreaInfo zyareaInfo = null;
            if (yuyueAreaId != null) {
                areaInfoR = areaInfoClient.getAreaInfoById(yuyueAreaId);
                if (areaInfoR.getCode() == ResultCode.SUCCESS && areaInfoR.getData() != null) {
                    zyareaInfo = areaInfoR.getData();
                }
            }
            //发微信
            smsService.sendZyWeixinMsg(shouhouYuyue.getId(), yuyue.getUserid(), "预约成功", "", "亲爱的用户，您的预约单已成功预约！点击可以查看详情", "http://fix.999buy.com/orderDetail/" + shouhouYuyue.getId() + "/0", zyareaInfo == null ? 0 : zyareaInfo.getCityId() == null ? 0 : zyareaInfo.getCityId());
        }

        //中邮逻辑结束

        Integer type1ID = 3;
        Integer type2ID = 4;
        Integer bindID = shouhouYuyue.getId();

        addinfopsService.remove(new LambdaQueryWrapper<Addinfops>().eq(Addinfops::getBindId, bindID).in(Addinfops::getType, Arrays.asList(type1ID, type2ID)));
        Addinfops addinfops = new Addinfops();
        if (Arrays.asList(ServicesWayEnum.SMQJ.getCode(), ServicesWayEnum.YJSX.getCode(), ServicesWayEnum.SMWX.getCode(), ServicesWayEnum.SMAZ.getCode()).contains(yuyue.getStype())) {
            addinfops.setReciver(yuyue.getReciver1());
            addinfops.setConsignee(yuyue.getConsignee1());
            addinfops.setAddress(yuyue.getAddress1());
            addinfops.setCityid(yuyue.getCityid1());
            addinfops.setType(type1ID);
            addinfops.setBindId(bindID);
            addinfopsService.save(addinfops);

            if (yuyue.getIsaddress1() != null && yuyue.getIsaddress1()) {
                addinfops.setType(type2ID);
                addinfopsService.save(addinfops);
            } else if (StringUtils.isNotEmpty(yuyue.getReciver2()) || StringUtils.isNotEmpty(yuyue.getAddress2())) {
                addinfops.setReciver(yuyue.getReciver2());
                addinfops.setConsignee(yuyue.getConsignee2());
                addinfops.setAddress(yuyue.getAddress2());
                addinfops.setCityid(yuyue.getCityid2());
                addinfops.setType(type2ID);
                addinfops.setBindId(bindID);
                addinfopsService.save(addinfops);
            }
        } else if (StringUtils.isNotEmpty(yuyue.getReciver2()) || StringUtils.isNotEmpty(yuyue.getAddress2())) {
            addinfops.setReciver(yuyue.getReciver2());
            addinfops.setConsignee(yuyue.getConsignee2());
            addinfops.setAddress(yuyue.getAddress2());
            addinfops.setCityid(yuyue.getCityid2());
            addinfops.setType(type2ID);
            addinfops.setBindId(bindID);
            addinfopsService.save(addinfops);
        }

        if (CollectionUtils.isNotEmpty(troubles)) {
            List<Trouble> validTroubles = troubleService.listAll();
            List<ShouhouYuyueTrouble> yuyueTroubleList = null;
            for (String t : troubles) {
                yuyueTroubleList = validTroubles.stream().filter(e -> e.getName().equals(t)).map(e -> {
                    ShouhouYuyueTrouble yuyueTrouble = new ShouhouYuyueTrouble();
                    yuyueTrouble.setShouhouYuyueId(shouhouYuyue.getId());
                    yuyueTrouble.setTroubleId(e.getId());
                    return yuyueTrouble;
                }).collect(Collectors.toList());
            }
            if (CollectionUtils.isNotEmpty(yuyueTroubleList)) {
                shouhouYuyueTroubleService.saveBatch(yuyueTroubleList);
            }
        }

        if (yuyue.getKdtype().equals(1) && CommenUtil.isNotNullZero(yuyue.getCityid1()) && yuyue.getStype().equals(3)) {
            shouhouYuyueService.update(new LambdaUpdateWrapper<ShouhouYuyue>().set(ShouhouYuyue::getKdtype, 1).eq(ShouhouYuyue::getId, shouhouYuyue.getId()));
        }

        //发微信
        if (StringUtils.isNotEmpty(yuyue.getArea()) && XtenantEnum.isSaasXtenant()) {
            if (yuyue.getStype().equals(1)) {
                Integer userId = null;

                R<List<Ch999UserVo>> userInfoR = userInfoClient.findBasicUserByArea1IdAndContainsRole(yuyueAreaId, Arrays.asList(9));
                if (userInfoR.getCode() == ResultCode.SUCCESS && CollectionUtils.isNotEmpty(userInfoR.getData())) {
                    userId = userInfoR.getData().get(0).getCh999Id();
                }
                if (userId != null) {
                    List<String> userIds = this.getShopkeeperByAreaNew(yuyueAreaId, 0, Arrays.asList("门店主管","主管", "店长", "副店长", "职员", "专员"), 1);
                    userId = CollectionUtils.isNotEmpty(userIds) ? Integer.valueOf(userIds.get(0)) : null;
                }
                if (userId != null) {
                    String host = sysConfigClient.getValueByCode(SysConfigConstant.MOA_URL).getData();
                    String link = host + "/Mshouhouyuyue/yuyueadd/" +  yuyue.getId();
                    //String content = "机型：" + yuyue.getName() + " " + yuyue.getColor() + "，预约单号：<a href='" + link + "'>" + yuyue.getId() + "</a> imei: " + yuyue.getImei() + " 预约到店，请尽快联系顾客，感谢您的付出。";
                    String content = "预约单号：<a href='" + link + "'>" + yuyue.getId() +",机型：" + yuyue.getName() + " " + yuyue.getColor() +  "</a> imei: " + yuyue.getImei() + " 预约到店，请尽快联系顾客，感谢您的付出。";

                    weixinUserService.senWeixinAndOaMsg(content, content, link, userId.toString(), OaMesTypeEnum.SHTZ.getCode().toString());
                }
            }
        }

        //推送预约通知(RabittMQ)
        try {
            RabbitYuyueMsgPushBo m = new RabbitYuyueMsgPushBo();
            m.setType("netYuyuePush_RabbitMQ");
            m.setYuyueid(yuyue.getId());
            rabbitTemplate.convertAndSend("OaNotice", JSONObject.toJSONString(m));
        } catch (Exception e) {
            String content = "售后预约推送异常：" + e.getMessage() + ":[netYuyuePush_RabbitMQ] StackTrace:" + e.getStackTrace();
            weixinUserService.senWeixinAndOaMsg(content, content, "", "1324,13685", OaMesTypeEnum.YCTZ.getCode().toString());
        }

        //客户微信或短信通知
        if (yuyue.getId() != null && yuyue.getUserid() != null) {
            try {
                this.sendWechatAndSmsNoticeToCustomer(yuyue, yuyueAreaId, true);
            } catch (Exception e) {
                StaticLog.error(e,"售后预约超提交成功推送程序异常,编号:857");
                String content = "售后预约超提交成功推送程序异常：" + e.getMessage();
                weixinUserService.senWeixinAndOaMsg(content, content, "", "1324,13685", OaMesTypeEnum.YCTZ.getCode().toString());
            }
        }
        //预约单MQ推送
        String routingKey = ConfigConsts.YU_YUE_ROUTING_KEY;
        rabbitTemplate.convertAndSend(ConfigConsts.YU_YUE_EXCHANGE, routingKey,String.valueOf(yuyueId));
        return R.success("操作成功！", yuyueId);
    }


    private ShouHouDetailVo setArea(ShouHouDetailVo shouHouDetailVo) {
        if (shouHouDetailVo.getAreaid() != null) {
            R<AreaInfo> areaInfoR = areaInfoClient.getAreaInfoById(shouHouDetailVo.getAreaid());
            if (areaInfoR.getCode() == ResultCode.SUCCESS && areaInfoR.getData() != null) {
                shouHouDetailVo.setArea(areaInfoR.getData().getArea());
            }
        }

        if (shouHouDetailVo.getBuyareaid() != null) {
            R<AreaInfo> areaInfoR = areaInfoClient.getAreaInfoById(shouHouDetailVo.getBuyareaid());
            if (areaInfoR.getCode() == ResultCode.SUCCESS && areaInfoR.getData() != null) {
                shouHouDetailVo.setBuyarea(areaInfoR.getData().getArea());
            }
        }

        if (shouHouDetailVo.getToareaid() != null) {
            R<AreaInfo> areaInfoR = areaInfoClient.getAreaInfoById(shouHouDetailVo.getToareaid());
            if (areaInfoR.getCode() == ResultCode.SUCCESS && areaInfoR.getData() != null) {
                shouHouDetailVo.setToarea(areaInfoR.getData().getArea());
            }
        }
        if (StringUtils.isNotEmpty(shouHouDetailVo.getNowarea())) {
            R<AreaInfo> areaInfoR = areaInfoClient.getAreaInfoById(Integer.valueOf(shouHouDetailVo.getNowarea()));
            if (areaInfoR.getCode() == ResultCode.SUCCESS && areaInfoR.getData() != null) {
                shouHouDetailVo.setNowarea(areaInfoR.getData().getArea());
            }
        }
        if (StringUtils.isNotEmpty(shouHouDetailVo.getArea_zh())) {
            R<AreaInfo> areaInfoR = areaInfoClient.getAreaInfoById(Integer.valueOf(shouHouDetailVo.getArea_zh()));
            if (areaInfoR.getCode() == ResultCode.SUCCESS && areaInfoR.getData() != null) {
                shouHouDetailVo.setArea_zh(areaInfoR.getData().getArea());
            }
        }
        if (StringUtils.isNotEmpty(shouHouDetailVo.getToarea_zh())) {
            R<AreaInfo> areaInfoR = areaInfoClient.getAreaInfoById(Integer.valueOf(shouHouDetailVo.getToarea_zh()));
            if (areaInfoR.getCode() == ResultCode.SUCCESS && areaInfoR.getData() != null) {
                shouHouDetailVo.setToarea_zh(areaInfoR.getData().getArea());
            }
        }
        return shouHouDetailVo;
    }

    private ShouHouYuyueRes calcYouhuimaInfo(List<YouhuiMaInfo> youhuimaList, ShouHouYuyueRes res) {

        res.setServiceCodeMoney(BigDecimal.ZERO);
        res.setCouponMoney(BigDecimal.ZERO);
        if (CollectionUtils.isNotEmpty(youhuimaList)) {
            Optional<String> optionalCardID = youhuimaList.stream().filter(e -> JiujiNumberCardTypeEnum.JIUJI_NUMBER_CARD_TYPE_23.getCode().equals(e.getCh999Id())).map(e -> e.getCardId()).findAny();
            if (optionalCardID.isPresent()) {
                String fuwuMa = optionalCardID.get();
                res.setFuwuma(fuwuMa);
            }
            Optional<BigDecimal> optionalServiceCodeMoney = youhuimaList.stream().filter(e -> JiujiNumberCardTypeEnum.JIUJI_NUMBER_CARD_TYPE_23.getCode().equals(e.getCh999Id())).map(e -> e.getPrices()).findAny();
            if (optionalServiceCodeMoney.isPresent()) {
                BigDecimal serviceCodeMoney = optionalServiceCodeMoney.get();
                res.setServiceCodeMoney(serviceCodeMoney);
            }
            Optional<String> optionalYouhuima = youhuimaList.stream().filter(e -> JiujiNumberCardTypeEnum.JIUJI_NUMBER_CARD_TYPE_25.getCode().equals(e.getCh999Id())).map(e -> e.getCardId()).findAny();
            if (optionalYouhuima.isPresent()) {
                String youhuima = optionalYouhuima.get();
                res.setYouhuima(youhuima);
            }
            Optional<BigDecimal> optionalCouponMoney = youhuimaList.stream().filter(e -> JiujiNumberCardTypeEnum.JIUJI_NUMBER_CARD_TYPE_25.getCode().equals(e.getCh999Id())).map(e -> e.getPrices()).findAny();
            if (optionalCouponMoney.isPresent()) {
                BigDecimal couponMoney = optionalCouponMoney.get();
                res.setCouponMoney(couponMoney);
            }

        }
        return res;
    }

    @Override
    public AfterSaleResultRes getAfterSale(Integer rows) {
        AfterSaleResultRes result = new AfterSaleResultRes();
        List<AfterSaleSearchVO> saleSearchVoLis = oaApiMapper.getAfterSale(rows);
        result.setShouhou(saleSearchVoLis);
        result.setCurPage(1);
        result.setTotalCount(saleSearchVoLis.size());
        result.setTotalPage(1);
        return result;
    }

    @Override
    public List<String> getShopkeeperByAreaNew(Integer areaId, Integer type, List<String> zhiwuName, Integer takeCount) {
        type = type == null ? 1 : type;
        takeCount = takeCount == null ? 1 : takeCount;
        List<String> ch999NameList = Collections.emptyList();

        if (CollectionUtils.isEmpty(zhiwuName)) {
            zhiwuName = new LinkedList<>();
            zhiwuName.add("店长");
            zhiwuName.add("副店长");
        }

        List<String> zhiwuList = zhiwuName;
        List<Ch999UserBasicBO> ch999UserList = ((OaApiServiceImpl) (AopContext.currentProxy())).listAllUserBasicInfo();
        ch999UserList = ch999UserList.stream().filter(t ->
                areaId.equals(t.getArea1id()) && zhiwuList.contains(t.getZhiwu())
                        && t.getIsshixi() != null && 4 != t.getIsshixi()
                        && t.getIslogin() != null && 1 != t.getIslogin())
                .sorted(Comparator.comparing(Ch999UserBasicBO::getLevel,Comparator.nullsLast(Comparator.naturalOrder())).thenComparing(Ch999UserBasicBO::getCh999Id))
                .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(ch999UserList)) {
            ch999UserList = ch999UserList.subList(0, takeCount);
            if (type.equals(1)) {
                ch999NameList = ch999UserList.stream().map(e -> e.getCh999Name()).collect(Collectors.toList());
            } else {
                ch999NameList = ch999UserList.stream().map(e -> e.getCh999Id().toString()).collect(Collectors.toList());
            }
        }

        return ch999NameList;
    }

    @Override
    public List<Ch999UserBasicBO> listAllUserBasicInfo() {
        List<Ch999UserBasicBO> list = oaApiMapper.listAllUserBasicInfo(jiujiSystemProperties.getOfficeName());
        if (list != null) {
            return list.stream().peek(tmp -> {
                tmp.setRoleList(CommonUtils.covertIdStr(tmp.getRoles()));
                tmp.setAreaIdList(CommonUtils.covertIdStr(tmp.getAreaId()));
            }).collect(Collectors.toList());
        } else {
            return new ArrayList<>();
        }
    }

    private void buidShouhouYuyueEntity(ShouhouYuyue shouhouYuyue, ShouhouYuyueReqVo yuyue) {
        BeanUtils.copyProperties(yuyue, shouhouYuyue);
        shouhouYuyue.setTuidata(yuyue.getTuiData());
        shouhouYuyue.setYPpriceid(yuyue.getY_ppriceid());

        if (StringUtils.isNotEmpty(yuyue.getY_mkcArea())) {
            R<AreaInfo> areaInfoR = areaInfoClient.getAreaInfoByArea(yuyue.getY_mkcArea());
            if (areaInfoR.getCode() == ResultCode.SUCCESS && areaInfoR.getData() != null) {
                shouhouYuyue.setYMkcareaid(areaInfoR.getData().getId());
            }
        }
        shouhouYuyue.setIsBakData(yuyue.getIsBakDATA());
        shouhouYuyue.setYuyuePPids(yuyue.getYy_ppids());
        shouhouYuyue.setProductId(yuyue.getProduct_id());
        shouhouYuyue.setGuideStaffId(yuyue.getStaffId());
        shouhouYuyue.setIsmobile(yuyue.getIsmobile() != null && yuyue.getIsmobile());
        shouhouYuyue.setSubId(yuyue.getSub_id());
        shouhouYuyue.setBasketId(yuyue.getBasket_id());
        shouhouYuyue.setId(null);
        shouhouYuyue.setDiscountAmount(yuyue.getDiscountAmount());
        shouhouYuyue.setDtime(LocalDateTime.now());
    }

    /**
     * 客户微信或短信通知
     */
    @Override
    public void sendWechatAndSmsNoticeToCustomer(ShouhouYuyueReqVo yuyue, Integer yuyueAreaId, boolean isTryWechat) {
        WxBO myWx = new WxBO();
        if(isTryWechat && ObjectUtil.defaultIfNull(yuyue.getUserid(), 0)>0){
            List<WxBO> wxUserList = smallproMapper.getWeixinUser(yuyue.getUserid().longValue());
            if (CollectionUtils.isNotEmpty(wxUserList)) {
                myWx = wxUserList.get(0);
            }
        }

        R<AreaInfo> areaInfoR = areaInfoClient.getAreaInfoById(yuyueAreaId);
        Integer fuwuType = yuyue.getStype();
        String yuyueTime = "";

        if (yuyue.getStime() != null && yuyue.getEtime() != null) {
            yuyueTime = DateTimeFormatter.ofPattern("M月d日 HH").format(yuyue.getStime()) + "-" + DateTimeFormatter.ofPattern("HH").format(yuyue.getEtime()) + "点";
        }
        if (myWx != null && StringUtils.isNotEmpty(myWx.getOpenid())) {
            String remark = "";
            if (YuYueSTypeEnum.YYDD.getCode().equals(fuwuType)) {
                remark = "尊敬的会员，您预约到店的时间是" + yuyueTime;
                if (yuyueAreaId != null) {
                    areaInfoR = areaInfoClient.getAreaInfoById(yuyueAreaId);
                    if (areaInfoR.getCode() == ResultCode.SUCCESS && areaInfoR.getData() != null) {
                        remark += "，您选择的门店是" + areaInfoR.getData().getAreaName() + "，地址为" + areaInfoR.getData().getCompanyAddress();
                    }
                }
                remark += "。点击可查看详情！";
            } else if (YuYueSTypeEnum.SMKX.getCode().equals(fuwuType)) {
                remark = String.format("尊敬的会员，您预约上门的时间是%s,地址为%s。点击可查看详情！", yuyueTime, yuyue.getAddress1());
            } else if (YuYueSTypeEnum.SMQJ.getCode().equals(fuwuType)) {
                remark = String.format("尊敬的会员，您预约上门的时间是%s,地址为%s。点击可查看详情！", yuyueTime, yuyue.getAddress1());
            } else if (YuYueSTypeEnum.YJSX.getCode().equals(fuwuType)) {
                remark = "尊敬的会员，您的预约维修订单已成功生成，为保证您的维修商品能正常使用，请尽快送修。点击可查看详情！";
            }
            R<String> valueByCode = sysConfigClient.getValueByCode(SysConfigConstant.M_URL);
            if(!valueByCode.isSuccess()){
                log.warn("售后小程序推送获取域名异常：{}", JSONUtil.toJsonStr(valueByCode));
            }
            shouhouMsgService.sendShouHouNotify(myWx.getOpenid(), valueByCode.getData() + "/after-service/reserve-detail/" + yuyue.getId(), "售后预约维修", remark, fuwuType.toString(), "预约订单提交成功", LocalDateTime.now(), "预约售后单" + yuyue.getId() + "已生成", Integer.valueOf(myWx.getWxid()), null, null);
        } else if (areaInfoR.getData().getIsSend() && StringUtils.isNotEmpty(yuyue.getMobile())) {

            String printName = sysConfigService.getWebNameByXtenant((int) Namespaces.get());
            String remark = "";
            if (YuYueSTypeEnum.YYDD.getCode().equals(fuwuType)) {
                if(shouhouYuyueService.isUseNewYuYue()){
                    remark = "尊敬的会员，您的预约维修单" + yuyue.getId() + "到店的时间更改为" + yuyueTime;
                } else {
                    remark = "尊敬的会员，您的预约维修单" + yuyue.getId() + "已生成，您预约到店的时间是" + yuyueTime;
                }
                String areaName = org.apache.commons.lang3.StringUtils.EMPTY;
                String areaAddress = org.apache.commons.lang3.StringUtils.EMPTY;
                if (yuyueAreaId != null) {
                    areaInfoR = areaInfoClient.getAreaInfoById(yuyueAreaId);
                    if (areaInfoR.getCode() == ResultCode.SUCCESS && areaInfoR.getData() != null) {
                        areaName = areaInfoR.getData().getAreaName();
                        areaAddress = areaInfoR.getData().getCompanyAddress();
                        remark += "，您选择的门店是" + areaInfoR.getData().getAreaName() + "，地址为" + areaInfoR.getData().getCompanyAddress();
                    }
                }
                if(shouhouYuyueService.isUseNewYuYue()){
                    remark += "。如有疑问，请咨询客服400-008-3939";
                } else {
                    remark += "。请核对您的预约维修订单，稍后客服将与您联系，请保持电话畅通。感谢您对九机网的支持，祝您生活愉快！";
                }
                long xtenant = Namespaces.get();
                String openXtenantStr = apolloEntity.getSmsConfigOpenXtenant();
                if (XtenantEnum.isSaasXtenant()
                        && StringUtils.isNotEmpty(openXtenantStr)
                        && CommonUtils.covertIdStr(openXtenantStr).contains((int) xtenant)) {
                    R<SmsConfigVO> smsConfigResult = smsConfigCloud.getConfigByCode(Namespaces.get(),
                            SmsConfigCodeEnum.CODE_83.getCode());
                    if (com.jiuji.tc.utils.common.CommonUtils.isRequestSuccess(smsConfigResult)) {
                        SmsConfigVO smsConfig = smsConfigResult.getData();
                        // 推送方式
                        List<Integer> pushMethods = smsConfig.getPushMethod();
                        // sms消息
                        boolean sendSmsMessage = CollectionUtils.isNotEmpty(pushMethods)
                                && pushMethods.contains(SmsPushMethodEnum.SMS.getCode());
                        // 消息内容
                        List<SmsConfigVO.SmsField> fields = smsConfig.getFields();
                        String smsMessage = smsConfig.getTemplate();
                        if (StringUtils.isNotEmpty(smsMessage)
                                && CollectionUtils.isNotEmpty(fields)) {
                            for (SmsConfigVO.SmsField field : fields) {
                                if ("<dateTime>".equals(field.getValue())) {
                                    smsMessage = smsMessage.replace(field.getValue(), yuyueTime);
                                }
                                if ("<areaName>".equals(field.getValue())) {
                                    smsMessage = smsMessage.replace(field.getValue(),  areaName);
                                }
                                if ("<areaAddress>".equals(field.getValue())) {
                                    smsMessage = smsMessage.replace(field.getValue(),  areaAddress);
                                }
                            }
                        }
                        if (sendSmsMessage) {
                            remark = smsMessage;
                        }
                    }
                }
            } else if (YuYueSTypeEnum.SMKX.getCode().equals(fuwuType)) {
                remark = String.format("尊敬的会员，您的预约维修单%s已生成,您预约上门的时间是%s。地址为%s，请核对您的预约维修订单，稍后客服将与您联系，请保持电话畅通。感谢您对九机网的支持，祝您生活愉快！", yuyue.getId(), yuyueTime, yuyue.getAddress1());
            } else if (YuYueSTypeEnum.SMQJ.getCode().equals(fuwuType)) {
                remark = String.format("尊敬的会员，您的预约维修单%s已生成,您预约上门的时间是%s。地址为%s，请核对您的预约维修订单，稍后客服将与您联系，请保持电话畅通。感谢您对九机网的支持，祝您生活愉快！", yuyue.getId(), yuyueTime, yuyue.getAddress1());
            } else if (YuYueSTypeEnum.YJSX.getCode().equals(fuwuType)) {
                remark = String.format("尊敬的会员，您的预约维修单%s已生成,为保证您的电子产品能正常使用，请尽快送修，稍后客服将与您联系，请保持电话畅通。感谢您对九机网的支持，祝您生活愉快！", yuyue.getId());
                long xtenant = Namespaces.get();
                String openXtenantStr = apolloEntity.getSmsConfigOpenXtenant();
                if (XtenantEnum.isSaasXtenant()
                        && StringUtils.isNotEmpty(openXtenantStr)
                        && CommonUtils.covertIdStr(openXtenantStr).contains((int) xtenant)) {
                    R<SmsConfigVO> smsConfigResult = smsConfigCloud.getConfigByCode(Namespaces.get(),
                            SmsConfigCodeEnum.CODE_86.getCode());
                    if (com.jiuji.tc.utils.common.CommonUtils.isRequestSuccess(smsConfigResult)) {
                        SmsConfigVO smsConfig = smsConfigResult.getData();
                        // 推送方式
                        List<Integer> pushMethods = smsConfig.getPushMethod();
                        // sms消息
                        boolean sendSmsMessage = CollectionUtils.isNotEmpty(pushMethods)
                                && pushMethods.contains(SmsPushMethodEnum.SMS.getCode());
                        // 消息内容
                        List<SmsConfigVO.SmsField> fields = smsConfig.getFields();
                        String smsMessage = smsConfig.getTemplate();
                        if (StringUtils.isNotEmpty(smsMessage)
                                && CollectionUtils.isNotEmpty(fields)) {
                            for (SmsConfigVO.SmsField field : fields) {
                                if ("<subId>".equals(field.getValue())) {
                                    smsMessage = smsMessage.replace(field.getValue(),  String.valueOf(yuyue.getId()));
                                }
                                if ("<printName>".equals(field.getValue())) {
                                    smsMessage = smsMessage.replace(field.getValue(),  printName);
                                }
                            }
                        }
                        if (sendSmsMessage) {
                            remark = smsMessage;
                        }
                    }
                }
                //九机设置为空  那就不进行推送
                if(shouhouYuyueService.isUseNewYuYue()){
                    remark = "";
                }

            }
            if (StringUtils.isNotEmpty(remark) && StringUtils.isNotEmpty(yuyue.getMobile())) {
                smsService.sendSms(yuyue.getMobile(), remark, DateUtil.localDateTimeToString(LocalDateTime.now()), "系统", smsService.getSmsChannelByTenant(yuyueAreaId, ESmsChannelTypeEnum.YZMTD));
            }
        }

    }

    /**
     * 获取用户壳膜订单
     * @param userId 会员id
     * @return 订单数量
     */
    @Override
    public R<Integer> getOrderClassByUserId(Integer userId) {
        //目前只用当前用户的团队id
        Integer teamId = oaApiMapper.listTeamIds(userId);
        if (CommenUtil.isNullOrZero(teamId)){
            return R.success(NumberConstant.ZERO);
        }
        //获取壳膜分类
        List<Integer> cids = categoryService.getProductChildCidList(Arrays.asList(43, 63));
        return R.success(oaApiMapper.getOrderClassByUserId(teamId, cids));
    }

    @Override
    public R<Boolean> isUserOrder(Integer orderId, Integer userId, Integer orderType) {
        BusinessTypeEnum typeEnum = EnumUtil.getEnumByCode(BusinessTypeEnum.class, orderType);
        if(typeEnum == null){
            return R.error(StrUtil.format("单据类型[{}]错误", orderType));
        }

        switch (typeEnum) {
            case NEW_ORDER:
                return R.success(SpringUtil.getBean(SubService.class).lambdaQuery().eq(Sub::getSubId, orderId)
                        .eq(Sub::getUserId, userId).count() > 0);
            case LP_ORDER:
                return R.success(SpringUtil.getBean(RecoverMarketinfoService.class).lambdaQuery().eq(RecoverMarketinfo::getSubId, orderId)
                        .eq(RecoverMarketinfo::getUserid, userId).count() > 0);
            case APPOINTMENT_ORDER:
                return R.success(SpringUtil.getBean(ShouhouYuyueService.class).lambdaQuery().eq(ShouhouYuyue::getId, orderId)
                        .eq(ShouhouYuyue::getUserid, userId).count() > 0);
            case AFTER_ORDER:
                return R.success(SpringUtil.getBean(ShouhouService.class).lambdaQuery().eq(Shouhou::getId, orderId)
                        .eq(Shouhou::getUserid, userId).count() > 0);
            case AFTER_SMALL_ORDER:
                return R.success(SpringUtil.getBean(SmallproService.class).lambdaQuery().eq(Smallpro::getId, orderId)
                        .eq(Smallpro::getUserId, userId).count() > 0);
            default:
                return R.success(StrUtil.format("单据类型[{}]未处理", orderType), true);
        }
    }

    @Override
    public String imeiQueryApi(String q) {
        return HttpUtil.get(imeiQueryApiUrl + q);
    }

    @Override
    public R<List<ShouhouRepaireUserVo>> getShouhouRepaireUserInfo(List<Long> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return R.error("缺少必要参数userIds");
        }
        List<ShouhouRepaireUserVo> resList = new LinkedList<>();

        //返回修手机记录的会员信息(已修好)
        List<Long> userIdList = shouhouService.getShouhouRepaireUserInfo(userIds);
        if (CollectionUtils.isNotEmpty(userIdList)) {
            resList = userIds.stream().map(e -> {
                ShouhouRepaireUserVo resVo = new ShouhouRepaireUserVo();
                resVo.setUserId(e);
                resVo.setIsRepaired(userIdList.contains(e));
                return resVo;
            }).collect(Collectors.toList());
        } else {
            resList = userIds.stream().map(e -> {
                ShouhouRepaireUserVo resVo = new ShouhouRepaireUserVo();
                resVo.setUserId(e);
                resVo.setIsRepaired(false);
                return resVo;
            }).collect(Collectors.toList());
        }

        return R.success(resList);
    }

    @Override
    public List<ProductKcInfo> queryProductStockByPpid(List<Integer> ppids, Integer type) {
        List<ProductKcInfo> productKcCountInfoList = oaApiMapper.queryProductStockByPpid(ppids, type);
        if (CollectionUtils.isEmpty(productKcCountInfoList)) {
            productKcCountInfoList = ppids.stream().map(ppid -> {
                ProductKcInfo countInfo = new ProductKcInfo();
                countInfo.setPpid(ppid);
                countInfo.setKcCount(0);
                return countInfo;
            }).collect(Collectors.toList());
            return productKcCountInfoList;
        } else {
            List<Integer> ppidList = productKcCountInfoList.stream().map(p -> p.getPpid()).collect(Collectors.toList());
            List<ProductKcInfo> kcInfoList = productKcCountInfoList;

            List<ProductKcInfo> resList = ppids.stream().map(ppid -> {
                ProductKcInfo countInfo = new ProductKcInfo();
                countInfo.setPpid(ppid);
                if (!ppidList.contains(ppid)) {
                    countInfo.setKcCount(0);
                } else {
                    ProductKcInfo kcInfo = kcInfoList.stream().filter(t -> t.getPpid().equals(ppid)).findFirst().orElse(null);
                    if (kcInfo != null) {
                        countInfo.setKcCount(kcInfo.getKcCount());
                    } else {
                        countInfo.setKcCount(0);
                    }
                }
                return countInfo;
            }).collect(Collectors.toList());
            return resList;
        }
    }
    @Override
    public List<ProductKcInfo> queryProductStockByPpidV3(List<Integer> ppids, Integer type) {
        List<ProductKcInfo> productKcCountInfoList = oaApiMapper.queryProductStockByPpidV3(ppids, type);
        if (CollectionUtils.isEmpty(productKcCountInfoList)) {
            productKcCountInfoList = ppids.stream().map(ppid -> {
                ProductKcInfo countInfo = new ProductKcInfo();
                countInfo.setPpid(ppid);
                countInfo.setKcCount(0);
                return countInfo;
            }).collect(Collectors.toList());
            return productKcCountInfoList;
        } else {
            List<Integer> ppidList = productKcCountInfoList.stream().map(ProductKcInfo::getPpid).collect(Collectors.toList());
            List<ProductKcInfo> kcInfoList = productKcCountInfoList;
            return ppids.stream().map(ppid -> {
                ProductKcInfo countInfo = new ProductKcInfo();
                countInfo.setPpid(ppid);
                if (!ppidList.contains(ppid)) {
                    countInfo.setKcCount(0);
                } else {
                    ProductKcInfo kcInfo = kcInfoList.stream().filter(t -> t.getPpid().equals(ppid)).findFirst().orElse(null);
                    if (kcInfo != null) {
                        countInfo.setKcCount(kcInfo.getKcCount());
                    } else {
                        countInfo.setKcCount(0);
                    }
                }
                return countInfo;
            }).collect(Collectors.toList());
        }
    }

    // mapper的getRoleNameByMobile
    @Override
    public Ch999SimpleUserRoleBo getRoleNameByMobile(String mobile) {
        return oaApiMapper.getRoleNameByMobile(mobile);
    }
}
