package com.jiuji.oa.afterservice.bigpro.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.jiuji.oa.afterservice.bigpro.bo.ShouhouMsgPushMessageBo;
import com.jiuji.oa.afterservice.bigpro.bo.ShouhouTestResultBo;
import com.jiuji.oa.afterservice.bigpro.bo.WeixiuOptionBo;
import com.jiuji.oa.afterservice.bigpro.bo.WeixiuTestOptionBo;
import com.jiuji.oa.afterservice.bigpro.dao.ShouhoutestInfoMapper;
import com.jiuji.oa.afterservice.bigpro.po.Shouhou;
import com.jiuji.oa.afterservice.bigpro.po.ShouhouQudao;
import com.jiuji.oa.afterservice.bigpro.po.ShouhoutestInfo;
import com.jiuji.oa.afterservice.bigpro.service.ShouhouQudaoService;
import com.jiuji.oa.afterservice.bigpro.service.ShouhouService;
import com.jiuji.oa.afterservice.bigpro.service.ShouhoutestInfoService;
import com.jiuji.oa.afterservice.bigpro.vo.req.WeixiuTestOptionReq;
import com.jiuji.oa.afterservice.common.constant.ShouhouConstants;
import com.jiuji.oa.afterservice.common.enums.MTableInfoEnum;
import com.jiuji.oa.afterservice.common.enums.TuihuanKindEnum;
import com.jiuji.oa.afterservice.common.enums.XtenantEnum;
import com.jiuji.oa.afterservice.common.util.CommenUtil;
import com.jiuji.oa.afterservice.other.po.ShouhouTuihuan;
import com.jiuji.oa.afterservice.other.service.ShouhouTuihuanService;
import com.jiuji.tc.common.vo.R;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-27
 */
@Service
public class ShouhoutestInfoServiceImpl extends ServiceImpl<ShouhoutestInfoMapper, ShouhoutestInfo> implements ShouhoutestInfoService {

    @Lazy
    @Autowired
    private ShouhouService shouhouService;

    @Autowired
    private ShouhouConstants shouhouConstants;

    @Autowired
    private ShouhouQudaoService shouhouQudaoService;

    @Autowired
    private ShouhouTuihuanService shouhouTuihuanService;

    @Autowired
    @Qualifier("pushMessageExecutor")
    private ThreadPoolTaskExecutor executor;

    //设置查询阈值，超过这个数量则进行拆分
    private static final Integer THRESHOLD_VALUE = 500;

    @Override
    public List<ShouhouTestResultBo> checkShouhouTestResult(List<Integer> shouhouIds) {
        //九机兼容新版测试结果查询
        if (XtenantEnum.isJiujiXtenant()) {
            return checkShouhouTestResultV2(shouhouIds);
        }

        // 参数个数超过2100个会报错，所以拆分查询
        Map<Integer, List<Integer>> queryMap = new HashMap<>();
        if (shouhouIds.size() > THRESHOLD_VALUE) {
            queryMap = CommenUtil.splitList(shouhouIds, shouhouIds.size() / THRESHOLD_VALUE + 1);
        }

        List<ShouhouTestResultBo> resList = new LinkedList<>();
        if (!queryMap.isEmpty()) {
            Map<Integer, List<Integer>> queryIdMap = queryMap;
            List<CompletableFuture<List<ShouhouTestResultBo>>> completableFutureList = queryIdMap
                    .keySet().stream().map(t -> CompletableFuture.supplyAsync(() -> checkTestResult(queryIdMap.get(t)), this.executor))
                    .collect(Collectors.toList());
            //等待所有任务执行完毕
//            completableFutureList.forEach(CompletableFuture::join);
            completableFutureList.forEach(future -> {
                resList.addAll(future.join());
            });
        } else {
            List<ShouhouTestResultBo> memberLogList = checkTestResult(shouhouIds);
            resList.addAll(memberLogList);
        }

        return resList;
    }


    private List<ShouhouTestResultBo> checkTestResult(List<Integer> shouhouIds){
        return CommenUtil.autoQueryHist(()->baseMapper.checkShouhouTestResult(shouhouIds));
    }

    private List<ShouhouTestResultBo> checkTestResultV2(List<Integer> shouhouIds){
        return CommenUtil.autoQueryHist(()->baseMapper.checkShouhouTestResultV2(shouhouIds));
    }

    private List<ShouhouTestResultBo> checkShouhouTestResultV2(List<Integer> shouhouIds) {
        List<ShouhouTestResultBo> resList = new ArrayList<>();
        if (CollectionUtil.isEmpty(shouhouIds)) {
            return resList;
        }
        // 参数个数超过2100个会报错，所以拆分查询
        List<List<Integer>> partitionList = Lists.partition(shouhouIds, THRESHOLD_VALUE);
        List<CompletableFuture<List<ShouhouTestResultBo>>> completableFutureList = partitionList.stream()
                .map(v -> CompletableFuture.supplyAsync(() -> checkTestResultV2(v), this.executor))
                .collect(Collectors.toList());
        completableFutureList.forEach(future -> {
            resList.addAll(future.join());
        });
        return resList;
    }

    @Override
    public R<Boolean> saveTestOptions(WeixiuTestOptionReq req) {

        if (StringUtils.isEmpty(req.getOptions()) || CollectionUtils.isEmpty(req.getOptionsList())) {
            return R.error("提交测试参数错误");
        }

        List<WeixiuTestOptionReq.WeixiuTestOptionValueItem> options = req.getOptionsList();

        //Shouhou shDt = shouhouService.getById(req.getShouhouId());
        Shouhou shDt = CommenUtil.autoQueryHist(() ->shouhouService.getById(req.getShouhouId()), MTableInfoEnum.SHOUHOU,req.getShouhouId());
        if (shDt == null){
            return R.error("维修单不存在");
        }

        List<WeixiuTestOptionBo>testOptionList = getWeixiuTestOptions();

        List<WeixiuOptionBo>opts = Collections.emptyList();
        for (WeixiuTestOptionBo a : testOptionList){
            for (WeixiuTestOptionReq.WeixiuTestOptionValueItem b : options){
                if (a.getName().equals(b.getName())){
                    Boolean flag = a.getOptions().get(0).equals(b.getValue());
                    opts.add(new WeixiuOptionBo(a.getName(),a.getOptions(),b.getValue(),flag));
                }
            }
        }
        Boolean testSuccess = opts.stream().allMatch(f->f.getIstrue());
        String testResultInfo = testSuccess ? "测试通过，可正常使用！" : "测试不通过；";
        if (!testSuccess){
            Optional<WeixiuOptionBo> optional = opts.stream().filter(e->e.getIstrue() == false).findAny();
            WeixiuOptionBo offopt = optional.isPresent() ? optional.get() : null;
            if (offopt != null){
                testResultInfo += String.format("%s %s", offopt.getName(), offopt.getValue());
            }
        }

        //提交修改
        if (req.getId() == null || req.getId() <=0){
           super.remove(new LambdaQueryWrapper<ShouhoutestInfo>().eq(ShouhoutestInfo::getShouhouId,req.getShouhouId()));
        }

        ShouhoutestInfo testInfo = new ShouhoutestInfo();
        testInfo.setShouhouId(req.getShouhouId());
        testInfo.setDtime(LocalDateTime.now());
        testInfo.setTestResult(testSuccess);
        testInfo.setInuser(req.getInuser());
        testInfo.setTestResultInfo(testResultInfo);
        testInfo.setTestParms(JSONObject.toJSONString(options));
        testInfo.setWaiguan("");
        testInfo.setTesttype(1);
        testInfo.setComment(StringUtils.isNotEmpty(req.getComment()) ? req.getComment() : "");

        super.save(testInfo);

        ShouhouMsgPushMessageBo pushmsg = new ShouhouMsgPushMessageBo();
        if (testSuccess){

            String processConfirmStats = shDt.getProcessConfirmStats();

            if (StringUtils.isEmpty(processConfirmStats)){
                processConfirmStats = shouhouConstants.getShouhouProcessStatsCSTG().toString();
            }
            else{
                processConfirmStats += "," + shouhouConstants.getShouhouProcessStatsCSTG();
            }

            shouhouService.update(new LambdaUpdateWrapper<Shouhou>().set(Shouhou::getProcessConfirmStats, processConfirmStats).set(Shouhou::getTestuser, req.getInuser()).set(Shouhou::getTesttime, LocalDateTime.now()).eq(Shouhou::getId, req.getShouhouId()));

            //消息推送
            pushmsg.setMsgId(24);
            pushmsg.setShouhouId(req.getShouhouId());
            pushmsg.setAreaId(shDt.getAreaid());
            pushmsg.setUserId(shDt.getUserid().intValue());
            pushmsg.setOptUser(req.getInuser());
            shouhouService.pushMessage(pushmsg, true);
        } else {
            //消息推送
            pushmsg.setMsgId(24);
            pushmsg.setShouhouId(req.getShouhouId());
            pushmsg.setAreaId(shDt.getAreaid());
            pushmsg.setUserId(shDt.getUserid().intValue());
            pushmsg.setOptUser(req.getInuser());
            shouhouService.pushMessage(pushmsg, true);
        }
        if (XtenantEnum.isJiujiXtenant()) {
            ShouhouQudao shouhouQudao = shouhouQudaoService.getShouhouQudaoByShouhouId(req.getShouhouId());
            Integer id = shouhouQudao.getId();
            if (CommenUtil.isNullOrZero(id)) {
                shouhouService.saveShouhouLog(id, "您的设备故障已处理完成，经测试员"
                        + req.getInuser() + "检测，送检故障已经解决。已为您的设备进行清洁，请您携带取机单到店取机。", req.getInuser(), null, true);
            } else {
                shouhouService.saveShouhouLog(id, "您的设备已从【售后站点】取回，售后处理已完成，设备已经进行全面检测，已为您的设备进行清洁，请您携带取机单到店取机。", req.getInuser(), null, true);
            }
            ShouhouTuihuan tuihuan = shouhouTuihuanService.lambdaQuery().eq(ShouhouTuihuan::getShouhouId, req.getShouhouId())
                    .list().stream().findFirst().orElse(null);
            if (Objects.nonNull(tuihuan)
                    && !tuihuan.getIsdel()
                    && Arrays.asList(TuihuanKindEnum.TK.getCode(), TuihuanKindEnum.HQTXH.getCode()).contains(tuihuan.getTuihuanKind())) {
                shouhouService.saveShouhouLog(id, "测试通过", req.getInuser(), null, false);
            }
        }


        return R.success("操作成功");
    }

    @Override
    public List<WeixiuTestOptionBo> getWeixiuTestOptions(){

        List<WeixiuTestOptionBo> res = new LinkedList<>();
        List<String> options  = Arrays.asList( "正常","不正常","无功能");
        WeixiuTestOptionBo option = new WeixiuTestOptionBo();

        res.add(new WeixiuTestOptionBo().setName("GPS").setType(1).setOptions(options));
        res.add(new WeixiuTestOptionBo().setName("陀螺仪").setType(1).setOptions(options));
        res.add(new WeixiuTestOptionBo().setName("指南针").setType(1).setOptions(options));
        res.add(new WeixiuTestOptionBo().setName("麦克风").setType(1).setOptions(options));
        res.add(new WeixiuTestOptionBo().setName("闪光灯").setType(1).setOptions(options));
        res.add(new WeixiuTestOptionBo().setName("WIFI").setType(1).setOptions(options));
        res.add(new WeixiuTestOptionBo().setName("扬声器").setType(1).setOptions(options));
        res.add(new WeixiuTestOptionBo().setName("距离传感器").setType(1).setOptions(options));
        res.add(new WeixiuTestOptionBo().setName("按键功能").setType(1).setOptions(options));
        res.add(new WeixiuTestOptionBo().setName("通话功能").setType(1).setOptions(options));
        res.add(new WeixiuTestOptionBo().setName("摄像功能").setType(1).setOptions(options));
        res.add(new WeixiuTestOptionBo().setName("触摸功能").setType(1).setOptions(options));
        res.add(new WeixiuTestOptionBo().setName("震动功能").setType(1).setOptions(options));
        res.add(new WeixiuTestOptionBo().setName("充电功能").setType(1).setOptions(options));
        res.add(new WeixiuTestOptionBo().setName("屏幕显示").setType(1).setOptions(options));

        res.add(new WeixiuTestOptionBo().setName("摄像头灰尘").setType(2).setOptions(Arrays.asList( "无灰尘","有灰尘")));
        res.add(new WeixiuTestOptionBo().setName("尾插螺丝").setType(2).setOptions(Arrays.asList( "齐全","不齐全")));
        res.add(new WeixiuTestOptionBo().setName("外观磕痕").setType(2).setOptions(Arrays.asList( "无新增","有新增")));

        return res;

    }
}
