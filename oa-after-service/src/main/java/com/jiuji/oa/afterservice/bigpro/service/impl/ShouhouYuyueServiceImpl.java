package com.jiuji.oa.afterservice.bigpro.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.codec.Base64;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.http.Header;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ch999.common.util.utils.CommonUtils;
import com.ch999.common.util.utils.XtenantJudgeUtil;
import com.ch999.common.util.vo.Result;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.jiuji.cloud.after.enums.ShouHouLogTypeEnum;
import com.jiuji.cloud.after.enums.ShouhouyuyueFromSourceEnum;
import com.jiuji.cloud.after.vo.req.ShouhouyuyueFromSource;
import com.jiuji.infra.lmstfy.anotation.LmstfyConsume;
import com.jiuji.oa.afterservice.api.bo.Ch999UserBasicBO;
import com.jiuji.oa.afterservice.api.service.OaApiService;
import com.jiuji.oa.afterservice.apollo.ApolloEntity;
import com.jiuji.oa.afterservice.bigpro.bo.*;
import com.jiuji.oa.afterservice.bigpro.bo.yuyue.YjsxAddress;
import com.jiuji.oa.afterservice.bigpro.dao.ShouhouExMapper;
import com.jiuji.oa.afterservice.bigpro.dao.ShouhouMapper;
import com.jiuji.oa.afterservice.bigpro.dao.ShouhouOtherMapper;
import com.jiuji.oa.afterservice.bigpro.dao.ShouhouYuyueMapper;
import com.jiuji.oa.afterservice.bigpro.entity.Productbarcode;
import com.jiuji.oa.afterservice.bigpro.enums.*;
import com.jiuji.oa.afterservice.bigpro.enums.discount.ConnectionMethodEnum;
import com.jiuji.oa.afterservice.bigpro.po.*;
import com.jiuji.oa.afterservice.delayQueuePush.appointmentFormStrategy.impl.YwConfirmDelayQueueStrategy;
import com.jiuji.oa.afterservice.delayQueuePush.vo.YwConfirmDelayPush;
import com.jiuji.oa.afterservice.bigpro.po.refund.ShouhouTuiHuanPo;
import com.jiuji.oa.afterservice.bigpro.service.*;
import com.jiuji.oa.afterservice.bigpro.statistics.enums.WuLiuType;
import com.jiuji.oa.afterservice.bigpro.statistics.po.Wuliu;
import com.jiuji.oa.afterservice.bigpro.statistics.service.WuliuService;
import com.jiuji.oa.afterservice.bigpro.vo.OrderPartsVo;
import com.jiuji.oa.afterservice.bigpro.vo.SmallProductInfo;
import com.jiuji.oa.afterservice.bigpro.vo.YuYueConfirmPushMsg;
import com.jiuji.oa.afterservice.bigpro.vo.req.*;
import com.jiuji.oa.afterservice.bigpro.vo.res.*;
import com.jiuji.oa.afterservice.cloud.service.IMCloud;
import com.jiuji.oa.afterservice.cloud.service.WebCloud;
import com.jiuji.oa.afterservice.cloud.vo.web.ShortUrlParam;
import com.jiuji.oa.afterservice.common.bo.OaUserBO;
import com.jiuji.oa.afterservice.common.bo.SubPushMsgBO;
import com.jiuji.oa.afterservice.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.afterservice.common.config.properties.JiujiSystemProperties;
import com.jiuji.oa.afterservice.common.config.rabbimq.RabbitMqConfig;
import com.jiuji.oa.afterservice.common.constant.ConfigConsts;
import com.jiuji.oa.afterservice.common.constant.DataSourceConstants;
import com.jiuji.oa.afterservice.common.constant.RequestAttrKeys;
import com.jiuji.oa.afterservice.common.constant.UrlConstants;
import com.jiuji.oa.afterservice.common.enums.ShouhouOrderTypeEnum;
import com.jiuji.oa.afterservice.common.enums.*;
import com.jiuji.oa.afterservice.common.exception.CustomizeException;
import com.jiuji.oa.afterservice.common.exception.RRException;
import com.jiuji.oa.afterservice.common.exception.RRExceptionHandler;
import com.jiuji.oa.afterservice.common.util.*;
import com.jiuji.oa.afterservice.common.vo.PageVo;
import com.jiuji.oa.afterservice.other.dao.ReceivePersonConfigMapper;
import com.jiuji.oa.afterservice.other.po.Basket;
import com.jiuji.oa.afterservice.other.service.BasketService;
import com.jiuji.oa.afterservice.refund.service.RefundMachineService;
import com.jiuji.oa.afterservice.shouhou.vo.ServiceInfoVO;
import com.jiuji.oa.afterservice.smallpro.constant.SmallProRelativePathConstant;
import com.jiuji.oa.afterservice.smallpro.enums.SmallProKindEnum;
import com.jiuji.oa.afterservice.smallpro.enums.SmallProServiceTypeEnum;
import com.jiuji.oa.afterservice.smallpro.po.SmallproBill;
import com.jiuji.oa.afterservice.smallpro.service.SmallproService;
import com.jiuji.oa.afterservice.smallpro.vo.req.SmallproReq;
import com.jiuji.oa.afterservice.stock.po.ProductKc;
import com.jiuji.oa.afterservice.stock.service.ProductKcService;
import com.jiuji.oa.afterservice.sys.service.RetryService;
import com.jiuji.oa.afterservice.sys.service.SysConfigService;
import com.jiuji.oa.afterservice.yuyue.vo.req.ShouhouYuyueReqVo;
import com.jiuji.oa.nc.SmsConfigCloud;
import com.jiuji.oa.nc.sms.SmsConfigCodeEnum;
import com.jiuji.oa.nc.sms.SmsConfigVO;
import com.jiuji.oa.nc.sms.SmsPushMethodEnum;
import com.jiuji.oa.oacore.oaorder.WuliuCloud;
import com.jiuji.oa.oacore.oaorder.req.AddWuliuReq;
import com.jiuji.oa.orginfo.areainfo.client.AreaInfoClient;
import com.jiuji.oa.orginfo.areainfo.client.AreaListClient;
import com.jiuji.oa.orginfo.areainfo.vo.AreaInfo;
import com.jiuji.oa.orginfo.areainfo.vo.res.AreaListSimpleRes;
import com.jiuji.oa.orginfo.departinfo.client.DepartInfoClient;
import com.jiuji.oa.orginfo.member.client.MemberClient;
import com.jiuji.oa.orginfo.member.req.MemberReq;
import com.jiuji.oa.orginfo.member.res.MemberBasicRes;
import com.jiuji.oa.orginfo.sysconfig.client.SysConfigClient;
import com.jiuji.oa.orginfo.userinfo.client.UserInfoClient;
import com.jiuji.oa.orginfo.userinfo.vo.Ch999UserVo;
import com.jiuji.oa.orginfo.userinfo.vo.RoleSimpleVo;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.common.vo.ResultCode;
import com.jiuji.tc.foundation.db.annotation.DS;
import com.jiuji.tc.utils.common.DecideUtil;
import com.jiuji.tc.utils.common.build.LambdaBuild;
import com.jiuji.tc.utils.constants.NumberConstant;
import com.jiuji.tc.utils.constants.SysConfigConstant;
import com.jiuji.tc.utils.constants.TimeFormatConstant;
import com.jiuji.tc.utils.enums.EnumUtil;
import com.jiuji.tc.utils.xtenant.Namespaces;
import com.meitu.platform.lmstfy.Job;
import com.meitu.platform.lmstfy.client.LmstfyClient;
import com.meitu.platform.lmstfy.exception.LmstfyException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-11
 */
@Slf4j
@Service
public class ShouhouYuyueServiceImpl extends ServiceImpl<ShouhouYuyueMapper, ShouhouYuyue> implements ShouhouYuyueService {

    @Resource
    private ShouHouPjService shouHouPjService;
    @Resource
    private AbstractCurrentRequestComponent currentRequestComponent;
    @Resource
    private AreaInfoClient areaInfoClient;
    @Autowired
    private YuyueLogsService yuyueLogsService;
    @Autowired
    private TroubleService troubleService;
    @Autowired
    private ImeisearchlogsService imeisearchlogsService;
    @Lazy
    @Autowired
    private ShouhouService shouhouService;
    @Resource
    private UserInfoClient userInfoClient;
    @Autowired
    private KaoqinService kaoqinService;
    @Autowired
    private WXSmsReceiverService wxSmsReceiverService;
    @Autowired
    private ShouhouSendaddressService shouhouSendaddressService;
    @Autowired
    private WeixinUserService weixinUserService;
    @Resource
    private WuliuCloud wuliuCloud;
    @Autowired
    private AddinfopsService addinfopsService;
    @Autowired
    private CardLogsService cardLogsService;
    @Autowired
    private SendMessageRecordService sendMessageRecordService;
    @Autowired
    private InstallServicesRecordService installServicesRecordService;
    @Autowired
    private NumberCardService numberCardService;
    @Autowired
    private ShouhouYuyueTroubleService shouhouYuyueTroubleService;
    @Autowired
    private SmsService smsService;
    @Autowired
    private MemberClient memberClient;
    @Autowired
    private ShouhouTroubleService shouhouTroubleService;
    @Autowired
    private ShouhouRomUpgradeService shouhouRomUpgradeService;
    @Autowired
    private AttachmentsService attachmentsService;
    @Autowired
    private DaiyonjiyuyueinfoService daiyonjiyuyueinfoService;
    @Autowired
    private ShouhouYuyueproductinfoService shouhouYuyueproductinfoService;
    @Autowired
    private ProductinfoService productinfoService;
    @Autowired
    private ShouhouYuyuelockppidsService shouhouYuyuelockppidsService;
    @Autowired
    private BasketService basketService;
    @Autowired
    private SmallproService smallproService;
    @Autowired
    private ShouhouYuyueMapper shouhouYuyueMapper;
    @Lazy
    @Autowired
    private ShouhouLogsService shouhouLogsService;
    @Lazy
    @Autowired
    private ShouhouYuyueService shouhouYuyueService;
    @Resource
    private IMCloud imCloud;
    @Autowired
    private AreaListClient areaListClient;
    @Autowired
    private ProductKcService productKcService;
    @Autowired
    private ShouhouOtherMapper shouhouOtherMapper;
    @Autowired
    private CommonService commonService;
    @Autowired
    private SysConfigService sysConfigService;
    @Autowired
    private DepartInfoClient departInfoClient;
    @Lazy
    @Autowired
    private OaApiService oaApiService;
    @Resource
    @Qualifier("oaRabbitTemplate")
    private RabbitTemplate rabbitTemplate;
    @Resource
    private JiujiSystemProperties jiujiSystemProperties;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private ThirdPlatStatusSyncService thirdPlatStatusSyncService;
    @Resource
    private Executor pushMessageExecutor;
    @Resource
    private SmsConfigCloud smsConfigCloud;
    @Resource
    private ApolloEntity apolloEntity;
    @Resource
    private SysConfigClient sysConfigClient;
    private static final String YUYUE_CONFIRM_PUSH_MSG_QUEUE = "${lmstfy.mult.first-lmstfy-client.yuyueConfirmPushMsgQueue}";
    @Resource(name = "firstLmstfyClient")
    private LmstfyClient firstLmstfyClient;
    @Value("${lmstfy.mult.first-lmstfy-client.yuyueConfirmPushMsgQueue}")
    private String yuyueConfirmPushMsgQueue;

    private static final Integer CHECK_DIAMONDS_MEMBER_CODE = 66;
    private static final Integer DEFAULT_MINUS_MINUTES = 20;
    @Resource
    private ReceivePersonConfigMapper receivePersonConfigMapper;
    @Resource
    private PlatformTransactionManager transactionManager;
    @Resource
    private ProductbarcodeService barcodeService;
    @Resource
    private ShouhouMapper shouhouMapper;
    @Resource
    private YwConfirmDelayQueueStrategy ywConfirmDelayQueueStrategy;
    /**
     * 预约单配件 订购单
     */
    public static final String ORDER_PARTS = "orderPartsVos";
    public static final Integer ONE_HOUR_SECOND = 60*60 ;


    /**
     * 预约单新版UI
     * @return
     */
    @Override
    public Boolean isUseNewUI() {
        if(XtenantJudgeUtil.isJiujiMore()){
            OaUserBO userBO = Optional.ofNullable(currentRequestComponent.getCurrentStaffId()).orElseThrow(() -> new CustomizeException("请登录"));
            Integer areaId = userBO.getArea1id();
            String redeemAreaId = Optional.ofNullable(apolloEntity.getYuYueNewUIAreaId()).orElse("");
            //     * 第一：如果过为空那就是全部门店使用，
            //     * 第二：如果存在具体门店id那就这几个门店id使用
            //     * 第三：如果只存在-1 那就是所有门店不使用
            List<Integer> areaIds = Arrays.stream(redeemAreaId.split(",")).filter(com.baomidou.mybatisplus.core.toolkit.StringUtils::isNotEmpty).map(Integer::valueOf).collect(Collectors.toList());
            if(CollUtil.isEmpty(areaIds)){
                return Boolean.TRUE;
            } else if(areaIds.contains(areaId)){
                return Boolean.TRUE;
            } else if(areaIds.contains(-1)){
                return Boolean.FALSE;
            }
        }
        return Boolean.FALSE;
    }

    /**
     * 新版预约单
     * @return
     */
    @Override
    public Boolean isUseNewYuYue() {
        if (XtenantEnum.isSaasXtenant()) {
            return Boolean.FALSE;
        } else {
            return Boolean.TRUE;
        }
    }

    /**
     * 创建订购配件（非事务）
     * 使用 NOT_SUPPORTED 传播级别，完全不参与任何事务
     *
     * @param orderPartsVoList 订购配件列表
     * @return 关联信息列表
     */
    @Override
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public List<CorrelationInfo> createOrderPartsNonTransactional(List<OrderPartsVo> orderPartsVoList) {
        List<CorrelationInfo> correlationInfoList = new ArrayList<>();
        if (CollUtil.isEmpty(orderPartsVoList)) {
            return correlationInfoList;
        }
        orderPartsVoList.forEach(item -> {
            try {
                R<Integer> orderParts = createOrderParts(item);
                if(!orderParts.isSuccess()){
                    throw new CustomizeException(item.getProductName()+"订购配件创建失败:"+orderParts.getUserMsg()+""+orderParts.getMsg());
                }
                CorrelationInfo correlationInfo = new CorrelationInfo();
                correlationInfo.setCorrelationType(CorrelationTypeEnum.SHOUHOU_APPLY.getCode())
                        .setCorrelationId(orderParts.getData())
                        .setRepairPlanId(item.getRepairPlanId())
                        .setAccessoryPpid(item.getPpid());
                correlationInfoList.add(correlationInfo);
            } catch (Exception e) {
                shouhouService.saveShouhouLog(item.getShouHouId(), e.getMessage(), "系统");
            }
        });
        return correlationInfoList;
    }

    @Override
    @DS(DataSourceConstants.CH999_OA_NEW)
    public PageVo getShouhouYuyuePage(ShouhouYuyueListReq param) {
        PageVo result = new PageVo(param.getCurrent(), param.getSize());
        OaUserBO oaUserBO = currentRequestComponent.getCurrentStaffId();
        if (StringUtils.isNotEmpty(param.getKeyVal())){
            param.setKeyVal(param.getKeyVal().trim());
        }
        //设置是否预约单优化流程
        param.setIsUseNewYuYue(Convert.toInt(isUseNewYuYue()));
        if ("id".equals(param.getKey())&&StringUtils.isNotEmpty(param.getKeyVal())) {
            try {
                Long.valueOf(param.getKeyVal());
            } catch (NumberFormatException e) {
                throw new IllegalArgumentException("请输入正确的单号", e);
            }
        }
        //a区 处理以a开头的门店
        if (!(Objects.equals(null, param.getAreaIds()) || param.getAreaIds().isEmpty())) {
            R<List<AreaInfo>> departR = areaInfoClient.listAll();
            Set<String> codes2 = param.getAreaIds().stream().filter(s -> s.startsWith("a"))
                    .map(s -> s.substring(1)).collect(Collectors.toSet());
            Set<Integer> departIds = new HashSet<>();
            for (String code : codes2) {
                departIds.addAll(departInfoClient.getAllLowNode(code).getData());
            }
            List<String> areaIds = departR.getData().stream().filter(areaInfo ->
                    departIds.contains(areaInfo.getDepartId()))
                    .map(AreaInfo::getId).map(String::valueOf).collect(Collectors.toList());
            areaIds.addAll(param.getAreaIds().stream().filter(s ->
                    !s.startsWith("a")).map(String::valueOf).collect(Collectors.toList()));
            param.setAreaIds(areaIds);
        }

        Integer total = baseMapper.getYuyueListCount(param, Long.valueOf(oaUserBO.getXTenant()), oaUserBO.getAreaKind1(),
                oaUserBO.getAuthorizeId());
        if (total == 0) {
            return result;
        }
        if (param.getCurrent() == null || param.getCurrent() == 0 || param.getSize() == null) {
            param.setCurrent(1);
            param.setSize(15);
        }
        param.setStartRow((param.getCurrent() - 1) * param.getSize());
        List<ShouhouYuyueListRes> records = baseMapper.getYuyueList(param, Long.valueOf(oaUserBO.getXTenant()), oaUserBO.getAreaKind1(),
                oaUserBO.getAuthorizeId());
        List<Integer> areaIds =
                records.stream().filter(e -> StringUtils.isEmpty(e.getArea())).map(e -> e.getAreaId()).collect(Collectors.toList());
        R<List<AreaInfo>> areaInfoRet = areaInfoClient.listAll();
        Map<Integer, AreaInfo> areaMap = null;
        if (ResultCode.SUCCESS == areaInfoRet.getCode() && CollectionUtils.isNotEmpty(areaInfoRet.getData())) {
            areaMap =
                    areaInfoRet.getData().stream().filter(e -> areaIds.stream().filter(e1 -> e.getId().equals(e1)).count() > 0).collect(Collectors.toMap(e -> e.getId(), e -> e));
        }
        LocalDateTime now = LocalDateTime.now();
        for (ShouhouYuyueListRes shouhouYuyue : records) {
            shouhouYuyue.setSubmitTimeStr(DateUtil.localDateTimeToString(shouhouYuyue.getSubmitTime()));
            shouhouYuyue.setServicesWayName(EnumUtil.getMessageByCode(ServicesWayEnum.class,
                    shouhouYuyue.getServicesWay()));
            //设置是否超时字段
            if(Arrays.asList(YuyueStatusEnum.WQR.getCode(),YuyueStatusEnum.YWQR.getCode()).contains(shouhouYuyue.getStatus())){
                shouhouYuyue.setIsOverTime(now.isAfter(Optional.ofNullable(shouhouYuyue.getEndTime()).orElse(now)));
            }

            shouhouYuyue.setDealWayName(EnumUtil.getMessageByCode(DealWayEnum.class, shouhouYuyue.getDealWay()));
            if (shouhouYuyue.getIsDel() != null && shouhouYuyue.getIsDel()) {
                shouhouYuyue.setStatus(YuyueStatusEnum.YQX.getCode());
                if(isUseNewYuYue()){
                    shouhouYuyue.setStatus(YuyueStatusEnum.YSC.getCode());
                }
            }
            if(isUseNewYuYue()){
                shouhouYuyue.setStatusName(YuyueStatusEnum.getNewMessageByCode(shouhouYuyue.getStatus()));
            } else {
                shouhouYuyue.setStatusName(EnumUtil.getMessageByCode(YuyueStatusEnum.class, shouhouYuyue.getStatus()));
            }
            StringBuffer yuyueTime = new StringBuffer();
            if (shouhouYuyue.getStartTime() != null && shouhouYuyue.getEndTime() != null) {
                yuyueTime.append(DateUtil.localDateTimeToHourStr(shouhouYuyue.getStartTime()));
                yuyueTime.append("~");
                yuyueTime.append(shouhouYuyue.getEndTime().getHour());
            }
            if (areaMap != null && areaMap.containsKey(shouhouYuyue.getAreaId())) {
                shouhouYuyue.setArea(areaMap.get(shouhouYuyue.getAreaId()).getArea());
            }
            shouhouYuyue.setYuyueTime(yuyueTime.toString());
        }
        result.setTotal(total);
        result.setRecords(records);
        //如果不是自营地区就统计预约机型数量
        if (AuthorizeIdEnum.CHINA_POST_NO_AIYIN_AREA.getCode().equals(oaUserBO.getAuthorizeId())) {
            Map<String, Object> exp = new HashMap<>();
            Integer yuyueCount = baseMapper.getYuyueCont(param, oaUserBO.getAreaKind1(), oaUserBO.getAuthorizeId());
            exp.put("yuyueCount", yuyueCount);
            result.setExp(exp);
        }
        return result;
    }

    /**
     * 初始化AreaSubject
     * @param areaId
     * @return
     */
    @Override
    public AreaInfo initializationAreaSubject(Integer areaId){
        AreaInfo areaInfo = Optional.ofNullable(shouhouService.getAreaSubject(areaId)).orElse(new AreaInfo());
        if(StringUtils.isEmpty(areaInfo.getSmsChannelMarketing())){
            Optional.ofNullable(smsService.getSmsChannelByTenant(areaId, ESmsChannelTypeEnum.YXTD))
                    .ifPresent(item->areaInfo.setSmsChannelMarketing(item.toString()));
        }
        if(StringUtils.isEmpty(areaInfo.getSmsChannel())){
            Optional.ofNullable(smsService.getSmsChannelByTenant(areaId, ESmsChannelTypeEnum.YZMTD))
                    .ifPresent(item->areaInfo.setSmsChannel(item.toString()));
        }
        return areaInfo;
    }

    @Override
    public ShouhouYuyueInfoRes getShouhouYuyueInfo(Integer id) {
        OaUserBO oaUserBO = currentRequestComponent.getCurrentStaffId();
        if (oaUserBO == null) {
            return null;
        }
        ShouhouYuyueInfoRes yuyueInfoRes = new ShouhouYuyueInfoRes();
        ShouhouYuyue shouhouYuyue = baseMapper.selectById(id);
        if (shouhouYuyue == null) {
            return yuyueInfoRes;
        }
        Integer currAreaId = Optional.ofNullable(shouhouYuyue.getAreaid()).orElse(oaUserBO.getAreaId());
        yuyueInfoRes.setAreaSubject(initializationAreaSubject(currAreaId));
        CompletableFuture.runAsync(() -> shouHouPjService.insertSearchHistory(oaUserBO.getUserId(), id + "|6", "oaSearch"), this.pushMessageExecutor);
        //判断是否中邮
        if (AuthorizeIdEnum.CHINA_POST_NO_AIYIN_AREA.getCode().equals(oaUserBO.getAuthorizeId())) {
            yuyueInfoRes.setIszy(true);
        } else {
            yuyueInfoRes.setIszy(false);
        }
        buildBasicShouhouYuyueInfo(yuyueInfoRes, shouhouYuyue);
        String wxpjJson = shouhouYuyue.getYuyuePPids();
        List<Integer> wxpjPpids = new LinkedList<>();
        List<YuyuePpidsInfo> wxpjPpidIsNull = null;
        Map<Integer, YuyuePpidsInfo> wxpjMap = null;
        if (StringUtils.isNotEmpty(wxpjJson)) {
            List<YuyuePpidsInfo> wxpjList = new LinkedList<>();
            if (CommenUtil.isNumer(wxpjJson)) {
                wxpjPpids.add(Integer.valueOf(wxpjJson));
            } else {
                try {
                    wxpjList = JSON.parseArray(wxpjJson, YuyuePpidsInfo.class);
                } catch (Exception e) {
                    throw new RRException("json序列化异常，预约单id" + id.toString() + wxpjJson + e.getMessage());
                }
                if (CollectionUtils.isNotEmpty(wxpjList)) {
                    wxpjPpids = wxpjList.stream().map(e -> e.getPpid()).collect(Collectors.toList());
                    wxpjMap = wxpjList.stream().collect(Collectors.toMap(e -> e.getPpid(), e -> e, (v1, v2) -> v1));
                    wxpjPpidIsNull = wxpjList.stream().filter(e -> e.getPpid() == 0 || e.getPpid() == null).collect(Collectors.toList());
                }
            }
        }


        List<LockWxpjBo> wxpjBoList = this.getLockWxpj(id, wxpjPpids);
        //ppid为0
        if (wxpjPpidIsNull != null && !wxpjPpidIsNull.isEmpty()) {
            LockWxpjBo lockWxpjBo = new LockWxpjBo();
            BeanUtils.copyProperties(wxpjPpidIsNull.get(0), lockWxpjBo);
            lockWxpjBo.setProductName(lockWxpjBo.getFaultDes());
            wxpjBoList.add(lockWxpjBo);
        }
        //锁定的维修配件
        if (CollectionUtils.isNotEmpty(wxpjBoList)) {
            List<Integer> areaIds = wxpjBoList.stream().filter(e -> e.getLockAreaId() != null && e.getLockAreaId() != 0).distinct().map(e -> e.getLockAreaId()).collect(Collectors.toList());
            R<List<AreaInfo>> areaInfoRet = areaInfoClient.listAreaInfo(areaIds);
            Map<Integer, String> areaMap = null;
            if (ResultCode.SUCCESS == areaInfoRet.getCode() && CollectionUtils.isNotEmpty(areaInfoRet.getData())) {
                areaMap = areaInfoRet.getData().stream().collect(Collectors.toMap(e -> e.getId(), e -> e.getArea()));
            }
            for (LockWxpjBo wxpjBo : wxpjBoList) {
                if (wxpjMap != null && wxpjMap.containsKey(wxpjBo.getPpid())) {
                    YuyuePpidsInfo yuyuePpidsInfo = wxpjMap.get(wxpjBo.getPpid());
                    BeanUtils.copyProperties(yuyuePpidsInfo, wxpjBo);
                    wxpjBo.setFaultDes(yuyuePpidsInfo.getTroubleDes());
                    if (wxpjBo.getPpid() == 0) {
                        //前端取值不方便，让后端处理一下
                        wxpjBo.setProductName(yuyuePpidsInfo.getTroubleDes());
                    }
                    if (areaMap != null && areaMap.containsKey(wxpjBo.getLockAreaId())) {
                        wxpjBo.setArea(areaMap.get(wxpjBo.getLockAreaId()));
                    }
                }
                Integer areaId = wxpjBo.getLockAreaId();
                if (areaId == null || areaId == 0) {
                    areaId = shouhouYuyue.getAreaid();
                }
                Integer currentStock = productKcService.getKcCount(wxpjBo.getPpid(), areaId);
                wxpjBo.setCurrentStock(currentStock);
                wxpjBo.setIsLock(CommenUtil.isNullOrZero(wxpjBo.getLockAreaId()) ? false : true);
                Optional.ofNullable(wxpjMap).ifPresent(map->{
                    YuyuePpidsInfo yuyuePpidsInfo = Optional.ofNullable(map.get(wxpjBo.getPpid())).orElse(new YuyuePpidsInfo());
                    wxpjBo.setMemberPrice(yuyuePpidsInfo.getMemberPrice());
                });

            }
        }
        //进程日志
        List<YuyueLogs> logs = yuyueLogsService.getYuyueLogs(id);
        if(CollectionUtils.isNotEmpty(logs)){
            HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
            String platform = Optional.ofNullable(request.getHeader("Platform")).orElse("");
            if (platform.contains("MOA")) {
                String moaHost = Optional.of(sysConfigClient.getValueByCode(SysConfigConstant.MOA_URL))
                        .filter(t -> t.getCode() == ResultCode.SUCCESS)
                        .map(R::getData)
                        .orElseThrow(() -> new CustomizeException("获取M端域名出错"));
                String pcOaHost = Optional.of(sysConfigClient.getValueByCode(SysConfigConstant.OA_URL))
                        .filter(t -> t.getCode() == ResultCode.SUCCESS)
                        .map(R::getData)
                        .orElseThrow(()-> new CustomizeException("获取OA域名出错"));
                logs.forEach(item -> {
                    if (item.getComment().contains("/staticpc/#/after-service/order/edit/")) {
                        item.setComment(item.getComment()
                                .replaceAll("/staticpc/#/after-service/order/edit/", "/new/#/afterService/order/detail/").replaceAll(pcOaHost, moaHost));
                    }
                });
            }
        }
        List<Trouble> troubles = troubleService.listAll();
        //维修类别
        yuyueInfoRes.setTroubles(troubles);
        yuyueInfoRes.setRepairType(((ShouhouYuyueService) AopContext.currentProxy()).getRepairTypes());
        yuyueInfoRes.setLogs(logs);
        yuyueInfoRes.setWxpjList(wxpjBoList);
        //是否预约代用机
        Boolean isYuyueDaiyonji = daiyonjiyuyueinfoService.isYuyueDaiyonji(shouhouYuyue.getShouhouId());
        yuyueInfoRes.setIsYuyueDaiyonji(isYuyueDaiyonji);

        //收货人、取件人信息
        AddinfopsBo addinfops = addinfopsService.getAddinfops(id, shouhouYuyue.getShouhouId());
        if (addinfops != null) {
            addinfops.setIsQuJianddress(shouhouYuyue.getIsQuJianddress());
            yuyueInfoRes.setAddressInfo(addinfops);
        }

        //附件信息
        List<FileReq> attachment = attachmentsService.getAttachmentsList(id, AttachmentsEnum.YUYUE.getCode(), null,null);
        yuyueInfoRes.setAttachment(attachment);

        //添加area
        Integer areaId = yuyueInfoRes.getAreaId();
        if (areaId != null) {
            R<AreaInfo> areaInfoRes = areaInfoClient.getAreaInfoById(areaId);

            if (areaInfoRes.getCode() == ResultCode.SUCCESS && areaInfoRes.getData() != null) {
                yuyueInfoRes.setArea(areaInfoRes.getData().getArea());
            }
        }
        //会员等级
        R<MemberBasicRes> memberBasicInfoRes = memberClient.getMemberBasicInfo(shouhouYuyue.getUserid());
        if (memberBasicInfoRes.getCode() == ResultCode.SUCCESS && memberBasicInfoRes.getData() != null) {
            MemberBasicRes memberBasicInfo = memberBasicInfoRes.getData();
            if (memberBasicInfo != null) {
                UserClassConfig newUserClassName = UserClassEnum.getNewUserClassName(memberBasicInfo.getUserClass(), memberBasicInfo.getXtenant(), stringRedisTemplate, memberBasicInfo.getAreaId(), areaInfoClient);
                //兜底处理
                if (ObjectUtil.isEmpty(newUserClassName)) {
                    yuyueInfoRes.setMemberLevel(EnumUtil.getMessageByCode(UserClassEnum.class, memberBasicInfo.getUserClass()));
                }else {
                    yuyueInfoRes.setMemberLevel(newUserClassName.getUserClassName());
                    yuyueInfoRes.setMemberLevelCode(newUserClassName.getUserClass());
                }
                try {
                    yuyueInfoRes.setUsername2(memberBasicInfo.getUserName());
                    yuyueInfoRes.setUserMobile(memberBasicInfo.getMobile());
                    yuyueInfoRes.setIsCorporateFans(shouhouService.getCorporateFans(shouhouYuyue.getUserid()));
                    if (StrUtil.isNotEmpty(yuyueInfoRes.getUserMobile())) {
                        List<Ch999UserBasicBO> ch999UserBasicBOS = SpringUtil.getBean(ShouhouExMapper.class).selectUserByMobile(yuyueInfoRes.getUserMobile());
                        if(CollectionUtils.isNotEmpty(ch999UserBasicBOS)){
                            Ch999UserBasicBO ch999UserBasicBO = ch999UserBasicBOS.get(NumberConstant.ZERO);
                            yuyueInfoRes.setCh999User(ch999UserBasicBO.getCh999Name());
                            ZhiWuService zhiWuService = SpringUtil.getBean(ZhiWuService.class);
                            Optional.ofNullable(zhiWuService.getById(ch999UserBasicBO.getZhiwuId())).ifPresent(zhiWu -> yuyueInfoRes.setZhiwu(zhiWu.getName()));
                        }
                    }
                } catch(Exception e){
                    RRExceptionHandler.logError("预约单会员信息展示优化异常", yuyueInfoRes, e, SpringUtil.getBean(SmsService.class)::sendOaMsgTo9JiMan);
                }
            }
        }
        Boolean isBindWx = false;
        WeixinUser weixinUser = weixinUserService.getWxxinUserByUserId(shouhouYuyue.getUserid());
        if (weixinUser != null) {
            isBindWx = StringUtils.isBlank(weixinUser.getOpenid());
        }
        yuyueInfoRes.setIsBindWx(isBindWx);

        //查询物流信息
        ShouHouYuYueWuLiuBo wuLiuBo = shouhouYuyueMapper.getShouHouYuYueWuLiuInfo(id);
        if (wuLiuBo != null) {
            wuLiuBo.setWuCompany(EnumUtil.getMessageByCode(WuLiuCompanyTypeEnum.class, wuLiuBo.getWuCompanyType()));
            yuyueInfoRes.setWuliuInfo(wuLiuBo);
        }
        //ppid pid 添加
        if (shouhouYuyue.getPpriceid() != null) {
            yuyueInfoRes.setPpid(shouhouYuyue.getPpriceid());
            Productinfo productinfo = productinfoService.getProductinfoByPpid(shouhouYuyue.getPpriceid());
            if (productinfo != null) {
                yuyueInfoRes.setPid(productinfo.getProductid1());
            }
        }
        //优惠码
        yuyueInfoRes.setYouhuima(cardLogsService.getYuyueUsedCoupon(id));
        //预约单详情交互排版优化
        handleYuyueInfoRes(yuyueInfoRes);
        return yuyueInfoRes;
    }

    /**
     * 预约单详情交互排版优化
     * @param yuyueInfoRes
     */
    private void handleYuyueInfoRes(ShouhouYuyueInfoRes yuyueInfoRes){
        //输出不开放改功能
        if(XtenantEnum.isSaasXtenant()){
            return;
        }
        try {
            //订单类型处理
            TuiProductInfoReq req = new TuiProductInfoReq();
            Integer subId = yuyueInfoRes.getSubId();
            Boolean isMobile = yuyueInfoRes.getIsMobile();
            req.setSubId(subId);
            req.setIsMobile(Convert.toInt(isMobile));
            TuiProductInfoRes tuiProductInfoRes = shouhouYuyueService.getTuiProductInfoBySubIdV2(req);
            List<YuyueProductInfoRes> tuiProductInfoBySubId = tuiProductInfoRes.getProductInfoList();
            if(CollUtil.isNotEmpty(tuiProductInfoBySubId)){
                Optional.ofNullable(tuiProductInfoBySubId.get(NumberConstant.ZERO)).ifPresent(yuyueProductInfoRes -> {
                    Integer type = Optional.ofNullable(yuyueProductInfoRes.getType()).orElse(NumberConstant.ONE);
                    yuyueInfoRes.setSubIdTypeStr(type.equals((NumberConstant.ONE))?"新机":"良品");
                    yuyueInfoRes.setSubDate(yuyueProductInfoRes.getSubDate());
                });
            }
            //判断邮寄送修是否推送过
            List<ShouhouSendaddress> shouhouSendaddressList = shouhouSendaddressService.lambdaQuery().eq(ShouhouSendaddress::getLinkid, yuyueInfoRes.getId())
                    .eq(ShouhouSendaddress::getKind, DealWayEnum.WX.getCode())
                    .list();
            if(CollectionUtils.isNotEmpty(shouhouSendaddressList)){
                yuyueInfoRes.setIsSend(NumberConstant.ONE);
            }
            //如果是小件的情况  查询商品名称
            if(Boolean.FALSE.equals(isMobile)){
                ShouhouYuyueReq.TuiData tuidata = yuyueInfoRes.getTuidata();
                if(ObjectUtil.isNotNull(tuidata)){
                    Map<Integer, YuyueProductInfoRes> yuyueProductInfoMap = tuiProductInfoBySubId.stream().collect(Collectors.toMap(YuyueProductInfoRes::getBasketId, Function.identity(), (n1, n2) -> n2));
                    List<SmallProductInfo> smallProductInfoList = tuidata.getBasket().stream().map(item->{
                        SmallProductInfo smallProductInfo = new SmallProductInfo();
                        YuyueProductInfoRes productInfoRes = yuyueProductInfoMap.getOrDefault(item.getId(), new YuyueProductInfoRes());
                        BeanUtil.copyProperties(productInfoRes,smallProductInfo);
                        smallProductInfo.setCount(item.getCount());
                        return smallProductInfo;
                    }).collect(Collectors.toList());
                    yuyueInfoRes.setSmallProductInfoList(smallProductInfoList);
                }
            }

            //获取手机号码归属地
            if (StringUtils.isNotEmpty(yuyueInfoRes.getMobile()) && yuyueInfoRes.getMobile().length() >= 7){
                String phoneAddress = shouhouMapper.getPhoneAddressByPhoneNumber(yuyueInfoRes.getMobile().substring(0,7));
                yuyueInfoRes.setUserPhoneAddress(phoneAddress);
            }
        }catch (Exception e){
            RRExceptionHandler.logError("预约单信息展示优化异常", yuyueInfoRes, e, SpringUtil.getBean(SmsService.class)::sendOaMsgTo9JiMan);
        }
    }

    @Override
    public List<LockWxpjBo> getLockWxpj(Integer yuyueId, List<Integer> ppids) {
        if (yuyueId == null || yuyueId == 0 || CollectionUtils.isEmpty(ppids)) {
            return null;
        }
        return baseMapper.getLockWxpj(yuyueId, ppids);
    }

    @Override
    public List<ShouhouYuyueInfoRes.RepairClassifyInfo> getRepairTypes() {
//        DomainEnum domainEnum = EnumUtil.getEnumByCode(DomainEnum.class, Namespaces.get());
        String host = Optional.ofNullable(sysConfigService.getValueByCodeAndXtenant(SysConfigConstant.WEB_URL, Integer.valueOf(String.valueOf(Namespaces.get()))))
                .filter(StrUtil::isNotBlank).orElseGet(()->sysConfigService.getValueByCode(SysConfigConstant.WEB_URL));
        String url = String.format(UrlConstants.REPAIR_TYPE_URL, host, LocalDateTime.now());
        String jsonStr = HttpClientUtil.get(url, new HashMap<>());
        if (StringUtils.isEmpty(jsonStr)) {
            return null;
        }
        R<List<ShouhouYuyueInfoRes.RepairClassifyInfo>> repairTypeRet = JSON.parseObject(jsonStr, R.class);
        if (ResultCode.SUCCESS == repairTypeRet.getCode()) {
            return repairTypeRet.getData();
        }
        return null;
    }

    @Override
    public R<WxServiceInfoRes> getServiceInfo(String imei, Boolean isSaveImeiSearchLog) {
        ServiceRecordService serviceRecordService = SpringUtil.getBean(ServiceRecordService.class);
        ServiceInfoVO serviceInfo = serviceRecordService.getRecord(imei, null, isSaveImeiSearchLog);
        WxServiceInfoRes wxServiceInfoRes = new WxServiceInfoRes(serviceInfo);
        //客户维修记录
        Page<WxRecordBo> page = new Page<>(1, NumberConstant.FIFTY);
        page.setDesc("h.id");
        List<WxRecordBo> wxRecordBoList = ObjectUtil.defaultIfNull(serviceInfo.getUserId(),0) >0 ?
                shouhouService.getWxRecord(page, serviceInfo.getUserId()).getRecords() : Collections.emptyList();
        wxServiceInfoRes.setWxTotal(page.getTotal());
        //获取硬件历史记录
        List<HardwareHistoryRecordBo> historyRecordBos = shouhouService.getHardwareHistoryRecords(imei);
        wxServiceInfoRes.setImeiSearchLogList(Convert.toList(ImeiSearchLogBo.class,serviceInfo.getImeilist()));
        wxServiceInfoRes.setWxRecordBoList(wxRecordBoList);
        wxServiceInfoRes.setHardwareHistoryRecords(historyRecordBos);
        return R.success("获取成功", wxServiceInfoRes);
    }

    //@Override
    public R<WxServiceInfoRes> getServiceInfoOld(String imei, Boolean isSaveImeiSearchLog) {
        OaUserBO oaUserBO = currentRequestComponent.getCurrentStaffId();
        if (oaUserBO == null) {
            return R.error("请登陆");
        }
        if (isSaveImeiSearchLog) {
            imeisearchlogsService.saveImeiSearchLogs(imei, oaUserBO.getAreaId(), oaUserBO.getUserName());
        }
        try {
            String json = HttpClientUtil.get(sysConfigService.getServiceInfoUrl(oaUserBO.getXTenant(), imei));
            if (StringUtils.isNotEmpty(json)) {
                BaoxiuAndBuyBo baoxiuAndBuyBo = JSON.parseObject(json, BaoxiuAndBuyBo.class);
                if (baoxiuAndBuyBo == null) {
                    return R.success("无保修状态", null);
                }
                WxServiceInfoRes wxServiceInfoRes = null;
                Integer userId = 0;
                if (baoxiuAndBuyBo.getIshuishou() == 1) {
                    HuishouInfoBo huishouInfo = baoxiuAndBuyBo.getHuishouinfo().get(baoxiuAndBuyBo.getHuishouinfo().size() - 1);
                    wxServiceInfoRes = new WxServiceInfoRes(huishouInfo);
                    userId = huishouInfo.getUserid();
                } else {
                    wxServiceInfoRes = new WxServiceInfoRes(baoxiuAndBuyBo);
                    userId = baoxiuAndBuyBo.getUserid();
                }
                //串号查询日志
                List<ImeiSearchLogBo> imeiSearchLogList =
                        imeisearchlogsService.getImeiSearchLogsByImei(imei);
                //客户维修记录
                Page<WxRecordBo> page = new Page<>(1, NumberConstant.ONE_THOUSAND);
                page.setDesc("h.id");
                List<WxRecordBo> wxRecordBoList = ObjectUtil.defaultIfNull(userId,0) >0 ?
                        shouhouService.getWxRecord(page, userId).getRecords() : Collections.emptyList();
                wxServiceInfoRes.setWxTotal(page.getTotal());
                //获取硬件历史记录
                List<HardwareHistoryRecordBo> historyRecordBos =
                        shouhouService.getHardwareHistoryRecords(imei);
                wxServiceInfoRes.setImeiSearchLogList(imeiSearchLogList);
                wxServiceInfoRes.setWxRecordBoList(wxRecordBoList);
                wxServiceInfoRes.setHardwareHistoryRecords(historyRecordBos);
                return R.success("获取成功", wxServiceInfoRes);
            }
        } catch (Exception e) {
            log.error("获取保修信息失败");
        }
        return R.error("获取失败");
    }

    @Override
    public List<UserSimpleInfoRes> getYuyueConfirmUsers(Integer stype, Integer yyid) {
        OaUserBO oaUserBO = currentRequestComponent.getCurrentStaffId();
        ShouhouYuyue shouhouYuyue = baseMapper.getShouhouYuyueById(yyid);

        List<UserSimpleInfoRes> resultUsers = null;
        if (shouhouYuyue == null) {
            return null;
        }
//        if (oaUserBO.getXTenant() > 1000){
//            return this.getYuyueConfirmUsersSaas(shouhouYuyue.getAreaid());
//        }
        Integer areaId = shouhouYuyue.getAreaid();
        if (oaUserBO.getXTenant() >= 1000) {
            //模式输出获取当前门店上班的人员信息
            List<UserSimpleInfoRes> ch999UserInfoList = baseMapper.getWorkingCh999UserInfoByAreaId(areaId);
            if (CollectionUtils.isNotEmpty(ch999UserInfoList)) {
                return ch999UserInfoList;
            }
        }
        //预约到店：推荐预约单所在区域的角色为手机技术员或者维修工程师并且在上班的人员
        if (YuYueSTypeEnum.YYDD.getCode().equals(stype)) {
            R<List<Ch999UserVo>> userRet = userInfoClient.findBasicUserByArea1IdAndContainsRole(areaId,
                    Arrays.asList(9, 180));
            if (ResultCode.SUCCESS == userRet.getCode() && userRet.getData() != null) {
                resultUsers = getWorkingUsers(userRet.getData().parallelStream().map(Ch999UserVo::getCh999Id).collect(Collectors.toList()));
//                resultUsers = buildUserSimpleInfoList(userRet.getData());
            }
        }
        //上门取件
        if (YuYueSTypeEnum.SMQJ.getCode().equals(stype)) {
            //根据电子围栏配置物流人员获取工号姓
            R<AreaInfo> areaInfoR = areaInfoClient.getAreaInfoById(areaId);
            List<Integer> ch999Ids = null;
            if (ResultCode.SUCCESS == areaInfoR.getCode() && areaInfoR.getData() != null) {
                String courier = areaInfoR.getData().getCourier();
                if (StringUtils.isNotEmpty(courier)) {
                    String courierArr[] = courier.split(",");
                    ch999Ids = Arrays.stream(courierArr).map(e -> Integer.parseInt(e)).collect(Collectors.toList());
                }
            } else {
                //电子围栏未配置，直接推送给OA微信员工推送绑定关系的固定员工
                ch999Ids = getWxSmsReceiverUserIds(SmsReceiverClassfyEnum.SMQJFZR.getCode());
            }
            resultUsers = getWorkingUsers(ch999Ids);
        }
        //上门快修
        if (YuYueSTypeEnum.SMKX.getCode().equals(stype)) {
            R<List<Ch999UserVo>> userRet = userInfoClient.findBasicUserByArea1IdAndContainsRole(areaId,
                    Arrays.asList(583));
            if (ResultCode.SUCCESS == userRet.getCode() && CollectionUtils.isNotEmpty(userRet.getData())) {
                resultUsers = getWorkingUsers(userRet.getData().parallelStream().map(Ch999UserVo::getCh999Id).collect(Collectors.toList()));
                if (CollUtil.isEmpty(resultUsers)) {
                    List<Integer> ch999Ids = getWxSmsReceiverUserIds(SmsReceiverClassfyEnum.SMKX.getCode());
                    resultUsers = getWorkingUsers(ch999Ids);
                }
            } else {
                List<Integer> ch999Ids = getWxSmsReceiverUserIds(SmsReceiverClassfyEnum.SMKX.getCode());
                resultUsers = getWorkingUsers(ch999Ids);
            }
        }
        //上门安装
        if (YuYueSTypeEnum.SMAZ.getCode().equals(stype)) {
            R<List<Ch999UserVo>> userRet = userInfoClient.findBasicUserByArea1IdAndContainsRole(areaId,
                    Arrays.asList(584));
            if (ResultCode.SUCCESS == userRet.getCode() && userRet.getData() != null) {
                resultUsers = buildUserSimpleInfoList(userRet.getData());
            }
        }
        return resultUsers;
    }

    public List<UserSimpleInfoRes> getYuyueConfirmUsersSaas(Integer areaId) {
        List<UserSimpleInfoRes> resultUsers = null;
        List<Integer> userIds = baseMapper.getCh999UserIdByAreaId(areaId);
        resultUsers = getWorkingUsers(userIds);
        return resultUsers;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<Boolean> yuyueConfirm(Integer yyid, Integer smUserId) {
        OaUserBO oaUserBO = currentRequestComponent.getCurrentStaffId();
        ShouhouYuyue shouhouYuyue = baseMapper.getShouhouYuyueById(yyid);
        if (shouhouYuyue == null) {
            return R.error("预约单不存在或已被删除");
        }
        if (!YuyueStatusEnum.WQR.getCode().equals(shouhouYuyue.getStats())) {
            return R.error("当前状态不允许操作");
        }
        //邮件送修，确认必须发发送收货地址短信
//        if (YuYueSTypeEnum.YJSX.getCode().equals(shouhouYuyue.getStype())) {
//            Boolean checkFlag = shouhouSendaddressService.checkHasShouhouSendaddress(yyid,
//                    ShouhouSendAddressKindEnum.YUYUE.getCode());
//            if (!checkFlag) {
//                return R.error("邮寄送修，请先发送邮寄地址！");
//            }
//        }
        shouhouYuyue.setStats(2);
        shouhouYuyue.setCheckUser(oaUserBO.getUserName());
        shouhouYuyue.setFchecktime(LocalDateTime.now());
        int num = baseMapper.updateById(shouhouYuyue);
        if (num < 1) {
            throw new CustomizeException("客服确认售后预约保存失败");
        }
        //人员验证
        if (smUserId == null || smUserId == 0) {
            throw new CustomizeException("工作人员不存在，请核对！" + 86);
        }
        R<Ch999UserVo> userRet = userInfoClient.getCh999UserByUserId(smUserId);
        if (ResultCode.SUCCESS != userRet.getCode() || userRet.getData() == null) {
            throw new CustomizeException("工作人员不存在，请核对！");
        }
        Ch999UserVo ch999UserVo = userRet.getData();
        boolean isYwConfirm = Objects.equals(SpringContextUtil.getRequest()
                .map(req -> req.getAttribute(RequestAttrKeys.SHOUHOU_YUYUE_CONFIRM_SOURCE)).orElse(null), ShouhouYuyueService.CONFIRM_BY_YW);
        if(isYwConfirm){
            //请先操作“发送邮寄地址”将邮寄地址发给给客户再进行“业务确认操作”
            if(Objects.equals(shouhouYuyue.getStype(),YuYueSTypeEnum.YJSX.getCode()) && shouhouSendaddressService
                    .lambdaQuery().eq(ShouhouSendaddress::getLinkid,yyid).eq(ShouhouSendaddress::getKind,DealWayEnum.WX.getCode()).count() <= 0){
                throw new CustomizeException("请先操作“发送邮寄地址”将邮寄地址发给给客户再进行“业务确认操作”");
            }
        }else{
            yuyueLogsService.yuyueLogsAdd(yyid, "由【未确认】转【客服确认】", oaUserBO.getUserName(), 0);
        }
        R<AreaInfo> areaInfoR = null;
        if (shouhouYuyue.getAreaid() != null && shouhouYuyue.getAreaid() != 0) {
            areaInfoR = areaInfoClient.getAreaInfoById(shouhouYuyue.getAreaid());
        }
        AreaInfo areaInfo = null;
        if (areaInfoR != null && ResultCode.SUCCESS == areaInfoR.getCode() && areaInfoR.getData() != null) {
            areaInfo = areaInfoR.getData();
        }
        //获取收货信息
        AddinfopsBo addinfops = addinfopsService.getAddinfops(yyid, shouhouYuyue.getShouhouId());
        String host = sysConfigService.getValueByCode(SysConfigConstant.MOA_URL);
        //预约到店
        if (YuYueSTypeEnum.YYDD.getCode().equals(shouhouYuyue.getStype())) {
            //修改业务人员和客服确认时间
            if(shouhouYuyue.getStime() == null || shouhouYuyue.getEtime() == null){
                throw new CustomizeException("预约到店的时间不允许为空");
            }
            shouhouYuyue.setEnterTime(LocalDateTime.now());
            shouhouYuyue.setEnterUser(ch999UserVo.getCh999Name());
            baseMapper.updateById(shouhouYuyue);
            yuyueConfirmSendMsg(oaUserBO, shouhouYuyue, ch999UserVo, areaInfo);
            //到店时间开始的前1个小时 发送延迟队列
            if(!isUseNewYuYue()){
                yuYueConfirmPushMsg(shouhouYuyue);
            }
        } else if (YuYueSTypeEnum.SMQJ.getCode().equals(shouhouYuyue.getStype())) {
            //上门取件
            //加入派送单
            wuliuCloud.addShouhouWuliu(buildWuliu(shouhouYuyue, ch999UserVo, addinfops));
            //写日志
            yuyueLogsService.yuyueLogsAdd(yyid, "客户确认修信息", ch999UserVo.getCh999Name(), 0);
            //修改业务人员和客服确认时间
            shouhouYuyue.setEnterTime(LocalDateTime.now());
            shouhouYuyue.setEnterUser(ch999UserVo.getCh999Name());
            baseMapper.updateById(shouhouYuyue);
            //同步取件员工
            addinfopsService.updateConsignee(ch999UserVo.getCh999Name(), yyid);
            wuliuCloud.updateShoujianren(ch999UserVo.getCh999Name(), yyid, 8);
            yuyueConfirmSendMsg(oaUserBO, shouhouYuyue, ch999UserVo, areaInfo);
        } else if (YuYueSTypeEnum.SMKX.getCode().equals(shouhouYuyue.getStype())) {
            //上门快修
            shouhouYuyue.setEnterTime(LocalDateTime.now());
            shouhouYuyue.setEnterUser(ch999UserVo.getCh999Name());
            baseMapper.updateById(shouhouYuyue);
            yuyueConfirmSendMsg(oaUserBO, shouhouYuyue, ch999UserVo, areaInfo);
            List<Integer> ch999Ids = null;
            String areaIds = "1,2,3,15,27,35,37,55,67,81,103";
            //售后综合组管理，售后综合维修专员
            if (AreaEnum.AREA_DC.getCode().equals(shouhouYuyue.getAreaid())) {
                ch999Ids = shouhouOtherMapper.getZongHeZuDirector();
            } else if (shouhouYuyue.getAreaid() != null && areaIds.contains(String.valueOf(shouhouYuyue.getAreaid()))) {
                ch999Ids = shouhouOtherMapper.getShouHouEngineer(shouhouYuyue.getAreaid());
            } else if (shouhouYuyue.getAreaid() != null) {
                Integer parentId = shouhouOtherMapper.getParentAreaId(shouhouYuyue.getAreaid());
                if (parentId != null && parentId > 0) {
                    ch999Ids = shouhouOtherMapper.getSubCompanyLeader(parentId);
                }
            }

            if (CollectionUtils.isNotEmpty(ch999Ids)) {

                String ch999IdsStr = Joiner.on(",").join(ch999Ids);
                String msg =
                        "您负责的地区有一个上门快修预约单需要处理，请跟进，单号：<a href='" + host + "/Mshouhouyuyue/yuyueadd/" + yyid + "'>" + yyid + "</a>";
                String link = host + "/Mshouhouyuyue/yuyueadd/" + yyid;
                smsService.sendOaMsg(msg, link, ch999IdsStr, OaMesTypeEnum.SHTZ.getCode().toString());
            }

        } else if (shouhouYuyue.getKdtype() != null && shouhouYuyue.getKdtype() == 1 && addinfops.getQCityId() != null && addinfops.getQCityId() > 0 && YuYueSTypeEnum.YJSX.getCode().equals(shouhouYuyue.getStype())) {
            //邮寄送修, 生成顺丰单
            R<String> b = createSfOrder(shouhouYuyue, addinfops.getQCityId(), addinfops.getQAddress());
            if (ResultCode.SUCCESS == b.getCode() && StringUtils.isNotEmpty(b.getData())) {
                shouhouYuyue.setKuaidigongsi("shunfeng");
                shouhouYuyue.setKuaididan(b.getData());
                //创建成功结果记录日志
                yuyueLogsService.yuyueLogsAdd(shouhouYuyue.getId(), StrUtil.format("生成顺丰快递单: [{}]", shouhouYuyue.getKuaididan()), oaUserBO.getUserName(),
                        YuyueLogViewTypeEnum.WZ_SHOUW.getCode());
                baseMapper.updateById(shouhouYuyue);
            }else{
                //创建失败记录日志
                yuyueLogsService.yuyueLogsAdd(shouhouYuyue.getId(), b.getUserMsg(), oaUserBO.getUserName(),
                        YuyueLogViewTypeEnum.WZ_NO_SHOUW.getCode());
            }

        } else{
            shouhouYuyue.setEnterTime(LocalDateTime.now());
            shouhouYuyue.setEnterUser(ch999UserVo.getCh999Name());
            baseMapper.updateById(shouhouYuyue);
            if (!Objects.equals(oaUserBO.getUserId(),smUserId)){
                String msg = "您有一个" + EnumUtil.getMessageByCode(YuYueSTypeEnum.class, shouhouYuyue.getStype()) + "预约单需要处理，请及时处理，单号：<a href='" + host + "/Mshouhouyuyue/yuyueadd/" + shouhouYuyue.getId() + "'>" + shouhouYuyue.getId() + "</a>";
                String link = host + "/Mshouhouyuyue/yuyueadd/" + shouhouYuyue.getId();
                String wxMsg = "您有一个" + EnumUtil.getMessageByCode(YuYueSTypeEnum.class, shouhouYuyue.getStype()) + "预约单需要处理，请及时处理，单号：" + shouhouYuyue.getId();
                weixinUserService.senWeixinAndOaMsg(wxMsg, msg, link, ch999UserVo.getCh999Id().toString(), OaMesTypeEnum.SHTZ.getCode().toString());
            }
        }
        //三方订单状态同步
        thirdPlatStatusSyncService.thirdShouhouStatusSync(ShouhouOrderTypeEnum.YUYUE.getCode(), yyid);
        return R.success(true);
    }

    /**
     * 延迟队列消息推送
     */
    private void yuYueConfirmPushMsg(ShouhouYuyue shouhouYuyue){
        //获取开始时间前一个小时
        LocalDateTime stime = shouhouYuyue.getStime();
        LocalDateTime eTime = shouhouYuyue.getEtime();
        LocalDateTime pusMsgTime = stime.minusHours(1L);
        LocalDateTime now = LocalDateTime.now();
        YuYueConfirmPushMsg yuYueConfirmPushMsg = new YuYueConfirmPushMsg();
        yuYueConfirmPushMsg.setYuYueId(shouhouYuyue.getId());
        //判断推送时间是否再当前时间之后
        try {
            String publish = "";
            if(eTime.isAfter(now)){
                //计算当前时间和开始时间差
                int timeDifference = getTimeSecond(eTime) - getTimeSecond(now);
                //如果timeDifference大于3600秒，
                int diffTime = ONE_HOUR_SECOND + (getTimeSecond(eTime) - getTimeSecond(stime));
                Integer pushTime = NumberConstant.ONE;
                if(timeDifference>diffTime){
                    pushTime = getTimeSecond(pusMsgTime)-getTimeSecond(now);
                    publish = firstLmstfyClient.publish(yuyueConfirmPushMsgQueue, JSONUtil.toJsonStr(yuYueConfirmPushMsg).getBytes(), 0, (short) 1,pushTime);
                } else {
                    publish = firstLmstfyClient.publish(yuyueConfirmPushMsgQueue, JSONUtil.toJsonStr(yuYueConfirmPushMsg).getBytes(), 0, (short) 1, pushTime);
                }
                log.warn("预约单确认之后消息延迟队列推送，推送参数{}，返回结果{}，推送时间{}",JSONUtil.toJsonStr(yuYueConfirmPushMsg),publish,pushTime);
            }

        } catch (LmstfyException e){
            RRExceptionHandler.logError("预约单确认之后消息延迟队列推送异常", yuYueConfirmPushMsg, e, SpringUtil.getBean(SmsService.class)::sendOaMsgTo9JiMan);
        }
    }

    /**
     * localdatetime转化成为秒
     * @param time
     * @return
     */
    private Integer getTimeSecond(LocalDateTime time){
        // 将LocalDateTime转换为Instant，需要通过时区转换
        ZonedDateTime zonedDateTime = time.atZone(ZoneId.systemDefault()); // 使用系统默认时区
        Instant instant = zonedDateTime.toInstant();
        return Convert.toInt( instant.getEpochSecond());
    }
    /**
     * 延迟队列监听消费
     * 到店时间开始的前1个小时 发送延迟队列
     * @param job
     */
    @LmstfyConsume(queues = YUYUE_CONFIRM_PUSH_MSG_QUEUE, clientBeanName = "firstLmstfyClient")
    public void sendMsoftNoticeQueue(Job job) {
        log.warn("YUYUE_CONFIRM_PUSH_MSG_QUEUE消费到数据：{}，业务数据：{}", JSONUtil.toJsonStr(job),JSONUtil.toJsonStr(job.getData()));
        String data = Optional.ofNullable(job).orElse(new Job()).getData();
        try {
            YuYueConfirmPushMsg yuYue = JSONUtil.toBean(data, YuYueConfirmPushMsg.class);
            Integer yueId = yuYue.getYuYueId();
            //预约单查询
            ShouhouYuyue shouhouYuyue = Optional.ofNullable(this.lambdaQuery().eq(ShouhouYuyue::getId, yueId)
                    .in(ShouhouYuyue::getStats, Arrays.asList(YuyueStatusEnum.KFQR.getCode(), YuyueStatusEnum.YWQR.getCode()))
                    .one()).orElse(null);
            if(ObjectUtil.isNull(shouhouYuyue)){
                log.warn("预约单当前状态不是客服确认获取已确认 不用进行消息通知，预约单id：{}", yueId);
                return;
            }
            //用户侧推送
            Integer ppriceid = shouhouYuyue.getPpriceid();
            Productinfo productinfo = Optional.ofNullable(productinfoService.getProductinfoByPpid(ppriceid)).orElse(new Productinfo());
            String name = Optional.ofNullable(productinfo.getProductName()).orElse("")+productinfo.getProductColor();
            String mHost = Optional.ofNullable(sysConfigClient.getValueByCode(SysConfigConstant.M_URL)).map(R::getData).filter(StringUtils::isNotEmpty).orElseThrow(() -> new CustomizeException("获取域名出错"));
            Integer userid = Optional.ofNullable(shouhouYuyue.getUserid()).orElse(NumberConstant.ZERO);
            String url = String.format("%s/after-service/reserve-detail/%s?uId=%s",mHost,yueId,Base64.encode(userid.toString()));
            ShortUrlParam shortUrlParam = LambdaBuild.create(new ShortUrlParam())
                    .set(ShortUrlParam::setUrl, url).set(ShortUrlParam::setDescription, "预约单详情短链").build();
            Result<String> urlR = SpringUtil.getBean(RetryService.class).retryByFeignRetryableException(()->
                    SpringUtil.getBean(WebCloud.class).generateShortUrl(shortUrlParam, XtenantEnum.getXtenant()));
            String shortUrl = urlR.getData();
            log.warn("预约售后服务将于1个小时后开始单生成短链 传入参数：{}，返回结果：{}",url,shortUrl);
            String msgMember = String.format("尊敬的九机会员，您好，您预约的%s 售后服务将于1个小时后开始，请您在预约时间前到店，我们期待为您服务，如有任何变动或疑问，请随时与我们联系，祝您生活愉快。详情请点击：%s", name, shortUrl);
            //短信推送
            Optional.ofNullable(shouhouYuyue.getMobile()).ifPresent(mobile -> {
                smsService.sendSms(mobile, msgMember, DateUtil.getDateTimeAsString(LocalDateTime.now()), "系统", smsService.getSmsChannelByTenant(shouhouYuyue.getAreaid(), ESmsChannelTypeEnum.YZMTD));
            });
            //员工侧推送
            String inuser = shouhouYuyue.getEnterUser();
            //根据inuser获取到ch999id
            String ch999Id = receivePersonConfigMapper.getCh999IdByName(inuser);
            String host = Optional.ofNullable(sysConfigClient.getValueByCode(SysConfigConstant.MOA_URL)).map(R::getData).filter(StringUtils::isNotEmpty).orElseThrow(() -> new CustomizeException("获取域名出错"));
            String link = String.format("%s/new/#/afterService/detail/%s",host,yueId);
            String msg = String.format("您负责的售后预约单%s，即将到约定时间，请提前做好准备，确保能准时为客户提供优质的售后服务，如无法及时接待客户请做好交接及报备",yueId);
            //OA推送
            smsService.sendOaMsg(msg,link,ch999Id, OaMesTypeEnum.DDTZ);
        } catch (Exception e) {
            RRExceptionHandler.logError("消费预约单确认之后异常", data, e, smsService::sendOaMsgTo9JiMan);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public R<Boolean> delYuyue(Integer yyid, String cancleKind, String remark, String operator, Integer userId) {
        ShouhouYuyue shouhouYuyue = baseMapper.selectById(yyid);
        if (shouhouYuyue == null || shouhouYuyue.getIsdel()) {
            return R.error("该预约单不存在");
        }
        if(ObjectUtil.defaultIfNull(userId, 0) >0 && ObjectUtil.notEqual(shouhouYuyue.getUserid(), userId)){
            return R.error("只能删除自己的预约单");
        }
        OaUserBO oaUser = SpringUtil.getBean(AbstractCurrentRequestComponent.class).getCurrentStaffId();
        if(oaUser == null){
            //用戶取消预约单,模拟构建oauser信息
            oaUser = LambdaBuild.create(new OaUserBO()).set(OaUserBO::setUserName, ObjectUtil.defaultIfBlank(operator, "用户"))
                    .set(OaUserBO::setAreaId, shouhouYuyue.getAreaid()).build();
            OaUserBO finalOaUser = oaUser;
            SpringContextUtil.getRequest().ifPresent(req -> req.setAttribute(RequestAttrKeys.REQUEST_ATTR_OA_USER, finalOaUser));
        }

        shouhouYuyue.setCancelRemark(remark);
        shouhouYuyue.setIsdel(true);
        shouhouYuyue.setCancelKind(cancleKind);
        List<Runnable> addLogs = new LinkedList<>();
        if (CommonUtils.isNumber(cancleKind)) {
            YuyueCancelTypeEnum enumByCode = EnumUtil.getEnumByCode(YuyueCancelTypeEnum.class, Integer.valueOf(cancleKind));
            if (enumByCode != null) {
                shouhouYuyue.setCancelKind(enumByCode.getMessage());
            }
        }
        try {
            if(isUseNewYuYue()){
                shouhouYuyue.setStats(YuyueStatusEnum.YSC.getCode());
            }
            Integer num = baseMapper.update(shouhouYuyue,new LambdaQueryWrapper<ShouhouYuyue>()
                    .eq(ShouhouYuyue::getId,shouhouYuyue.getId()).in(ShouhouYuyue::getStats,1,2,4,6,7));
            if (num < 1) {
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return R.error("删除预约单失败");
            }
            //解除配件锁定
            R<Boolean> unlockWxPeijianR = shouhouService.lockWxPeijian(yyid, null, shouhouYuyue.getAreaid(),
                    LockWxPeijianTypeEnum.NO_LOCK.getCode());
            if(!unlockWxPeijianR.isSuccess()){
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return R.error(StrUtil.format("取消预约单解锁配件发生{}异常",unlockWxPeijianR.getUserMsg()));
            }
            //取消优惠码
            cardLogsService.cancelCoupon(yyid);
            String comment = "取消预约单";
            if (StringUtils.isNotEmpty(cancleKind)) {
                if (CommenUtil.isNumer(cancleKind)) {
                    cancleKind = EnumUtil.getMessageByCode(YuyueCancelTypeEnum.class, Integer.valueOf(cancleKind));
                }
                if (Objects.equals(cancleKind, remark)) {
                    comment += String.format(",原因:%s", cancleKind);
                } else {
                    comment += String.format(",原因:%s", cancleKind + ";" + remark);
                }
            }
            yuyueLogsService.yuyueLogsAdd(shouhouYuyue.getId(), comment, Optional.ofNullable(operator).filter(StringUtils::isNoneBlank).orElse("用户"), NumberConstant.ZERO);
//            Optional<Integer> wxIdOpt = shouhouService.lambdaQuery().select(Shouhou::getId).eq(Shouhou::getYuyueid, yyid).orderByDesc(Shouhou::getId)
//                    .list().stream().map(Shouhou::getId).findFirst();
            Optional<Integer> wxIdOpt = CommenUtil.autoQueryHist(()-> shouhouService.lambdaQuery().select(Shouhou::getId).eq(Shouhou::getYuyueid, yyid).orderByDesc(Shouhou::getId)
                    .list().stream().map(Shouhou::getId).findFirst());
            if(wxIdOpt.isPresent()){
                Integer shouhouId = wxIdOpt.get();
                Integer wxkcCount = SpringUtil.getBean(WxkcoutputService.class).lambdaQuery().eq(Wxkcoutput::getWxid, shouhouId)
                        .and(cnd -> cnd.isNull(Wxkcoutput::getStats).or().ne(Wxkcoutput::getStats,3)).count();
                if(wxkcCount > 0){
                    TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                    return R.error("必须撤销售后单的所有配件,才能取消预约单");
                }
                //增加校验是否存在退换机记录
                ShouhouTuiHuanPo shouhouTuiHuanPo = SpringUtil.getBean(RefundMachineService.class).getShouhouTuihuanByShouhouId(shouhouId);
                if(shouhouTuiHuanPo != null){
                    TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                    return R.error("必须撤销售后单的退换机记录,才能取消预约单");
                }
                shouhouService.delYuyueUpdateShouhou(yyid);
            }

            WeixinUser weixinUser = weixinUserService.getWxxinUserByUserId(shouhouYuyue.getUserid());
            if (weixinUser != null && StringUtils.isNotEmpty(weixinUser.getOpenid())) {
                sendMessageRecordService.saveSendMesaageRecord(SengMsgRecordType.SH.getCode(), weixinUser.getOpenid(),
                        "预约维修", LocalDateTime.now(), "",jiujiSystemProperties.getOfficeName());
            }
            if (StringUtils.isNotEmpty(shouhouYuyue.getFuwuma())) {
                installServicesRecordService.updateInstallServicesRecord(shouhouYuyue.getFuwuma());
            }
            OaUserBO finalOaUser = oaUser;
            //删除物流单(派送单也跟着消失)
            Optional.ofNullable(shouhouYuyue)
                    .flatMap(syy -> SpringUtil.getBean(WuliuService.class).lambdaQuery().eq(Wuliu::getDanhaobind, syy.getId())
                            .eq(Wuliu::getWutype, WuLiuType.PICK_UP.getCode()).ge(Wuliu::getDtime, syy.getDtime().truncatedTo(ChronoUnit.MINUTES))
                            .orderByDesc(Wuliu::getId).list().stream().findFirst())
                    .ifPresent(wuliu -> {
                        //推送作废通知
                        RabbitTemplate oaAsyncRabbitTemplate = SpringUtil.getBean("oaAsyncRabbitTempe",RabbitTemplate.class);
                        oaAsyncRabbitTemplate.convertAndSend(RabbitMqConfig.WULIU_INVALID, JSON.toJSONString(Dict.create()
                                .set("wuliuId",wuliu.getId()).set("userName",finalOaUser.getUserName())));
                    });
            //取消快递单
            String moaUrl = sysConfigService.getValueByCode(SysConfigConstant.MOA_URL);
            if(StrUtil.isNotBlank(moaUrl) && StrUtil.isNotBlank(shouhouYuyue.getKuaididan())){
                Optional.ofNullable(HttpUtil.createPost(StrUtil.format(UrlConstants.LOGISTICS_CENTER_CANCEL_ORDER, moaUrl))
                        .header(Header.AUTHORIZATION.getValue(), DigestUtil.md5Hex(LocalDateTime.now().format(DateTimeFormatter.ofPattern(TimeFormatConstant.YYYY_MM_DD))))
                        .body(JSON.toJSONString(Dict.create().set("waybillCode", shouhouYuyue.getKuaididan())))
                        .execute())
                        .ifPresent(cancelRes -> {
                            if(!cancelRes.isOk()){
                                addLogs.add(()->yuyueLogsService.yuyueLogsAdd(shouhouYuyue.getId(), StrUtil.format("关联取消快递单[{}],快递公司:{},结果:{}异常",
                                        shouhouYuyue.getKuaididan(),shouhouYuyue.getKuaidigongsi(),cancelRes.getStatus()),
                                        finalOaUser.getUserName(), YuyueLogViewTypeEnum.WZ_NO_SHOUW.getCode()));
                                return;
                            }
                            R<?> crr = JSON.parseObject(cancelRes.body(), new TypeReference<R<?>>() {});
                            if(crr.isSuccess()){
                                //取消结果记录日志
                                addLogs.add(()->yuyueLogsService.yuyueLogsAdd(shouhouYuyue.getId(), StrUtil.format("取消预约单,关联取消快递单[{}]",
                                        shouhouYuyue.getKuaididan(),shouhouYuyue.getKuaidigongsi()), finalOaUser.getUserName(),
                                        YuyueLogViewTypeEnum.WZ_SHOUW.getCode()));
                                return;
                            }
                            //取消结果记录日志
                            addLogs.add(()->yuyueLogsService.yuyueLogsAdd(shouhouYuyue.getId(), StrUtil.format("关联取消快递单[{}],快递公司:{},结果:{}",
                                    shouhouYuyue.getKuaididan(),shouhouYuyue.getKuaidigongsi(),crr.isSuccess() ? "取消成功" : crr.getUserMsg()),
                                    finalOaUser.getUserName(), YuyueLogViewTypeEnum.WZ_NO_SHOUW.getCode()));
                        });
            }
            //批量添加日志
            addLogs.forEach(Runnable::run);
            //三方订单状态同步
            thirdPlatStatusSyncService.thirdShouhouStatusSync(ShouhouOrderTypeEnum.YUYUE.getCode(), yyid);
            //广播预约单的删除消息
            SpringUtil.getBean("oaRabbitTemplate",RabbitTemplate.class).convertAndSend(RabbitMqConfig.DEL_AFTER_SHOUHOU_YU_YUE,"", Convert.toStr(yyid));
            return R.success("删除成功", true);
        } catch (Exception e) {
            RRExceptionHandler.logError("取消预约单",yyid,e,null);
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        }
        return R.error("删除失败");
    }

    @Override
    public R<Integer> saveOrUpdateYuyue(ShouhouYuyueReq param) {

        Integer yuyueId = null;
        R checkParaRes = checkShouhouYuyueReqPara(param);
        if (checkParaRes.getCode() != ResultCode.SUCCESS) {
            return checkParaRes;
        }
        OaUserBO oaUserBO = currentRequestComponent.getCurrentStaffId();

        //参数处理
        if (ServicesWayEnum.YJSX.getCode().equals(param.getServicesWay())) {
            param.setStartTime(null);
            param.setEndTime(null);
        }
        String host = sysConfigService.getValueByCode(SysConfigConstant.MOA_URL);
        String host1 = sysConfigService.getValueByCode(SysConfigConstant.M_URL);
        if (StringUtils.isNotEmpty(host1)) {
            host = host1;
        }
        String upLogs = "";
        String imeiLogContent = "";
        try {
            ShouhouYuyue shouhouYuyue = null;
            ShouhouYuyue orignYuyue = new ShouhouYuyue();
            if (param.getId() != null && param.getId() != 0) {
                shouhouYuyue = baseMapper.selectById(param.getId());
                if (shouhouYuyue.getStats() != null && shouhouYuyue.getStats().equals(YuyueStatusEnum.YQX.getCode())) {
                    return R.error("预约单已取消，不可操作");
                }
                if(ObjectUtil.notEqual(param.getAreaId(),shouhouYuyue.getAreaid()) && shouhouYuyuelockppidsService.lambdaQuery()
                        .eq(ShouhouYuyuelockppids::getYyid, shouhouYuyue.getId()).count() > 0){
                    return R.error("解除配件预留后才可以切换地区");
                }
                if (!param.getImei().equals(shouhouYuyue.getImei())) {
                    if (Objects.equals(param.getImeiFromtype(), 1)) {
                        imeiLogContent = "扫码获取串号，串号由【" + shouhouYuyue.getImei() + "】更改为【" + param.getImei() + "】";
                    } else if (Objects.equals(param.getImeiFromtype(), 2)) {
                        imeiLogContent = "无法获取串号，串号由【" + shouhouYuyue.getImei() + "】更改为【" + param.getImei() + "】";
                    } else {
                        imeiLogContent = "串号由【" + shouhouYuyue.getImei() + "】更改为【" + param.getImei() + "】";
                    }
                }
                BeanUtils.copyProperties(shouhouYuyue, orignYuyue);

                //对于已上门、已到店、已处理的预约单、不能再更改其状态
                List<Integer> statsList = Stream.of(YuyueStatusEnum.YSM.getCode(), YuyueStatusEnum.YDD.getCode(), YuyueStatusEnum.YWC.getCode()).collect(Collectors.toList());
                if (statsList.contains(shouhouYuyue.getStats()) && !shouhouYuyue.getStats().equals(param.getStatus())) {
                    return R.error("已上门、已到店、已处理的预约单，不能再更改其状态");
                }

            } else {
                //默认参数处理
                if (ServicesWayEnum.YYDD.getCode().equals(param.getServicesWay())) {
                    //到店自取
                    param.setDelivery(1);
                } else if (ServicesWayEnum.SMQJ.getCode().equals(param.getServicesWay())) {
                    //三九快递
                    param.setDelivery(2);
                } else if (ServicesWayEnum.YJSX.getCode().equals(param.getServicesWay())) {
                    //快递运输,邮寄
                    param.setDelivery(4);
                }
                if (param.getStatus() == null || param.getStatus() == 0) {
                    param.setStatus(1);
                }
                shouhouYuyue = new ShouhouYuyue();

            }
            LocalDateTime startTimeNew = param.getStartTime();
            LocalDateTime startTimeOld = shouhouYuyue.getStime();
            buildShouhouYuyue(param, shouhouYuyue);
            if ((null == shouhouYuyue.getUserid() || 0 == shouhouYuyue.getUserid()) && StringUtils.isNotEmpty(shouhouYuyue.getMobile())) {
                //如果手机号码不是会员就注册一个
                MemberReq memberReq = new MemberReq();
                memberReq.setMobile(shouhouYuyue.getMobile());
                memberReq.setUserName(shouhouYuyue.getMobile());
                memberReq.setRealName(shouhouYuyue.getUsername());
                memberReq.setInUser("系统");
                memberReq.setAreaId(shouhouYuyue.getAreaid());

                memberReq.setXtenant(oaUserBO.getXTenant());
                R<Integer> memberR = memberClient.checkAndRegisterUser(memberReq);
                if (ResultCode.SUCCESS == memberR.getCode() && memberR.getData() != null) {
                    shouhouYuyue.setUserid(memberR.getData());
                }
            }
            if (StringUtils.isEmpty(orignYuyue.getInuser())) {
                shouhouYuyue.setInuser(oaUserBO.getUserName());
            }
            shouhouYuyue.setImeiFromtype(param.getImeiFromtype());
            this.saveOrUpdate(shouhouYuyue);
            //预约单增加二维码加单标识 xxk
            ShouhouExtendService shouhouExtendService = SpringUtil.getBean(ShouhouExtendService.class);

            param.setScanCodeUserMobile(ObjectUtil.defaultIfBlank(param.getScanCodeUserMobile(),param.getMobile()));
            shouhouExtendService.asyncScanCodeAddShouhou(BeanUtil.copyProperties(param,ShouhouYuyueReq.class).setId(shouhouYuyue.getId())
                    ,oaUserBO,BusinessTypeEnum.APPOINTMENT_ORDER);
            yuyueId = shouhouYuyue.getId();
            if (startTimeNew != null && startTimeOld != null && Duration.between(startTimeOld, startTimeNew).toHours() > 8) {
                wuliuCloud.updateWuliuTime(DateUtil.localDateTimeToString(startTimeNew), 8, shouhouYuyue.getId());
            }
            //保存附件
            attachmentsService.saveAttachemnts(param.getAttachment(), shouhouYuyue.getId(), AttachmentsEnum.YUYUE.getCode(), oaUserBO.getUserId(),null);
            //如果是上门快修，并且填写了优惠码，自动使用优惠码
            if (shouhouYuyue.getStype() != null && (YuYueSTypeEnum.SMKX.getCode().equals(shouhouYuyue.getStype()) || YuYueSTypeEnum.SMAZ.getCode().equals(shouhouYuyue.getStype())) && StringUtils.isNotEmpty(param.getCoupon())) {
                numberCardService.useYuyueCoupon(param.getCoupon(), shouhouYuyue.getId(), shouhouYuyue.getInuser(),
                        shouhouYuyue.getStype(), shouhouYuyue.getAreaid());
            }

            shouhouYuyueTroubleService.updateShouhouYuyueTroubles(shouhouYuyue.getShouhouId(), param.getTroubleIds());
            //原寄送地址
            AddinfopsBo originAddinfops = addinfopsService.getAddinfops(shouhouYuyue.getId(), shouhouYuyue.getShouhouId());
            //新的寄送地址
            AddinfopsBo newAddinfops = param.getAddressInfo();
            List<String> addinfopsLogs = ReflectUtil.getFieldModifiedLog(AddinfopsBo.class, originAddinfops == null ? new AddinfopsBo() : originAddinfops, newAddinfops == null ? new AddinfopsBo() : newAddinfops);
            if (CollectionUtils.isNotEmpty(addinfopsLogs)) {
                upLogs = addinfopsLogs.stream().collect(Collectors.joining(StringPool.COMMA));
            }
            addinfopsService.saveAddinfops(shouhouYuyue.getId(), shouhouYuyue.getShouhouId(), shouhouYuyue.getStype()
                    , shouhouYuyue.getMobile(), param.getAddressInfo());
            //提交为确认状态
            Boolean flag =
                    (param.getId() == null || param.getId() == 0) && YuyueStatusEnum.KFQR.getCode().equals(shouhouYuyue.getStats()) && shouhouYuyue.getIsmobile() && StringUtils.isNotEmpty(shouhouYuyue.getImei());
            if (flag) {
                R<Ch999UserVo> ch999UserVoR = userInfoClient.getCh999UserByUserName(shouhouYuyue.getInuser());
                if (ResultCode.SUCCESS == ch999UserVoR.getCode() && ch999UserVoR.getData() != null) {
                    this.yuyueConfirm(shouhouYuyue.getId(), ch999UserVoR.getData().getCh999Id());
                }
                //快修，并且是手机，增加处理进程
                if (DealWayEnum.KX.getCode().equals(shouhouYuyue.getKind()) && shouhouYuyue.getIsmobile() && StringUtils.isNotEmpty(shouhouYuyue.getComment())) {
                    yuyueLogsService.yuyueLogsAdd(shouhouYuyue.getId(), shouhouYuyue.getComment(),
                            shouhouYuyue.getInuser(), 0);
                }
            }
            if(ObjectUtil.defaultIfNull(param.getId(), 0)>0 && ObjectUtil.defaultIfNull(param.getUserId(), 0)>0){
                ShouhouYuyueReqVo yuyue = new ShouhouYuyueReqVo();
                BeanUtils.copyProperties(shouhouYuyue, yuyue);
                oaApiService.sendWechatAndSmsNoticeToCustomer(yuyue, shouhouYuyue.getAreaid(), false);
            }

            /*if (param.getId() != null && param.getId() != 0 && param.getUserId() != null && param.getUserId() != 0) {
                WeixinUser weixinUser = weixinUserService.getWxxinUserByUserId(shouhouYuyue.getUserid());
                Integer serviceWay = shouhouYuyue.getStype();
                String yuyueTime = "";
                if (shouhouYuyue.getStime() != null && shouhouYuyue.getEtime() != null) {
                    yuyueTime = StrUtil.format("{}~{}点", DateUtil.format_m_d_h.format(shouhouYuyue.getStime()), shouhouYuyue.getEtime().getHour());
                }

                if (weixinUser != null && StringUtils.isNotEmpty(weixinUser.getOpenid())) {
                    ShouhouYuyueReqVo yuyue = new ShouhouYuyueReqVo();
                    BeanUtils.copyProperties(shouhouYuyue, yuyue);
                    // 产品和业务方对接之后暂时下掉此功能（杨继武）
                   // oaApiService.sendWechatAndSmsNoticeToCustomer(yuyue, shouhouYuyue.getAreaid());
                    saveYuyueSendMsg(shouhouYuyue.getId(), shouhouYuyue.getMobile(), serviceWay, yuyueTime,
                            shouhouYuyue.getAreaid(), param.getAddressInfo().getQAddress(), 1);
                } else if (shouhouYuyue.getIssend() != null && shouhouYuyue.getIssend() && StringUtils.isNotEmpty(weixinUser.getOpenid())) {
                    saveYuyueSendMsg(shouhouYuyue.getId(), shouhouYuyue.getMobile(), serviceWay, yuyueTime,
                            shouhouYuyue.getAreaid(), param.getAddressInfo().getQAddress(), 2);
                }
            }*/

            if (StringUtils.isNotEmpty(imeiLogContent)) {
                yuyueLogsService.yuyueLogsAdd(shouhouYuyue.getId(), imeiLogContent, oaUserBO.getUserName(), YuyueLogViewTypeEnum.WZ_NO_SHOUW.getCode());
            }

            //添加日志
            if (param.getId() == null || param.getId() == 0) {
                yuyueLogsService.yuyueLogsAdd(shouhouYuyue.getId(), "添加预约单", oaUserBO.getUserName(), YuyueLogViewTypeEnum.WZ_NO_SHOUW.getCode());
            } else {
                List<String> list = ReflectUtil.getFieldModifiedLog(ShouhouYuyue.class, orignYuyue, shouhouYuyue);
                List<String> speciaLogs = specialFildLog(orignYuyue, shouhouYuyue);

                if (CollectionUtils.isNotEmpty(speciaLogs)) {
                    list.addAll(speciaLogs);
                }
                //对于上门快修，需要记录地址信息变更情况
                if (shouhouYuyue.getStype().equals(YuYueSTypeEnum.SMKX.getCode()) && param.getAddressInfo() != null && originAddinfops != null) {
                    speciaLogs = specialFildLog(originAddinfops, newAddinfops, param);
                    if (CollectionUtils.isNotEmpty(speciaLogs)) {
                        list.addAll(speciaLogs);
                    }
                }

                if(CollUtil.isNotEmpty(list)){
                    if(StrUtil.isNotEmpty(upLogs)){
                        upLogs = StrUtil.join(StringPool.COMMA,list.stream().collect(Collectors.joining(StringPool.COMMA)),upLogs);
                    } else {
                        upLogs = list.stream().collect(Collectors.joining(StringPool.COMMA));
                    }

                }
                if (StringUtils.isNotEmpty(upLogs) && !upLogs.equals("[]")) {
                    upLogs = "修改预约单信息：" + upLogs;
                    yuyueLogsService.yuyueLogsAdd(shouhouYuyue.getId(), upLogs, oaUserBO.getUserName(), YuyueLogViewTypeEnum.WZ_NO_SHOUW.getCode());
                }
                String speciaLogsShow = specialFildShowUserLog(orignYuyue, shouhouYuyue);
                if(StringUtils.isNotEmpty(speciaLogsShow)){
                    yuyueLogsService.yuyueLogsAdd(shouhouYuyue.getId(), speciaLogsShow, oaUserBO.getUserName(), YuyueLogViewTypeEnum.WZ_SHOUW.getCode());
                }
            }

            //如果选择了上门快修人，需要对维修人进行消息推送
            if (shouhouYuyue.getStype().equals(YuYueSTypeEnum.SMKX.getCode()) && StringUtils.isNotEmpty(param.getAddressInfo().getQConsignee())) {

                if (originAddinfops != null && (StringUtils.isEmpty(originAddinfops.getQConsignee()) || (StringUtils.isNotEmpty(originAddinfops.getQConsignee()) && !originAddinfops.getQConsignee().equals(param.getAddressInfo().getQConsignee())))) {
                    //售后消息推送

                    //维修人修改   售后消息推送
                    R<Ch999UserVo> ch999UserVoR = null;
                    if (param.getAddressInfo().getQConsignee().indexOf("-") > 0) {
                        ch999UserVoR = userInfoClient.getCh999UserByUserId(Integer.valueOf(param.getAddressInfo().getQConsignee().split("-")[0]));
                    } else {
                        ch999UserVoR = userInfoClient.getCh999UserByUserName(param.getAddressInfo().getQConsignee());
                    }
                    if (ch999UserVoR.getCode() != ResultCode.SUCCESS || ch999UserVoR.getData() == null) {
                        throw new RRException("上门维修人信息不存在");
                    }

                    //微信，OAAPP 通知员工
                    String link = host + "/Mshouhouyuyue/yuyueadd/" + shouhouYuyue.getId();
                    String msg =
                            "您有一个上门快修预约单需要处理，请及时处理，单号：<a href='" + host + "/Mshouhouyuyue/yuyueadd/" + shouhouYuyue.getId() + "'>" + shouhouYuyue.getId() + "</a>";
                    String wcfHost = "http://inwcf.ch999.cn";
                    String inwcfUrl = sysConfigService.getValueByCode(SysConfigConstant.IN_WCF_HOST);
                    if (StringUtils.isEmpty(inwcfUrl)) {
                        inwcfUrl = wcfHost;
                    }
                    String sendHanderUrl = String.format(UrlConstants.OA_MSG_GET_URL.replaceAll(wcfHost, inwcfUrl), msg, link, ch999UserVoR.getData().getCh999Id(),
                            OaMesTypeEnum.SHTZ.getCode());
                    HttpClientUtil.get(sendHanderUrl);

                    //微信推送移除
//                    R<AreaInfo> areaInfoR = areaInfoClient.getAreaInfoById(ch999UserVoR.getData().getArea1id());
//                    AreaInfo areaInfo = areaInfoR.getData();
//                    if (areaInfo != null && areaInfo.getIsSend()) {
//                        WeixinUser weixinUser = weixinUserService.getWxxinUserByUserId(shouhouYuyue.getUserid());
//                        if (weixinUser != null && StringUtils.isNotEmpty(weixinUser.getOpenid())) {
//                            //微信模板类型27
//                            msg += "，点击查看！";
//                            imCloud.sendServicesCompleteMsg(weixinUser.getOpenid(), host + "/after-service/detail/" + shouhouYuyue.getId(), "", "售后预约提醒", DateUtil.localDateTimeToString(LocalDateTime.now()), "", msg, null, 0l);
//
//                        }
//                    }
                }
            }

            //预约单接件成功消息推送
            if (CommenUtil.isNullOrZero(param.getId()) && CommenUtil.isNotNullZero(yuyueId)) {
                String value = sysConfigService.getValueByCode(SysConfigConstant.YU_YUE_ADD_PUSH);
                if (StringUtils.isNotEmpty(value) && Objects.equals(value, "1")) {
                    //消息推送
                    String msg =
                            "您所在门店有新的预约单需要跟进 单号：<a href='" + host + "/Mshouhouyuyue/yuyueadd/" + yuyueId + "'>" + yuyueId + "</a>";
                    Integer ch999Id = baseMapper.getWorkingCh999IdByAreaId(shouhouYuyue.getAreaid());
                    if (CommenUtil.isNotNullZero(ch999Id)) {
                        String link = sysConfigService.getValueByCode(SysConfigConstant.MOA_URL) + "/Mshouhouyuyue/yuyueadd/" + yuyueId;
                        smsService.sendOaMsg(msg, link, ch999Id.toString(), OaMesTypeEnum.SHTZ.getCode().toString());
                    }
                }
                //预约单MQ推送
                String routingKey = ConfigConsts.YU_YUE_ROUTING_KEY;
                rabbitTemplate.convertAndSend(ConfigConsts.YU_YUE_EXCHANGE, routingKey,String.valueOf(yuyueId));
                final Integer finalYuyueId = yuyueId;
                RabbitTemplate oaAsyncRabbitTemplate = SpringUtil.getBean(RabbitTemplate.class);
                CompletableFuture.runAsync(()->{
                    //发送预约加单通知 xxk 2021-09-24
                    String pushMsg = JSON.toJSONString(new SubPushMsgBO()
                            .setAct("subAddMsgPushToFollow").setData(new SubPushMsgBO.DataBO().setSubType(BusinessTypeEnum.APPOINTMENT_ORDER)
                                    .setOpType(SubPushMsgBO.OpTypeEnum.ADD_SUB).setSubId(finalYuyueId).setUserId(Long.valueOf(DecideUtil.isNull(param.getUserId(),0)))));
                    log.info("推送预约加单消息:{}",pushMsg);
                    oaAsyncRabbitTemplate.convertAndSend(RabbitMqConfig.QUEUE_TOPIC_OAASYNC, pushMsg);
                });

                //如果是上门取件或上门快修 且是钻石会员，则推送给会员
                if (CommenUtil.isNullOrZero(param.getUserId())
                        && Arrays.asList(YuYueSTypeEnum.SMQJ.getCode(), YuYueSTypeEnum.SMKX.getCode()).contains(param.getServicesWay())) {
                    //钻石会员消息推送
                    smsService.sendDiamondMemberMsgWhenBusiness(param.getUserId(), shouhouYuyue.getAreaid(), BusinessTypeEnum.APPOINTMENT_ORDER.getCode(), shouhouYuyue.getId());
                }
            }
            //三方订单状态同步
            thirdPlatStatusSyncService.thirdShouhouStatusSync(ShouhouOrderTypeEnum.YUYUE.getCode(), yuyueId);
            return R.success("保存或更新成功", yuyueId).addAllBusinessLog(SpringContextUtil.getRequestKeyListMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG));

        } catch (Exception e) {
            AtomicReference<String> msgRef = new AtomicReference<>();
            RRExceptionHandler.logError("售后预约单保存或更新",param,e,msgRef::set);
            return R.error(msgRef.get());
        }
    }

    /**
     * 特殊字段日志处理
     *
     * @return
     */
    private List<String> specialFildLog(ShouhouYuyue orignYuyue, ShouhouYuyue shouhouYuyue) {
        String msg = "";
        List<String> list = new ArrayList<>();
        if(XtenantEnum.isJiujiXtenant()){
           if(!Optional.ofNullable(orignYuyue.getSubId()).orElse(NumberConstant.ZERO).equals(Optional.ofNullable(shouhouYuyue.getSubId()).orElse(NumberConstant.ZERO))){
               msg = "订单 由【" + orignYuyue.getSubId() + "】修改为【" + shouhouYuyue.getSubId() + "】";
               list.add(msg);
           }
            if(!Optional.ofNullable(orignYuyue.getPpriceid()).orElse(NumberConstant.ZERO).equals(Optional.ofNullable(shouhouYuyue.getPpriceid()).orElse(NumberConstant.ZERO))){
                msg = "ppid 由【" + orignYuyue.getPpriceid() + "】修改为【" + shouhouYuyue.getPpriceid() + "】";
                list.add(msg);
            }
            if(!Optional.ofNullable(orignYuyue.getBasketId()).orElse(NumberConstant.ZERO).equals(Optional.ofNullable(shouhouYuyue.getBasketId()).orElse(NumberConstant.ZERO))){
                msg = "basketId 由【" + orignYuyue.getBasketId() + "】修改为【" + shouhouYuyue.getBasketId() + "】";
                list.add(msg);
            }
            // 解析tuidata字段并比较basketId和count
            YuyueTuiDataBo origTuiData = JSON.parseObject(orignYuyue.getTuidata(), YuyueTuiDataBo.class);
            YuyueTuiDataBo newTuiData = JSON.parseObject(shouhouYuyue.getTuidata(), YuyueTuiDataBo.class);
            if (origTuiData != null && newTuiData != null) {
                Map<Integer, Integer> origMap = new HashMap<>();
                Map<Integer, Integer> newMap = new HashMap<>();

                // 构建basketId和count的映射
                Optional.ofNullable(origTuiData.getBasket()).ifPresent(basket ->
                        basket.stream()
                                .filter(b -> b != null && b.getId() != null)
                                .forEach(b -> origMap.put(b.getId(), Optional.ofNullable(b.getCount()).orElse(0)))
                );

                Optional.ofNullable(newTuiData.getBasket()).ifPresent(basket ->
                        basket.stream()
                                .filter(b -> b != null && b.getId() != null)
                                .forEach(b -> newMap.put(b.getId(), Optional.ofNullable(b.getCount()).orElse(0)))
                );

                // 比较两个映射中的basketId和count
                Set<Integer> allIds = new HashSet<>();
                allIds.addAll(origMap.keySet());
                allIds.addAll(newMap.keySet());
                for (Integer id : allIds) {
                    int origCount = origMap.getOrDefault(id, 0);
                    int newCount = newMap.getOrDefault(id, 0);
                    if (origCount != newCount) {
                        BasketInfoBO basketInfo = Optional.ofNullable(CommenUtil.autoQueryHist(()->basketService.getBasketInfo(id),MTableInfoEnum.BASKET,id)).orElse(new BasketInfoBO());
                        list.add("商品【" + basketInfo.getName() + "】的数量由【" + origCount + "】修改为【" + newCount + "】");
                    }
                }
            }
        }

        //处理状态
        if (!orignYuyue.getStats().equals(shouhouYuyue.getStats())) {
            msg = "处理状态 由【" + EnumUtil.getMessageByCode(YuyueStatusEnum.class, orignYuyue.getStats()) + "】修改为【" + EnumUtil.getMessageByCode(YuyueStatusEnum.class, shouhouYuyue.getStats()) + "】";
            list.add(msg);
        }
        //处理方式
        if (!orignYuyue.getKind().equals(shouhouYuyue.getKind())) {
            msg = "处理方式 由【" + EnumUtil.getMessageByCode(DealWayEnum.class, orignYuyue.getKind()) + "】修改为【" + EnumUtil.getMessageByCode(DealWayEnum.class, shouhouYuyue.getKind()) + "】";
            list.add(msg);
        }
        //服务方式
        if (!orignYuyue.getStype().equals(shouhouYuyue.getStype())) {
            msg = "服务方式 由【" + EnumUtil.getMessageByCode(ServicesWayEnum.class, orignYuyue.getStype()) + "】修改为【" + EnumUtil.getMessageByCode(ServicesWayEnum.class, shouhouYuyue.getStype()) + "】";
            list.add(msg);
        }
        //是否数据备份
        orignYuyue.setIsBakData(orignYuyue.getIsBakData() == null ? 0 : orignYuyue.getIsBakData());
        if (!orignYuyue.getIsBakData().equals(shouhouYuyue.getIsBakData())) {
            msg = "是否数据备份 由【" + (orignYuyue.getIsBakData() == 0 ? "否" : "是") + "】修改为【" + (shouhouYuyue.getIsBakData() == 0 ? "否" : "是") + "】";
            list.add(msg);
        }
//        //预约时间
//        String stime1 = StringUtils.isEmpty(DateUtil.localDateTimeToString(orignYuyue.getStime())) ? "空" : DateUtil.localDateTimeToString(orignYuyue.getStime());
//        String stime2 = StringUtils.isEmpty(DateUtil.localDateTimeToString(shouhouYuyue.getStime())) ? "空" : DateUtil.localDateTimeToString(shouhouYuyue.getStime());
//        String etime1 = StringUtils.isEmpty(DateUtil.localDateTimeToString(orignYuyue.getEtime())) ? "空" : DateUtil.localDateTimeToString(orignYuyue.getEtime());
//        String etime2 = StringUtils.isEmpty(DateUtil.localDateTimeToString(shouhouYuyue.getEtime())) ? "空" : DateUtil.localDateTimeToString(shouhouYuyue.getEtime());
//        if (!stime1.equals(stime2) || !etime1.equals(etime2)) {
//            msg = "预约时间 由【" + stime1+"-"+etime1 + "】修改为【" + stime2+"-"+etime2 + "】";
//            list.add(msg);
//        }
        //快递上门时间
        String kdtime1 = StringUtils.isEmpty(DateUtil.localDateTimeToString(orignYuyue.getKdtime())) ? "空" : DateUtil.localDateTimeToString(orignYuyue.getKdtime());
        String kdtime2 = StringUtils.isEmpty(DateUtil.localDateTimeToString(shouhouYuyue.getKdtime())) ? "空" : DateUtil.localDateTimeToString(shouhouYuyue.getKdtime());
        if (!kdtime1.equals(kdtime2)) {
            msg = "快递上门时间 由【" + kdtime1 + "】修改为【" + kdtime2 + "】";
            list.add(msg);
        }
        //处理地区
        if (!orignYuyue.getAreaid().equals(shouhouYuyue.getAreaid())) {
            R<AreaInfo> areaInfoR1 = areaInfoClient.getAreaInfoById(orignYuyue.getAreaid());
            R<AreaInfo> areaInfoR2 = areaInfoClient.getAreaInfoById(shouhouYuyue.getAreaid());

            if (areaInfoR1.getCode() == ResultCode.SUCCESS && areaInfoR2.getCode() == ResultCode.SUCCESS) {
                msg = "处理地区 由【" + areaInfoR1.getData().getArea() + "】修改为【" + areaInfoR2.getData().getArea() + "】";
                list.add(msg);
            }
        }

        return list;
    }

    /**
     * 展示给用户看的日志
     * @param orignYuyue
     * @param shouhouYuyue
     * @return
     */
    private String specialFildShowUserLog(ShouhouYuyue orignYuyue, ShouhouYuyue shouhouYuyue) {
        String msg = "";
        //预约时间
        LocalDateTime orignStime = orignYuyue.getStime();
        LocalDateTime orignEtime = orignYuyue.getEtime();
        String oldTime="";
        if(ObjectUtil.isNotNull(orignStime) || ObjectUtil.isNotNull(orignEtime)){
            oldTime = getTimeStr(orignStime,orignEtime);
        } else {
            oldTime = "空";
        }
        LocalDateTime newStime = shouhouYuyue.getStime();
        LocalDateTime newEtime = shouhouYuyue.getEtime();
        String newTime="";
        if(ObjectUtil.isNotNull(newEtime) || ObjectUtil.isNotNull(newStime)){
            newTime = getTimeStr(newStime,newEtime);
        } else {
            newTime = "空";
        }
        if (!oldTime.equals(newTime)) {
            msg = "预约时间 由【" + oldTime + "】修改为【" + newTime + "】";
        }
        return msg;
    }

    /**
     * 获取时间字符串
     * @param stime
     * @param etime
     * @return
     */
    private String getTimeStr(LocalDateTime stime, LocalDateTime etime){
        int year = stime.getYear();
        int month = stime.getMonthValue();
        int day = stime.getDayOfMonth();
        int startHour = stime.getHour();
        int startMinute = stime.getMinute();
        int endHour = etime.getHour();
        int endMinute = etime.getMinute();
        return String.format("%s-%s-%s %s:%s-%s:%s",year,getStrFormt(month),getStrFormt(day),getStrFormt(startHour),getStrFormt(startMinute),getStrFormt(endHour),getStrFormt(endMinute));
    }
    private String getStrFormt(Integer num){
        if(num<NumberConstant.TEN){
            return "0"+num;
        }
        return num.toString();
    }


    /**
     * 联系地址字段变更记录处理
     *
     * @return
     */
    private List<String> specialFildLog(AddinfopsBo originAddinfops, AddinfopsBo newAddinfops, ShouhouYuyueReq param) {
        String msg = "";
        List<String> list = new ArrayList<>();
        //维修人
        String qConsignee1 = ObjectUtil.defaultIfNull(originAddinfops.getQConsignee(),"空");
        String qConsignee2 = ObjectUtil.defaultIfNull(newAddinfops.getQConsignee(),"空");
        if (!qConsignee1.equals(qConsignee2)) {
            msg = "维修人 由【" + qConsignee1 + "】修改为【" + qConsignee2 + "】";
            list.add(msg);
        }
        //取件联系人
        String qReceiver1 = ObjectUtil.defaultIfNull(originAddinfops.getQReceiver(),"空");
        String qReceiver2 = ObjectUtil.defaultIfNull(newAddinfops.getQReceiver(),"空");
        if (!qReceiver1.equals(qReceiver2)) {
            msg = "取件联系人 由【" + qReceiver1 + "】修改为【" + qReceiver2 + "】";
            list.add(msg);
        }
        //省份
        String qCityName1 = ObjectUtil.defaultIfNull(originAddinfops.getQCityName(),"空");
        String qCityName2 = ObjectUtil.defaultIfNull(newAddinfops.getQCityName(),"空");
        if (!qCityName1.equals(qCityName2)) {
            msg = "省份 由【" + qCityName1 + "】修改为【" + qCityName2 + "】";
            list.add(msg);
        }
        //取件地址
        String qAddress1 = ObjectUtil.defaultIfNull(originAddinfops.getQAddress(),"空");
        String qAddress2 = ObjectUtil.defaultIfNull(newAddinfops.getQAddress(),"空");
        if (!qAddress1.equals(qAddress2)) {
            msg = "取件地址 由【" + qAddress1 + "】修改为【" + qAddress2 + "】";
            list.add(msg);
        }
        return list;
    }

    /**
     * 通过预约单构建维修单信息
     * @param id
     * @return
     */
    private R<ShouhouReq> createShouHouReq(ShouhouYuyue yy,Integer smddType,OaUserBO oaUserBO,CreateShouHouReq createShouHouReq) {
        //如果串号为空
        Boolean flag = (StringUtils.isEmpty(yy.getImei()) || yy.getImei().length() < 5) && !DealWayEnum.KX.getCode().equals(yy.getKind());
        if (flag) {
            return R.error("串号错误");
        }
        //判断预约单是否已经存在维修单 （如果已经生成了维修单那就不在生成了）
//        List<Shouhou> list = shouhouService.lambdaQuery().eq(Shouhou::getYuyueid, yy.getId())
//                .eq(Shouhou::getXianshi, Boolean.TRUE)
//                .list();
        List<Shouhou> list = CommenUtil.autoQueryHist(()->shouhouService.lambdaQuery().eq(Shouhou::getYuyueid, yy.getId())
                .eq(Shouhou::getXianshi, Boolean.TRUE)
                .list());
        if(CollectionUtils.isNotEmpty(list)){
            Shouhou shouhou = list.get(0);
            ShouhouReq shouhouReq = new ShouhouReq();
            BeanUtils.copyProperties(shouhou,shouhouReq);
            shouhouReq.setExistsRepair(Boolean.TRUE);
            return R.success(shouhouReq);
        }
        //已上门、已到店  生成维修单
        ShouhouReq sh = yuyueTransShouhou(yy,createShouHouReq);
        sh.setIssoft(false);
        Boolean flag1 = null != sh.getProblem() && (sh.getProblem().contains("严重摔坏") || sh.getProblem().contains(
                "浸液"));
        if (flag1) {
            sh.setBaoxiu(0);
        }
        //预约单生成的维修单，故障描述会生成很多内容，（优化为故障描述默认为：空白）
        if (XtenantEnum.isJiujiXtenant()) {
            sh.setProblem("");
        }
        //如果是快修，则默认不在保
        if (DealWayEnum.KX.getCode().equals(yy.getKind())) {
            sh.setBaoxiu(0);
            sh.setWxkind(6);
        }
        //保修和购买信息
        if (StringUtils.isNotEmpty(yy.getImei()) && yy.getImei().length() > 5) {
            dealBaoxiuAndBuyInfo(sh, yy);
        }
        sh.setFeiyong(BigDecimal.ZERO);
        sh.setCostprice(BigDecimal.ZERO);
        // 判断是否是返修
        Shouhou shouhou = shouhouService.getShouhouInfoByImei(sh.getImei());
        if (shouhou != null) {
            sh.setWcount(1);
            shouhouService.updateIsticheng(shouhou.getId(), false);
        }
        sh.setIsticheng(false);
        sh.setIsfan(false);
        sh.setInuser(oaUserBO.getUserName());
        if (ServicesWayEnum.SMQJ.getCode().equals(yy.getStype())) {
            sh.setInuser("预约上门");
        } else if (ServicesWayEnum.SMWX.getCode().equals(yy.getStype())) {
            //上门快修 维修单默认勾选中
            sh.setWxkind(6);
        } else if (ServicesWayEnum.SMAZ.getCode().equals(yy.getStype())) {
            sh.setWxkind(1);
        }
        if ((null == sh.getUserid() || 0 == sh.getUserid()) && StringUtils.isNotEmpty(yy.getMobile())) {
            //如果手机号码不是会员就注册一个
            MemberReq memberReq = new MemberReq();
            memberReq.setMobile(yy.getMobile());
            memberReq.setUserName(yy.getMobile());
            memberReq.setRealName(yy.getUsername());
            memberReq.setInUser("系统");
            memberReq.setAreaId(sh.getAreaid());
            memberReq.setXtenant(oaUserBO.getXTenant());
            R<Integer> userR = memberClient.checkAndRegisterUser(memberReq);
            if (ResultCode.SUCCESS == userR.getCode() && userR.getData() != null) {
                sh.setUserid((long) userR.getData());
                sh.setUsername(yy.getMobile());
            }
        }
        //c#代码中的特殊处理
        if (yy.getPpriceid() != null && 77990 == yy.getPpriceid()) {
            sh.setStats(1);
            sh.setBaoxiu(2);
            sh.setIsBakData(0);
            sh.setInuser(oaUserBO.getUserName());
        }
        //如果 guideStaffId 不为空 则生成售后单接件人为此人，不限制预约类别
        if (null != yy.getGuideStaffId() && 0 != yy.getGuideStaffId()) {
            R<List<Ch999UserVo>> userRet = userInfoClient.listCh999UserInfo(Arrays.asList());
            if (ResultCode.SUCCESS == userRet.getCode() && CollectionUtils.isNotEmpty(userRet.getData())) {
                Ch999UserVo ch999UserVo = userRet.getData().get(0);
                sh.setInuser(ch999UserVo.getCh999Name());
            }
        }

        //如果维修方式为空，给默认值1 修
        if (CommenUtil.isNullOrZero(sh.getWxkind())) {
            sh.setWxkind(1);
        }
        return R.success(sh);
//        //保存售后单
//        R<Boolean> saveShouhouFlag = shouhouService.saveShouhou(sh);
//        if(saveShouhouFlag.isSuccess()){
//            R<ShouhouReq> reqR = R.success(sh);
//            reqR.setExData(saveShouhouFlag.getExData());
//            return reqR;
//        } else {
//            return R.error(saveShouhouFlag.getUserMsg());
//        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<List<OrderPartsVo>> orderingAccessories(Integer id, Integer smddType, Boolean wuliuFlag){
        OaUserBO oaUserBO = currentRequestComponent.getCurrentStaffId();
        if (oaUserBO == null) {
            return R.error("请登陆");
        }
        ShouhouYuyue yy = baseMapper.selectById(id);
        if( ObjectUtil.isNotNull(yy) && ObjectUtil.isNotNull(yy.getShouhouId())){
            return R.error("已经存在订购配件维修单："+yy.getShouhouId());
        }

        if(Boolean.FALSE.equals(Optional.ofNullable(yy.getIsmobile()).orElse(Boolean.FALSE))){
            return R.error("只有大件商品可以进行订购配件");
        }
        if (yy == null || yy.getIsdel()) {
            return R.error("预约单不存在或已被删除");
        }
        if (yy.getAreaid() == null || yy.getAreaid() == 0) {
            return R.error("地区不能为空");
        }
        if (yy.getUserid() == null || yy.getUserid() == 0) {
            return R.error("会员账号不能为空");
        }
        if (!YuyueStatusEnum.YWQR.getCode().equals(yy.getStats())) {
            return R.error("当前状态不允许操作");
        }
        CreateShouHouReq createShouHouReq = new CreateShouHouReq();
        createShouHouReq.setCreateType(ConnectionMethodEnum.ORDER_COMPLETED.getMessage());
        R<ShouhouReq> shouHouReq = createShouHouReq(yy, smddType, oaUserBO,createShouHouReq);
        if(!shouHouReq.isSuccess()){
            throw new CustomizeException(shouHouReq.getUserMsg());
        }
        ShouhouReq sh = shouHouReq.getData();
        R<Shouhou> shouHou = shouhouService.createShouHou(sh, oaUserBO);
        if(!shouHou.isSuccess()){
            throw new CustomizeException(shouHou.getUserMsg());
        }
        Shouhou shouhou = Optional.ofNullable(shouHou.getData()).orElse(new Shouhou());
        Integer shouHouId = shouhou.getId();
        sh.setId(Optional.ofNullable(shouHouId).orElseThrow(()->new CustomizeException("创建维修单失败")));
        //售后单id回写到预约单
        boolean update = this.lambdaUpdate().eq(ShouhouYuyue::getId, id).set(ShouhouYuyue::getShouhouId, shouHouId).update();
        if(!update){
            throw new CustomizeException("订购配件失败");
        }
        if(YuYueSTypeEnum.YJSX.getCode().equals(yy.getStype())) {
            UpdateWrapper<Addinfops> addinfopsUpdateWrapper = new UpdateWrapper<>();
            addinfopsUpdateWrapper.lambda().set(Addinfops::getBindId, shouHouId).set(Addinfops::getType, 1)
                    .eq(Addinfops::getType, 3).eq(Addinfops::getBindId, id);
            addinfopsService.update(addinfopsUpdateWrapper);
            UpdateWrapper<Addinfops> addinfopsUpdateWrapper1 = new UpdateWrapper<>();
            addinfopsUpdateWrapper1.lambda().set(Addinfops::getBindId, shouHouId).set(Addinfops::getType, 2)
                    .eq(Addinfops::getType, 4).eq(Addinfops::getBindId, id);
            addinfopsService.update(addinfopsUpdateWrapper1);
        }
        //新增的逻辑
        String host = Optional.ofNullable(sysConfigClient.getValueByCode(SysConfigConstant.OA_URL)).map(R::getData).filter(org.apache.commons.lang3.StringUtils::isNotEmpty).orElseThrow(() -> new CustomizeException("获取域名出错"));
        String orderUrl = String.format("<a href=\"%s/staticpc/#/after-service/order/edit/%s\">%s</a>", host, shouHouId,shouHouId);
        yuyueLogsService.yuyueLogsAdd(id, "订购配件生成维修单："+orderUrl, oaUserBO.getUserName(), null);
        //List<OrderPartsVo> orderPartsVos = handLeWxFeeBoListByYuYueId(sh, oaUserBO, yy);
        return R.success(new ArrayList<>());
    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<Boolean> yuyueconfirmSmDdEnter(Integer id, Integer smddType, Boolean wuliuFlag) {
        OaUserBO oaUserBO = currentRequestComponent.getCurrentStaffId();
        if (oaUserBO == null) {
            return R.error("请登陆");
        }
        ShouhouYuyue yy = baseMapper.selectById(id);
        if (yy == null || yy.getIsdel()) {
            return R.error("预约单不存在或已被删除");
        }
        if (yy.getAreaid() == null || yy.getAreaid() == 0) {
            return R.error("地区不能为空");
        }
        if (yy.getUserid() == null || yy.getUserid() == 0) {
            return R.error("会员账号不能为空");
        }
        if (!YuyueStatusEnum.YWQR.getCode().equals(yy.getStats())) {
            return R.error("当前状态不允许操作");
        }
        //6 = 已上门 7=已到店
        Integer stateTemp = 6;
        String stateStr = "已上门";
        if (1 == smddType) {
            stateTemp = 7;
            stateStr = "已到店";
        }
        Map<String, Object> shouHouExData = new HashMap<>();
        if (yy.getIsmobile()) {
            // region 大件售后start
/*            //如果串号为空
            Boolean flag =
                    (StringUtils.isEmpty(yy.getImei()) || yy.getImei().length() < 5) && !DealWayEnum.KX.getCode().equals(yy.getKind());
            if (flag) {
                return R.error("串号错误");
            }
            //已上门、已到店  生成维修单
            ShouhouReq sh = yuyueTransShouhou(yy);
            sh.setIssoft(false);
            Boolean flag1 = null != sh.getProblem() && (sh.getProblem().contains("严重摔坏") || sh.getProblem().contains(
                    "浸液"));
            if (flag1) {
                sh.setBaoxiu(0);
            }
            //预约单生成的维修单，故障描述会生成很多内容，（优化为故障描述默认为：空白）
            if (XtenantEnum.isJiujiXtenant()) {
                sh.setProblem("");
            }
            //如果是快修，则默认不在保
            if (DealWayEnum.KX.getCode().equals(yy.getKind())) {
                sh.setBaoxiu(0);
                sh.setWxkind(6);
            }
            //保修和购买信息
            if (StringUtils.isNotEmpty(yy.getImei()) && yy.getImei().length() > 5) {
                dealBaoxiuAndBuyInfo(sh, yy);
            }
            sh.setFeiyong(BigDecimal.ZERO);
            sh.setCostprice(BigDecimal.ZERO);
            // 判断是否是返修
            Shouhou shouhou = shouhouService.getShouhouInfoByImei(sh.getImei());
            if (shouhou != null) {
                sh.setWcount(1);
                shouhouService.updateIsticheng(shouhou.getId(), false);
            }
            sh.setIsticheng(false);
            sh.setIsfan(false);
            sh.setInuser(oaUserBO.getUserName());
            if (ServicesWayEnum.SMQJ.getCode().equals(yy.getStype())) {
                sh.setInuser("预约上门");
            } else if (ServicesWayEnum.SMWX.getCode().equals(yy.getStype())) {
                //上门快修 维修单默认勾选中
                sh.setWxkind(6);
            } else if (ServicesWayEnum.SMAZ.getCode().equals(yy.getStype())) {
                sh.setWxkind(1);
            }
            if ((null == sh.getUserid() || 0 == sh.getUserid()) && StringUtils.isNotEmpty(yy.getMobile())) {
                //如果手机号码不是会员就注册一个
                MemberReq memberReq = new MemberReq();
                memberReq.setMobile(yy.getMobile());
                memberReq.setUserName(yy.getMobile());
                memberReq.setRealName(yy.getUsername());
                memberReq.setInUser("系统");
                memberReq.setAreaId(sh.getAreaid());
                memberReq.setXtenant(oaUserBO.getXTenant());
                R<Integer> userR = memberClient.checkAndRegisterUser(memberReq);
                if (ResultCode.SUCCESS == userR.getCode() && userR.getData() != null) {
                    sh.setUserid((long) userR.getData());
                    sh.setUsername(yy.getMobile());
                }
            }
            //c#代码中的特殊处理
            if (yy.getPpriceid() != null && 77990 == yy.getPpriceid()) {
                sh.setStats(1);
                sh.setBaoxiu(2);
                sh.setIsBakData(0);
                sh.setInuser(oaUserBO.getUserName());
            }
            //如果 guideStaffId 不为空 则生成售后单接件人为此人，不限制预约类别
            if (null != yy.getGuideStaffId() && 0 != yy.getGuideStaffId()) {
                R<List<Ch999UserVo>> userRet = userInfoClient.listCh999UserInfo(Arrays.asList());
                if (ResultCode.SUCCESS == userRet.getCode() && CollectionUtils.isNotEmpty(userRet.getData())) {
                    Ch999UserVo ch999UserVo = userRet.getData().get(0);
                    sh.setInuser(ch999UserVo.getCh999Name());
                }
            }

            //如果维修方式为空，给默认值1 修
            if (CommenUtil.isNullOrZero(sh.getWxkind())) {
                sh.setWxkind(1);
            }

            //保存售后单
            R<Boolean> saveShouhouFlag = shouhouService.saveShouhou(sh);*/
            CreateShouHouReq createShouHouReq = new CreateShouHouReq();
            createShouHouReq.setCreateType(ConnectionMethodEnum.APPOINTMENT_FORM_COMPLETED.getMessage());
            R<ShouhouReq> shouHouReq = createShouHouReq(yy, smddType, oaUserBO,createShouHouReq);
            if(!shouHouReq.isSuccess()){
                return R.error(shouHouReq.getUserMsg());
            }
            ShouhouReq sh = shouHouReq.getData();
            //保存售后单
            R<Boolean> saveShouhouFlag = shouhouService.saveShouhou(sh);
            if(!saveShouhouFlag.isSuccess()){
                return R.error(saveShouhouFlag.getUserMsg());
            }
            shouHouExData = saveShouhouFlag.getExData();
            //增加是否扫码加单标识
            baseMapper.insertUserCodeSubBySource(id,BusinessTypeEnum.APPOINTMENT_ORDER.getCode(),sh.getId(),BusinessTypeEnum.AFTER_ORDER.getCode());
            //上门安装有服务码的生成取机的售后单
            if (ServicesWayEnum.SMQJ.getCode().equals(yy.getStype())) {
                if (StringUtils.isNotEmpty(yy.getFuwuma())) {
                    sh.setStats(1);
                    sh.setWeixiuren(yy.getCheckUser());
                    sh.setModidate(LocalDateTime.now());
                    sh.setIsquji(true);
                    sh.setOfftime(LocalDateTime.now());
                    sh.setWxTestStats(1);
                    sh.setWxTestTime(LocalDateTime.now());
                    sh.setTestuser(oaUserBO.getUserName());
                    sh.setTesttime(LocalDateTime.now());
                    shouhouService.updateById(sh);
                }
                //自动添加维修方案
                shouhouLogsService.addShouhouLog(oaUserBO.getUserName(), sh.getId(), ShouHouLogTypeEnum.WXFA.getCode(), "上门服务，处理完成；", new ShouhouLogNoticeBo(), false, 0);
            } else {
                ShouhouYuyueTrouble shouhouYuyueTrouble =
                        shouhouYuyueTroubleService.getYuyueTroubleByYuyueId(yy.getId());
                if (shouhouYuyueTrouble != null) {
                    ShouhouTrouble shouhouTrouble = new ShouhouTrouble();
                    shouhouTrouble.setShouhouId(sh.getId());
                    shouhouTrouble.setTroubleId(shouhouYuyueTrouble.getTroubleId());
                    shouhouTrouble.setIsValid(shouhouYuyueTrouble.getIsValid());
                    shouhouTroubleService.save(shouhouTrouble);
                }
                //更新容量升级
                ShouhouRomUpgrade shouhouRomUpgrade =
                        shouhouRomUpgradeService.getShouhouRomUpgradeByYuyueId(yy.getId());
                if (shouhouRomUpgrade != null) {
                    shouhouRomUpgrade.setShouhouId(sh.getId());
                    shouhouRomUpgradeService.updateById(shouhouRomUpgrade);
                    sh.setRepairLevel(2);
                    shouhouService.updateById(sh);
                }
            }
            //复制图片
            List<Attachments> attachments = attachmentsService.getAttachmentsByLinkId(yy.getId(), AttachmentsEnum.YUYUE.getCode(),null);
            if (CollectionUtils.isNotEmpty(attachments)) {
                List<Attachments> shouhouAttachmentsList = attachments.stream().map(e -> {
                    e.setId(null);
                    e.setLinkedID(sh.getId());
                    e.setType(AttachmentsEnum.SHOUHOU.getCode());
                    e.setDtime(LocalDateTime.now());
                    return e;
                }).collect(Collectors.toList());

                attachmentsService.saveBatchAttachments(shouhouAttachmentsList);
            }
            //门禁维修预约
            if (YuYueSTypeEnum.YYDD.getCode().equals(yy.getStype()) && yy.getPpriceid() != null && 77990 == yy.getPpriceid()) {
                Productinfo productinfo = productinfoService.getProductinfoByPpid(yy.getPpriceid());
                if (productinfo != null) {
                    //添加维修费用
                    addCostPrice(2, yy.getAreaid(), 77987, productinfo.getCostprice(), productinfo.getMemberprice(),
                            productinfo.getMemberprice(), productinfo.getProductName(), sh.getId(), "系统", null);
                }
                //自动添加维修方案
                shouhouLogsService.addShouhouLog("系统", sh.getId(), ShouHouLogTypeEnum.WXFA.getCode(), "自动添加维修方案：门禁卡软件服务", new ShouhouLogNoticeBo(), false, 0);
            }
            //更新售后预约
            UpdateWrapper<ShouhouYuyue> up = new UpdateWrapper<>();
            up.lambda().set(ShouhouYuyue::getCheckUser, oaUserBO.getUserName())
                    .set(ShouhouYuyue::getCheckTime, LocalDateTime.now())
                    .set(ShouhouYuyue::getShouhouId, sh.getId())
                    .eq(ShouhouYuyue::getId, id);
            if(isUseNewYuYue()){
                up.lambda().set(ShouhouYuyue::getStats, YuyueStatusEnum.YWC.getCode());
            } else {
                up.lambda().set(ShouhouYuyue::getStats, YuyueStatusEnum.YDD.getCode());
            }
            ((ShouhouYuyueService) AopContext.currentProxy()).update(up);
            UpdateWrapper<Addinfops> addinfopsUpdateWrapper = new UpdateWrapper<>();
            addinfopsUpdateWrapper.lambda().set(Addinfops::getBindId, sh.getId()).set(Addinfops::getType, 1)
                    .eq(Addinfops::getType, 3).eq(Addinfops::getBindId, id);
            Boolean upFlag = addinfopsService.update(addinfopsUpdateWrapper);
            UpdateWrapper<Addinfops> addinfopsUpdateWrapper1 = new UpdateWrapper<>();
            addinfopsUpdateWrapper1.lambda().set(Addinfops::getBindId, sh.getId()).set(Addinfops::getType, 2)
                    .eq(Addinfops::getType, 4).eq(Addinfops::getBindId, id);
            Boolean upFlag1 = addinfopsService.update(addinfopsUpdateWrapper1);
            if (!upFlag || !upFlag1) {
                R.error("确认售后预约保存失败");
            }
            //写入进程日志
            yuyueLogsService.yuyueLogsAdd(id, "已收到商品且为您生成售后单，预约单完成", oaUserBO.getUserName(), null);
            if (YuYueSTypeEnum.SMQJ.getCode().equals(yy.getStype()) || YuYueSTypeEnum.SMKX.getCode().equals(yy.getStype()) || YuYueSTypeEnum.SMAZ.getCode().equals(yy.getStype())) {
                if ("已上门".equals(stateStr)) {
                    R<AreaInfo> areaInfoR = areaInfoClient.getAreaInfoById(yy.getAreaid());
                    AreaInfo areaInfo = null;
                    Integer xtenant = null;
                    if (ResultCode.SUCCESS == areaInfoR.getCode() && areaInfoR.getData() != null) {
                        xtenant = areaInfoR.getData().getXtenant();
                        areaInfo = areaInfoR.getData();
                    }
                    //已上门，自动出配件30元,PPID = 71287
                    //wxkind=6是上门快修, yy.stype=6是上门安装
                    if ((sh.getWxkind() != null && 6 == sh.getWxkind() && 0 == xtenant) || (YuYueSTypeEnum.SMAZ.getCode().equals(yy.getStype()) && StringUtils.isEmpty(yy.getFuwuma()))) {
                        BigDecimal price = BigDecimal.valueOf(30);
                        //判断绑定的预约单是否使用优惠码，如果使用，修改费用为0，维修配件费用为0
                        String cardId = cardLogsService.getYuyueUsedCoupon(id);
                        if (StringUtils.isNotEmpty(cardId) && cardId.length() > 4) {
                            price = BigDecimal.ZERO;
                            String youhuicode = cardId.substring(0, 2) + "****" + cardId.substring(cardId.length() - 2);
                            shouhouService.saveShouhouLog(sh.getId(), "使用预约单优惠码：" + youhuicode, "系统", null, false);
                        }
                        //上门安装服务
                        if (YuYueSTypeEnum.SMAZ.getCode().equals(yy.getStype())) {
                            Integer fuwuppid = 76412;
                            Productinfo productinfo = productinfoService.getProductinfoByPpid(fuwuppid);
                            if (productinfo != null) {
                                price = productinfo.getMemberprice();
                            }
                            Boolean isyouhuima = false;
                            if (StringUtils.isNotEmpty(cardId)) {
                                isyouhuima = true;
                                price = BigDecimal.ZERO;
                            }
                            //添加上门安装服务费
                            addCostPrice(2, yy.getAreaid(), fuwuppid, BigDecimal.valueOf(30), price,
                                    productinfo.getMemberprice(), "服务工时费(上门安装)", sh.getId(), "系统", isyouhuima);
                            //预约单中预约的配件自动带到维修单中
                            List<YuyueLockProductInfoBo> yuyueLockProductList =
                                    shouhouYuyuelockppidsService.getYuyueLockProductList(id, yy.getAreaid());
                            if (CollectionUtils.isNotEmpty(yuyueLockProductList)) {
                                //删除预留库存
                                for (YuyueLockProductInfoBo yyProductinfo : yuyueLockProductList) {
                                    R<Boolean> lockWxPeijianRes = shouhouService.lockWxPeijian(yy.getId(),
                                            yyProductinfo.getPpid(), yy.getAreaid(), 2);
                                    if (ResultCode.SUCCESS == lockWxPeijianRes.getCode()) {
                                        //维修单添加配件
                                        addCostPrice(2, yy.getAreaid(), yyProductinfo.getPpid(),
                                                yyProductinfo.getInprice(), yyProductinfo.getMemberPrice(),
                                                productinfo.getMemberprice(), yyProductinfo.getProductName(),
                                                sh.getId(), "系统", null);
                                    }
                                }

                            }
                        } else {
                            addCostPrice(2, yy.getAreaid(), 71287, BigDecimal.valueOf(30), price, BigDecimal.valueOf(30),
                                    "服务工时费", sh.getId(), "系统", null);
                        }

                        String host = sysConfigService.getValueByCode(SysConfigConstant.MOA_URL);
                        if (areaInfo != null && areaInfo.getIsSend() && !YuYueSTypeEnum.SMAZ.getCode().equals(yy.getStype())) {
                            WeixinUser weixinUser = weixinUserService.getWxxinUserByUserId(yy.getUserid());
                            if (weixinUser != null && StringUtils.isNotEmpty(weixinUser.getOpenid())) {
                                String msg = "亲！您的售后预约单：" + yy.getId() + " 已取件。系统已为您生成维修订单，单号：" + sh.getId() +
                                        "，点击可查看详细！";
                                //微信模板类型27
                                imCloud.sendServicesCompleteMsg(weixinUser.getOpenid(), host + "/after-service/detail/" + sh.getId(), "", "上门取件提醒", DateUtil.localDateTimeToString(LocalDateTime.now()), "", msg, null, 0L);
                            }

                        }


                    }
                }
            }

            if (YuYueSTypeEnum.SMQJ.getCode().equals(yy.getStype())) {
                yuyueLogsService.yuyueLogsAdd(id, "手机已被取回进行处理", oaUserBO.getUserName(), null);
            } else if (YuYueSTypeEnum.SMKX.getCode().equals(yy.getStype())) {
                yuyueLogsService.yuyueLogsAdd(id, "工程师已到达维修地点", oaUserBO.getUserName(), null);
            }
            //解锁锁定配件库存
            shouhouService.unlockWxpjByYyid(yy.getId());
            //预约单的配件解锁之后要加入到维修单厘 （功能下架.客户提交预约单申请，生成维修单后会自动提交订购申请，若客户所预约的配件与实际实际配件不符会存在订购错误的情况，导致无效的人工成本）
//            if(isUseNewYuYue()){
//                List<OrderPartsVo> orderPartsVos = handLeWxFeeBoListByYuYueId(sh, oaUserBO, yy);
//                shouHouExData.put(ORDER_PARTS,JSONUtil.toJsonStr(orderPartsVos));
//            }
        } else {
            //region 小件流程start
            if (yy.getKind() == null || 0 == yy.getKind()) {
                return R.error("预约处理方式错误！");
            }
            Integer yyKind = yy.getKind();
            //预约和小件单退换KIND选项是反的
            if (SmallProKindEnum.SMALL_PRO_KIND_EXCHANGE.getCode().equals(yy.getKind())) {
                //3是退货
                yyKind = SmallProKindEnum.SMALL_PRO_KIND_RETURN.getCode();
            } else if (SmallProKindEnum.SMALL_PRO_KIND_RETURN.getCode().equals(yy.getKind())) {
                //2是换货
                yyKind = SmallProKindEnum.SMALL_PRO_KIND_EXCHANGE.getCode();
            }
            Integer finalYyKind = yyKind;
            if (Stream.of(SmallProKindEnum.SMALL_PRO_KIND_RETURN,SmallProKindEnum.SMALL_PRO_KIND_EXCHANGE)
                    .anyMatch(sk -> Objects.equals(sk.getCode(), finalYyKind))
                    && (StringUtils.isEmpty(yy.getTuidata()) || yy.getTuidata().length() <= 3)) {
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return R.error("退换商品错误，点击订单后的获取按钮查看。如未勾选，请删单重录");
            }
            YuyueTuiDataBo yuyueTuiDataBo = JSON.parseObject(yy.getTuidata(), YuyueTuiDataBo.class);
            Boolean tuiNotExistFlag = false;
            if (Objects.nonNull(yuyueTuiDataBo)){
                for (YuyueTuiDataBo.Basket basket : yuyueTuiDataBo.getBasket()) {
                    if (basket.getId() <= 0) {
                        tuiNotExistFlag = true;
                        break;
                    }
                }
            }

            if (yuyueTuiDataBo == null || tuiNotExistFlag) {
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return R.error("退换商品查询失败");
            }
            if(CommenUtil.isNullOrZero(yy.getSubId())){
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return R.error("小件预约单,订单号不能为空");
            }
            List<SubSimpleBo> subList = basketService.getSubSimpleInfoList(yy.getSubId());
            if (CollectionUtils.isEmpty(subList)) {
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return R.error("单号错误");
            }
            SubSimpleBo subSimpleBo = subList.get(0);
            long days = Duration.between(subSimpleBo.getTradedate1(), LocalDateTime.now()).toDays();
            //创建一个小件单
            SmallproReq smallproReq = new SmallproReq();
            smallproReq.setMobile(yy.getMobile());
            smallproReq.setUserName(yy.getUsername());
            smallproReq.setProblem(yy.getProblem());
            smallproReq.setOutward("");
            smallproReq.setStats(0);
            smallproReq.setSubId(yy.getSubId());
            smallproReq.setUserId(subSimpleBo.getUserId());
            smallproReq.setBuyDate(subSimpleBo.getTradedate1());
            smallproReq.setKind(yyKind);
            smallproReq.setComment("[售后预约:客户期望" + (yyKind == 1 ? "维修" : yyKind == 2 ? "换货" : "退货") + "，预约单" + yy.getId() + "]" + yy.getComment());
            smallproReq.setGroupId(1);
            smallproReq.setAreaId(yy.getAreaid());
            smallproReq.setIsBaoxiu(days < 15);
            smallproReq.setIsToArea(false);
            smallproReq.setInUser(oaUserBO.getUserName());
            smallproReq.setInDate(LocalDateTime.now());
            smallproReq.setYuyueId(yy.getId());
            if(Objects.equals(yy.getFromSource(), ShouhouyuyueFromSourceEnum.NIANBAODUIHUAN.getCode())){
                List<YuyuePpidsInfo> ppidsInfoList = JSON.parseArray(yy.getYuyuePPids(), YuyuePpidsInfo.class);
                if(CollUtil.isEmpty(ppidsInfoList)){
                    throw new CustomizeException("年包兑换对换商品不能为空");
                }
                YuyuePpidsInfo changePpidInfo = ppidsInfoList.stream().findFirst().get();
                smallproReq.setServiceType(SmallProServiceTypeEnum.SMALL_PRO_KIND_YEAR_CARD.getCode());
                smallproReq.setChangePpriceid(changePpidInfo.getPpid());
                smallproReq.setFeiyong(changePpidInfo.getPrice());
            }

            Map<Integer, YuyueTuiDataBo.Basket> tuiMap =
                    yuyueTuiDataBo.getBasket().stream().collect(Collectors.toMap(e -> e.getId(), e -> e));
            List<SmallproBill> proList = new ArrayList<>();
            for (SubSimpleBo sub : subList) {
                if (tuiMap.containsKey(sub.getBasketId())) {
                    YuyueTuiDataBo.Basket basket = tuiMap.get(sub.getBasketId());
                    SmallproBill smallproBill = new SmallproBill();
                    smallproBill.setBasketId(sub.getBasketId());
                    smallproBill.setPpriceid(sub.getPpid().longValue());
                    smallproBill.setCount(ObjectUtil.defaultIfNull(basket.getCount(), 1));
                    proList.add(smallproBill);
                    if (StringUtils.isEmpty(smallproReq.getName())) {
                        smallproReq.setName(sub.getProductName());
                    } else {
                        smallproReq.setName(smallproReq.getName() + "," + sub.getProductName());
                    }
                    if(ObjectUtil.defaultIfNull(smallproReq.getChangePpriceid(), 0) == 0
                            && Objects.equals(SmallProKindEnum.SMALL_PRO_KIND_EXCHANGE.getCode(), finalYyKind)){
                        //获取商品的ppid
                        Integer changePpid = CommenUtil.autoQueryHist(() -> basketService.lambdaQuery()
                                .eq(Basket::getBasketId, sub.getBasketId()).select(Basket::getBasketId,Basket::getPpriceid).list(),
                                MTableInfoEnum.BASKET, sub.getBasketId()).stream().map(Basket::getPpriceid)
                                .filter(Objects::nonNull).findFirst().map(Convert::toInt).orElse(null);
                        smallproReq.setChangePpriceid(changePpid);
                    }
                }

            }
            if (CollectionUtils.isEmpty(proList)) {
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return R.error("退换商品查询失败");
            }
            smallproReq.setSmallproBillList(proList);
            smallproReq.setConnectionMethod(ConnectionMethodEnum.APPOINTMENT_FORM_COMPLETED.getMessage());
            R<SmallproReq> saveSmallproRes = smallproService.saveSmallpro(smallproReq, oaUserBO.getAreaId(), oaUserBO);
            if (ResultCode.SUCCESS != saveSmallproRes.getCode()) {
//                return R.error("确认售后预约保存失败");
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return R.error(saveSmallproRes.getUserMsg());
            }
            //更新预约状态
            UpdateWrapper<ShouhouYuyue> up = new UpdateWrapper<>();
            up.lambda().set(ShouhouYuyue::getCheckUser, oaUserBO.getUserName())
                    .set(ShouhouYuyue::getSmallproid, saveSmallproRes.getData().getId())
                    .eq(ShouhouYuyue::getId, id);
            if(isUseNewYuYue()){
                up.lambda().set(ShouhouYuyue::getStats, YuyueStatusEnum.YWC.getCode());
            } else {
                up.lambda().set(ShouhouYuyue::getStats, stateTemp);
            }
            this.update(up);
            yuyueLogsService.yuyueLogsAdd(id, "已收到商品且为您生成售后单，预约单完成", oaUserBO.getUserName(), null);
            //yuyueLogsService.yuyueLogsAdd(id, "配件已被取回进行处理", oaUserBO.getUserName(), null);
            //解锁锁定配件库存
            shouhouService.unlockWxpjByYyid(yy.getId());
            // endregion 小件流程end
        }
        if (!wuliuFlag) {
            wuliuCloud.updateWuliuStatus(4, 8, id);
        }
        //三方订单状态同步
        thirdPlatStatusSyncService.thirdShouhouStatusSync(ShouhouOrderTypeEnum.YUYUE.getCode(), id);
        R<Boolean> success = R.success("处理成功", true);
        success.setExData(shouHouExData);
        return success;
    }

    /**
     * 根据预约单获取维修配件详情
     * @param yuyueId
     * @return
     */
    private List<OrderPartsVo> handLeWxFeeBoListByYuYueId(ShouhouReq sh,OaUserBO oaUserBO,ShouhouYuyue shouhouYuyue) {
        List<WxFeeBo> WxFeeBoLit = new ArrayList<>();
        String wxpjJson = shouhouYuyue.getYuyuePPids();
        List<Integer> ppidList = new ArrayList<>();
        Map<Integer, YuyuePpidsInfo> wxpjMap = null;
        if (StringUtils.isNotEmpty(wxpjJson)) {
            List<YuyuePpidsInfo> wxpjList = new LinkedList<>();
            if (CommenUtil.isNumer(wxpjJson)) {
                ppidList.add(Integer.valueOf(wxpjJson));
            } else {
                try {
                    wxpjList = JSON.parseArray(wxpjJson, YuyuePpidsInfo.class);
                } catch (Exception e) {
                    throw new RRException("json序列化异常，预约单id" + shouhouYuyue.getId() + wxpjJson + e.getMessage());
                }
                if (CollectionUtils.isNotEmpty(wxpjList)) {
                    wxpjList.stream().map(e -> e.getPpid()).forEach(ppidList::add);
                }
            }
        }
        //解锁预约单锁定的配件
        ppidList.forEach(ppid->shouhouService.lockWxPeijian(shouhouYuyue.getId(),ppid,shouhouYuyue.getAreaid(),NumberConstant.TWO));
        //判断是否已经生成了维修配件  如果是已经生成那就不在进行生成
        Integer shouHouId = sh.getId();
        //查询改维修单的维修配件
        WxkcoutputService wxkcoutputService = SpringUtil.getBean(WxkcoutputService.class);
        List<HexiaoBo> hexiaoBoList = wxkcoutputService.getHexiao(shouHouId);
        //查询改维修单的维修配件订购单
        ShouhouApplyService shouhouApplyService = SpringUtil.getBean(ShouhouApplyService.class);
        List<HexiaoBo> shouhouApplyHexiaoList = shouhouApplyService.getShouhouApplyHexiaoBo(shouHouId);
        hexiaoBoList.addAll(shouhouApplyHexiaoList);
        //把已经生成过维修配件或者已经生成订购单的商品移除(并且移除ppid 为0 或者ppid为空的数据)
        if(CollectionUtils.isNotEmpty(hexiaoBoList)){
            List<Integer> removePpidList = hexiaoBoList.stream().map(HexiaoBo::getPpid).collect(Collectors.toList());
            ppidList.removeIf(item->removePpidList.contains(item) || ObjectUtil.isNull(item) || NumberConstant.ZERO.equals(item));
        }
        List<OrderPartsVo> orderPartsList = new ArrayList<>();
        //判断是否已经存在维修单，如果已经存在维修单那就不进行配件的生成 或者ppid为空那就也还是不进行配件的生成
        Boolean existsRepair = Optional.ofNullable(sh.getExistsRepair()).orElse(Boolean.FALSE);
        if(CollectionUtils.isEmpty(ppidList) || existsRepair){
            return orderPartsList;
        }
        Map<Integer, Productinfo> productInfoMap = productinfoService.getProductMapByPpids(ppidList);
        //成本价格获取
        Integer areaid = sh.getAreaid();
        List<ProductKc> productKcList = com.jiuji.tc.utils.common.CommonUtils.bigDataInQuery(ppidList, ids ->
                productKcService.lambdaQuery().in(ProductKc::getPpriceid, ppidList)
                        .eq(ProductKc::getAreaid, areaid)
                        .gt(ProductKc::getLeftCount,NumberConstant.ZERO)
                        .list());
        //获取相关库存信息
        Map<Integer, ProductKc> productKcMap= new HashMap<>();
        if(CollectionUtils.isNotEmpty(productKcList)){
            productKcMap = productKcList.stream().collect(Collectors.toMap(ProductKc::getPpriceid, Function.identity(), (n1, n2) -> n2));
        }
        //根据商品封装成为配件对象
        for (Integer ppid:ppidList){
            //判断是否存在
            ProductKc productKc = productKcMap.get(ppid);
            Productinfo productinfo = productInfoMap.getOrDefault(ppid, new Productinfo());
            String productName = productinfo.getProductName();
            BigDecimal memberprice = productinfo.getMemberprice();
            //判断是否可以进行负库存出库
            Boolean negativeInventory = productKcService.negativeInventory(ppid);
            if(ObjectUtil.isNotNull(productKc) || negativeInventory){
                //有库存的时候
                BigDecimal costPrice = Optional.ofNullable(Optional.ofNullable(productKc).orElse(new ProductKc()).getInprice()).orElse(productinfo.getCostprice());
                WxFeeBo wxFeeBo = new WxFeeBo();
                wxFeeBo.setKinds(NumberConstant.TWO);
                wxFeeBo.setAreaId(areaid);
                wxFeeBo.setPpid(ppid);
                wxFeeBo.setInprice(costPrice);
                wxFeeBo.setPrice(memberprice);
                wxFeeBo.setPrice1(memberprice);
                wxFeeBo.setProductName(productName);
                wxFeeBo.setShouhouId(shouHouId);
                wxFeeBo.setUser(oaUserBO.getUserName());
                R<ShouhouCostPriceRes> shouhouCostPriceResR = shouhouService.addCostPriceWithAutoBind(wxFeeBo, false, Boolean.TRUE);
                if (!shouhouCostPriceResR.isSuccess()){
                    TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                    RRExceptionHandler.logError("预约单生成维修配件失败",wxFeeBo,null,smsService::sendOaMsgTo9JiMan);
                    throw new CustomizeException(shouhouCostPriceResR.getUserMsg()+"，ppid："+ppid);
                }
            } else {
                OrderPartsVo orderPartsVo = new OrderPartsVo();
                orderPartsVo.setPpid(ppid)
                        .setShouHouId(shouHouId)
                        .setProductName(productName);
                orderPartsList.add(orderPartsVo);
            }
        }
        return orderPartsList;
    }


    /**
     * 创建订单配件
     * @param shouHouId
     * @param productName
     * @param ppid
     * @param memberprice
     */
    @Override
    public R<Integer> createOrderParts(OrderPartsVo orderPartsVo){
        Integer ppid = orderPartsVo.getPpid();
        //判断如果ppid为0或者为空那就不进行创建
        R<Integer> result = R.success(NumberConstant.ZERO);
        if(ObjectUtil.isNull(ppid) || NumberConstant.ZERO.equals(ppid)){
            return result;
        }
        Integer shouHouId = orderPartsVo.getShouHouId();
        String productName = orderPartsVo.getProductName();
        String content = orderPartsVo.getContent();
        //没有库存的时候
        Map<String, Object> map = new HashMap<>();
        map.put("wxid",shouHouId);
        map.put("autoDiaobo", NumberConstant.ONE);
        map.put("title",productName);
        map.put("ppid",ppid);
        map.put("content",content);
        String host = Optional.ofNullable(sysConfigClient.getValueByCode(SysConfigConstant.IN_WCF_HOST)).map(R::getData).filter(org.apache.commons.lang3.StringUtils::isNotEmpty).orElseThrow(() -> new CustomizeException("获取域名出错"));
        String evidenceUrl = host + "/oaApi.svc/rest/ShouhouGouMai";
        HttpResponse evidenceResult = HttpUtil.createPost(evidenceUrl)
                .body(JSONUtil.toJsonStr(map))
                .execute();
        log.warn("生成维修单调用OA接口生成订购单：{}，返回结果：{}，地址：{}",JSONUtil.toJsonStr(map),evidenceResult.body(),evidenceUrl);
        if(evidenceResult.isOk()){
            try {
                result = JSONUtil.toBean(JSONUtil.toJsonStr(evidenceResult.body()), R.class);
            }catch (Exception e){
                RRExceptionHandler.logError("生成维修单调用OA接口生成订购单异常", map, e, SpringUtil.getBean(SmsService.class)::sendOaMsgTo9JiMan);
            }
        } else {
            RRExceptionHandler.logError("生成维修单调用OA接口生成订购单异常", Dict.create().set("url",evidenceUrl).set("参数",JSONUtil.toJsonStr(map)), null, SpringUtil.getBean(SmsService.class)::sendOaMsgTo9JiMan);
        }
        return result;
    }
    private void addCostPrice(Integer kind, Integer areaId, Integer ppid, BigDecimal inprice, BigDecimal price,
                              BigDecimal price1, String name, Integer wxId, String user, Boolean isyouhuima) {
        WxFeeBo wxFeeBo = new WxFeeBo();
        wxFeeBo.setKinds(kind);
        wxFeeBo.setAreaId(areaId);
        wxFeeBo.setPpid(ppid);
        wxFeeBo.setInprice(inprice);
        wxFeeBo.setPrice(price);
        wxFeeBo.setPrice1(price1);
        wxFeeBo.setProductName(name);
        wxFeeBo.setShouhouId(wxId);
        wxFeeBo.setUser(user);
        if (isyouhuima != null) {
            wxFeeBo.setIsyouhuima(isyouhuima);
        }
        shouhouService.addCostPrice(wxFeeBo, false, true);
    }

    @Override
    public R<Integer> hasYuyueSubByMobileAndImei(String mobile, String imei) {
        QueryWrapper<ShouhouYuyue> qw = new QueryWrapper();
        qw.lambda().select(ShouhouYuyue::getId);
        qw.lambda().in(ShouhouYuyue::getStats, Arrays.asList(1, 2, 4, 6, 7)).ge(ShouhouYuyue::getDtime, "2019-1-1 " +
                "00:00:00").eq(ShouhouYuyue::getIsdel, 0);
        if (StringUtils.isNotEmpty(mobile)) {
            qw.lambda().eq(ShouhouYuyue::getIsmobile, mobile);
        }
        if (StringUtils.isNotEmpty(imei)) {
            qw.lambda().eq(ShouhouYuyue::getImei, imei);
        }
        ShouhouYuyue shouhouYuyue = baseMapper.selectList(qw).stream().findFirst().orElse(null);
        if (shouhouYuyue != null) {
            return R.success("请求成功", shouhouYuyue.getId());
        }
        return R.error("无相关数据");
    }

    //todo 这里需要手动事务 或者分布式事务
    @Override
    public List<YuyueProductInfoRes> getYuyueProduct(Integer subId) {
        if(Objects.isNull(subId)){
            return Collections.emptyList();
        }
        List<YuyueProductInfoRes> list = baseMapper.getYuyueProduct(subId);
        Iterator<YuyueProductInfoRes> it = list.iterator();
        while (it.hasNext()) {
            YuyueProductInfoRes productInfoRes = it.next();
            if (productInfoRes.getPpid() == null || productInfoRes.getPpid() == 0 || productInfoRes.getBasketId() == null || productInfoRes.getBasketId() == 0) {
                continue;
            }
            String instalServicePpidStr = sysConfigService.getValueByCode(SysConfigConstant.INSTAL_SERVICES_PPID);
            if (StringUtils.isNotEmpty(instalServicePpidStr)) {
                String[] instalServicePpidArr = instalServicePpidStr.split(",");
                List<Integer> ppids =
                        Arrays.stream(instalServicePpidArr).map(e -> Integer.parseInt(e.trim())).collect(Collectors.toList());
                Boolean tuiFlag = installServicesRecordService.checkServiceRecordUsed(productInfoRes.getBasketId());
                //判断上门安装已使用，不可退,跳过
                if (ppids.contains(productInfoRes.getPpid()) && tuiFlag) {
                    it.remove();
                }
            }
        }
        return list;
    }

    @Override
    public List<YuyueProductInfoRes> getTuiProductInfoBySubId(Integer subId, Integer isMobile) {

        List<YuyueProductInfoRes> resList = CommenUtil.autoQueryHist(()->baseMapper.getTuiProductInfoBySubId(subId, isMobile));

        return resList;
    }


    @Override
    public TuiProductInfoRes getTuiProductInfoBySubIdV2(TuiProductInfoReq req) {
        TuiProductInfoRes tuiProductInfoRes = new TuiProductInfoRes();
        Integer subId = req.getSubId();
        Integer isMobile = req.getIsMobile();
        List<YuyueProductInfoRes> resList = CommenUtil.autoQueryHist(()->baseMapper.getTuiProductInfoBySubId(subId, isMobile));
        tuiProductInfoRes.setProductInfoList(resList);
        tuiProductInfoRes.setSubId(subId);
        //过滤掉不是同一个用户的订单
        Optional.ofNullable(req.getYuYueId()).ifPresent(yuYueId->{
            ShouhouYuyue shouhouYuyue = Optional.ofNullable(shouhouYuyueService.getById(yuYueId)).orElse(new ShouhouYuyue());
            Optional.ofNullable(shouhouYuyue.getUserid()).ifPresent(userId->resList.removeIf(info->!userId.equals(info.getUserId())));
        });
        if(CollUtil.isNotEmpty(resList)){
            Optional.ofNullable(resList.get(NumberConstant.ZERO)).ifPresent(yuyueProductInfoRes -> {
                Integer type = Optional.ofNullable(yuyueProductInfoRes.getType()).orElse(NumberConstant.ONE);
                tuiProductInfoRes.setSubIdTypeStr(type.equals((NumberConstant.ONE))?"新机":"良品");
                tuiProductInfoRes.setSubDate(yuyueProductInfoRes.getSubDate());
            });
            // 收集所有ppid
            List<Integer> ppidList = resList.stream().filter(item->Boolean.FALSE.equals(item.getIsMobile()))
                .map(YuyueProductInfoRes::getPpid)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
                if (CollUtil.isNotEmpty(ppidList)) {
                    Integer xtenant = XtenantEnum.getXtenant();
                    // 查询所有ppid对应的Productbarcode
                    List<Productbarcode> productbarcodeList = barcodeService.lambdaQuery()
                        .eq(Productbarcode::getOpXtenant, xtenant)
                        .in(Productbarcode::getPpriceid, ppidList)
                        .list();
                    if (CollUtil.isNotEmpty(productbarcodeList)) {
                        // 按ppid分组，并按isDefault倒序排列
                        Map<Integer, List<String>> ppidBarcodeMap = productbarcodeList.stream()
                                .filter(Objects::nonNull) // 过滤空对象
                                .filter(item -> item.getPpriceid() != null && item.getBarCode() != null) // 过滤关键字段为空的记录
                                .sorted(Comparator.comparing(
                                        productbarcode -> Optional.ofNullable(productbarcode.getIsDefault()).orElse(false),
                                        Comparator.reverseOrder()
                                ))
                                .collect(Collectors.groupingBy(
                                        Productbarcode::getPpriceid,
                                        Collectors.mapping(Productbarcode::getBarCode, Collectors.toList())
                                ));

                        // 将barCodeList设置到对应的YuyueProductInfoRes中
                        if (CollUtil.isNotEmpty(resList)) {
                            resList.stream()
                                    .filter(Objects::nonNull) // 过滤空对象
                                    .filter(item -> Boolean.FALSE.equals(item.getIsMobile()))
                                    .filter(obj -> obj.getPpid() != null) // 确保ppid不为空
                                    .forEach(obj -> {
                                        List<String> barcodeList = ppidBarcodeMap.getOrDefault(obj.getPpid(), Collections.emptyList());
                                        obj.setBarCodeList(barcodeList);
                                    });
                        }
                    }
                }

        }
        return tuiProductInfoRes;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<Boolean> yuyueconfirmYwEnter(Integer yuyueId) {
        OaUserBO oaUserBO = currentRequestComponent.getCurrentStaffId();
        if (oaUserBO == null) {
            return R.error("请登陆");
        }
        YuyueStatusEnum prevStatusEnum = YuyueStatusEnum.KFQR;
        if(XtenantEnum.isJiujiXtenant() && lambdaQuery().eq(ShouhouYuyue::getId,yuyueId).eq(ShouhouYuyue::getStats,YuyueStatusEnum.WQR.getCode()).count() > 0){
            prevStatusEnum = YuyueStatusEnum.WQR;
            SpringContextUtil.getRequest().ifPresent(req -> req.setAttribute(RequestAttrKeys.SHOUHOU_YUYUE_CONFIRM_SOURCE,ShouhouYuyueService.CONFIRM_BY_YW));
            R<Boolean> kfConfirmR = shouhouYuyueService.yuyueConfirm(yuyueId, oaUserBO.getUserId());
            if(!kfConfirmR.isSuccess()){
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return kfConfirmR;
            }
        }
        ShouhouYuyue shouhouYuyue = baseMapper.selectById(yuyueId);
        if (shouhouYuyue == null) {
            return R.error("预约单不存在或已被删除");
        }
        if (!YuyueStatusEnum.KFQR.getCode().equals(shouhouYuyue.getStats())) {
            return R.error("当前状态不允许操作");
        }
        shouhouYuyue.setStats(YuyueStatusEnum.YWQR.getCode());
        shouhouYuyue.setCheckUser(oaUserBO.getUserName());
        shouhouYuyue.setYchecktime(LocalDateTime.now());
        int i = baseMapper.updateById(shouhouYuyue);
        if (i < 1) {
            return R.error("客服确认售后预约保存失败");
        }
        String comment= "";
        if(isUseNewYuYue()){
            comment = StrUtil.format("由【{}】转【已确认】",prevStatusEnum.getMessage());
        } else {
            comment = StrUtil.format("由【{}】转【业务确认】",prevStatusEnum.getMessage());
        }
        yuyueLogsService.yuyueLogsAdd(yuyueId,comment, oaUserBO.getUserName(), 0);
        if (YuYueSTypeEnum.SMQJ.getCode().equals(shouhouYuyue.getStype()) || YuYueSTypeEnum.SMKX.getCode().equals(shouhouYuyue.getStype())) {
            String logmsg = "业务人员【" + oaUserBO.getUserName() + "】已确认，将在指定时间上门服务。";
            yuyueLogsService.yuyueLogsAdd(yuyueId, logmsg, oaUserBO.getUserName(), 0);
        }
        if(isUseNewYuYue()){
            R<AreaInfo> areaInfoR = null;
            if (shouhouYuyue.getAreaid() != null && shouhouYuyue.getAreaid() != 0) {
                areaInfoR = areaInfoClient.getAreaInfoById(shouhouYuyue.getAreaid());
            }
            AreaInfo areaInfo = null;
            if (areaInfoR != null && ResultCode.SUCCESS == areaInfoR.getCode() && areaInfoR.getData() != null) {
                areaInfo = areaInfoR.getData();
            }
            R<Ch999UserVo> userRet = userInfoClient.getCh999UserByUserId(oaUserBO.getUserId());
            if (ResultCode.SUCCESS != userRet.getCode() || userRet.getData() == null) {
                throw new CustomizeException("工作人员不存在，请核对！");
            }
            Ch999UserVo ch999UserVo = userRet.getData();
            if (YuYueSTypeEnum.YYDD.getCode().equals(shouhouYuyue.getStype())) {
                //到店时间开始的前1个小时 发送延迟队列
                yuYueConfirmPushMsg(shouhouYuyue);
            }
            if (YuYueSTypeEnum.YJSX.getCode().equals(shouhouYuyue.getStype())) {
                shouhouYuyue.setEnterUser(oaUserBO.getUserName());
                yuyueConfirmSendMsg(oaUserBO, shouhouYuyue, ch999UserVo, areaInfo);
            }
        }
        //三方订单状态同步
        thirdPlatStatusSyncService.thirdShouhouStatusSync(ShouhouOrderTypeEnum.YUYUE.getCode(), yuyueId);
        //业务确认后延迟队列推送 stime-20分钟-now
        if(XtenantJudgeUtil.isJiujiMore()){
            pushYwConfirmDelayQueue(shouhouYuyue);

        }
        return R.success("确认成功", true);
    }

    /**
     * 业务确认后延迟队列推送
     * 延迟时间为 stime-20分钟-now
     * @param shouhouYuyue 预约单信息
     */
    private void pushYwConfirmDelayQueue(ShouhouYuyue shouhouYuyue) {
        try {
            // 检查预约单开始时间是否存在
            if (Objects.isNull(shouhouYuyue.getStime())|| !YuYueSTypeEnum.YYDD.getCode().equals(shouhouYuyue.getStype())) {
                return;
            }
            LocalDateTime stime = shouhouYuyue.getStime();
            LocalDateTime now = LocalDateTime.now();
            // 计算延迟时间：stime - 20分钟 - now
            LocalDateTime targetTime = stime.minusMinutes(DEFAULT_MINUS_MINUTES);
            // 如果目标时间已经过了，则不推送
            if (targetTime.isBefore(now) || targetTime.isEqual(now)) {
                return;
            }
            // 计算延迟秒数
            long delaySeconds = java.time.Duration.between(now, targetTime).getSeconds();
            if (delaySeconds <= 0) {
                log.warn("预约单{}延迟时间计算结果为{}秒，跳过延迟队列推送", shouhouYuyue.getId(), delaySeconds);
                return;
            }
            // 创建推送数据
            YwConfirmDelayPush ywConfirmDelayPush = new YwConfirmDelayPush();
            ywConfirmDelayPush.setYuyueId(shouhouYuyue.getId());
            // 推送延迟队列
            ywConfirmDelayQueueStrategy.pushData(ywConfirmDelayPush, (int) delaySeconds);
            log.warn("业务确认延迟队列推送信息，预约单ID：{}，开始时间：{}，目标推送时间：{}，延迟{}秒", shouhouYuyue.getId(), stime, targetTime, delaySeconds);
        } catch (Exception e) {
            RRExceptionHandler.logError("业务确认延迟队列推送异常", shouhouYuyue, e, SpringUtil.getBean(SmsService.class)::sendOaMsgTo9JiMan);
        }
    }

    /**
     * 恢复处理的数据
     * @param data
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<HandleYuYueData> recoveryHandleData(String data){
        List<HandleYuYueData> list = JSONUtil.toList(data, HandleYuYueData.class);
        List<HandleYuYueData> handleYuYueDataList = new ArrayList<>();
        if(CollectionUtils.isEmpty(list)){
            return handleYuYueDataList;
        }
        list.forEach(item->{
            boolean update = this.lambdaUpdate().eq(ShouhouYuyue::getId, item.getYuYueId())
                    .eq(ShouhouYuyue::getStats, item.getNewState())
                    .set(ShouhouYuyue::getStats, item.getOldState())
                    .update();
           // Boolean update = Boolean.TRUE;
            if(!update){
                throw new CustomizeException("修改失败"+JSONUtil.toJsonStr(item));
            }
            HandleYuYueData handleYuYueData = new HandleYuYueData();
            handleYuYueData.setYuYueId(item.getYuYueId())
                    .setIsdel(item.getIsdel())
                    .setNewState(item.getOldState())
                    .setOldState(item.getNewState());
            handleYuYueDataList.add(handleYuYueData);
        });
        log.warn("预约单流程优化恢复数据处理："+JSONUtil.toJsonStr(handleYuYueDataList));
        return handleYuYueDataList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String handleDataDel() {
        List<Integer> yuYueIdList = this.baseMapper.selectAddSubDelCollect();
        StringJoiner joiner = new StringJoiner(",");
        LocalDateTime now = LocalDateTime.now();
        List<SubDelCollect> subDelCollectList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(yuYueIdList)){
            yuYueIdList.forEach(item->{
                joiner.add(Optional.ofNullable(item).orElse(0).toString());
                SubDelCollect subDelCollect = new SubDelCollect();
                subDelCollect.setDelType(NumberConstant.ZERO);
                subDelCollect.setSubType(3);
                subDelCollect.setSubId(item);
                subDelCollect.setDtime(now);
                subDelCollectList.add(subDelCollect);
            });
        }
        List<List<SubDelCollect>> partition = Lists.partition(subDelCollectList, 300);
        partition.forEach(item->{
            boolean b = this.baseMapper.batchInsertSubDelCollect(item);
            //boolean b = Boolean.TRUE;
            if(!b){
                throw new CustomizeException("写入失败");
            }
        });
        String format = String.format("修改时间%s，写入预约单%s", now, joiner);
        log.warn(format);
        return format;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<HandleYuYueData> handleData() {
        List<HandleYuYueData> handleYuYueDataList = new ArrayList<>();
        //已上门、已到店”状态名称调整为“已完成
        List<Integer> list = Arrays.asList(YuyueStatusEnum.YSM.getCode(), YuyueStatusEnum.YDD.getCode());
        List<ShouhouYuyue> updateList1 = this.lambdaQuery().in(ShouhouYuyue::getStats, list).last("and isnull(isdel,0)=0")
                .select(ShouhouYuyue::getId,ShouhouYuyue::getStats,ShouhouYuyue::getIsdel).list();
        if(CollectionUtils.isNotEmpty(updateList1)){
            updateList1.forEach(item->{
                HandleYuYueData handleYuYueData = new HandleYuYueData();
                handleYuYueData.setYuYueId(item.getId());
                handleYuYueData.setOldState(item.getStats());
                handleYuYueData.setIsdel(item.getIsdel());
                boolean update = this.lambdaUpdate().set(ShouhouYuyue::getStats, YuyueStatusEnum.YWC.getCode())
                        .eq(ShouhouYuyue::getId, item.getId())
                        .update();
             //   boolean update = Boolean.TRUE;
                if(update){
                    handleYuYueData.setNewState(YuyueStatusEnum.YWC.getCode());
                    handleYuYueDataList.add(handleYuYueData);
                } else {
                    throw new CustomizeException("预约单已上门、已到店”状态名称调整为“已完成异常 单号："+item.getId());
                }
            });
        }
        //“未取件”状态查询目前最近的数据为19年数据，共计62个数据，“未取件”状态统一处理为“已完成”状态
        List<ShouhouYuyue> updateList2 = this.lambdaQuery().eq(ShouhouYuyue::getStats, YuyueStatusEnum.WQJ.getCode()).last("and isnull(isdel,0)=0")
                .select(ShouhouYuyue::getId,ShouhouYuyue::getStats,ShouhouYuyue::getIsdel).list();
        if(CollectionUtils.isNotEmpty(updateList2)){
            updateList2.forEach(item->{
                HandleYuYueData handleYuYueData = new HandleYuYueData();
                handleYuYueData.setYuYueId(item.getId());
                handleYuYueData.setOldState(item.getStats());
                handleYuYueData.setIsdel(item.getIsdel());
                boolean update = this.lambdaUpdate().set(ShouhouYuyue::getStats, YuyueStatusEnum.YWC.getCode())
                        .eq(ShouhouYuyue::getId, item.getId())
                        .update();
              //  boolean update = Boolean.TRUE;
                if(update){
                    handleYuYueData.setNewState(YuyueStatusEnum.YWC.getCode());
                    handleYuYueDataList.add(handleYuYueData);
                } else {
                    throw new CustomizeException("“未取件”状态统一处理为“已完成”状态“已完成异常 单号："+item.getId());
                }
            });
        }
        log.warn("预约单流程优化数据处理："+JSONUtil.toJsonStr(handleYuYueDataList));
        return handleYuYueDataList;
    }

    @Override
    public R<String> checkDiamondsMember(Integer yuyueId) {
        ShouhouYuyue shouhouYuyue = baseMapper.selectById(yuyueId);
        if (shouhouYuyue == null) {
            return R.error("预约单不存在或已被删除");
        }
        Integer userid = shouhouYuyue.getUserid();
        if(userid==null){
            return R.error("预约单用户为空");
        }
        //判断是否为钻石会员
        R<MemberBasicRes> memberBasicInfoR = memberClient.getMemberBasicInfo(userid);
        MemberBasicRes memberBasicRes = com.jiuji.tc.utils.common.CommonUtils.isRequestSuccess(memberBasicInfoR) ? memberBasicInfoR.getData() : null;
        if (memberBasicRes != null) {
            boolean isDiamondMember = UserClassEnum.USER_CLASS_DIAMOND.getCode().equals(memberBasicRes.getUserClass())
                    || UserClassEnum.USER_CLASS_DOUBLE_DIAMOND.getCode().equals(memberBasicRes.getUserClass());
            if(isDiamondMember){
                R<String> result = new R<>();
                result.setMsg("该会员为钻级会员，享有免费高价值备用机专享权益，请询问客户需求并按规定处理。");
                result.setUserMsg("该会员为钻级会员，享有免费高价值备用机专享权益，请询问客户需求并按规定处理。");
                result.setCode(CHECK_DIAMONDS_MEMBER_CODE);
                return result;
            }
        }
        return R.success("校验通过");
    }

    @Override
    public R<Boolean> sendYuyueSms(Integer type, String mobile, Integer id, Integer userId) {
        String smsMsg = "";
        AreaInfo areaInfo = null;
        if (type == 1) {
            ShouhouYuyue shouhouYuyue = baseMapper.selectById(id);
            if (shouhouYuyue == null) {
                return R.error("预约单不存在！");
            }
            R<AreaInfo> areaInfoR = areaInfoClient.getAreaInfoById(shouhouYuyue.getAreaid());
            if (ResultCode.SUCCESS != areaInfoR.getCode() || areaInfoR.getData() == null || !areaInfoR.getData().getIsSend()) {
                return R.error("当前地区不支持短信发送");
            }
            areaInfo = areaInfoR.getData();
            if (YuyueSmsTypeEnum.YWQRJD.getCode().equals(type)) {
                R<Boolean> ret = certificateShouhouYuyueAndShouhou(id, userId, 4, areaInfo.getWebUrl());
                smsMsg =
                        "尊敬的" + CommenUtil.toPrintName(areaInfo.getPrintName()) + "会员！您的售后预约客服已确认。请您保持通讯畅通，详情请点击：" + ret.getUserMsg();
            }
        } else if (type == 2) {
            //Shouhou shouhou = shouhouService.getById(id);
            Shouhou shouhou = CommenUtil.autoQueryHist(() ->shouhouService.getById(id), MTableInfoEnum.SHOUHOU,id);
            if (shouhou != null) {
                R<AreaInfo> areaInfoR = areaInfoClient.getAreaInfoById(shouhou.getAreaid());
                if (ResultCode.SUCCESS != areaInfoR.getCode() || areaInfoR.getData() == null || !areaInfoR.getData().getIsSend()) {
                    return R.error("当前地区不支持短信发送");
                }
                areaInfo = areaInfoR.getData();
                R<Boolean> ret = certificateShouhouYuyueAndShouhou(id, userId, 5, areaInfo.getWebUrl());
                smsMsg = "亲！您的设备已录入系统,查看维修订单请点击：" + ret.getUserMsg();
            }
        } else {
            return R.error("type类型错误");
        }
        if (StringUtils.isNotEmpty(smsMsg)) {
            return smsService.sendSms(mobile, smsMsg,
                    DateUtil.localDateTimeToString(LocalDateTime.now().plusMinutes(-4)), "系统", smsService.getSmsChannelByTenant(areaInfo.getId(), ESmsChannelTypeEnum.YZMTD));
        }
        return R.error("未知错误 系统开小差了！");
    }


    @Override
    public R<String> getYuyueSmsContent(Integer shouhouId, Integer userId) {
       // Shouhou shouhou = shouhouService.getById(shouhouId);
        Shouhou shouhou = CommenUtil.autoQueryHist(() ->shouhouService.getById(shouhouId), MTableInfoEnum.SHOUHOU,shouhouId);
        AreaInfo areaInfo = null;
        if (shouhou != null) {

            R<AreaInfo> areaInfoR = areaInfoClient.getAreaInfoById(shouhou.getAreaid());
            if (ResultCode.SUCCESS != areaInfoR.getCode() || areaInfoR.getData() == null || !areaInfoR.getData().getIsSend()) {
                return R.error("当前地区不支持短信发送");
            }
            areaInfo = areaInfoR.getData();
            R<Boolean> ret = certificateShouhouYuyueAndShouhou(shouhouId, userId, 5, areaInfo.getWebUrl());
            String smsMsg = "亲！您的设备已录入系统,查看维修订单请点击：" + ret.getUserMsg();
            return R.success("操作成功", smsMsg);

        }
        return R.error("维修单不存在");
    }


    @Override
    public R<Boolean> certificateShouhouYuyueAndShouhou(Integer subId, Integer userId, Integer type, String webUrl) {
        String errorMsg = "发送电子凭证失败";
        OaUserBO oaUserBO = currentRequestComponent.getCurrentStaffId();
        Integer xtenant = oaUserBO.getXTenant();
        try {
            if (StringUtils.isEmpty(webUrl)) {
                webUrl = commonService.getUrlByXtenant(Long.valueOf(xtenant), ExtenAntUrlTypeEnum.WEBURL.getCode());
            }
            String url =
                    webUrl + "/web/api/urlRelevant/getShortUrl/v1?orderid=" + subId + "&userid=" + userId + "&type=" + type + "&t=" + LocalDateTime.now().getSecond();
            Optional<R<Boolean>> resultOpt = Optional.ofNullable(HttpClientUtil.get(url)).map(json -> JSON.parseObject(json, new TypeReference<R<String>>() {
            })).map(r -> DecideUtil.iif(r.getCode() == ResultCode.SUCCESS, R.success(r.getData(), true), R.error(r.getUserMsg())));
            if(resultOpt.isPresent()){
                return resultOpt.get();
            }
        } catch (Exception e) {
            errorMsg = "发送电子凭证异常";
            log.error("发送电子凭证异常",e);
        }
        return R.error(errorMsg);
    }

    @Override
    public R<Boolean> zyYishoujian(Integer yuyueId) {
        try {
            OaUserBO oaUserBO = currentRequestComponent.getCurrentStaffId();
            if (oaUserBO == null) {
                return R.error("请登陆");
            }
            String inuser = oaUserBO.getUserName();
            ShouhouYuyue shouhouYuyue = baseMapper.selectById(yuyueId);
            if (shouhouYuyue == null) {
                return R.error("该预约单不存在");
            }
            if (shouhouYuyue.getAreaid() == null || shouhouYuyue.getAreaid() == 0) {
                return R.error("请先选择处理地区");
            }
            //生成售后单
            List<ShouhouYuyueproductinfo> productList = shouhouYuyueproductinfoService.getYuyueProducts(yuyueId);
            if (CollectionUtils.isEmpty(productList)) {
                return R.error("预约机型不存在");
            }
            List<ShouhouReq> shouhouList = productList.stream().map(e -> {
                ShouhouReq sh = yuyueTransShouhou(shouhouYuyue,new CreateShouHouReq());
                sh.setName(e.getProductname());
                sh.setProductColor(e.getProductcolor());
                sh.setImei(e.getImei());
                sh.setProductId(e.getProductid().longValue());
                sh.setPpriceid(e.getPpriceid());
                sh.setIszy(true);
                return sh;
            }).collect(Collectors.toList());
            shouhouService.saveBatchShouhou(shouhouList);
            //处理预约单
            List<Integer> shouhouIds = shouhouList.stream().map(e -> e.getId()).collect(Collectors.toList());
            String shIdsStr = "";
            for (int i = 0; i < shouhouIds.size(); i++) {
                shIdsStr += shouhouIds.get(i);
                if (i < shouhouIds.size() - 1) {
                    shIdsStr += ",";
                }
            }
            shouhouYuyue.setStats(9);
            shouhouYuyue.setZyShouhouIDs(shIdsStr);
            baseMapper.updateById(shouhouYuyue);
            //写日志
            yuyueLogsService.yuyueLogsAdd(yuyueId, "已收件，生成售后单：" + shIdsStr, inuser, 0);

            R<AreaInfo> areaInfoR = areaInfoClient.getAreaInfoById(shouhouYuyue.getAreaid());
            Integer cityId = 0;
            if (areaInfoR.getCode() == ResultCode.SUCCESS && areaInfoR.getData() != null) {
                cityId = areaInfoR.getData().getCityId();
            }
            smsService.sendZyWeixinMsg(shouhouYuyue.getId(), shouhouYuyue.getUserid(), "收件成功", "", "亲爱的用户，您的预约单已成功收件，感谢使用中邮高讯！点击可以查看详情", "http://fix.999buy.com/orderDetail/" + shouhouYuyue.getId() + "/0", cityId);
            return R.success("操作成功", true);
        } catch (Exception e) {
            log.error("中邮已收件异常：" + e.getMessage());
        }
        return R.error("操作失败");
    }

    @Override
    public R<List<YuyuePaiduiPrintRes>> getPaiduiPring(String area) {
        OaUserBO oaUserBO = currentRequestComponent.getCurrentStaffId();
        if (StringUtils.isEmpty(area)) {
            area = oaUserBO.getArea();
        }
        String host = sysConfigService.getValueByCode(30);
        String url = host + "?Act=getc&area=" + area;
        String json = HttpClientUtil.get(url);
        JSONObject object = JSONObject.parseObject(json);
        if (1 == Integer.parseInt(object.get("stats").toString())) {
            List<YuyuePaiduiPrintRes> list = JSONObject.parseArray(object.get("data").toString(),
                    YuyuePaiduiPrintRes.class);
            return R.success(list);
        }
        return R.error("获取数据失败");
    }

    @Override
    public R<Boolean> updateImeiById(ShouhouYuYueAlterImeiReq req) {
        OaUserBO oaUserBO = currentRequestComponent.getCurrentStaffId();
        ShouhouYuyue shouhouYuyue = super.getById(req.getId());
        if (shouhouYuyue == null) {
            return R.error("预约单信息不存在");
        }
        String conent = "";
        if (StringUtils.isNotEmpty(shouhouYuyue.getImei())) {
            conent = "串号由：" + shouhouYuyue.getImei() + " 变更为：" + req.getImei();
        } else {
            conent = "串号录入：" + req.getImei();
        }

        yuyueLogsService.yuyueLogsAdd(req.getId(), conent, oaUserBO.getUserName(), 0);
        super.update(new LambdaUpdateWrapper<ShouhouYuyue>()
                .set(ShouhouYuyue::getImei, req.getImei()).eq(ShouhouYuyue::getId, req.getId()));
        //三方订单状态同步
        thirdPlatStatusSyncService.thirdShouhouStatusSync(ShouhouOrderTypeEnum.YUYUE.getCode(), req.getId());
        return R.success("操作成功");
    }

    @Override
    public R<List<YjsxAddress>> getYjsxAddress() {
        OaUserBO oaUserBO = currentRequestComponent.getCurrentStaffId();
        Integer authId = oaUserBO.getAuthorizeId();
        return R.success(baseMapper.getYjsxAddressByAuthId(authId));
    }

    @Override
    public String updateYuyueLog() {
        Map<String, String> map = new HashMap<>();
        map.put("配件无法订购", "维修配件无法订购");
        map.put("配件无现货", "维修配件无现货订货周期长");
        map.put("配件订购时间长", "维修配件无现货订货周期长");
        map.put("订购周期长", "维修配件无现货订货周期长");
        map.put("机器已回收", "维修设备回收出售");
        map.put("信息错误重新预约", "重复/错误提交");
        map.put("已处理", " 重复/错误提交");
        map.put("维修价格高", "维修报价高");
        int num = 0;
        for (Map.Entry<String, String> entry : map.entrySet()) {
            String x = entry.getKey();
            String y = entry.getValue();
            String comment = "取消预约单,原因:" + x;
            List<YuyueLogs> list = yuyueLogsService.lambdaQuery()
                    .like(YuyueLogs::getComment, comment).list();
            for (YuyueLogs z : list) {
                String newComment = z.getComment().replaceAll(x, y);
                yuyueLogsService.update(new LambdaUpdateWrapper<YuyueLogs>()
                        .set(YuyueLogs::getComment, newComment).eq(YuyueLogs::getId, z.getId()));
                num++;
            }
        }
        return "已经更新条数：" + num;
    }

    @Override
    public Integer getFromsourceById(Integer id) {
        return baseMapper.getFromsourceById(id);
    }

    @Override
    public String getFromSourceDescById(Integer yuyueId) {
        Integer fromSource = getFromsourceById(yuyueId);
        return ShouhouyuyueFromSourceEnum.descByCode(fromSource);
    }


    /**
     * 创建新 BaoxiuAndBuyBo
     * @param sh
     * @param yy
     * @return
     */
    private BaoxiuAndBuyBo createBaoxiuAndBuyBoNew(ShouhouYuyue yy){

        return shouhouService.getServiceInfo(yy.getImei(), Boolean.FALSE);
//        BaoxiuAndBuyBo baoxiuAndBuyBo = new BaoxiuAndBuyBo();
//        ServiceRecordService serviceRecordService = SpringUtil.getBean(ServiceRecordService.class);
//        ServiceInfoVO record = serviceRecordService.getRecord(yy.getImei(), yy.getUserid(), yy.getSubId(), Boolean.TRUE);
//        baoxiuAndBuyBo.setStats(NumberConstant.ONE)
//                .setIshuishou(Convert.toInt(record.getHuishou()))
//                .setMkc_id(record.getMkcId())
//                .setProductid(record.getProductId())
//                .setPpriceid(record.getPpriceId())
//                .setProduct_name(record.getProductName())
//                .setProduct_color(record.getProductColor())
//                .setTradedate(record.getTradeDate())
//                .setUsername(record.getUsername())
//                .setAreaid(record.getMkcAreaId())
//                .setSub_id(record.getSubId())
//                .setBasket_id(record.getBasketId());
//        if(Optional.ofNullable(record.getHuishou()).orElse(Boolean.FALSE)){
//            HuishouInfoBo huishouInfoBo = new HuishouInfoBo();
//            huishouInfoBo.setProduct_name(record.getProductName())
//                    .setAreaid(record.getMkcAreaId())
//                    .setSub_id(record.getSubId())
//                    .setPpriceid(record.getPpriceId())
//                    .setMkc_id(record.getMkcId())
//                    .setProduct_color(record.getProductColor())
//                    .setTradedate(record.getTradeDate())
//                    .setUsername(record.getUsername())
//                    .setBasket_id(record.getBasketId());
//            baoxiuAndBuyBo.setHuishouinfo(Collections.singletonList(huishouInfoBo));
//        }
//        return baoxiuAndBuyBo;
    }

    /**
     * 保修和购买信息处理
     *
     * @param sh
     * @param yy
     */
    private void dealBaoxiuAndBuyInfo(Shouhou sh, ShouhouYuyue yy) {
        int goto_count = 1;
        try {
            BaoxiuAndBuyBo baoxiuAndBuyBo = null;
            try {
                 //启用新线程来执行
                 baoxiuAndBuyBo = CompletableFuture.supplyAsync(SpringContextUtil.supplierWithContext(()-> createBaoxiuAndBuyBoNew(yy))).get();
                 log.warn("预约单：{}，串号：{}，相关数据：{}",yy.getId(),yy.getImei(), JSONUtil.toJsonStr(baoxiuAndBuyBo));
            } catch (Exception e){
                RRExceptionHandler.logError("预约单新版串号查询服务异常",yy,e,smsService::sendOaMsgTo9JiMan);
                String url = String.format(UrlConstants.BAOXIU_BUY_RUL, sysConfigService.getValueByCode(SysConfigConstant.OA_WCF_HOST), yy.getImei(), Namespaces.get());
                String json = HttpClientUtil.get(url);
                sh.setBaoxiu(NumberConstant.ZERO);
                if (StringUtils.isEmpty(json)) {
                    return;
                }
                 baoxiuAndBuyBo = JSON.parseObject(json, BaoxiuAndBuyBo.class);
            }
            if (baoxiuAndBuyBo == null) {
                return;
            }
            Boolean lastRecoverFlag=Boolean.FALSE;
            //查不到串号信息，则是外修
            if ((0 == baoxiuAndBuyBo.getStats() && 0 == baoxiuAndBuyBo.getIshuishou()) || Optional.ofNullable(baoxiuAndBuyBo.getLastRecoverFlag()).orElse(Boolean.FALSE)) {
                sh.setBaoxiu(2);
            } else {
                if (1 == baoxiuAndBuyBo.getIshuishou()) {
                    HuishouInfoBo huishouInfo = null;
                    if (CollectionUtils.isNotEmpty(baoxiuAndBuyBo.getHuishouinfo())) {
                        huishouInfo = baoxiuAndBuyBo.getHuishouinfo().get(baoxiuAndBuyBo.getHuishouinfo().size() - 1);
                    }
                    if (null == huishouInfo) {
                        return;
                    }
                    sh.setName(huishouInfo.getProduct_name());
                    sh.setBuyareaid(huishouInfo.getAreaid());
                    sh.setBaoxiu(3);
                    sh.setSubId(huishouInfo.getSub_id());
                    sh.setIshuishou(1);
                    sh.setPpriceid(huishouInfo.getPpriceid());
                    sh.setMkcId(huishouInfo.getMkc_id());
                    sh.setProductColor(huishouInfo.getProduct_color());
                    sh.setTradedate(huishouInfo.getTradedate());
                    //sh.setUserid((long)huishouInfo.getUserid());
                    sh.setUsername(huishouInfo.getUsername());
                    sh.setBasketId(huishouInfo.getBasket_id());
                    sh.setWuliyou(yy.getWuliyou());
                } else if (1 == baoxiuAndBuyBo.getStats()) {
                    if (baoxiuAndBuyBo.getMkc_id() != null) {
                        sh.setMkcId(baoxiuAndBuyBo.getMkc_id());
                    }
                    sh.setProductId((long) baoxiuAndBuyBo.getProductid());
                    sh.setPpriceid(baoxiuAndBuyBo.getPpriceid());
                    sh.setName(baoxiuAndBuyBo.getProduct_name());
                    sh.setProductColor(baoxiuAndBuyBo.getProduct_color());
                    sh.setTradedate(baoxiuAndBuyBo.getTradedate());
                    //sh.setUserid((long)baoxiuAndBuyBo.getUserid());
                    sh.setUsername(baoxiuAndBuyBo.getUsername());
                    sh.setBuyareaid(baoxiuAndBuyBo.getAreaid());
                    sh.setSubId(baoxiuAndBuyBo.getSub_id());
                    sh.setBasketId(baoxiuAndBuyBo.getBasket_id());
                    sh.setBaoxiu(3);
                    // 新逻辑出险不能走简单的逻辑, 需要选择配件进行出险
                    //sh.setServiceType(baoxiuAndBuyBo.getIsyiwai());
                }
                if (StringUtils.isEmpty(sh.getTruename())) {
                    sh.setTruename(sh.getUsername());
                }
            }

        } catch (Exception e) {
            goto_count++;
            if (goto_count < 3) {
                try {
                    Thread.sleep(100);
                    dealBaoxiuAndBuyInfo(sh, yy);
                } catch (Exception ex) {

                    log.error("递归处理异常:{}", ex.getMessage());
//                    ex.printStackTrace();
                }
            } else {
                String msg = "售后预约生成维修单异常(" + yy.getId() + "," + yy.getImei() + "):" + e.getMessage();
//                String sendHanderUrl = String.format(UrlConstants.OA_MSG_GET_URL, msg, null, "1324",
//                        OaMesTypeEnum.YCTZ.getCode());
//                HttpClientUtil.get(sendHanderUrl);
                smsService.sendOaMsgTo9JiMan(msg);
            }
        }
    }
    /**
     * 预约转售后单
     *
     * @param yy
     * @return
     */
    private ShouhouReq yuyueTransShouhou(ShouhouYuyue yy,CreateShouHouReq createShouHouReq) {
        ShouhouReq shouhou = new ShouhouReq();
        shouhou.setName(yy.getName());
        shouhou.setProductColor(yy.getColor());
        shouhou.setImei(yy.getImei());
        shouhou.setAreaid(yy.getAreaid());
        shouhou.setTruename(yy.getUsername());
        shouhou.setMobile(yy.getMobile());
        shouhou.setComment(yy.getComment());
        shouhou.setProblem(yy.getProblem());
        shouhou.setQuestionType(ShouhouResourceTypeEnum.APPOINTMENT.getCode());
        shouhou.setWebtype2(yy.getStype());
        shouhou.setWebtype1(yy.getKind());
        //预约处理组
        shouhou.setWeixiuzuid(25);
        shouhou.setStats(0);
        //审核状态为已审核
        shouhou.setWebstats(2);
        shouhou.setXianshi(true);
        shouhou.setWcount(0);
        shouhou.setModidate(LocalDateTime.now());
        shouhou.setOrderid(shouhouService.getOrderIdsh());
        shouhou.setYuyueid(yy.getId());
        shouhou.setUsername(yy.getUsername());
        shouhou.setIsBakData(yy.getIsBakData());
        shouhou.setProductId(yy.getProductId() == null ? 0 : (long) yy.getProductId());
        shouhou.setPpriceid(yy.getPpriceid());
        shouhou.setIszy(yy.getIszy());
        shouhou.setUserid((long) yy.getUserid());
        //九机并且是扫码加单的时候才进行赋值
        if(XtenantEnum.isJiujiXtenant() && ShouhouyuyueFromSourceEnum.SCAN_CODE.getCode().equals(yy.getFromSource())){
            shouhou.setFromSource(yy.getFromSource());
        }
        shouhou.setConnectionMethod(String.format(createShouHouReq.getCreateType()));
        return shouhou;
    }


    /**
     * 获取预约单地址
     * @param yuYueId
     * @return
     */
    @Override
    public String getYuYueIdUrl(Integer yuYueId){
        String url = "";
        try {
            if(ObjectUtil.isNull(yuYueId)){
                return "";
            }
            url = String.format("<a href= /staticpc/#/after-service/bespeak/%s>%s</a>",yuYueId,yuYueId);
        } catch (Exception e){
            RRExceptionHandler.logError("生成预约单地址异常",yuYueId,e,smsService::sendOaMsgTo9JiMan);
        }
        return url;
    }

    @Deprecated
    private void saveYuyueSendMsg(Integer yyid, String mobile, Integer serviceWay, String yuyueTime, Integer areaId,
                                  String qAddress, Integer dataType) {
        try {
            String remark = "";
            if (ServicesWayEnum.YYDD.getCode().equals(serviceWay)) {
                String areaName = "";
                String companyAddress = "";
                if (areaId != null && areaId != 0) {
                    R<AreaInfo> areaInfoR = areaInfoClient.getAreaInfoById(areaId);
                    if (ResultCode.SUCCESS == areaInfoR.getCode() && areaInfoR.getData() != null) {
                        areaName = areaInfoR.getData().getAreaName();
                        companyAddress = areaInfoR.getData().getCompanyAddress();
                    }
                }
                if (dataType == 1) {
                    remark = "尊敬的会员，您预约到店的时间是" + yuyueTime;
                    if (StringUtils.isNotEmpty(areaName)) {
                        remark += "，您选择的门店是" + areaName + "，地址为" + companyAddress;
                    }
                    remark += "。点击可查看详情！";
                } else {
                    remark = "尊敬的会员，您的预约维修单" + yyid + "已生成，您预约到店的时间是" + yuyueTime;
                    if (StringUtils.isNotEmpty(areaName)) {
                        remark += "，您选择的门店是" + areaName + "，地址为" + companyAddress;
                    }
                    remark += "。请核对您的预约维修订单，稍后客服将与您联系，请保持电话畅通。感谢您对九机网的支持，祝您生活愉快！";
                }
            } else if (ServicesWayEnum.SMWX.getCode().equals(serviceWay)) {
                if (dataType == 1) {
                    remark = "尊敬的会员，您预约上门的时间是" + yuyueTime + "，地址为" + qAddress + "。点击可查看详情！";
                } else {
                    remark = "尊敬的会员，您的预约维修单" + yyid + "已生成，您预约上门的时间是" + yuyueTime + "，地址为" + qAddress +
                            "，请核对您的预约维修订单，稍后客服将与您联系，请保持电话畅通。感谢您对九机网的支持，祝您生活愉快！";
                }
            } else if (ServicesWayEnum.SMQJ.getCode().equals(serviceWay)) {
                if (dataType == 1) {
                    remark = "尊敬的会员，您预约上门的时间是" + yuyueTime + "，地址为" + qAddress + "。点击可查看详情！";
                } else {
                    remark = "尊敬的会员，您的预约维修单" + yyid + "已生成，您预约上门的时间是" + yuyueTime + "，地址为" + qAddress +
                            "，请核对您的预约维修订单，稍后客服将与您联系，请保持电话畅通。感谢您对九机网的支持，祝您生活愉快！";
                }
            } else if (ServicesWayEnum.YJSX.getCode().equals(serviceWay)) {
                if (dataType == 1) {
                    remark = "尊敬的会员，您的预约维修订单已成功生成，为保证您的维修商品能正常使用，请尽快送修。点击可查看详情！";
                } else {
                    remark = "尊敬的会员，您的预约维修单" + yyid + "已生成，为保证您的电子产品能正常使用，请尽快送修，稍后客服将与您联系，请保持电话畅通。感谢您对九机网的支持，祝您生活愉快！";
                }

            }
            if (StringUtils.isNotEmpty(remark)) {
                smsService.sendSms(mobile, remark, DateUtil.getDateTimeAsString(LocalDateTime.now()),
                        "系统", smsService.getSmsChannelByTenant(areaId, ESmsChannelTypeEnum.YZMTD));
            }
        } catch (Exception e) {
            RRExceptionHandler.logError("售后预约提交成功推送程序",new Object[]{yyid, mobile, serviceWay, yuyueTime, areaId,qAddress, dataType},e,msg->{
                smsService.sendOaMsgTo9JiMan(msg);
            });

        }
    }

    private void yuyueConfirmSendMsg(OaUserBO oaUserBo, ShouhouYuyue shouhouYuyue, Ch999UserVo ch999UserVo, AreaInfo areaInfo) {
        String oaAppointmentType = "";
        String webAppointmentType = "售后预约单";
        String processMsg = "";
        String memberWxMsg = "";
        Integer oaMgsType = 0;
        String startTimeStr = DateUtil.localDateTimeToMinutesStr(shouhouYuyue.getStime());
        String endTimeStr = DateUtil.localDateTimeToMinutesStr(shouhouYuyue.getEtime());
        Integer smsConfigCode = SmsConfigCodeEnum.CODE_105.getCode();
        String host = sysConfigService.getValueByCode(SysConfigConstant.MOA_URL);
        if (YuYueSTypeEnum.YYDD.getCode().equals(shouhouYuyue.getStype())) {
            oaAppointmentType = "售后预约到店的预约单";
            webAppointmentType = YuYueSTypeEnum.YYDD.getMessage();
            processMsg = "已指派门店工作人员。";
            oaMgsType = OaMesTypeEnum.SHTZ.getCode();
            memberWxMsg = "亲！您的售后预约到店订单：" + shouhouYuyue.getId() + "，预约时间为" + startTimeStr + "至" + endTimeStr;
            yuyueLogsService.yuyueLogsAdd(shouhouYuyue.getId(), processMsg , "系统", 0);
        } else if(YuYueSTypeEnum.YJSX.getCode().equals(shouhouYuyue.getStype())){
            oaAppointmentType = "售后预约到店的预约单";
            webAppointmentType = YuYueSTypeEnum.YJSX.getMessage();
            processMsg = "已指派门店工作人员。";
            oaMgsType = OaMesTypeEnum.SHTZ.getCode();
            yuyueLogsService.yuyueLogsAdd(shouhouYuyue.getId(), processMsg, "系统", 0);
        } else if (YuYueSTypeEnum.SMQJ.getCode().equals(shouhouYuyue.getStype())) {
            oaAppointmentType = "上门取件预约单";
            webAppointmentType = YuYueSTypeEnum.SMQJ.getMessage();
            processMsg = "已指派上门取件工作人员";
            oaMgsType = OaMesTypeEnum.SMQJTZ.getCode();
            memberWxMsg = "亲！您的上门取件预约单：" + shouhouYuyue.getId() + "。预约上门时间为" + startTimeStr + "至" + endTimeStr +
                    "。请您在预约时段保持通讯畅通，工作人员将会及时和您联系";
            smsConfigCode = SmsConfigCodeEnum.CODE_137.getCode();
            yuyueLogsService.yuyueLogsAdd(shouhouYuyue.getId(), processMsg + "：" + ch999UserVo.getCh999Name(), "系统", 0);
        } else if (YuYueSTypeEnum.SMKX.getCode().equals(shouhouYuyue.getStype())) {
            oaAppointmentType = "上门快修预约单";
            processMsg = "已指派维修工程师";
            webAppointmentType = YuYueSTypeEnum.SMKX.getMessage();
            oaMgsType = OaMesTypeEnum.SHTZ.getCode();
            memberWxMsg = "亲！您的上门快修预约单：" + shouhouYuyue.getId() + "。预约上门时间为" + startTimeStr + "至" + endTimeStr +
                    "。请您在预约时段保持通讯畅通，工作人员将会及时和您联系";
            smsConfigCode = SmsConfigCodeEnum.CODE_137.getCode();
            yuyueLogsService.yuyueLogsAdd(shouhouYuyue.getId(), processMsg + "：" + ch999UserVo.getCh999Name(), "系统", 0);
        }
        //微信，OAAPP 通知员工
        String msg = "您有一个" + oaAppointmentType + "需要处理，请及时处理，单号：" + shouhouYuyue.getId() ;
        String link = host + "/Mshouhouyuyue/yuyueadd/" + shouhouYuyue.getId();
        smsService.sendOaMsg(msg, link, ch999UserVo.getCh999Id().toString(), oaMgsType.toString());
        String mHost = sysConfigService.getValueByCode(SysConfigConstant.M_URL);
        if (areaInfo != null && areaInfo.getIsSend()) {
            WeixinUser weixinUser = weixinUserService.getWxxinUserByUserId(shouhouYuyue.getUserid());
            if (XtenantEnum.isJiujiXtenant()) {
                if (weixinUser != null && StringUtils.isNotEmpty(weixinUser.getOpenid())) {
                    //微信模板类型27
                    memberWxMsg += "，点击查看！";
                    imCloud.sendAfterServiceProgressMsg(weixinUser.getOpenid(), mHost + "/after-service/reserve-detail/" + shouhouYuyue.getId(),"",webAppointmentType , "处理中", DateUtil.localDateTimeToString(LocalDateTime.now()), processMsg, memberWxMsg, null, 0L);
                } else {
                    if (StringUtils.isBlank(shouhouYuyue.getMobile())) {
                        log.info("手机号码为空,消息发送失败");
                        return;
                    }
                    R<Boolean> response = smsService.sendSms(shouhouYuyue.getMobile(), memberWxMsg, DateUtil.localDateTimeToString(LocalDateTime.now()), "系统", smsService.getSmsChannelByTenant(Optional.ofNullable(areaInfo.getId()).orElse(0), ESmsChannelTypeEnum.YZMTD));

                    if (response.getCode() == ResultCode.RETURN_ERROR) {
                        log.info("消息发送失败");
                    }
                }
            } else {
                long xtenant = Namespaces.get();
                String openXtenantStr = apolloEntity.getSmsConfigOpenXtenant();
                if (StringUtils.isNotEmpty(openXtenantStr)
                        && com.jiuji.tc.utils.common.CommonUtils.covertIdStr(openXtenantStr).contains((int) xtenant)) {
                    R<SmsConfigVO> smsConfigResult = smsConfigCloud.getConfigByCode(xtenant,
                            smsConfigCode);
                    // 发送微信消息
                    boolean sendWxMessage = weixinUser != null && StringUtils.isNotEmpty(weixinUser.getOpenid());
                    // 发送微信消息
                    boolean sendSmsMessage = StringUtils.isNotBlank(shouhouYuyue.getMobile());
                    if (com.jiuji.tc.utils.common.CommonUtils.isRequestSuccess(smsConfigResult)) {
                        SmsConfigVO smsConfig = smsConfigResult.getData();
                        // 消息内容
                        List<SmsConfigVO.SmsField> fields = smsConfig.getFields();
                        String message = smsConfig.getTemplate();
                        if (StringUtils.isNotEmpty(message)
                                && CollectionUtils.isNotEmpty(fields)) {
                            for (SmsConfigVO.SmsField field : fields) {
                                if ("<subId>".equals(field.getValue())) {
                                    message = message.replace(field.getValue(), String.valueOf(shouhouYuyue.getId()));
                                }
                                if ("<startTime>".equals(field.getValue())) {
                                    message = message.replace(field.getValue(), startTimeStr);
                                }
                                if ("<endTime>".equals(field.getValue())) {
                                    message = message.replace(field.getValue(), endTimeStr);
                                }
                                if ("<serviceType>".equals(field.getValue())) {
                                    message = message.replace(field.getValue(), oaAppointmentType);
                                }
                            }
                        }
                        // 推送方式
                        List<Integer> pushMethods = smsConfig.getPushMethod();
                        // sms消息
                        sendSmsMessage = sendSmsMessage
                                && CollectionUtils.isNotEmpty(pushMethods)
                                && pushMethods.contains(SmsPushMethodEnum.SMS.getCode());
                        memberWxMsg = message;
                    }
                    if (sendWxMessage) {
                        //微信模板类型27
                        memberWxMsg += "，点击查看！";
                        imCloud.sendAfterServiceProgressMsg(weixinUser.getOpenid(),
                                mHost + "/after-service/reserve-detail/" + shouhouYuyue.getId(), "", webAppointmentType,
                                "处理中", DateUtil.localDateTimeToString(LocalDateTime.now()), processMsg,
                                memberWxMsg, null, 0L);
                    }
                    if (sendSmsMessage) {
                        R<Boolean> response = smsService.sendSms(shouhouYuyue.getMobile(), memberWxMsg, DateUtil.localDateTimeToString(LocalDateTime.now()), "系统", smsService.getSmsChannelByTenant(Optional.ofNullable(areaInfo.getId()).orElse(0), ESmsChannelTypeEnum.YZMTD));
                        if (response.getCode() == ResultCode.RETURN_ERROR) {
                            log.info("消息发送失败");
                        }
                    }
                }
            }
        }
    }


    /**
     * 构建上门取件物流单
     *
     * @param shouhouYuyue
     * @param ch999UserVo
     * @param addinfops
     * @return
     */
    private AddWuliuReq buildWuliu(ShouhouYuyue shouhouYuyue, Ch999UserVo ch999UserVo, AddinfopsBo addinfops) {
        AddWuliuReq wuliuReq = new AddWuliuReq();
        wuliuReq.setSubId(shouhouYuyue.getId());
        wuliuReq.setAreaId(shouhouYuyue.getAreaid());
        wuliuReq.setInuser(ch999UserVo.getCh999Name());
        wuliuReq.setSName(addinfops.getQReceiver());
        wuliuReq.setSMobile(shouhouYuyue.getMobile());
        wuliuReq.setSCityId(addinfops.getQCityId());
        wuliuReq.setSAddress(addinfops.getQAddress());
        wuliuReq.setRName(ch999UserVo.getCh999Name());
        wuliuReq.setRMobile(ch999UserVo.getMobile());
        wuliuReq.setRAreaId(shouhouYuyue.getAreaid());
        wuliuReq.setStatus("0");
        wuliuReq.setWCateId(WuliuCateEnum.SMQJ.getCode());
        return wuliuReq;
    }

    /**
     * 获取微信接收人id
     *
     * @param classify
     * @return
     */
    @Override
    public List<Integer> getWxSmsReceiverUserIds(String classify) {
        List<SmsReceiverRes> receiverList = wxSmsReceiverService.getWxSmsReceiversByClassify(classify);
        if (CollectionUtils.isNotEmpty(receiverList)) {
            return receiverList.stream().map(re -> re.getCh999Id()).collect(Collectors.toList());
        }
        return null;
    }

    /**
     * 获取在职人员
     *
     * @param ch999Ids
     * @return
     */
    @Override
    public List<UserSimpleInfoRes> getWorkingUsers(List<Integer> ch999Ids) {
        if (CollectionUtils.isEmpty(ch999Ids)) {
            return null;
        }
        List<Integer> userIds = kaoqinService.getCurrentWork(ch999Ids);
        if (CollectionUtils.isEmpty(userIds)) {
            return null;
        }
        R<List<Ch999UserVo>> userRet = userInfoClient.listCh999UserInfo(userIds);
        if (ResultCode.SUCCESS == userRet.getCode() && userRet.getData() != null) {
            return buildUserSimpleInfoList(userRet.getData());
        }
        return null;
    }

    private void buildBasicShouhouYuyueInfo(ShouhouYuyueInfoRes yuyueInfoRes, ShouhouYuyue shouhouYuyue) {

        BeanUtils.copyProperties(shouhouYuyue, yuyueInfoRes);
        yuyueInfoRes.setUserName(shouhouYuyue.getUsername());
        yuyueInfoRes.getMemberLevel();
        if (shouhouYuyue.getIsdel() != null && shouhouYuyue.getIsdel()) {
            yuyueInfoRes.setStatus(YuyueStatusEnum.YQX.getCode());
            if(isUseNewYuYue()){
                yuyueInfoRes.setStatus(YuyueStatusEnum.YSC.getCode());
            }
        } else {
            yuyueInfoRes.setStatus(shouhouYuyue.getStats());
        }
        if(isUseNewYuYue()){
            yuyueInfoRes.setStatusName(YuyueStatusEnum.getNewMessageByCode(yuyueInfoRes.getStatus()));
        } else {
            yuyueInfoRes.setStatusName(EnumUtil.getMessageByCode(YuyueStatusEnum.class, yuyueInfoRes.getStatus()));
        }
        yuyueInfoRes.setStartTime(shouhouYuyue.getStime());
        yuyueInfoRes.setEndTime(shouhouYuyue.getEtime());
        yuyueInfoRes.setKdtime(shouhouYuyue.getKdtime());
        yuyueInfoRes.setKdtype(shouhouYuyue.getKdtype());
        yuyueInfoRes.setSubmitUser(shouhouYuyue.getInuser());
        yuyueInfoRes.setSubmitTime(shouhouYuyue.getDtime());
        yuyueInfoRes.setDealWay(shouhouYuyue.getKind());
        yuyueInfoRes.setServicesWay(shouhouYuyue.getStype());
        yuyueInfoRes.setAreaId(shouhouYuyue.getAreaid());
        yuyueInfoRes.setProductName(shouhouYuyue.getName());
        yuyueInfoRes.setProductColor(shouhouYuyue.getColor());
        yuyueInfoRes.setSmallproId(shouhouYuyue.getSmallproid());
        yuyueInfoRes.setIsMobile(shouhouYuyue.getIsmobile());
        yuyueInfoRes.setTuidata(JSON.parseObject(shouhouYuyue.getTuidata(), ShouhouYuyueReq.TuiData.class));
        yuyueInfoRes.setDelivery(shouhouYuyue.getDelivery());
        yuyueInfoRes.setUserId(shouhouYuyue.getUserid());
        yuyueInfoRes.setShouhouId(shouhouYuyue.getShouhouId());
        yuyueInfoRes.setKuaididan(shouhouYuyue.getKuaididan());
        yuyueInfoRes.setKuaidigongsi(shouhouYuyue.getKuaidigongsi());
        yuyueInfoRes.setFromSourceDesc(ShouhouyuyueFromSourceEnum.descByCode(shouhouYuyue.getFromSource()));
    }

    private List<UserSimpleInfoRes> buildUserSimpleInfoList(List<Ch999UserVo> list) {
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        List<AreaInfo> allArea = areaInfoClient.listAll().getData();
        List<RoleSimpleVo> roles = userInfoClient.getRoleNames(list.parallelStream().map(Ch999UserVo::getMainRole).distinct().collect(Collectors.toList())).getData();
        return list.stream().map(e -> {
            AreaInfo areaInfo = allArea.stream().filter(p -> p.getId().equals(e.getArea1id())).findFirst().orElse(null);
            RoleSimpleVo role = roles.stream().filter(p -> p.getId().equals(e.getMainRole())).findFirst().orElse(null);
            return new UserSimpleInfoRes(e.getCh999Id(), e.getCh999Name(),
                    areaInfo == null ? "" : areaInfo.getArea(), areaInfo == null ? "" : areaInfo.getAreaName(), role == null ? "" : role.getName());
        }).collect(Collectors.toList());
    }

    private void buildShouhouYuyue(ShouhouYuyueReq param, ShouhouYuyue shouhouYuyue) {
        BeanUtils.copyProperties(param, shouhouYuyue);
        if(ObjectUtil.isNull(param.getId())){
             shouhouYuyue.setUsername(param.getUserName());
             shouhouYuyue.setUserid(param.getUserId());
        }
        shouhouYuyue.setIsmobile(param.getIsMobile());
        shouhouYuyue.setStats(param.getStatus());
        shouhouYuyue.setKind(param.getDealWay());
        Optional.ofNullable(param.getAddressInfo()).ifPresent(item->{
            shouhouYuyue.setIsQuJianddress(item.getIsQuJianddress());
        });
        shouhouYuyue.setStype(param.getServicesWay());
        shouhouYuyue.setPpriceid(param.getPpid());
        shouhouYuyue.setProductId(param.getPid());
        shouhouYuyue.setName(param.getProductName());
        shouhouYuyue.setColor(param.getProductColor());
        shouhouYuyue.setAreaid(param.getAreaId());
        shouhouYuyue.setArea(null);
        shouhouYuyue.setStime(param.getStartTime());
        shouhouYuyue.setEtime(param.getEndTime());
        if(ObjectUtil.defaultIfNull(shouhouYuyue.getKdtype(),-1) == 1 && param.getKdtime() != null
                && YuYueSTypeEnum.YJSX.getCode().equals(shouhouYuyue.getStype())){
            shouhouYuyue.setKdtime(param.getKdtime());
        }
        shouhouYuyue.setTuidata(JSONObject.toJSONString(param.getTuidata()));
        if (CollectionUtils.isNotEmpty(param.getWxpjList())) {
            List<YuyuePpidsInfo> yuyuePpidsInfos = param.getWxpjList().stream().map(e -> {
                YuyuePpidsInfo yuyuePpidsInfo = new YuyuePpidsInfo();
                BeanUtils.copyProperties(e, yuyuePpidsInfo);
                yuyuePpidsInfo.setTroubleDes(e.getFaultDes());
                return yuyuePpidsInfo;
            }).collect(Collectors.toList());
            String yuyuePpids = JSON.toJSONString(yuyuePpidsInfos);
            shouhouYuyue.setYuyuePPids(yuyuePpids);
        }
        if (CommenUtil.isNullOrZero(param.getId())) {
            shouhouYuyue.setDtime(LocalDateTime.now());
        }
    }


    private void saveAddinfops(ShouhouYuyueReq param, Integer shouhouId) {

        AddinfopsBo addinfops = new AddinfopsBo();
        BeanUtils.copyProperties(param, addinfops);
        if (param.getServicesWay().equals(ServicesWayEnum.SMQJ.getCode())) {
            addinfops.setIsQuJianddress(true);
        }
        addinfopsService.saveAddinfops(param.getId(), shouhouId, param.getServicesWay(), param.getMobile(), addinfops);

    }

    /**
     * 生成顺丰单
     */
    private R<String> createSfOrder(ShouhouYuyue yy, Integer qCityId, String qAddress) {
        OaUserBO oaUserBO = currentRequestComponent.getCurrentStaffId();
        SfOrderInfoBo.OrderInfo item = new SfOrderInfoBo.OrderInfo();
        item.setDocallFlag(true);
        item.setDocall(1);
        item.setHHTwaydil(1);
        item.setCargo_total_weight("0");
        item.setCustid("8711353857");
        item.setD_company("云南九机科技有限公司");
        item.setD_province("云南省");
        item.setD_city("昆明市");
        item.setD_county("五华区");
        item.setD_address("云南省昆明市五华区学府路690号金鼎科技园3号平台九机科技");
        item.setD_contact("严女士");
        item.setD_mobile("15608807767");
        item.setD_tel("15608807767");
        item.setDestcode("");
        item.setExpress_type("1");
        ShouhouSendaddress addressinfo = shouhouSendaddressService.getShouhouSendaddress(yy.getId(), 1);
        if (addressinfo != null) {
            R<AreaListSimpleRes> areaListSimpleResR = areaListClient.getAreaDetailByCityId(addressinfo.getCityid());
            if (ResultCode.SUCCESS == areaListSimpleResR.getCode() && areaListSimpleResR.getData() != null) {
                AreaListSimpleRes areaListSimpleRes = areaListSimpleResR.getData();
                item.setD_province(areaListSimpleRes.getPname());
                item.setD_city(areaListSimpleRes.getZname());
                item.setD_county(areaListSimpleRes.getDname());
                if (oaUserBO.getXTenant() >= 1000) {
                    item.setD_address(addressinfo.getAddress());
                    item.setD_contact(addressinfo.getRecover());
                    item.setD_mobile(addressinfo.getPhone());
                    item.setD_tel(addressinfo.getPhone());
                }
            }
            item.setD_address(addressinfo.getAddress());
            item.setD_contact(addressinfo.getRecover());
            item.setD_mobile(addressinfo.getPhone());
            item.setD_tel(addressinfo.getPhone());
        }

        R<AreaListSimpleRes> areaListSimpleResR1 = areaListClient.getAreaDetailByCityId(qCityId);
        if (ResultCode.SUCCESS == areaListSimpleResR1.getCode() && areaListSimpleResR1.getData() != null) {
            AreaListSimpleRes citem = areaListSimpleResR1.getData();
            //省
            item.setJ_province(citem.getPname());
            //市
            item.setJ_city(citem.getZname());
            //区
            item.setJ_county(citem.getDname());
        }
        item.setJ_address(handleProvinceCity(item.getJ_province(),item.getJ_city(), qAddress));
        item.setJ_company("");
        item.setJ_contact(yy.getUsername());
        item.setJ_mobile(yy.getMobile());
        item.setJ_tel(yy.getMobile());

        item.setOrder_source("预约订单");
        item.setOrderid("s" + yy.getId());
        item.setOrgcode("");
        item.setParcel_quantity(1);
        item.setPay_method(2);
        item.setPaytype("2");
        item.setRemark("");
        item.setSendstarttime(DateUtil.localDateTimeToString(yy.getKdtime()));
        SfOrderInfoBo sfOrderInfoBo = new SfOrderInfoBo();
        sfOrderInfoBo.setOrderInfo(item);
        String host = sysConfigService.getValueByCode(SysConfigConstant.IN_WCF_HOST);
        String json = HttpClientUtil.post(host + SmallProRelativePathConstant.CREATE_SF_ORDER_URL, JSON.toJSONString(sfOrderInfoBo), new HashMap<>());
        if (StringUtils.isNotEmpty(json)) {
            JSONObject jsonObject = JSON.parseObject(json);
            if ("1".equals(jsonObject.get("stats").toString())) {
                return R.success("生成顺丰单成功", jsonObject.get("result").toString());
            }else{
                return R.error(StrUtil.format("生成顺丰单失败,原因: {}",jsonObject.getString("result")));
            }
        }
        return R.error("生成顺丰单失败,返回结果为空");
    }

    private String handleProvinceCity(String j_province, String j_city, String qAddress) {
        int len = StrUtil.totalLength(j_province,j_city);
        String matchStr = StrUtil.subPre(qAddress, len);
        StringBuilder qAddressBuild = new StringBuilder(qAddress);
        if(StrUtil.isNotBlank(j_city) && !StrUtil.contains(matchStr,j_city)){
            qAddressBuild.insert(0, j_city);
            qAddressBuild.insert(0, j_province);
        }else if(StrUtil.isNotBlank(j_province) && !StrUtil.startWith(matchStr,j_province)){
            qAddressBuild.insert(0, j_province);
        }
        return qAddressBuild.toString();
    }


    @Override
    public boolean update(Wrapper<ShouhouYuyue> updateWrapper) {
        return retBool(baseMapper.update(null, updateWrapper));
    }

    private R checkShouhouYuyueReqPara(ShouhouYuyueReq para) {
        if (para == null) {
            return R.error("请求参数为空");
        }

        if (StringUtils.isBlank(para.getMobile()) || para.getMobile().length() > 11) {
            return R.error("客户手机号有误");
        }
        if (StringUtils.isBlank(para.getUserName())) {
            return R.error("客户名称不能为空");
        }

        if (StringUtils.isBlank(para.getProblem())) {
            return R.error("故障描述不能为空");
        }
        //大件机型和串号都不为空
        if (para.getIsMobile() == null) {
            return R.error("大小件标识不能为空");
        }
        if (para.getIsMobile()) {
            if (StringUtils.isBlank(para.getProductName()) || StringUtils.isBlank(para.getImei())) {
                return R.error("机型或串号不能为空");
            }
        }
        if (para.getAreaId() == null || para.getAreaId() == 0) {
            return R.error("地区不能为空");
        }
        if (para.getDealWay() == null || para.getDealWay() == 0) {
            return R.error("处理方式不能为空");
        }
        if (para.getServicesWay() == null || para.getServicesWay() == 0) {
            return R.error("服务方式不能为空");
        }

        return R.success("数据检验通过");
    }


    @Override
    public R<Boolean> setShouhouyuyueFromSource(ShouhouyuyueFromSource shouhouyuyueFromSource) {
        Integer yuyueId = shouhouyuyueFromSource.getYuyueId();
        if(ShouhouyuyueFromSourceEnum.YIXIUGE.getCode().equals(shouhouyuyueFromSource.getFromSource())){
            //获取实际会员号码信息增加到日志
            yuyueLogsService.yuyueLogsAdd(yuyueId, StrUtil.format("会员联系号码: {}", baseMapper.getUserMobileByYuyueId(yuyueId)), "系统", YuyueLogViewTypeEnum.WZ_NO_SHOUW.getCode());
        }
        return R.success(lambdaUpdate().eq(ShouhouYuyue::getId, yuyueId)
                .eq(ObjectUtil.defaultIfNull(shouhouyuyueFromSource.getUserId(), 0) >0,
                        ShouhouYuyue::getUserid, shouhouyuyueFromSource.getUserId())
                .set(ShouhouYuyue::getFromSource, shouhouyuyueFromSource.getFromSource()).update());
    }


}
