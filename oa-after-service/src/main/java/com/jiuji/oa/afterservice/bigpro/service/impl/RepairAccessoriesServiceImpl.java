package com.jiuji.oa.afterservice.bigpro.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.ch999.common.util.vo.Result;
import com.jiuji.cloud.after.enums.ShouHouLogTypeEnum;
import com.jiuji.oa.afterservice.api.bo.Ch999UserBasicBO;
import com.jiuji.oa.afterservice.apollo.ApolloEntity;
import com.jiuji.oa.afterservice.bigpro.bo.BindPpidInfoBo;
import com.jiuji.oa.afterservice.bigpro.bo.ShouhouLogNoticeBo;
import com.jiuji.oa.afterservice.bigpro.bo.WxFeeBo;
import com.jiuji.oa.afterservice.bigpro.dao.RepairAccessoriesMapper;
import com.jiuji.oa.afterservice.bigpro.dao.ShouhouExMapper;
import com.jiuji.oa.afterservice.bigpro.dao.ShouhouMapper;
import com.jiuji.oa.afterservice.bigpro.entity.ProductSn;
import com.jiuji.oa.afterservice.bigpro.entity.ProductXtenantInfo;
import com.jiuji.oa.afterservice.bigpro.entity.Productbarcode;
import com.jiuji.oa.afterservice.bigpro.enums.*;
import com.jiuji.oa.afterservice.bigpro.po.*;
import com.jiuji.oa.afterservice.bigpro.service.*;
import com.jiuji.oa.afterservice.bigpro.statistics.service.ShouhouStatisticsService;
import com.jiuji.oa.afterservice.bigpro.statistics.vo.res.SearchProductVO;
import com.jiuji.oa.afterservice.bigpro.vo.ProductColorRes;
import com.jiuji.oa.afterservice.bigpro.vo.req.*;
import com.jiuji.oa.afterservice.bigpro.vo.res.*;
import com.jiuji.oa.afterservice.cloud.service.WebCloud;
import com.jiuji.oa.afterservice.common.bo.OaUserBO;
import com.jiuji.oa.afterservice.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.afterservice.common.enums.XtenantEnum;
import com.jiuji.oa.afterservice.common.exception.CustomizeException;
import com.jiuji.oa.afterservice.config.service.ShouhouPpidBindService;
import com.jiuji.oa.afterservice.other.enums.ProductLabelEnum;
import com.jiuji.oa.afterservice.stock.po.ProductKc;
import com.jiuji.oa.afterservice.stock.service.ProductKcService;
import com.jiuji.oa.orginfo.sysconfig.client.SysConfigClient;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.utils.common.DecideUtil;
import com.jiuji.tc.utils.constants.NumberConstant;
import com.jiuji.tc.utils.constants.SysConfigConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class RepairAccessoriesServiceImpl implements RepairAccessoriesService {

    @Resource
    private ApolloEntity apolloEntity;
    @Resource
    private RepairAccessoriesMapper mapper;

    @Resource
    private ProductinfoService productinfoService;
    @Resource
    private WebCloud webCloud;
    @Resource
    private ProductXtenantInfoService productXtenantInfoService;
    @Resource
    private ProductbarcodeService barcodeService;
    @Resource
    private WxproductconfigService wxproductconfigService;
    @Resource
    private SysConfigClient sysConfigClient;
    @Resource
    private ShouhouMapper shouhouMapper;
    @Resource
    private ProductKcService kcService;
    @Resource
    private ShouhouService shouhouService;
    @Resource
    private ShouhouPpidBindService shouhouPpidBindService;
    @Resource
    private ShouhouPpidDictService shouhouPpidDictService;
    @Resource
    private AbstractCurrentRequestComponent currentRequestComponent;
    @Resource
    private ShouhouApplyService shouhouApplyService;
    @Resource
    private SmsService smsService;
    @Resource
    private ShouhouLogsService shouhouLogsService;
    @Resource
    private ShouhouExMapper shouhouExMapper;
    @Resource
    private ProductKcService productKcService;
    @Resource
    private ShouhouServiceConfigService shouhouServiceConfigService;
    @Resource
    private ProductSnService snService;



    private static final Integer LIST_MAX_SIZE = 2000;



    @Override
    public AccessoriesDetailsRes selectAccessoriesDetails(AccessoriesDetailsReq req) {
        AccessoriesDetailsRes accessoriesDetailsRes = new AccessoriesDetailsRes();
        Integer ppid = req.getPpid();
        Integer currentAreaId = req.getCurrentAreaId();
        Long productId = req.getProductId();
        //封装商品基本信息
        accessoriesDetailsRes.setBaseProduct(createAccessoriesBaseProduct(req));
        //封装售后服务
        accessoriesDetailsRes.setServiceInfoList(getServiceInfoList(productId,Collections.singletonList(ppid)));
        //封装绑定配件
        accessoriesDetailsRes.setBindPpidKcInfo(createBindPpidKcInfo(ppid,currentAreaId));
        //封装库存信息
        List<ProductKc> list = kcService.lambdaQuery().eq(ProductKc::getPpriceid, ppid).eq(ProductKc::getAreaid, currentAreaId) .list();
        if(CollectionUtils.isNotEmpty(list)){
            accessoriesDetailsRes.setProductKc(list.get(NumberConstant.ZERO));
        } else {
            accessoriesDetailsRes.setProductKc(new ProductKc());
        }
        return accessoriesDetailsRes;
    }

    /**
     * 获取绑定配件
     * @param ppid
     * @param currentAreaId
     * @return
     */
    @Override
    public List<BindPpidKcInfo> createBindPpidKcInfo(Integer ppid,Integer currentAreaId){
        List<BindPpidKcInfo> bindPpidKcInfo = Optional.ofNullable(shouhouMapper.getBindPpidKcInfo(ppid, XtenantEnum.getXtenant(), currentAreaId)).orElse(new ArrayList<>());
        //过滤leftcount为0的数据
        bindPpidKcInfo = bindPpidKcInfo.stream().filter(item->Optional.ofNullable(item.getLeftCount()).orElse(NumberConstant.ZERO)>0).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(bindPpidKcInfo)){
            //收集没有name的商品  然后通过商品查询来进行赋值
            List<Integer> ppidList = bindPpidKcInfo.stream().filter(item -> StringUtils.isEmpty(item.getName())).map(BindPpidKcInfo::getPpid).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(ppidList)){
                Map<Integer, Productinfo> map = productinfoService.getProductMapByPpids(ppidList);
                bindPpidKcInfo.forEach(item->{
                    if(StringUtils.isEmpty(item.getName())){
                        Productinfo productinfo = map.getOrDefault(item.getPpid(),new Productinfo());
                        item.setName(Optional.ofNullable(productinfo.getProductName()).orElse("")+productinfo.getProductColor());
                    }
                });
            }
        }
        return bindPpidKcInfo;
    }

    /**
     * 主站获取套餐信息
     * @param ppidList
     * @return
     */
    @Override
    public List<ShouhouServiceConfig> getServiceInfoList(Long productId,List<Integer> ppidList){


        return DecideUtil.iif(XtenantEnum.isJiujiXtenant(),
                ()->shouhouServiceConfigService.getShouhouServiceConfigs(Optional.ofNullable(productId).orElse(0L),ppidList).getData(),
                ()->shouhouServiceConfigService.getShouhouServicesList(ppidList));
//        if (CollectionUtils.isNotEmpty(fuwuConfigList)) {
//            fuwuConfig = JSON.toJSONString(fuwuConfigList);
//        }
//        Map<Integer, List<RepairServiceInfo>> map = new HashMap<>();
//        if(CollectionUtil.isEmpty(ppidList)){
//            return map;
//        }
//        String host = Optional.ofNullable(sysConfigClient.getValueByCode(SysConfigConstant.M_URL)).map(R::getData).filter(StringUtils::isNotEmpty).orElseThrow(() -> new CustomizeException("获取域名出错"));
//        String evidenceUrl = host + "/web/api/product/opening/batchGetServicePrice/v1";
//        HttpResponse evidenceResult = HttpUtil.createPost(evidenceUrl).body(JSONUtil.toJsonStr(ppidList)).execute();
//        if(evidenceResult.isOk()){
//            log.warn("调用主站获取服务信息传入参数：{}，返回结果：{}",evidenceUrl,evidenceResult.body());
//            R result = JSONUtil.toBean(JSONUtil.toJsonStr(evidenceResult.body()), R.class);
//            if(result.isSuccess()){
//                Object data = result.getData();
//                if(ObjectUtil.isNull(data)){
//                    throw new CustomizeException("调用主站获取服务返回数据为空");
//                } else {
//                    List<ServiceInfoRes> serviceInfoRes = JSONUtil.toList(JSONUtil.toJsonStr(data), ServiceInfoRes.class);
//                    if(CollectionUtil.isNotEmpty(serviceInfoRes)){
//                        map = serviceInfoRes.stream().collect(Collectors.toMap(ServiceInfoRes::getPpid, ServiceInfoRes::getServiceList,(n1,n2)->n2));
//                    }
//                }
//            } else {
//                throw new CustomizeException("调用主站获取服务信息失败："+Optional.ofNullable(result.getUserMsg()).orElse(result.getMsg()));
//            }
//        } else {
//            log.warn("调用主站获取服务信息传入参数：{}",JSONUtil.toJsonStr(ppidList));
//            throw new CustomizeException("调用主站获取服务信息接口异常");
//        }
//        return map;
    }


    /**
     * 封装商品基本信息
     * @param ppid
     * @return
     */
    private AccessoriesBaseProduct createAccessoriesBaseProduct(AccessoriesDetailsReq req){
        Integer ppid = req.getPpid();
        AccessoriesBaseProduct accessoriesBaseProduct = new AccessoriesBaseProduct();
        Integer xtenant = XtenantEnum.getXtenant();
        //获取商品基本信息
        Productinfo productinfo = Optional.ofNullable(productinfoService.getProductinfoByPpid(ppid)).orElseThrow(() -> new CustomizeException("ppid无效"));
        BeanUtils.copyProperties(productinfo,accessoriesBaseProduct);
        //设置会员价格
        accessoriesBaseProduct.setPrice(productinfo.getMemberprice());
        //设置成本价格
        List<ProductKc> productKcList = productKcService.lambdaQuery().eq(ProductKc::getAreaid, req.getCurrentAreaId())
                .eq(ProductKc::getPpriceid, ppid)
                .list();
        if(CollectionUtils.isNotEmpty(productKcList)){
            ProductKc productKc = Optional.ofNullable(productKcList.get(0)).orElse(new ProductKc());
            accessoriesBaseProduct.setInprice(Optional.ofNullable(productKc.getInprice()).orElse(productinfo.getCostprice()));
        } else {
            accessoriesBaseProduct.setInprice(productinfo.getCostprice());
        }
        //获取商品图片
        Map<Integer, PictureInfo> pictureInfoMap = productinfoService.selectPictureByEntityMap(Collections.singletonList(productinfo));
        PictureInfo pictureInfo = pictureInfoMap.getOrDefault(ppid, new PictureInfo());
        BeanUtils.copyProperties(pictureInfo,accessoriesBaseProduct);
        //获取商品标签
        List<ProductXtenantInfo> list = productXtenantInfoService.lambdaQuery().eq(ProductXtenantInfo::getXtenant,xtenant )
                .eq(ProductXtenantInfo::getPpriceid, ppid)
                .list();
        if(CollectionUtils.isNotEmpty(list)){
            ProductXtenantInfo productXtenantInfo = list.get(0);
            Integer productLabel = productXtenantInfo.getProductLabel();
            accessoriesBaseProduct.setProductLabel(productLabel)
                    .setProductLabelValue(ProductLabelEnum.getMessage(productLabel));
        }
        //获取商品条码
        List<Productbarcode> productbarcodeList = barcodeService.lambdaQuery().eq(Productbarcode::getOpXtenant, xtenant)
                .eq(Productbarcode::getPpriceid, ppid)
                .list();
        accessoriesBaseProduct.setProductbarcodeList(productbarcodeList);
        //获取注意事项
        List<Wxproductconfig> wxproductconfigs = wxproductconfigService.lambdaQuery().eq(Wxproductconfig::getPpriceid, ppid).list();
        if(CollectionUtils.isNotEmpty(wxproductconfigs)){
            accessoriesBaseProduct.setDescripion(wxproductconfigs.stream().map(Wxproductconfig::getDescripion)
                    .filter(StringUtils::isNotEmpty)
                    .collect(Collectors.joining(",")));
        }
        //判断商品是否可以负库存出库
        accessoriesBaseProduct.setNegativeInventory(productKcService.negativeInventory(ppid));
        return accessoriesBaseProduct;
    }


    /**
     * 规格选择组件
     * @param req
     * @return
     */
    @Override
    public ProductColorRes selectProductColor(ProductColorReq req) {
        ProductColorRes productColorRes = new ProductColorRes();
        List<ProductColorInfo> colorInfoList = mapper.selectProductColorInfo(req.getProductId());
        if(CollectionUtils.isEmpty(colorInfoList)){
            return productColorRes;
        }
        //colorInfoList根据standId进行分组
        Map<String, List<ProductColorInfo>> nameMap = colorInfoList.stream().collect(Collectors.groupingBy(ProductColorInfo::getName));
        if(CollectionUtils.isNotEmpty(nameMap)){
            List<StandGroup> standGroups = new ArrayList<>();
            nameMap.forEach((k,v)->{
                StandGroup standGroup = new StandGroup();
                standGroup.setColorName(k);
                standGroup.setStandId(v.get(NumberConstant.ZERO).getStandId());
                standGroup.setDetailIdList(v.stream().map(item->{
                    StandGroupDetail standGroupDetail = new StandGroupDetail();
                    standGroupDetail.setDetailId(item.getDetailid())
                            .setValue(item.getValue());
                    return standGroupDetail;
                }).distinct().collect(Collectors.toList()));
                standGroups.add(standGroup);
            });
            productColorRes.setStandGroupList(standGroups.stream().sorted(Comparator.comparing(StandGroup::getStandId)).collect(Collectors.toList()));
        }
        Map<Integer, List<ProductColorInfo>> ppidMap = colorInfoList.stream().collect(Collectors.groupingBy(ProductColorInfo::getPpriceid));
        if(CollectionUtils.isNotEmpty(ppidMap)){
            List<StandGroupPpid> groupPpids = new ArrayList<>();
            ppidMap.forEach((k,v)->{
                StandGroupPpid groupPpid = new StandGroupPpid();
                groupPpid.setPpid(k);
                groupPpid.setDetailIdList(v.stream().sorted(Comparator.comparing(ProductColorInfo::getStandId)).map(ProductColorInfo::getDetailid).distinct().collect(Collectors.toList()));
                groupPpids.add(groupPpid);
            });
            productColorRes.setStandGroupPpidList(groupPpids);
        }
        return productColorRes;
    }

    /**
     * 添加维修配件校验
     * @param costPriceNewReq
     */
    private void checkDate(CostPriceNewReq costPriceNewReq) {
        List<WxFeeBo> wxFeeBoList = costPriceNewReq.getWxFeeBoList();
        if(CollectionUtils.isEmpty(wxFeeBoList)){
            throw new CustomizeException("提交维修配件数据不能为空");
        }
         //过滤 isService  = 0  的 WxFeeBo （只看主商品的）
         wxFeeBoList.stream().filter(item -> !Boolean.TRUE.equals(item.getIsService())).forEach(item -> {
             Productinfo productinfo = Optional.ofNullable(productinfoService.getProductinfoByPpid(item.getPpid())).orElse(new Productinfo());
             Boolean isSn = productinfo.getIsSn();
             item.setIsSn(isSn);
             //需要绑定sn的商品才进行校验
             if(Boolean.TRUE.equals(isSn)){
                 // 判断 sn 不能为空
                 String sn = Optional.ofNullable(item.getSn())
                         .filter(StrUtil::isNotEmpty)
                         .orElseThrow(() -> new CustomizeException("请先绑定维修配件SN码"));
                 //如果sn 不为空那就判断sn绑定是否正确
                 List<ProductSn> snList = snService.lambdaQuery().eq(ProductSn::getSn, sn).eq(ProductSn::getPpid, item.getPpid())
                         .and(wrapper -> wrapper.eq(ProductSn::getIsDel, false).or().isNull(ProductSn::getIsDel))
                         .in(ProductSn::getBindState, Arrays.asList(BindStateEnum.UNBOUND.getCode(), BindStateEnum.UNBIND.getCode()))
                         .eq(ProductSn::getSnType, SnTypeEnum.WEI_XIU.getCode())
                         .list();
                 if(CollUtil.isEmpty(snList)){
                     throw new CustomizeException("维修配件SN码已绑定或不存在");
                 }
             }
         });
    }

    /**
     * 新维修配件按添加
     * @param costPriceNewReq
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public CostPriceNewRes addCostPriceNew(CostPriceNewReq costPriceNewReq) {
        CostPriceNewRes costPriceNewRes = new CostPriceNewRes();
        checkDate(costPriceNewReq);
        List<WxFeeBo> wxFeeBoList = costPriceNewReq.getWxFeeBoList();
        OaUserBO oaUserBO = currentRequestComponent.getCurrentStaffId();
        Integer bindId = null;
        for(WxFeeBo wxFeeBo: wxFeeBoList){
            wxFeeBo.setAreaId(oaUserBO.getAreaId());
            wxFeeBo.setUser(oaUserBO.getUserName());
            wxFeeBo.setXtenant(oaUserBO.getXTenant());
            wxFeeBo.setXtenant(oaUserBO.getXTenant());
            if(ObjectUtil.isNull(wxFeeBo.getBindId())){
                wxFeeBo.setBindId(bindId);
            }
            R<ShouhouCostPriceRes> shcpRes = shouhouService.addCostPrice(wxFeeBo, Boolean.FALSE, Boolean.TRUE);
            if(!shcpRes.isSuccess()){
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                throw new CustomizeException(Optional.ofNullable(shcpRes.getUserMsg()).orElse(shcpRes.getMsg()));
            }
            //如果不是服务
            if(Optional.ofNullable(wxFeeBo.getIsService()).orElse(NumberConstant.ZERO).equals(NumberConstant.ZERO)){
                bindId = wxFeeBo.getId();
            }
            //处理维修配件
            List<BindPpidInfoBo> bindPpidInfos = wxFeeBo.getBindPpidInfos();
            if(CollectionUtils.isNotEmpty(bindPpidInfos)){
                //数据处理
                bindPpidInfos.forEach(item->{
                    //设置出库数量默认为 1
                    item.setOutPutNumber(Optional.ofNullable(item.getOutPutNumber()).orElse(NumberConstant.ONE));
                });
                R<List<ShouhouCostPriceRes>> listR = shouhouService.addBindCostPrice(wxFeeBo, false, Boolean.TRUE, true);
                if(!listR.isSuccess()){
                    TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                    throw new CustomizeException(Optional.ofNullable(listR.getUserMsg()).orElse(listR.getMsg()));
                }
            }
        }
        //解锁收银lock
        WxFeeBo feeBo = Optional.ofNullable(wxFeeBoList.get(NumberConstant.ZERO)).orElse(new WxFeeBo());
        Integer shouhouId = feeBo.getShouhouId();
        SpringUtil.getBean(ShouhouMapper.class).unShouYinLock(shouhouId);
        //添加商品之后进行sn的绑定
        if(XtenantEnum.isJiujiXtenant()){
            wxFeeBoList.stream().filter(item -> NumberConstant.ZERO.equals(Optional.ofNullable(item.getIsService()).orElse(NumberConstant.ZERO))
                            && Boolean.TRUE.equals(item.getIsSn()))
                    .findFirst()
                    .ifPresent(wxFeeBo->{
                ProductSnUpdateBindReq req = new ProductSnUpdateBindReq();
                req.setSn(wxFeeBo.getSn())
                        .setPpid(wxFeeBo.getPpid())
                        .setBasketId(wxFeeBo.getId())
                        .setOperationType(OperationTypeEnum.ADD.getCode());
                R<Boolean> booleanR = snService.updateBasketIdAndUnbind(req);
                if(!booleanR.isSuccess()){
                    TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                    throw new CustomizeException(Optional.ofNullable(booleanR.getUserMsg()).orElse(booleanR.getMsg()));
                }
            });
        }
        return costPriceNewRes;
    }


    @Override
    public R<String> applyDel(ApplyDelReq req) {
        OaUserBO userBO = Optional.ofNullable(currentRequestComponent.getCurrentStaffId()).orElseThrow(() -> new CustomizeException("请登录"));
        ShouhouApply shouhouApply = Optional.ofNullable(shouhouApplyService.getById(req.getId())).orElseThrow(() -> new CustomizeException("不存在该订购单"));
        List<Integer> statList = Arrays.asList(ShouhouCaigouTypeEnum.WAITING_FOR_PROCUREMENT.getCode(),
                ShouhouCaigouTypeEnum.TRANSFER_IN_PROGRESS.getCode(),
                ShouhouCaigouTypeEnum.IN_PROCUREMENT.getCode(),
                ShouhouCaigouTypeEnum.PENDING_REVIEW.getCode());
        if(!statList.contains(shouhouApply.getKindstats())){
            throw new CustomizeException("只有订购单"+ShouhouCaigouTypeEnum.PENDING_REVIEW.getMessage()+"状态下才能删除");
        }

        String host = Optional.ofNullable(sysConfigClient.getValueByCode(SysConfigConstant.MOA_URL)).map(R::getData).filter(StringUtils::isNotEmpty).orElseThrow(() -> new CustomizeException("获取域名出错"));
        String evidenceUrl = host + "/oa/shouhou/shouhouApplyDel";
        Map<String, Object> param = new HashMap<>();
        param.put("id",req.getId());
        param.put("comment",req.getComment());
        HttpResponse evidenceResult = HttpUtil.createPost(evidenceUrl)
                .header("authorization", userBO.getToken())
                .body(JSONUtil.toJsonStr(param))
                .execute();
        log.warn("维修订购删除接口传入参数：{}，返回结果：{}，传入userBO:{}",param,evidenceResult.body(),JSONUtil.toJsonStr(userBO));
        if(evidenceResult.isOk()){
            R result = JSONUtil.toBean(JSONUtil.toJsonStr(evidenceResult.body()), R.class);
            if(!result.isSuccess()){
                throw new CustomizeException("维修订购删除接口调用失败："+Optional.ofNullable(result.getUserMsg()).orElse(result.getMsg()));
            }
        } else {
            log.warn("维修订购删除接口调用异常传入参数：{}",evidenceUrl);
            throw new CustomizeException("维修订购删除接口调用异常");
        }
        //取消大疆维修绑定
        RepairPlanService planService = SpringUtil.getBean(RepairPlanService.class);
        planService.cancelBind(req.getId(), CorrelationTypeEnum.SHOUHOU_APPLY.getCode(), userBO.getUserName());

        return R.success("删除成功");
    }


    /**
     * list
     * 第一：如果过为空那就是全部门店使用，
     * 第二：如果存在具体门店id那就这几个门店id使用
     * 第三：如果只存在-1 那就是所有门店不使用
     * @return
     */
    @Override
    public List<Integer> usingStores() {
        List<Integer> list = new ArrayList<>();
        if (XtenantEnum.isSaasXtenant()) {
            list.add(-1);
        } else {
            String areaIdList = Optional.ofNullable(apolloEntity.getRepairAccessoriesAreaId()).orElse("");
            list = Arrays.stream(areaIdList.split(",")).filter(StringUtils::isNotEmpty).map(Integer::valueOf).collect(Collectors.toList());
        }
        return list;
    }


    /**
     * list
     * 第一：如果过为空那就是全部门店使用，
     * 第二：如果存在具体门店id那就这几个门店id使用
     * 第三：如果只存在-1 那就是所有门店不使用
     * @return
     */
    @Override
    public List<Integer> selectNewShouHouInfo() {
        List<Integer> list = new ArrayList<>();
        if (XtenantEnum.isSaasXtenant()) {
            list.add(-1);
        } else {
            String areaIdList = Optional.ofNullable(apolloEntity.getRepairOrderNewAreaId()).orElse("");
            list = Arrays.stream(areaIdList.split(",")).filter(StringUtils::isNotEmpty).map(Integer::valueOf).collect(Collectors.toList());
        }
        return list;
    }

    /**
     * 维修配件查询
     * @param req
     * @return
     */
    @Override
    public RepairAccessoriesRes searchRepairAccessories(RepairAccessoriesReq req) {
        //获取查询key的类型
        req.setKeyType(productinfoService.getKeyType(req.getKey()));
        //查询商品相关的维修配件
        req.setMaintenanceAccessoriesProductIdList(queryAndSetProductIds(req.getProductMaintenance()));
        //设置相关租户
        req.setXtenant(XtenantEnum.getXtenant());
        //参数封装
        RepairAccessoriesRes repairAccessoriesRes = new RepairAccessoriesRes();
        List<RepairAccessoriesDetailRes> repairAccessoriesDetailRes = mapper.searchProductKC(req);
        repairAccessoriesRes.setDetails(repairAccessoriesDetailRes);
        return repairAccessoriesRes;
    }

    @Override
    public List<PartsAssociationRes> partsAssociation(String key) {
        SelectKeyType keyType = productinfoService.getKeyType(key);
        return mapper.selectPartsAssociation(keyType);
    }

    @Override
    public List<PartsAssociationRes> selectProductByKey(PartsAssociationReq req) {
        SelectKeyType keyType = productinfoService.getKeyType(req.getKey());
        req.setType(keyType.getType())
                .setValue(keyType.getValue());
        return mapper.selectProductByKey(req);
    }


    /**
     * 查询商品相关的维修配件
     * @param productMaintenance
     * @return
     */
    private List<Integer> queryAndSetProductIds(String productMaintenance) {
        List<Integer> productIdList = new ArrayList<>();
        SelectKeyType keyType = productinfoService.getKeyType(productMaintenance);
        Integer type = keyType.getType();
        String value = keyType.getValue();
        //处理商品id的情况
        if (RepairAccessoriesKeyEnum.getProductIdList().contains(type) && StringUtils.isNotEmpty(value)) {
            Result<List<Integer>> productResult = webCloud.getProductRepirParts(Integer.valueOf(value));
            if(productResult.isSuccess()){
                productIdList = productResult.getData();
            }
        }else if(RepairAccessoriesKeyEnum.PRODUCT_NAME.getCode().equals(type)&& StringUtils.isNotEmpty(value)){
            //查询产品信息
            SearchProductVO.Req spReq = new SearchProductVO.Req().setWorld(value);
            spReq.setSize(NumberConstant.ONE_HUNDRED);
            List<Integer> productIds = SpringUtil.getBean(ShouhouStatisticsService.class).searchProduct(spReq)
                    .stream().map(SearchProductVO::getPid).distinct().collect(Collectors.toList());
            Optional<List<Integer>> productIdsOpt = Optional.ofNullable(webCloud.getMapPIdsByPpids(productIds)).map(Result::getData).map(Map::values)
                    .map(values -> values.stream().flatMap(vs -> vs.stream()).collect(Collectors.toList())).filter(CollUtil::isNotEmpty);
            if(productIdsOpt.isPresent()){
                productIdList = productIdsOpt.get();
            }
        }
        //截取 防止数据in超长
        if(productIdList.size() >LIST_MAX_SIZE ){
            productIdList = productIdList.stream().limit(LIST_MAX_SIZE).collect(Collectors.toList());
        }
        return productIdList;
    }



}
