package com.jiuji.oa.afterservice.csharp.returns.service.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.http.Header;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.jiuji.oa.afterservice.bigpro.service.SmsService;
import com.jiuji.oa.afterservice.common.bo.OaUserBO;
import com.jiuji.oa.afterservice.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.afterservice.common.exception.CustomizeException;
import com.jiuji.oa.afterservice.common.exception.RRExceptionHandler;
import com.jiuji.oa.afterservice.common.util.SpringContextUtil;
import com.jiuji.oa.afterservice.csharp.returns.service.CsharpReturnService;
import com.jiuji.oa.afterservice.csharp.returns.vo.*;
import com.jiuji.oa.afterservice.sys.service.SysConfigService;
import com.jiuji.oa.orginfo.sysconfig.client.SysConfigClient;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.common.vo.ResultCode;
import com.jiuji.tc.utils.constants.NumberConstant;
import com.jiuji.tc.utils.constants.SysConfigConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.HttpCookie;
import java.time.Duration;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicReference;

/**
 * c# 退款服务接口
 * <AUTHOR>
 * @since 2022/7/20 17:35
 */
@Service
@Slf4j
public class CsharpReturnServiceImpl implements CsharpReturnService {
    @Resource
    private SysConfigClient sysConfigClient;
    @Resource
    private SysConfigService sysConfigService;
    @Resource
    private SmsService smsService;

    /**
     * 获取符合条件的退款方式列表
     * @param returnWayReq
     * @return
     */
    @Override
    public R<List<String>> getReturnWayJava(@Valid ReturnWayReqVo returnWayReq) {
        //sub_id = 1 能使用微信秒退和验证
        return Optional.ofNullable(sysConfigService.getValueByCode(SysConfigConstant.IN_WCF_HOST))
                .filter(StrUtil::isNotBlank)
                .map(url -> {
                    //sub_id = 1 能使用微信秒退和验证
                    String param = JSON.toJSONString(returnWayReq);
                    String returnUrl = String.format("%s/oaApi.svc/rest/getReturnWayJava", url);
                    log.warn("获取退款方式请求地址: {},请求参数: {}",returnUrl,param);
                    HttpResponse httpResponse = HttpUtil.createPost(returnUrl)
                            .body(param).execute();
                    log.warn("获取退款方式响应结果: {}",httpResponse.body());
                    if (httpResponse.isOk()) {
                        return Optional.ofNullable(httpResponse.body()).filter(StrUtil::isNotBlank)
                                .map(json -> JSON.parseObject(json, new TypeReference<R<List<String>>>() {
                                })).orElse(null);
                    } else {
                        log.error("发生{}错误,url:{},参数:{}", httpResponse.getStatus(), url, param);
                        return new R<List<String>>(ResultCode.SERVER_ERROR, StrUtil.format("获取退款方式接口发生{}错误", httpResponse.getStatus()));
                    }
                }).orElseGet(() -> new R<>(ResultCode.SERVER_ERROR, "获取退款方式的inwcf地址异常"));
    }

    @Override
    public R<Boolean> cancelDiyOrder(CancelDiyReq cancelDiyReq) {
        String param = JSON.toJSONString(cancelDiyReq);
        R<Boolean> result = Optional.ofNullable(sysConfigService.getValueByCode(SysConfigConstant.IN_WCF_HOST))
                .filter(StrUtil::isNotBlank)
                .map(url -> {
                    //sub_id = 1 能使用微信秒退和验证
                    HttpResponse httpResponse = HttpUtil.createPost(StrUtil.format("{}/oaapi.svc/rest/CancelDIYOrder", url))
                            .body(param).execute();
                    if (httpResponse.isOk()) {
                        return Optional.ofNullable(httpResponse.body()).filter(StrUtil::isNotBlank)
                                .map(json -> JSON.parseObject(json, new TypeReference<R<Boolean>>() {
                                })).orElse(null);
                    } else {
                        return new R<Boolean>(ResultCode.SERVER_ERROR, StrUtil.format("取消洛克订单{}错误", httpResponse.getStatus()));
                    }
                }).orElseGet(() -> new R<>(ResultCode.SERVER_ERROR, "获取取消洛克订单的inwcf地址异常"));
        if (!result.isSuccess() || ObjectUtil.notEqual(Boolean.TRUE,result.getData())){
            RRExceptionHandler.logError("取消洛克订单",param,new CustomizeException(result.getUserMsg()),smsService::sendOaMsgTo9JiMan);
        }
        return result;
    }

    @Override
    public R<Boolean> cancelWmsOrder(CancelWmsReq cancelWmsReq) {
        String param = JSON.toJSONString(cancelWmsReq);
        AtomicReference<String> urlRef = new AtomicReference<>("");
        R<Boolean> result = Optional.ofNullable(sysConfigService.getValueByCode(SysConfigConstant.IN_WCF_HOST))
                .filter(StrUtil::isNotBlank)
                .map(url -> {
                    //sub_id = 1 能使用微信秒退和验证
                    urlRef.set(StrUtil.format("{}/oaapi.svc/rest/CancelWmsOrder", url));
                    log.debug("获取退款方式请求地址: {},请求参数: {}",urlRef.get(),param);
                    HttpResponse httpResponse = HttpUtil.createPost(urlRef.get())
                            .body(param).execute();
                    log.debug("获取退款方式响应结果: {}",httpResponse.body());
                    if (httpResponse.isOk()) {
                        return Optional.ofNullable(httpResponse.body()).filter(StrUtil::isNotBlank)
                                .map(json -> JSON.parseObject(json, new TypeReference<R<Boolean>>() {
                                })).orElse(null);
                    } else {
                        return new R<Boolean>(ResultCode.SERVER_ERROR, StrUtil.format("取消wms订单{}错误", httpResponse.getStatus()));
                    }
                }).orElseGet(() -> new R<>(ResultCode.SERVER_ERROR, "获取取消wms订单的inwcf地址异常"));
        if (!result.isSuccess() || ObjectUtil.notEqual(Boolean.TRUE,result.getData())){
            RRExceptionHandler.logError("取消wms订单", Dict.create().set("cancelWmsReq",param).set("url",urlRef.get()),
                    new CustomizeException(result.getUserMsg()),smsService::sendOaMsgTo9JiMan);
        }
        return result;
    }

    /**
     * 查询微信支付状态
     * @param refundResultReq
     * @return {@link }
     */
    @Override
    public R<WechatPayResultRes> queryMiaoTuiResultTips(WechatPayResultReq refundResultReq){

        String param = JSON.toJSONString(refundResultReq);
        log.warn("参数: {}",param);
        R<WechatPayResultRes> result = Optional.ofNullable(sysConfigClient.getValueByCode(SysConfigConstant.IN_WCF_HOST)).filter(R::isSuccess)
                .map(R::getData).filter(StrUtil::isNotBlank)
                .map(url -> {
                    String method;
                    if (refundResultReq.getDetailId() != null) {
                        method = "QueryMiaoTuiResultTips";
                    } else {
                        method = "QueryWeiXinMiaoTuiResultTips";
                    }

                    String fullUrl = StrUtil.format("{}/oaapi.svc/rest/{}", url, method);
                    log.warn("请求地址: {}", fullUrl);
                    HttpResponse httpResponse = HttpUtil.createPost(fullUrl)
                            .body(param).execute();
                    log.warn("响应结果: {}", httpResponse.body());
                    if (httpResponse.isOk()) {
                        return Optional.ofNullable(httpResponse.body()).filter(StrUtil::isNotBlank)
                                .map(json -> JSON.parseObject(json, new TypeReference<R<WechatPayResultRes>>() {
                                }))
                                .orElseGet(() -> new R<>(ResultCode.SERVER_ERROR, "查询秒退支付返回空"));
                    } else {
                        return new R<WechatPayResultRes>(ResultCode.SERVER_ERROR, StrUtil.format("查询秒退支付状态{}错误", httpResponse.getStatus()));
                    }
                }).orElseGet(() -> new R<>(ResultCode.SERVER_ERROR, "获取查询秒退支付状态的inwcf地址异常"));
        if (!result.isSuccess() || result.getData() == null){
            RRExceptionHandler.logError("查询秒退支付状态",param,new CustomizeException(result.getUserMsg()),smsService::sendOaMsgTo9JiMan);
        }
        return result;
    }

    /**
     * 重新发起微信支付
     * @param retryPayReq
     * @return {@link }
     */
    @Override
    public R<Boolean> retryMiaoTuiPay(WechatRetryPayReq retryPayReq){

        String param = JSON.toJSONString(retryPayReq);
        log.warn("退换id: {},参数: {}",retryPayReq.getOrderid(),param);
        R<Boolean> result = Optional.ofNullable(sysConfigClient.getValueByCode(SysConfigConstant.IN_WCF_HOST)).filter(R::isSuccess)
                .map(R::getData).filter(StrUtil::isNotBlank)
                .map(url -> {
                    String method;
                    if (retryPayReq.getDetailId() != null) {
                        method = "GroupRefundRetryPay";
                    } else {
                        method = "RetryWeiXinPay";
                    }
                    String fullUrl = StrUtil.format("{}/oaapi.svc/rest/{}", url, method);
                    log.warn("请求地址: {}",fullUrl);
                    HttpResponse httpResponse = HttpUtil.createPost(fullUrl)
                            .body(param).execute();
                    log.warn("响应结果: {}",httpResponse.body());
                    if (httpResponse.isOk()) {
                        return Optional.ofNullable(httpResponse.body()).filter(StrUtil::isNotBlank)
                                .map(json -> JSON.parseObject(json, new TypeReference<R<Boolean>>() {}))
                                .orElse(null);
                    } else {
                        return new R<Boolean>(ResultCode.SERVER_ERROR, StrUtil.format("重新发起秒退支付{}错误", httpResponse.getStatus()));
                    }
                }).orElseGet(() -> new R<>(ResultCode.SERVER_ERROR, "获取重新发起秒退支付的inwcf地址异常"));
        if (!result.isSuccess() || ObjectUtil.notEqual(Boolean.TRUE,result.getData())){
            RRExceptionHandler.logError("重新发起秒退支付",param,new CustomizeException(result.getUserMsg()),smsService::sendOaMsgTo9JiMan);
        }
        return result;
    }

    @Override
    public R<Boolean> tuihuanDel(Integer tuiHuanId,String marck) {
        log.warn("退换id: {}",tuiHuanId);
        R<Boolean> result = Optional.ofNullable(sysConfigClient.getValueByCode(SysConfigConstant.MOA_URL)).filter(R::isSuccess)
                .map(R::getData).filter(StrUtil::isNotBlank)
                .map(url -> {
                    String fullUrl = StrUtil.format("{}/oa/shouhou/TuihuanDel/{}", url,tuiHuanId);
                    log.warn("退换id: {},请求地址: {}",tuiHuanId,fullUrl);
                    HttpResponse httpResponse = HttpUtil.createPost(fullUrl)
                            .form("marck",marck)
                            .auth(SpringContextUtil.getRequest().map(req -> req.getHeader(Header.AUTHORIZATION.getValue())).orElse(null))
                            .execute();
                    log.warn("退换id: {},响应结果: {}",tuiHuanId,httpResponse.body());
                    if (httpResponse.isOk()) {
                        return Optional.ofNullable(httpResponse.body()).filter(StrUtil::isNotBlank)
                                .map(json -> JSON.parseObject(json, new TypeReference<TuihuanRes>() {}))
                                .map(tdl -> {
                                    if(ObjectUtil.notEqual(tdl.getStats(), NumberConstant.ONE)){
                                        R<Boolean> error = R.error(tdl.getMsg());
                                        return error;
                                    }
                                    return R.success(true);
                                })
                                .orElse(null);
                    } else {
                        return new R<Boolean>(ResultCode.SERVER_ERROR, StrUtil.format("撤销退换机操作{}错误", httpResponse.getStatus()));
                    }
                }).orElseGet(() -> new R<Boolean>(ResultCode.SERVER_ERROR, "获取撤销退换机操作的inwcf地址异常"));
        return result;
    }

    @Override
    public R<Integer> refundMachineCheck1(Integer tuiHuanId) {
        log.warn("退换机一审,退换id: {}",tuiHuanId);
        R<Integer> result = Optional.ofNullable(sysConfigClient.getValueByCode(SysConfigConstant.MOA_URL)).filter(R::isSuccess)
                .map(R::getData).filter(StrUtil::isNotBlank)
                .map(url -> {
                    String fullUrl = StrUtil.format("{}/oa/shouhou/Check1/{}", url,tuiHuanId);
                    log.warn("退换id: {},请求地址: {}",tuiHuanId,fullUrl);
                    HttpResponse httpResponse = HttpUtil.createPost(fullUrl)
                            .auth(SpringContextUtil.getRequest().map(req -> req.getHeader(Header.AUTHORIZATION.getValue())).orElse(null))
                            .execute();
                    log.warn("退换id: {},响应结果: {}",tuiHuanId,httpResponse.body());
                    if (httpResponse.isOk()) {
                        return Optional.ofNullable(httpResponse.body()).filter(StrUtil::isNotBlank)
                                .map(json -> JSON.parseObject(json, new TypeReference<TuihuanRes>() {}))
                                .map(tdl -> {
                                    if(ObjectUtil.notEqual(tdl.getStats(), NumberConstant.ONE)){
                                        R<Integer> error = R.error(tdl.getMsg());
                                        return error;
                                    }
                                    return R.success(tuiHuanId);
                                })
                                .orElse(null);
                    } else {
                        return new R<Integer>(ResultCode.SERVER_ERROR, StrUtil.format("退换机一审操作{}错误", httpResponse.getStatus()));
                    }
                }).orElseGet(() -> new R<Integer>(ResultCode.SERVER_ERROR, "获取退换机一审操作的inwcf地址异常"));
        return result;
    }

    @Override
    public R<Integer> refundMachineCheck2(Integer tuiHuanId, Integer tuiHuanKind) {
        log.warn("退换机二审,退换id: {}",tuiHuanId);
        R<Integer> result = Optional.ofNullable(sysConfigClient.getValueByCode(SysConfigConstant.MOA_URL)).filter(R::isSuccess)
                .map(R::getData).filter(StrUtil::isNotBlank)
                .map(url -> {
                    String fullUrl = StrUtil.format("{}/oa/shouhou/Check2/{}", url,tuiHuanId);
                    log.warn("退换机二审,退换id: {},请求地址: {}",tuiHuanId,fullUrl);
                    HttpResponse httpResponse = HttpUtil.createGet(fullUrl).form("kind",tuiHuanKind)
                            .auth(SpringContextUtil.getRequest().map(req -> req.getHeader(Header.AUTHORIZATION.getValue())).orElse(null))
                            .execute();
                    log.warn("退换机二审,退换id: {},响应结果: {}",tuiHuanId,httpResponse.body());
                    if (httpResponse.isOk()) {
                        return Optional.ofNullable(httpResponse.body()).filter(StrUtil::isNotBlank)
                                .map(json -> JSON.parseObject(json, new TypeReference<TuihuanRes>() {}))
                                .map(tdl -> {
                                    if(ObjectUtil.notEqual(tdl.getStats(), NumberConstant.ONE)){
                                        R<Integer> error = R.error(tdl.getMsg());
                                        return error;
                                    }
                                    return R.success(tuiHuanId);
                                })
                                .orElse(null);
                    } else {
                        return new R<Integer>(ResultCode.SERVER_ERROR, StrUtil.format("退换机二审操作{}错误", httpResponse.getStatus()));
                    }
                }).orElseGet(() -> new R<Integer>(ResultCode.SERVER_ERROR, "获取退换机二审操作的inwcf地址异常"));
        return result;
    }

    @Override
    public R<Integer> refundMachineTransact(Integer tuiHuanId, Integer newBasketId,TransactVo transactVo) {
        log.warn("退换机办理操作,退换id: {}",tuiHuanId);
        //获取良品自动退款
        Integer rankFlag = Convert.toInt(Optional.ofNullable(transactVo).orElse(new TransactVo()).getLpAutomaticRefund());
        R<Integer> result = Optional.ofNullable(sysConfigClient.getValueByCode(SysConfigConstant.MOA_URL)).filter(R::isSuccess)
                .map(R::getData).filter(StrUtil::isNotBlank)
                .map(url -> {
                    String fullUrl = StrUtil.format("{}/oa/shouhou/Transact", url);
                    log.warn("退换机办理操作,退换id: {},请求地址: {} ,参数: id: {} basket_id: {} rankFlag:{}",tuiHuanId,fullUrl,tuiHuanId,newBasketId,rankFlag);
                    HttpResponse httpResponse = HttpUtil.createGet(fullUrl).form("id",tuiHuanId).form("basket_id",newBasketId)
                            .form("rankFlag", rankFlag)
                            .cookie(new HttpCookie("isApp", "1"))
                            .auth(SpringContextUtil.getRequest().map(req -> req.getHeader(Header.AUTHORIZATION.getValue()))
                                    //获取当前模拟用户信息
                                    .orElseGet(()-> {
                                        AbstractCurrentRequestComponent currentRequestComponent = SpringUtil.getBean(AbstractCurrentRequestComponent.class);
                                        OaUserBO oaUserBO = currentRequestComponent.getCurrentStaffId();
                                        if(oaUserBO == null){
                                            throw new CustomizeException("请先登录");
                                        }
                                        String token = IdUtil.fastSimpleUUID();
                                        currentRequestComponent.login(oaUserBO, token, Duration.ofMinutes(1));
                                        return token;
                                    }))
                            .execute();
                    log.warn("退换机办理操作,退换id: {},响应结果: {}",tuiHuanId,httpResponse.body());
                    if (httpResponse.isOk()) {
                        return Optional.ofNullable(httpResponse.body()).filter(StrUtil::isNotBlank)
                                .map(json -> JSON.parseObject(json, new TypeReference<TuihuanRes>() {}))
                                .map(tdl -> {
                                    if(ObjectUtil.notEqual(tdl.getStats(), NumberConstant.ONE)){
                                        R<Integer> error = R.error(tdl.getMsg());
                                        return error;
                                    }
                                    return R.success(tuiHuanId);
                                })
                                .orElse(null);
                    } else {
                        return new R<Integer>(ResultCode.SERVER_ERROR, StrUtil.format("退换机办理操作{}错误", httpResponse.getStatus()));
                    }
                }).orElseGet(() -> new R<Integer>(ResultCode.SERVER_ERROR, "获取退换机办理操作的inwcf地址异常"));
        return result;
    }
}
