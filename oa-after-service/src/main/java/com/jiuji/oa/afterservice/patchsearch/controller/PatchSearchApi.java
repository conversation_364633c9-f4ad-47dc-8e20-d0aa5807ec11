package com.jiuji.oa.afterservice.patchsearch.controller;


import com.jiuji.oa.afterservice.common.util.ValidatorUtil;
import com.jiuji.oa.afterservice.patchsearch.service.PatchSearchService;
import com.jiuji.oa.afterservice.patchsearch.vo.req.PatchSearchReq;
import com.jiuji.oa.afterservice.patchsearch.vo.req.PatchSearchReqV2;
import com.jiuji.oa.afterservice.patchsearch.vo.res.PatchSearchRes;
import com.jiuji.oa.afterservice.patchsearch.vo.res.PatchSearchResV2;
import com.jiuji.tc.common.vo.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * 我的贴膜 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020/03/05
 */
@RestController
@RequestMapping("api/patchSearch/patchSearch")
@Api(tags = "我的贴膜接口")
public class PatchSearchApi {
    @Autowired
    private PatchSearchService pathchSearchService;

    @PostMapping("/getList")
    @ApiOperation(value = "根据basket查询", httpMethod = "POST")
    public R<List<PatchSearchRes>> getList(@RequestBody PatchSearchReq patchSearchReq) {
        ValidatorUtil.validateEntity(patchSearchReq);
        return R.success(pathchSearchService.listPageById(patchSearchReq));
    }

    @PostMapping("/getPatch")
    @ApiOperation(value = "根据basket查询", httpMethod = "POST")
    public R<PatchSearchResV2> getPatchById(@RequestBody @Valid PatchSearchReqV2 patchSearchReq) {
        return pathchSearchService.getPatchById(patchSearchReq);
    }
}
