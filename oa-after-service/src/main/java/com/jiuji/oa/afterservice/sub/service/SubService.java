package com.jiuji.oa.afterservice.sub.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.oa.afterservice.bigpro.bo.ShouhouTuihuanReturnDataBo;
import com.jiuji.oa.afterservice.bigpro.vo.MemberSubVO;
import com.jiuji.oa.afterservice.sub.bo.SaleOrderParamBO;
import com.jiuji.oa.afterservice.sub.bo.SaleOrderResultBO;
import com.jiuji.oa.afterservice.sub.po.Sub;
import com.jiuji.tc.foundation.db.annotation.DS;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @since 2020/3/21
 */
public interface SubService extends IService<Sub> {


    /**
     * 获取区域价格
     * @param ppidList
     * @param cityid
     * @param xtenant
     * @return
     */
    Map<Integer,BigDecimal> getRegionalPrice(Collection<Integer> ppidList, Integer cityid, Integer xtenant);
    /**
     * 创建订单
     * @param saleOrderParamBO
     * @return
     */
    Integer createSub(SaleOrderParamBO saleOrderParamBO);
    /**
     * 通过小件单创建销售单
     * @param smallProId
     */
    Integer createSubBySmallProId(Integer smallProId);

    Sub splitSub(Sub oldSub, Integer subCheck,
                 BigDecimal yingfuM, BigDecimal yifuM);


    Sub splitSub(Sub oldSub, Integer subCheck
            , BigDecimal yingfuM, BigDecimal yifuM, BigDecimal feeM, BigDecimal youhui1M
            , BigDecimal shouxuM, BigDecimal jidianM);

    /**
     * description: <SqlServer-getById>
     * translation: <SqlServer-getById>
     *
     * @param id 主键
     * @return com.jiuji.oa.afterservice.sub.po.Sub
     * <AUTHOR>
     * @date 14:00 2020/4/8
     * @since 1.0.0
     **/
    Sub getByIdSqlServer(Integer id);

    /**
     * description: <SqlServer-list>
     * translation: <SqlServer-list>
     *
     * @param wrapper 筛选条件
     * @return java.util.List<com.jiuji.oa.afterservice.sub.po.Sub>
     * <AUTHOR>
     * @date 14:00 2020/4/8
     * @since 1.0.0
     **/
    List<Sub> listSqlServer(Wrapper wrapper);

    /**
     * 获取订单代金券金额
     *
     * @param subId
     * @return
     */
    BigDecimal getSubDJQPrice(Integer subId);

    /**
     * 查询用户id
     *
     * @param subId
     * @return
     */
    Integer getUserIdBySubId(Integer subId);

    /**
     * 查询用户id
     *
     * @param subId
     * @return
     */
    @DS("oanew_his")
    Integer getHisUserIdBySubId(Integer subId);

    /**
     * 获取退款数据
     *
     * @param subId
     * @param type
     * @return
     */
    ShouhouTuihuanReturnDataBo getReturnDataTable(Integer subId, String type);

    /**
     * 根据用户id查询订单信息
     *
     * @param userId 用户id
     * @return 订单列表
     */
    List<MemberSubVO> searchSubInfoByUserId(Integer userId);

    /**
     * 根据用户id查询良品订单信息
     *
     * @param userId 用户id
     * @return 订单列表
     */
    List<MemberSubVO> searchLpInfoByUserId(Integer userId);

    /**
     * 根据串号查询商品订单信息
     * @param imei
     * @return
     */
    List<MemberSubVO> searchSubInfoByImei(String imei);

    /**
     *  获取服务订单
     * @param basketId
     * @param ppid
     * @return
     */
    Sub getServiceSub(Integer basketId,Integer ppid);

    /**
     *  获取服务订单
     * @param basketId
     * @param ppid
     * @return
     */
    Sub getHistoryServiceSub(Integer basketId,Integer ppid);

    /**
     * 根据手机号码查询九机盾服务
     * @param key 手机号码
     * @return
     */
    List<MemberSubVO> getShieldService(String key);
}
