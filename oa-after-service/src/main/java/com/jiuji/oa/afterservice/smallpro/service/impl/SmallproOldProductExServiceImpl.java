package com.jiuji.oa.afterservice.smallpro.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.ch999.common.util.vo.Result;
import com.jiuji.cloud.stock.enums.ProductConfigTypeEnum;
import com.jiuji.cloud.stock.service.OaStockCloud;
import com.jiuji.cloud.stock.vo.request.IsVirtualProductReqVo;
import com.jiuji.cloud.stock.vo.response.IsVirtualProductResVo;
import com.jiuji.oa.afterservice.bigpro.enums.OaMesTypeEnum;
import com.jiuji.oa.afterservice.bigpro.service.SmsService;
import com.jiuji.oa.afterservice.bigpro.vo.req.DisplayProductAddReq;
import com.jiuji.oa.afterservice.cloud.service.StockCloud;
import com.jiuji.oa.afterservice.common.bo.OaUserBO;
import com.jiuji.oa.afterservice.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.afterservice.common.constant.DataSourceConstants;
import com.jiuji.oa.afterservice.common.constant.RequestCacheKeys;
import com.jiuji.oa.afterservice.common.enums.XtenantEnum;
import com.jiuji.oa.afterservice.common.exception.CustomizeException;
import com.jiuji.oa.afterservice.common.exception.RRExceptionHandler;
import com.jiuji.oa.afterservice.common.util.*;
import com.jiuji.oa.afterservice.common.vo.PageVo;
import com.jiuji.oa.afterservice.other.bo.*;
import com.jiuji.oa.afterservice.other.po.Areainfo;
import com.jiuji.oa.afterservice.other.po.Basket;
import com.jiuji.oa.afterservice.other.po.ShouhouFanchang;
import com.jiuji.oa.afterservice.other.service.*;
import com.jiuji.oa.afterservice.patchsearch.enums.SubStateEnum;
import com.jiuji.oa.afterservice.smallpro.bo.SmallproOldPartBO;
import com.jiuji.oa.afterservice.smallpro.bo.SmallproOperationInfoBO;
import com.jiuji.oa.afterservice.smallpro.bo.smallproExchange.SmallproIncomingInfoBO;
import com.jiuji.oa.afterservice.smallpro.bo.smallproExchange.SmallproScrapInfoBO;
import com.jiuji.oa.afterservice.smallpro.bo.smallproInfo.SmallproInfoInuserInfoBO;
import com.jiuji.oa.afterservice.smallpro.dao.SmallproMapper;
import com.jiuji.oa.afterservice.smallpro.enums.LogisticsTypeEnum;
import com.jiuji.oa.afterservice.smallpro.enums.SmallProKindEnum;
import com.jiuji.oa.afterservice.smallpro.enums.SmallProServiceTypeEnum;
import com.jiuji.oa.afterservice.smallpro.enums.SmallproOldStatsExtendEnum;
import com.jiuji.oa.afterservice.smallpro.po.Smallpro;
import com.jiuji.oa.afterservice.smallpro.po.SmallproBill;
import com.jiuji.oa.afterservice.smallpro.service.*;
import com.jiuji.oa.afterservice.smallpro.vo.req.SmallOldProTransformDisplayReq;
import com.jiuji.oa.afterservice.smallpro.vo.req.SmallproOldPartReq;
import com.jiuji.oa.afterservice.smallpro.vo.res.LossForSmallProductsCodeMessageRes;
import com.jiuji.oa.afterservice.smallpro.vo.res.SmallproNormalCodeMessageRes;
import com.jiuji.oa.afterservice.smallpro.vo.res.SmallproOldPartRes;
import com.jiuji.oa.afterservice.stock.po.ProductKc;
import com.jiuji.oa.afterservice.stock.service.ProductKcService;
import com.jiuji.oa.afterservice.sys.service.AuthConfigService;
import com.jiuji.oa.oacore.oaorder.WuliuCloud;
import com.jiuji.oa.oacore.oaorder.req.SubWLModelReq;
import com.jiuji.oa.orginfo.areainfo.client.AreaInfoClient;
import com.jiuji.oa.orginfo.areainfo.vo.BigAreaQueryReq;
import com.jiuji.oa.orginfo.areainfo.vo.res.BigAreaInfoRes;
import com.jiuji.oa.orginfo.sysconfig.client.SysConfigClient;
import com.jiuji.oa.orginfo.userinfo.client.UserInfoClient;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.common.vo.ResultCode;
import com.jiuji.tc.foundation.db.annotation.DS;
import com.jiuji.tc.utils.common.CommonUtils;
import com.jiuji.tc.utils.constants.NumberConstant;
import com.jiuji.tc.utils.constants.SignConstant;
import com.jiuji.tc.utils.constants.SysConfigConstant;
import com.jiuji.tc.utils.constants.TimeFormatConstant;
import com.jiuji.tc.utils.enums.EnumUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

import static java.util.stream.Collectors.joining;
import static java.util.stream.Collectors.toList;

/**
 * description: <小件旧件商品扩展服务实现类>
 * translation: <Small piece old goods expansion service implementation class>
 *
 * <AUTHOR>
 * @date 2020/4/10
 * @since 1.0.0
 */
@Service
@Slf4j
public class SmallproOldProductExServiceImpl implements SmallproOldProductExService {

    // region autowired

    @Autowired
    private SmallproService smallproService;
    @Autowired
    private SmallproLogService smallproLogService;
    @Autowired
    private ShouhouFanchangService shouhouFanchangService;
    @Autowired
    private SmallproForwardExService smallproForwardExService;
    @Autowired
    private VoucherService voucherService;
    @Autowired
    private AreainfoService areainfoService;
    @Autowired
    private ReceivePersonConfigService receivePersonConfigService;
    @Autowired
    private SmallproMapper smallproMapper;
    @Autowired
    private WuliuCloud wuliuCloud;
    @Resource
    private AbstractCurrentRequestComponent currentRequestComponent;
    @Autowired
    private AuthConfigService authConfigService;
    @Resource
    private SysConfigClient sysConfigClient;
    @Autowired
    private AreaInfoClient areaInfoClient;
    @Autowired
    private ProductKcService productKcService;
    @Resource
    private StockCloud stockCloud;
    @Autowired
    private RedissonClient redissonClient;
    @Resource
    private SmallproBillService smallproBillService;
    @Resource
    private AbstractCurrentRequestComponent abstractCurrentRequestComponent;
    @Resource
    private SmsService smsService;
    @Resource
    private UserInfoClient userInfoClient;
    @Resource
    private BasketService basketService;

    /**
     * 小件id分布式锁定key格式化
     */
    private static final String LOCK_SMALL_PRO_ID = "OA:JAVA:AFTER:LOCK_SMALL_PRO_ID:%s";
    private static final String CHU_LI_ZHONG_MESSAGE = "当前订单处理中,请稍后重试！";

    // endregion

    // region 逻辑方法

    // region 获取小件接件旧货列表 getSmallproOldPartPage

    @Override
    @DS(DataSourceConstants.CH999_OA_NEW)
    public PageVo getSmallproOldPartPage(SmallproOldPartReq query, Integer pageSize, Integer currentPage) {
        if (currentPage == null || currentPage == 0) {
            currentPage = 1;
        }
        if (pageSize == null || pageSize == 0) {
            pageSize = 1;
        }
        PageVo result = new PageVo(currentPage, pageSize);

        //当前登录地区和小件单所属地区不在同一个授权体系下不能进行查看
        OaUserBO oaUserBO = currentRequestComponent.getCurrentStaffId();
        Boolean authPart = authConfigService.isAuthPart(oaUserBO);
        query.setAuthorizeId(oaUserBO.getAuthorizeId());
        query.setAuthPart(authPart);
        Integer total = smallproMapper.getSmallproOldPartTotalNum(query);
        if (total == 0) {
            return result;
        }
        Integer startRow = pageSize * (currentPage - 1);
        List<SmallproOldPartBO> list = smallproMapper.getSmallproOldPartList(query, pageSize, startRow);
        //判断是否有权值查看成本
        boolean hasSpcb = oaUserBO.getRank().contains("spcb");
        List<String> inUserNameList = list.stream().peek(spo -> {
                    if(!hasSpcb){
                        //没有权限成本全部设置为0
                        spo.setCost(BigDecimal.ZERO);
                    }
                })
                .map(SmallproOldPartBO::getInUser).distinct().collect(toList());
        List<SmallproInfoInuserInfoBO> inUserInfoBOList = smallproMapper.getSmallproInUserInfo(inUserNameList);
        Map<Integer, SmallproInfoInuserInfoBO> inUserIdInUserMap =
                inUserInfoBOList.stream().collect(Collectors.toMap(SmallproInfoInuserInfoBO::getInUserId,
                        bo -> bo));
        Map<String, Integer> inUserNameInUserIdMap = new HashMap<>(inUserInfoBOList.size());
        inUserInfoBOList.forEach(bo -> {
            Integer inUserId = inUserNameInUserIdMap.get(bo.getInUserName());
            if (inUserId != null) {
                SmallproInfoInuserInfoBO temp = inUserIdInUserMap.get(inUserId);
                if (temp != null) {
                    inUserId = bo.getIsZaiZhi() ? bo.getInUserId() : (temp.getIsZaiZhi() ?
                            (bo.getInUserId() > temp.getInUserId() ? bo.getInUserId() : temp.getInUserId()) :
                            temp.getInUserId());
                    inUserNameInUserIdMap.put(bo.getInUserName(), inUserId);
                } else {
                    inUserNameInUserIdMap.put(bo.getInUserName(), bo.getInUserId());
                }
            } else {
                inUserNameInUserIdMap.put(bo.getInUserName(), bo.getInUserId());
            }
        });

        //增加是否要查询操作进程列表 xxk
        if (CollectionUtils.isNotEmpty(list) && query.isQueryOperations()) {
            list = list.stream().map(e -> {
                List<SmallproOperationInfoBO> operationInfoList =
                        smallproService.getSmallproOperationLogs(e.getSmallproId(), 0);
                e.setOperationInfoList(operationInfoList);
                e.setInUserId(inUserNameInUserIdMap.get(e.getInUser()));
                return e;
            }).collect(toList());
        }

        // 订单状态 key 转 value
        list.forEach(item -> item.setSubCheckValue(EnumUtil.getMessageByCode(SubStateEnum.class, item.getSubCheck())));
        //合计成本
     //   SmallproOldPartRes oldPartRes = smallproMapper.getAssessCostAndAmount(query);
        SmallproOldPartRes oldPartRes = new SmallproOldPartRes();
        //当前页成本计算 当前页cost的和
        oldPartRes.setCurrentPageCost(list.stream().map(SmallproOldPartBO::getCost).reduce(BigDecimal.ZERO,BigDecimal::add));
        BigDecimal assessCost = Optional.of(hasSpcb).filter(Boolean::booleanValue)
                .map(spcb -> smallproMapper.getSmallproOldPartAllCost(query))
                .map(allCost -> allCost.setScale(NumberConstant.TWO, RoundingMode.HALF_UP))
                .orElse(BigDecimal.ZERO);
        oldPartRes.setAssessCost(assessCost);

        oldPartRes.setList(list);
        result.setRecords(oldPartRes);
        result.setTotal(total);
        result.setPages(PageUtil.getPages(total, pageSize));
        return result;
    }

    /**
     * 设置成本价格
     * @param list
     * @param oaUserBO
     * @return
     */
    private List<SmallproBill> setCostPrice(List<SmallproOldPartBO> list, OaUserBO oaUserBO) {
        //获取小件的旧件成本
        //根据小件的单号 和维修单号 来查询
        List<Integer> smallProList = list.stream().map(SmallproOldPartBO::getSmallproId).collect(toList());
        //查询SmallproBill
        List<SmallproBill> smallproBillList =
                CommonUtils.bigDataInQuery(NumberConstant.PROGRAMMER_NUMBER, smallProList, ids -> smallproBillService.list(new LambdaQueryWrapper<SmallproBill>().in(SmallproBill::getSmallproID, ids)));
        //查询basket表
        List<Integer> basketIds = list.stream().map(SmallproOldPartBO::getBasketId).filter(Objects::nonNull).distinct().collect(toList());
        List<Basket> baskets = new ArrayList<>();
        if (CollUtil.isNotEmpty(basketIds)){
            baskets = CommonUtils.bigDataInQuery(NumberConstant.PROGRAMMER_NUMBER, basketIds, ids -> basketService.list(new LambdaQueryWrapper<Basket>().in(Basket::getBasketId, ids)));
        }
        //判断是否有权值查看
        if (oaUserBO.getRank().contains("spcb")) {
            List<Basket> finalBaskets = baskets;
            list.forEach(l -> smallproBillList.stream().filter(sb -> Objects.equals(sb.getSmallproID(), l.getSmallproId()))
                    .filter(sb -> Objects.equals(sb.getBasketId(), l.getBasketId())).findFirst()
                    .ifPresent(sb -> {
                        //小件成本取值逻辑  先取smallproBill的价格 如果取不到则取shouhou_fanchang的价格 如果还是取不到，取basket的成本价，都取不到返回0
                        l.setCost(Optional.ofNullable(sb.getInprice()).orElse(
                                Optional.ofNullable(l.getInPrice()).orElseGet(()-> getBasket(finalBaskets, l))
                        ).setScale(NumberConstant.TWO, RoundingMode.HALF_UP));
                    }));
        }
        return smallproBillList;
    }

    /**
     * 获取basket表中的价格
     * @param baskets baskets
     * @param l SmallproOldPartBO实体
     * @return
     */
    private BigDecimal getBasket(List<Basket> baskets, SmallproOldPartBO l) {
        for (Basket basket : baskets) {
            if (Objects.equals(l.getBasketId(), basket.getBasketId())) {
                return basket.getInprice();
            }
        }
        return BigDecimal.ZERO;
    }

    // endregion

    // region 转现小件 incomingSmallpro

    @Transactional(rollbackFor = Exception.class)
    @Override
    public SmallproNormalCodeMessageRes incomingSmallpro(Integer returnFactoryId, String userName, Integer smallproId, Integer transformXc, boolean isWriteLog) {
        RLock lock = redissonClient.getLock(String.format(LOCK_SMALL_PRO_ID, smallproId));
        SmallproNormalCodeMessageRes result = new SmallproNormalCodeMessageRes();
        if (!lock.tryLock()) {
            result.setCode(NumberConstant.FIVE_HUNDRED);
            return result.setMessage(CHU_LI_ZHONG_MESSAGE);
        }
        try {
            SmallproIncomingInfoBO incomingInfoBO = smallproMapper.getIncomingSmallproInfo(returnFactoryId);
            if (incomingInfoBO == null) {
                result.setCode(NumberConstant.FIVE_HUNDRED);
                result.setMessage("未能查询到旧件信息！");
                return result;
            }
            //获取小件转地区的areaid
            Optional<Integer> toAreaId = Optional.ofNullable(incomingInfoBO.getToAreaId());
            //获取当前门店id
            Integer isThisAreaId = abstractCurrentRequestComponent.getCurrentStaffId().getAreaId();
            //当转地区id不为空 不等于接件地区时
            if (toAreaId.isPresent() && (!Objects.equals(toAreaId.get(), isThisAreaId))) {
                String area = areaInfoClient.getAreaInfoById(toAreaId.orElse(incomingInfoBO.getAreaId())).getData().getArea();
                result.setCode(NumberConstant.FIVE_HUNDRED);
                result.setMessage("该小件单归属"+area+"门店，无法进行操作，请核实地区是否一致");
                return result;
            }
            BigDecimal inPrice = incomingInfoBO.getTuikuanM() == null ? BigDecimal.ZERO : BigDecimal.valueOf(incomingInfoBO.getTuikuanM());
            if (CommenUtil.isNullOrZero(incomingInfoBO.getBasketId()) && inPrice.compareTo(BigDecimal.ZERO)<=0) {
                Integer areaId = incomingInfoBO.getAreaId();
                Integer ppid = incomingInfoBO.getPpid1();
                ProductKc kcInfo = productKcService.getOne(new LambdaQueryWrapper<ProductKc>().eq(ProductKc::getAreaid, areaId).eq(ProductKc::getPpriceid, ppid));
                if (kcInfo != null) {
                    inPrice = kcInfo.getInprice() == null ? BigDecimal.ZERO : kcInfo.getInprice();
                }
            }
            if (incomingInfoBO.getIsToArea()) {
                result.setCode(NumberConstant.FIVE_HUNDRED);
                result.setMessage("小件单转地区结束后，才可以转现。");
                return result;
            }
            int areaId = (incomingInfoBO.getToAreaId() == null || incomingInfoBO.getToAreaId() <= 0)
                    ? incomingInfoBO.getAreaId() : incomingInfoBO.getToAreaId();

            ShouhouFanchang shouhouFanchang = shouhouFanchangService.getByIdSqlServer(returnFactoryId);
            if (CommenUtil.isNotNullZero(shouhouFanchang.getRstats())) {
                result.setCode(NumberConstant.FIVE_HUNDRED);
                result.setMessage("旧件状态不可操作，请刷新页面后重试。");
                return result;
            }

            boolean flag = ((SmallproOldProductExServiceImpl) AopContext.currentProxy())
                    .incomingSmallproWriter(result, inPrice, smallproId, areaId, userName, shouhouFanchang, incomingInfoBO, transformXc, true, true);
            if (!flag) {
                result.setCode(NumberConstant.FIVE_HUNDRED);
                result.setMessage(ObjectUtil.defaultIfBlank(result.getMessage(),"小件单转地区结束后，才可以转现。"));
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return result;
            }
            result.setCode(0);
            result.setMessage("操作成功！");
        } finally {
            lock.unlock();
        }
        return result;
    }

    //获取返厂信息
    private List<ShouhouFanchang> shouhouFanchangList(List<Integer> ppidList, Integer smallproId) {
        //根据ppidList 和 smallproId 查询 shouhou_fanchang的id 信息
        return shouhouFanchangService.listSqlServer(new LambdaQueryWrapper<ShouhouFanchang>()
                .eq(ShouhouFanchang::getSmallproid, smallproId)
                .eq(ShouhouFanchang::getRstats, 0)
                .in(ShouhouFanchang::getPpid, ppidList).orderByDesc(ShouhouFanchang::getId)
        );
    }

    //更据返厂id查询小件接件转现信息 查询1000个
    private List<SmallproIncomingInfoBO> incomingInfoBoList(List<Integer> fanChangIdList) {
        //根据ppidList 和 smallproId 查询 shouhou_fanchang的id 信息
        return IntStream.rangeClosed(0, (int) Math.ceil(fanChangIdList.size() * 1.0 / NumberConstant.ONE_THOUSAND) - 1)
                .mapToObj(p -> smallproMapper.getIncomingSmallproInfoList(fanChangIdList.stream()
                        .skip((long) p * NumberConstant.ONE_THOUSAND).limit(NumberConstant.ONE_THOUSAND).collect(toList())))
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
    }

    /**
     * 批量转现小件
     *
     * @param ppidList   ppid集合
     * @param oaUserBo   当前操作用户名
     * @param smallproId 小件商品id
     * @return 转现结果
     */
    @Override
    public SmallproNormalCodeMessageRes batchIncomingSmallpro(List<Integer> ppidList, OaUserBO oaUserBo, Integer smallproId) {
        SmallproNormalCodeMessageRes result = new SmallproNormalCodeMessageRes();
        RLock lock = redissonClient.getLock(String.format(LOCK_SMALL_PRO_ID, smallproId));
        if (!lock.tryLock()) {
            result.setCode(NumberConstant.FIVE_HUNDRED);
            return result.setMessage(CHU_LI_ZHONG_MESSAGE);
        }
        try {
            //获取返厂信息
            List<ShouhouFanchang> shouhouFanchangList = shouhouFanchangList(ppidList, smallproId);
            //批量获取旧件信息
            List<Integer> fanChangIdList = shouhouFanchangList.stream().map(ShouhouFanchang::getId).collect(toList());
            if (fanChangIdList.isEmpty()) {
                result.setCode(NumberConstant.FIVE_HUNDRED);
                result.setMessage("商品的ppid不正确！");
                return result;
            }
            //更据返厂id查询小件接件转现信息 查询1000个
            List<SmallproIncomingInfoBO> incomingInfoBoList = incomingInfoBoList(fanChangIdList);
            //过滤出没有包含
            List<Integer> errorFanChangIds = fanChangIdList.stream().filter(fc -> incomingInfoBoList.stream()
                    .noneMatch(ii -> ii.getShouhouFanchangId().equals(fc))).collect(toList());
            if (!errorFanChangIds.isEmpty()) {
                result.setCode(NumberConstant.FIVE_HUNDRED);
                result.setMessage(String.format("[%s]未能查询到旧件信息！", errorFanChangIds.stream().map(String::valueOf).collect(joining(","))));
                return result;
            }

            //根据返厂id集合来遍历出小件单的实体中有没有转地区的状态
            List<Integer> errorIsToAreaList = fanChangIdList.stream().filter(fc -> incomingInfoBoList.stream()
                    .anyMatch(SmallproIncomingInfoBO::getIsToArea)).collect(toList());
            if (!errorIsToAreaList.isEmpty()) {
                result.setCode(NumberConstant.FIVE_HUNDRED);
                result.setMessage(String.format("[%s]小件单转地区结束后，才可以转现,编号:318！", errorIsToAreaList.stream().map(String::valueOf).collect(joining(","))));
                return result;
            }

            //过滤出返厂信息中的getRstats 为空的值
            List<Integer> errorRstatsList = fanChangIdList.stream()
                    .filter(fc -> shouhouFanchangList.stream().anyMatch(sf -> CommenUtil.isNotNullZero(sf.getRstats()))).collect(toList());
            if (!errorRstatsList.isEmpty()) {
                result.setCode(NumberConstant.FIVE_HUNDRED);
                result.setMessage(String.format("[%s]旧件状态不可操作，请刷新页面后重试,编号:327!", errorRstatsList.stream().map(String::valueOf).collect(joining(","))));
                return result;
            }

            //根据ppid查询出符合门店id的商品库存信息集合
            List<ProductKc> kcInfoList = productKcService.list(new LambdaQueryWrapper<ProductKc>().eq(ProductKc::getAreaid, oaUserBo.getAreaId())
                    .in(ProductKc::getPpriceid, ppidList));
            //遍历返厂信息
            //这里永远不为空,前面已经判断
            //批量获取incomingInfoBO.getBasketId() 为空或者零 的 inprice 参照
            //循环调用  incomingSmallproWriter(result, inPrice, smallproId, areaId, userName, shouhouFanchang, incomingInfoBO, Boolean.FALSE, false)
            //如果其中一个失败,后面不再进行转现,并提示具体是哪个returnFactoryId转现失败,也就是在message前加上: [returnFactoryId]
            List<String> writerResults = shouhouFanchangList.parallelStream()
                    .map(fanchang -> fanchangIncomingWriter(oaUserBo, smallproId, result, incomingInfoBoList, kcInfoList, fanchang))
                    .collect(toList());
            handleBatchResult(ppidList, oaUserBo, smallproId, result, writerResults, "批量转现操作，旧件ppid：%s，转现成功数量：%s"
                    , incomingInfoBoList.stream().map(SmallproIncomingInfoBO::getWuliuId));

            return result;
        } finally {
            lock.unlock();
        }
    }

    public String fanchangIncomingWriter(OaUserBO oaUserBo, Integer smallproId, SmallproNormalCodeMessageRes result
            , Collection<SmallproIncomingInfoBO> incomingInfoBoList, Collection<ProductKc> kcInfoList, ShouhouFanchang fanchang) {
        //这里永远不为空,前面已经判断
        SmallproIncomingInfoBO incomingInfoBO = incomingInfoBoList.stream().filter(ii -> ii.getShouhouFanchangId().equals(fanchang.getId())).findFirst().get();
        int areaId = (incomingInfoBO.getToAreaId() == null || incomingInfoBO.getToAreaId() <= 0)
                ? incomingInfoBO.getAreaId() : incomingInfoBO.getToAreaId();
        BigDecimal inPrice = incomingInfoBO.getTuikuanM() == null ? BigDecimal.ZERO : BigDecimal.valueOf(incomingInfoBO.getTuikuanM());

        if (CommenUtil.isNullOrZero(incomingInfoBO.getBasketId()) && inPrice.compareTo(BigDecimal.ZERO)<=0) {
            //批量获取incomingInfoBO.getBasketId() 为空或者零 的 inprice 参照
            inPrice = kcInfoList.stream().filter(kc -> kc.getPpriceid().equals(incomingInfoBO.getPpid1()))
                    .findFirst().map(kc -> Optional.ofNullable(kc.getInprice()).orElse(BigDecimal.ZERO)).orElse(inPrice);
        }

        //循环调用  incomingSmallproWriter(result, inPrice, smallproId, areaId, userName, shouhouFanchang, incomingInfoBO, Boolean.FALSE, false)
        //这里只能转现进行操作 通过iswritelog控制
        boolean flag = SpringUtil.getBean(SmallproOldProductExServiceImpl.class).incomingSmallproWriter(result, inPrice, smallproId, areaId, oaUserBo.getUserName(), fanchang, incomingInfoBO
                , NumberConstant.TWO, false, false);
        //如果失败,返回错误信息,并提示具体是哪个returnFactoryId转现失败,也就是在message前加上: [returnFactoryId]
        if (flag) {
            return null;
        } else {
            return String.format("[%s]%s", fanchang.getId(), result.getMessage());
        }
    }

    public void handleBatchResult(Collection<Integer> ppidList, OaUserBO oaUserBo, Integer smallproId, SmallproNormalCodeMessageRes result
            , Collection<String> writerResults, String s, Stream<Integer> wuliuIds) {
        //批量执行成功记录日志
        long count = writerResults.stream().filter(Objects::isNull).count();
        if (count > 0) {
            smallproLogService.addLogs(smallproId, String.format(s
                    , ppidList.stream().map(String::valueOf).collect(joining(SignConstant.COMMA))
                    , count), oaUserBo.getUserName(), 0);
            //如果物流單id不為空，自動批量完成物流單
            Integer wl = wuliuIds.filter(Objects::nonNull).findFirst().orElse(0);
            if (!CommenUtil.isNullOrZero(wl)) {
                wuliuCloud.smallproAutoComplete(wl, smallproId, oaUserBo.getUserName());
            }
        }
        //结果处理
        StringJoiner errorMsg = new StringJoiner(SignConstant.ZHENG_XIE_GANG);
        writerResults.stream().filter(Objects::nonNull).limit(NumberConstant.FIVE).forEach(errorMsg::add);
        if (errorMsg.length() > 0) {
            result.setCode(NumberConstant.FIVE_HUNDRED);
            result.setMessage(errorMsg.toString());
        } else {
            result.setCode(0);
            result.setMessage("操作成功！");
        }
    }

    /**
     * 小件批量报废
     *
     * @param ppidList   商品ppid
     * @param oaUserBo   当前操作用户
     * @param smallproId 小件订单id
     * @return Message
     */
    @Override
    public SmallproNormalCodeMessageRes batchScrapSmallpro(List<Integer> ppidList, OaUserBO oaUserBo, Integer smallproId) {
        SmallproNormalCodeMessageRes result = new SmallproNormalCodeMessageRes();
        RLock lock = redissonClient.getLock(String.format(LOCK_SMALL_PRO_ID, smallproId));
        if (!lock.tryLock()) {
            result.setCode(NumberConstant.FIVE_HUNDRED);
            return result.setMessage(CHU_LI_ZHONG_MESSAGE);
        }
        try {
            //获取返厂信息
            List<ShouhouFanchang> shouhouFanchangList = shouhouFanchangService.listSqlServer(new LambdaQueryWrapper<ShouhouFanchang>()
                    .eq(ShouhouFanchang::getSmallproid, smallproId)
                    .eq(ShouhouFanchang::getRstats, 0)
                    .in(ShouhouFanchang::getPpid, ppidList)
            );
            //获取返厂信息中的返厂id集合
            List<Integer> fanChangIdList = shouhouFanchangList.stream().map(ShouhouFanchang::getId).collect(toList());
            if (fanChangIdList.isEmpty()) {
                result.setCode(NumberConstant.FIVE_HUNDRED);
                result.setMessage("商品的ppid不正确！");
                return result;
            }
            //更据返厂id查询小件接件转现信息
//            List<SmallproIncomingInfoBO> scrapInfoBoList = smallproMapper.getScrapSmallproInfoList(fanChangIdList);
            //更据返厂id查询小件接件转现信息 查询1000个
            List<SmallproScrapInfoBO> scrapInfoBoList = IntStream.rangeClosed(0, (int) Math.ceil(fanChangIdList.size() * 1.0 / NumberConstant.ONE_THOUSAND) - 1)
                    .mapToObj(p -> smallproMapper.getScrapSmallproInfoList(fanChangIdList.stream()
                            .skip((long) p * NumberConstant.ONE_THOUSAND).limit(NumberConstant.ONE_THOUSAND).collect(toList())))
                    .flatMap(Collection::stream)
                    .collect(toList());

            //过滤出没有包含
            List<Integer> errorFanChangIds = fanChangIdList.stream().filter(fc -> scrapInfoBoList.stream()
                    .noneMatch(ii -> ii.getShouhouFanchangId().equals(fc))).collect(toList());
            if (!errorFanChangIds.isEmpty()) {
                result.setCode(NumberConstant.FIVE_HUNDRED);
                result.setMessage(String.format("[%s]未能查询到旧件信息！", errorFanChangIds.stream().map(String::valueOf).collect(joining(","))));
                return result;
            }
            //如果没有传入小件id  则把订单的小件id赋值过去
            if (smallproId == null || smallproId == 0) {
                smallproId = scrapInfoBoList.stream().map(SmallproScrapInfoBO::getSmallproId).findFirst().orElse(-1);
            }
            if (smallproId == -1) {
                result.setCode(NumberConstant.FIVE_HUNDRED);
                result.setMessage("接件id错误！");
                return result;
            }
            //判断订单是否在转地区状态
            List<Integer> errorIsToAreaList = fanChangIdList.stream().filter(fc -> scrapInfoBoList.stream()
                    .anyMatch(SmallproScrapInfoBO::getIsToArea)).collect(toList());
            //判断订单地区状态是否是null的值
            List<Integer> errorAreaList = fanChangIdList.stream().filter(fc -> scrapInfoBoList.stream()
                    .anyMatch(ii -> CommenUtil.isNullOrZero(ii.getAreaId()))).collect(toList());
            if (!errorIsToAreaList.isEmpty()) {
                result.setCode(NumberConstant.FIVE_HUNDRED);
                result.setMessage(String.format("[%s]小件单转地区结束后，才可以报废处理！", errorIsToAreaList.stream().map(String::valueOf).collect(joining(","))));
                return result;
            }
            //判断地区状态是否为空
            if (!errorAreaList.isEmpty()) {
                result.setCode(NumberConstant.FIVE_HUNDRED);
                result.setMessage(String.format("[%s]地区错误，请确认接件地区是否正确！", errorAreaList.stream().map(String::valueOf).collect(joining(","))));
                return result;
            }
            //判断售后返厂单的处理状态 是否正常
            List<ShouhouFanchang> errorGetRstatsList = shouhouFanchangList.stream().filter(fc -> fc.getRstats() == null || (fc.getRstats() != 0 && fc.getRstats() != 1)).collect(toList());
            if (!errorGetRstatsList.isEmpty()) {
                result.setCode(NumberConstant.FIVE_HUNDRED);
                result.setMessage("状态错误，可能已被操作！");
                return result;
            }

            //遍历返厂信息
            final Integer tempSmallproId = smallproId;
            //查询是否虚拟商品
            List<Integer> ppids = shouhouFanchangList.stream().map(ShouhouFanchang::getPpid).collect(toList());
            IsVirtualProductReqVo reqVo = new IsVirtualProductReqVo();
            reqVo.setType(ProductConfigTypeEnum.PPID.getCode()).setValue(ppids);
            Map<Integer, IsVirtualProductResVo> virtualProductMap = SpringContextUtil.reqCache(() ->
                    CommonUtils.getResultData(SpringUtil.getBean(OaStockCloud.class).isVirtualProduct(reqVo),
                            userMsg -> {
                                throw new CustomizeException(StrUtil.format("小件单: {}, 查询虚拟商品接口发生{}异常", tempSmallproId, userMsg));
                            }), RequestCacheKeys.OA_STOCK_CLOUD_IS_VIRTUAL_PRODUCT, reqVo);
            List<String> writerResults = shouhouFanchangList.stream().map(fanchang -> {
                SmallproScrapInfoBO scrapInfoBo = scrapInfoBoList.stream().filter(ii -> ii.getShouhouFanchangId().equals(fanchang.getId())).findFirst().get();
                //循环调用
                boolean flag = SpringUtil.getBean(SmallproOldProductExServiceImpl.class)
                        .scrapSmallproWrite(result, scrapInfoBo, virtualProductMap, fanchang, oaUserBo, tempSmallproId, false);
                //如果失败,返回错误信息,并提示具体是哪个returnFactoryId转现失败,也就是在message前加上: [returnFactoryId]
                if (flag) {
                    return null;
                } else {
                    return String.format("[%s]%s,编号:484", fanchang.getId(), result.getMessage());
                }
            }).collect(toList());
            //批量执行成功记录日志
            handleBatchResult(ppidList, oaUserBo, smallproId, result, writerResults, "批量报废操作，旧件ppid：%s，报废数量：%s", scrapInfoBoList.stream()
                    .map(SmallproScrapInfoBO::getWuliuId));
            return result;
        } finally {
            lock.unlock();
        }

    }
    // endregion

    // region 报废小件 scrapSmallpro

    @Override
    public SmallproNormalCodeMessageRes scrapSmallpro(Integer returnFactoryId, String userName, Integer smallproId, Integer areaId) {
        SmallproNormalCodeMessageRes smallproNormalCodeMessageRes = new SmallproNormalCodeMessageRes();
        RLock lock = redissonClient.getLock(String.format(LOCK_SMALL_PRO_ID, smallproId));
        if (!lock.tryLock()) {
            smallproNormalCodeMessageRes.setCode(NumberConstant.FIVE_HUNDRED);
            return smallproNormalCodeMessageRes.setMessage(CHU_LI_ZHONG_MESSAGE);
        }
        try {
            SmallproScrapInfoBO smallproScrapInfoBO = smallproMapper.getScrapSmallproInfo(returnFactoryId);
            if (smallproScrapInfoBO == null) {
                smallproNormalCodeMessageRes.setCode(NumberConstant.FIVE_HUNDRED);
                smallproNormalCodeMessageRes.setMessage("旧件ID错误");
                return smallproNormalCodeMessageRes;
            } else if (smallproScrapInfoBO != null) {
                if (smallproId == null || smallproId == 0) {
                    smallproId = smallproScrapInfoBO.getSmallproId();
                }
                //获取小件转地区的areaid
                Optional<Integer> toAreaId = Optional.ofNullable(smallproScrapInfoBO.getToAreaId());
                //获取当前门店id
                Integer isThisAreaId = abstractCurrentRequestComponent.getCurrentStaffId().getAreaId();
                //当转地区id不为空 不等于接件地区时
                if (toAreaId.isPresent() && (!Objects.equals(toAreaId.get(), isThisAreaId))) {
                    String area = areaInfoClient.getAreaInfoById(toAreaId.orElse(smallproScrapInfoBO.getAreaId())).getData().getArea();
                    smallproNormalCodeMessageRes.setCode(NumberConstant.FIVE_HUNDRED);
                    smallproNormalCodeMessageRes.setMessage("该小件单归属"+area+"门店，无法进行操作，请核实地区是否一致");
                    return smallproNormalCodeMessageRes;
                }
                if (smallproScrapInfoBO.getIsToArea()) {
                    smallproNormalCodeMessageRes.setCode(NumberConstant.FIVE_HUNDRED);
                    smallproNormalCodeMessageRes.setMessage("正在转地区，请先收件再操作！");
                    return smallproNormalCodeMessageRes;
                }
                if (smallproScrapInfoBO.getAreaId() == null) {
                    smallproNormalCodeMessageRes.setCode(NumberConstant.FIVE_HUNDRED);
                    smallproNormalCodeMessageRes.setMessage("地区错误，请确认接件地区是否正确！");
                    return smallproNormalCodeMessageRes;
                }
                ShouhouFanchang shouhouFanchang = shouhouFanchangService.getByIdSqlServer(returnFactoryId);
                if (shouhouFanchang.getRstats() == null || (shouhouFanchang.getRstats() != 0 && shouhouFanchang.getRstats() != 1)) {
                    smallproNormalCodeMessageRes.setCode(NumberConstant.FIVE_HUNDRED);
                    smallproNormalCodeMessageRes.setMessage("状态错误，可能已被操作！");
                    return smallproNormalCodeMessageRes;
                }
                OaUserBO oaUserBo = currentRequestComponent.getCurrentStaffId();
                oaUserBo.setUserName(userName);
                IsVirtualProductReqVo reqVo = new IsVirtualProductReqVo();
                reqVo.setType(ProductConfigTypeEnum.PPID.getCode()).setValue(Collections.singletonList(shouhouFanchang.getPpid()));
                Integer finalSmallproId = smallproId;
                Map<Integer, IsVirtualProductResVo> virtualProductMap = SpringContextUtil.reqCache(() ->
                        CommonUtils.getResultData(SpringUtil.getBean(OaStockCloud.class).isVirtualProduct(reqVo),
                                userMsg -> {
                                    throw new CustomizeException(StrUtil.format("小件单: {}, 查询虚拟商品接口发生{}异常", finalSmallproId, userMsg));
                                }), RequestCacheKeys.OA_STOCK_CLOUD_IS_VIRTUAL_PRODUCT, reqVo);
                ((SmallproOldProductExServiceImpl) AopContext.currentProxy())
                        .scrapSmallproWrite(smallproNormalCodeMessageRes, smallproScrapInfoBO, virtualProductMap, shouhouFanchang, oaUserBo,
                                smallproId, true);
                return smallproNormalCodeMessageRes;
            }
            smallproNormalCodeMessageRes.setCode(NumberConstant.FIVE_HUNDRED);
            smallproNormalCodeMessageRes.setMessage("未知错误！");
            return smallproNormalCodeMessageRes;
        } finally {
            lock.unlock();
        }
    }

    // endregion

    // region 生成物流单 addSmallproReturnToFactoryLogistics

    @Override
    public SmallproNormalCodeMessageRes addSmallproReturnToFactoryLogistics(List<Integer> smallproIds,
                                                                            OaUserBO oaUserBO) {
        SmallproNormalCodeMessageRes result = new SmallproNormalCodeMessageRes();
        if (CollectionUtils.isEmpty(smallproIds)) {
            result.setCode(NumberConstant.FIVE_HUNDRED);
            result.setMessage("小件单号为空");
            return result;
        }
        Areainfo senderInfo = areainfoService.getByIdSqlServer(oaUserBO.getAreaId());
        AreaBelongsDcHqD1AreaId backEndInfo = areainfoService.getAreaBelongsDcHqD1AreaId(oaUserBO.getAreaId());
        Areainfo receiveInfo = areainfoService.getByIdSqlServer(backEndInfo.getD1AreaId());

        String toAreaName = receiveInfo.getAreaName() + "(" + receiveInfo.getArea() + ")";

        Integer wType = LogisticsTypeEnum.LOGISTICS_TYPE_13.getCode();

        LogisticsRecipientBO receiver = receivePersonConfigService
                .getReceiverUserInfo(wType, senderInfo.getId(), receiveInfo.getId());
        // 没有正在转地区的，不生成物流单
        List<Smallpro> list = smallproService.list(new LambdaQueryWrapper<Smallpro>().in(Smallpro::getId, smallproIds)
                .eq(Smallpro::getIsToArea, true));
        if (CollectionUtils.isEmpty(list)) {
            result.setCode(NumberConstant.FIVE_HUNDRED);
            result.setMessage("小件单没有转地区，生成失败");
            return result;
        }
        smallproIds = list.stream().map(Smallpro::getId).collect(toList());
        if (senderInfo != null && receiveInfo != null) {
            SubWLModelReq req = new SubWLModelReq();
            String comment = "售后小件旧件调拨，小件单号：" + smallproIds;
            smallproService.buildCommonSmallProWuLiu(senderInfo, receiveInfo, oaUserBO, receiver, req, comment);
            req.setAreaId(oaUserBO.getAreaId());
            req.setActionName("售后小件旧件调拨");
            Areainfo areaInfo = areainfoService.getById(backEndInfo.getH1AreaId());
            req.setReceiveAddress(areaInfo.getCompanyAddress());
            Integer wuliuId =
                    ((SmallproOldProductExServiceImpl) AopContext.currentProxy()).addSmallproReturnToFactoryLogisticsWrite(result, req, oaUserBO, smallproIds);
            result.setMessage(wuliuId.toString());
        }
        return result;
    }

    @Override
    public SXSSFWorkbook getWorkBookOldPart(SmallproOldPartReq query, String fileName) {
        // 限定查询前三个月的数据 xiexiongkun 解除三个月内的限定,否则导出excel与查询列表数据不一致
        /*LocalDateTime date = LocalDateTime.now().plusMonths(-3L).with(TemporalAdjusters.firstDayOfMonth());
        String format = date.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        if(StringUtils.isBlank(query.getStartExcelTime()) || format.compareTo(query.getStartExcelTime())>0){
            query.setStartExcelTime(format);
        }*/
        //不查询操作进程列表 xxk 要不然操作进程列表会把内存撑爆 而且导出用不上操作经常列表数据
        query.setQueryOperations(false);
        PageVo smallproPage =
                ((SmallproOldProductExServiceImpl) AopContext.currentProxy()).getSmallproOldPartPage(query, 5000, 1);
        SmallproOldPartRes records = (SmallproOldPartRes) smallproPage.getRecords();
        List<SmallproOldPartBO> list = records == null ? Collections.emptyList() : records.getList();
        // excel标题
        //判断是否有权值查看
        OaUserBO oaUserBo = currentRequestComponent.getCurrentStaffId();
        String[] title = {"当前所在地", "接件大区", "接件地区", "接件人", "处理方式", "小件单号", "PPID", "条码", "商品", "提交时间", "故障描述"};
        if (oaUserBo.getRank().contains("spcb")) {
            title = new String[]{"当前所在地", "接件大区", "接件地区", "接件人", "处理方式", "小件单号", "PPID", "条码", "商品","提交时间", "故障描述", "成本"};
        }
        // excel文件名
        // sheet名
        String sheetName = "小件退换货旧件列表";
        int size = list.size();
        log.info("小件excel导出数据量:{}", size);
        String[][] content = new String[size + 2][12];
        //获取大区信息
        BigAreaQueryReq baq = new BigAreaQueryReq();
        baq.setDataType(3).setAreaIds(list.stream().map(SmallproOldPartBO::getAreaId).collect(toList()));
        R<List<BigAreaInfoRes>> bigAreaInfoR = areaInfoClient.getBigAreaInfoList(baq);
        List<BigAreaInfoRes> bigAreaInfoRes = Optional.ofNullable(bigAreaInfoR.getData()).orElse(Collections.emptyList());
        for (int i = 0; i < size; i++) {
            content[i] = new String[title.length];
            SmallproOldPartBO bo = list.get(i);
            content[i][0] = bo.getToArea();
            //接件大区
            content[i][1] = bigAreaInfoRes.stream().filter(bi -> Objects.equals(bo.getAreaId(), bi.getAreaId()))
                    .map(BigAreaInfoRes::getDepartName).findFirst().orElse(bo.getArea());
            content[i][2] = bo.getArea();
            //接件人
            content[i][3] = bo.getInUser();
            //处理方式
            content[i][4] = Optional.ofNullable(SmallProKindEnum.valueOfByCode(bo.getKind()))
                    .map(SmallProKindEnum::getMessage).orElse(null);
            content[i][5] = Optional.ofNullable(bo.getSmallproId()).map(Convert::toStr).orElse("");
            content[i][6] = Optional.ofNullable(bo.getPpid()).map(Convert::toStr).orElse("");
            content[i][7] = bo.getBarCode();
            content[i][8] = bo.getProductName();
            content[i][9] = DateUtil.localDateTimeToString(bo.getSubmitTime());
            //故障描述
            content[i][10] = bo.getProblem();
            //当有权值的时候
            if (oaUserBo.getRank().contains("spcb")) {
                //故障描述
                content[i][11] = String.valueOf(bo.getCost());
            }

        }
        // 创建HSSFWorkbook
        return ExcelUtils.getSXSSFWorkbook(sheetName, title, content);
    }

    // endregion

    // endregion

    // region transactional

    /**
     * 售后转现接口
     *
     * @param result
     * @param inprice
     * @param smallproId
     * @param areaId
     * @param userName
     * @param shouhouFanchang
     * @param incomingInfoBO
     * @param transformXc
     * @param isWriteLog
     * @param isAutoCompleteWuLiu
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean incomingSmallproWriter(SmallproNormalCodeMessageRes result, BigDecimal inprice, Integer smallproId,
                                          Integer areaId, String userName,
                                          ShouhouFanchang shouhouFanchang, SmallproIncomingInfoBO incomingInfoBO, Integer transformXc
            , boolean isWriteLog, boolean isAutoCompleteWuLiu) {
        boolean flag = shouhouFanchangService.update(new LambdaUpdateWrapper<ShouhouFanchang>().set(ShouhouFanchang::getRstats, NumberConstant.TWO)
                .set(ShouhouFanchang::getZxinuser, userName).set(ShouhouFanchang::getZxdtime, LocalDateTime.now())
                .eq(ShouhouFanchang::getId, shouhouFanchang.getId()).apply("isnull(rstats,0)=0 and zxdtime is null"));
        if (!flag) {
            result.setCode(NumberConstant.FIVE_HUNDRED);
            result.setMessage("数据库更新售后返厂失败,编号:671！");
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return false;
        }
        //当转报损单时会进行转现操作，所以在进此逻辑不在执行下面逻辑
        if (CommenUtil.isNotNullZero(transformXc) && transformXc.equals(NumberConstant.THREE)) {
            LossForSmallProductsCodeMessageRes kcResult = smallproForwardExService.lossForSmallProducts(
                    incomingInfoBO.getPpid(), 1, inprice, areaId, userName, "", "售后小件转报损单，数量：1",
                    incomingInfoBO.getShouhouFanchangId(), 1, 1, null, false, false,smallproId);
            String logPrev = "旧件编号：" + shouhouFanchang.getId();
            if (kcResult.getCode() == ResultCode.SUCCESS) {
                CompletableFuture.runAsync(()->{
                    smallproLogService.addLogs(smallproId, logPrev + "，转报损配件操作成功,报损单：<a href="+kcResult.getReturnSubUrl()+">"+kcResult.getReturnSubId()+"</a>", userName, 0);
                    Optional.ofNullable(incomingInfoBO.getInUser()).filter(StrUtil::isNotBlank).map(userInfoClient::getCh999UserByUserName)
                            .filter(R::isSuccess).map(R::getData)
                            .ifPresent(ch999User -> {
                                String host = sysConfigClient.getValueByCode(SysConfigConstant.MOA_URL).getData();
                                String link = host + "/new/%23/small-refund/" + smallproId;
                                smsService.sendOaMsg("您接件的小件单<a href = '"+link+"'>"+smallproId+"</a>，因小件单异常已转报损出库，报损单"+kcResult.getReturnSubId()+"", link,
                                        String.valueOf(ch999User.getCh999Id()), OaMesTypeEnum.SYSTEM.getCode().toString());
                            });
                    ;
                }).exceptionally(e -> {
                    RRExceptionHandler.logError("转报损日志和通知", Dict.create().set("smallproId",smallproId)
                            .set("incomingInfoBO",incomingInfoBO),e,smsService::sendOaMsgTo9JiMan);
                    return null;
                });
                return true;
            } else {
                result.setCode(NumberConstant.FIVE_HUNDRED);
                result.setMessage(kcResult.getMessage());
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                smallproLogService.addLogs(smallproId, logPrev + "，转报损配件操作失败", userName, 0);
                return false;
            }
        }
        //库存转现操作
        SmallproNormalCodeMessageRes kcResult = smallproForwardExService.stockOperations(
                incomingInfoBO.getPpid(), 1, inprice, areaId, userName, "", "售后小件退换转现(新)，数量：1",
                incomingInfoBO.getShouhouFanchangId(), 1, 1, null, false, false);
        if (kcResult.getCode() == NumberConstant.FIVE_HUNDRED.intValue()) {
//            smallproForwardExService.stockOperations(
//                    incomingInfoBO.getPpid1(), -1, inprice, areaId, userName, "", "售后小件退换转现，数量：-1，分布式事务补偿操作",
//                    incomingInfoBO.getShouhouFanchangId(), 1, 1, null, false, false);
            result.setCode(NumberConstant.FIVE_HUNDRED);
            result.setMessage(kcResult.getMessage());
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return false;
        }
        if (incomingInfoBO.getWuliuId() != null && isAutoCompleteWuLiu) {
            try {
                wuliuCloud.smallproAutoComplete(incomingInfoBO.getWuliuId(), incomingInfoBO.getSmallproId(), userName);
            } catch (RuntimeException e) {
                RRExceptionHandler.logError("小件转现物流自动完成", new Object[]{incomingInfoBO.getWuliuId(), smallproId,userName},e,smsService::sendOaMsgTo9JiMan);
            }
        }
        if (isWriteLog) {
            String logPrev = "旧件编号：" + shouhouFanchang.getId();
            smallproLogService.addLogs(smallproId, logPrev + "，转现", userName, 0);
            //当需要转瑕疵机时
            if (CommenUtil.isNotNullZero(transformXc) && transformXc.equals(NumberConstant.ONE)) {
                //转瑕疵 等物流组提供接口，同时记录转瑕疵日志
                SmallOldProTransformDisplayReq req = new SmallOldProTransformDisplayReq();
                req.setAreaId(areaId)
                        .setCount(1)
                        .setInUserName(userName)
                        .setPpid(incomingInfoBO.getPpid());
                try {
                    if(XtenantEnum.isJiujiXtenant()){
                        Result<DisplayProductAddReq> cloudResult = stockCloud.smallOldProTransformDisplayV2(req);
                        log.warn("转瑕疵机添加陈列操作传入参数：{}，返回结果：{}", JSONUtil.toJsonStr(req),JSONUtil.toJsonStr(cloudResult));
                        if (cloudResult.getCode() == ResultCode.SUCCESS) {
                            DisplayProductAddReq data = Optional.ofNullable(cloudResult.getData()).orElse(new DisplayProductAddReq());
                            Long id = data.getId();
                            if(ObjectUtil.isNotNull(id)){
                                String host = Optional.of(sysConfigClient.getValueByCode(SysConfigConstant.OA_URL))
                                        .filter(t -> t.getCode() == ResultCode.SUCCESS)
                                        .map(R::getData)
                                        .orElseThrow(()-> new CustomizeException("获取域名出错"));
                                String url = String.format("<a href=\"%s/mainTain/edit?areaid=%s&searchWay=mainTainId&searchKey=%s\">%s</a>", host,areaId ,id,id);
                                smallproLogService.addLogs(smallproId, logPrev + "，转瑕疵机添加陈列操作成功，陈列编号："+url, userName, 0);
                            } else {
                                smallproLogService.addLogs(smallproId, logPrev + "，转瑕疵机添加陈列操作成功", userName, 0);
                            }
                        } else {
                            smallproLogService.addLogs(smallproId, logPrev + "，转瑕疵机添加陈列操作失败", userName, 0);
                        }
                    } else {
                        Result<String> cloudResult = stockCloud.smallOldProTransformDisplay(req);
                        log.warn("转瑕疵机添加陈列操作传入参数：{}，返回结果：{}", JSONUtil.toJsonStr(req),JSONUtil.toJsonStr(cloudResult));
                        if (cloudResult.getCode() == ResultCode.SUCCESS) {
                            smallproLogService.addLogs(smallproId, logPrev + "，转瑕疵机添加陈列操作成功", userName, 0);
                        } else {
                            smallproLogService.addLogs(smallproId, logPrev + "，转瑕疵机添加陈列操作失败", userName, 0);
                        }
                    }

                } catch (RuntimeException e) {
                    RRExceptionHandler.logError("小件转瑕疵机", req,e,smsService::sendOaMsgTo9JiMan);
                }
            }
        }
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    public boolean scrapSmallproWrite(SmallproNormalCodeMessageRes smallproNormalCodeMessageRes,
                                      SmallproScrapInfoBO smallproScrapInfoBO,
                                      Map<Integer, IsVirtualProductResVo> virtualProductMap,
                                      ShouhouFanchang shouhouFanchang, OaUserBO oaUserBo, Integer smallproId
            , boolean isWriteAndAutoCompleteWuLiu) {
        boolean logFlag = false;
        LocalDateTime now = LocalDateTime.now();
        shouhouFanchang.setBfinuser(oaUserBo.getUserName()).setBftime(now).setRstats(NumberConstant.THREE);
        UpdateWrapper<ShouhouFanchang> shouhouUpdateWrapper = new UpdateWrapper<>();
        shouhouUpdateWrapper.lambda().set(ShouhouFanchang::getBfinuser, oaUserBo.getUserName()).set(ShouhouFanchang::getZxdtime
                , now).set(ShouhouFanchang::getBftime, now).set(ShouhouFanchang::getRstats, 3).eq(ShouhouFanchang::getId, shouhouFanchang.getId())
                .eq(ShouhouFanchang::getRstats, SmallproOldStatsExtendEnum.SMALLPRO_OLD_STATS_EXTEND_ALL.getCode());
        boolean flag = shouhouFanchangService.update(shouhouUpdateWrapper);
        if (!flag) {
            smallproNormalCodeMessageRes.setCode(NumberConstant.FIVE_HUNDRED);
            smallproNormalCodeMessageRes.setMessage("数据库更新失败！");
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return false;
        }
        PingzhengBO smallproPingzhengBO = new PingzhengBO();
        if (smallproScrapInfoBO.getKind1() == 1 && smallproScrapInfoBO.getInPrice() != null && smallproScrapInfoBO.getInPrice().doubleValue() > 0
                && !SmallProServiceTypeEnum.SMALL_PRO_KIND_YEAR_CARD.getCode().equals(smallproScrapInfoBO.getServiceType())) {
            double inPrice = smallproScrapInfoBO.getInPrice().setScale(2, RoundingMode.HALF_UP).doubleValue();
            double kcInPrice = smallproScrapInfoBO.getKcInprice() == null ? 0.0 :
                    smallproScrapInfoBO.getKcInprice().setScale(2, RoundingMode.HALF_UP).doubleValue();
            double differentPrice = inPrice - kcInPrice;
            if (smallproScrapInfoBO.getUserId() != 76783) {
                String kemu = "671102|640102|140502";
                String fzhs = smallproScrapInfoBO.getAreaCode() + "|" + smallproScrapInfoBO.getAreaCode() + "|无";
                String jief =
                        inPrice + "|" + (-(differentPrice)) + "|0";
                String daif = "0|0|" + kcInPrice;
                String zhaiyao =
                        "小件单：" + smallproScrapInfoBO.getSmallproId() + "，订单：" + smallproScrapInfoBO.getSubId() +
                                "，小件退换货,报废金额：" + inPrice;
                if (oaUserBo.getXTenant() >= NumberConstant.ONE_THOUSAND && oaUserBo.getXTenant() < NumberConstant.TWO_THOUSAND) {
                    kemu = "66013601|140517";
                    zhaiyao = zhaiyao + "|" + zhaiyao;
                    fzhs = smallproScrapInfoBO.getAreaCode() + "|无";
                    jief = inPrice + "|0";
                    daif = "0|" + inPrice;
                } else {
                    zhaiyao = zhaiyao + "|" + zhaiyao + "|" + zhaiyao;
                }
                smallproPingzhengBO = voucherService.buildPingzheng(zhaiyao, kemu, jief, daif, fzhs);
            } else {
                //现货凭证
                String kemu = "671102|140502";
                if (oaUserBo.getXTenant() >= NumberConstant.ONE_THOUSAND && oaUserBo.getXTenant() < NumberConstant.TWO_THOUSAND) {
                    kemu = "66013601|140517";
                }
                String fzhs = smallproScrapInfoBO.getAreaCode() + "|无";
                String jief = kcInPrice + "|0";
                String daif = "0|" + kcInPrice;
                String zhaiyao = Optional.ofNullable(XtenantEnum.isJiujiXtenant()).filter(Boolean::booleanValue)
                        .map(isJiuji-> StrUtil.format("{}（转现时间），{}（转现门店），小件接件单：{}（小件接件单）报废，金额{}元（转现配件金额）",
                                LocalDate.now().format(DateTimeFormatter.ofPattern(TimeFormatConstant.YYYY_MM_DD)),smallproScrapInfoBO.getAreaCode()
                                ,smallproScrapInfoBO.getSmallproId(),smallproScrapInfoBO.getKcInprice()))
                        .orElseGet(()->"小件单:" + smallproScrapInfoBO.getSmallproId() + ",现货，报废金额:" + smallproScrapInfoBO.getKcInprice());
                zhaiyao = zhaiyao + "|" + zhaiyao;
                smallproPingzhengBO = voucherService.buildPingzheng(zhaiyao, kemu, jief, daif, fzhs);
            }
            smallproPingzhengBO.setIsaudit("1");
            PingzhengResultBO bo = new PingzhengResultBO();
            if (oaUserBo.getXTenant() < NumberConstant.TWO_THOUSAND) {
             // 凭证接口已经废弃 无需使用
             //   bo = voucherService.addPingZheng(smallproPingzhengBO);
                bo.setFlag(Boolean.TRUE);
            } else {
                Integer ztId = authConfigService.getZtIdByAuId(oaUserBo.getAuthorizeId());
                NewVoucherBo voucher = new NewVoucherBo();
                voucher.setAct("scrapKc");
                voucher.setActName("小件报废");
                voucher.setAccountSetId(ztId.toString());
                NewVoucherBo.VoucherArgs argsO = new NewVoucherBo.VoucherArgs();
                argsO.setId(shouhouFanchang.getId());
                voucher.setArgsO(argsO);
                voucher.setSubId(shouhouFanchang.getId().toString());
                DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                voucher.setVoucherTime(LocalDateTime.now().format(dtf));
                voucher.setAreaId(oaUserBo.getAreaId());
                IsVirtualProductResVo isVirtualProductRes = virtualProductMap.get(shouhouFanchang.getPpid());
                if (isVirtualProductRes == null || Boolean.FALSE.equals(isVirtualProductRes.getIsVirtual())) {
                    //异步处理
                    voucherService.addNewVoucher(voucher);
                }
                bo.setFlag(Boolean.TRUE);
            }
            if (Boolean.TRUE.equals(bo.getFlag()) && oaUserBo.getXTenant() < NumberConstant.TWO_THOUSAND) {
                // 更新shouhoufanchang表里的pzid
                shouhouFanchang.setPzid(bo.getPzId());
                if (bo.getPzId() != null) {
                    smallproLogService.addLogs(smallproId, StrUtil.format("旧件编号：{},报废,凭证:{}", shouhouFanchang.getId(), bo.getPzId()), oaUserBo.getUserName(), 0);
                }
                boolean updateFlag = shouhouFanchangService.updateById(shouhouFanchang);
                if (!updateFlag) {
                    log.error("售后返厂更新凭证Id失败！pzId:" + bo.getPzId());
                }
            }
            if (!bo.getFlag()) {
                smallproNormalCodeMessageRes.setCode(NumberConstant.FIVE_HUNDRED);
                smallproNormalCodeMessageRes.setMessage("凭证生成失败！");
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return false;
            } else {
                logFlag = true;
                smallproNormalCodeMessageRes.setCode(0);
                smallproNormalCodeMessageRes.setMessage("操作成功！");
            }
        } else {
            logFlag = true;
            smallproNormalCodeMessageRes.setCode(0);
            smallproNormalCodeMessageRes.setMessage("操作成功！");
        }
        if (logFlag && isWriteAndAutoCompleteWuLiu) {
            if (smallproScrapInfoBO.getWuliuId() != null) {
                wuliuCloud.smallproAutoComplete(smallproScrapInfoBO.getWuliuId(), smallproId, oaUserBo.getUserName());
            }
            //物流单自动完成失败,不记录报废日志 日志放到最后 xxk
            smallproLogService.addLogs(smallproId, "旧件编号：" + shouhouFanchang.getId() + ",报废", oaUserBo.getUserName(), 0);
        }
        return true;
    }


    @Transactional(rollbackFor = Exception.class)
    public Integer addSmallproReturnToFactoryLogisticsWrite(SmallproNormalCodeMessageRes result, SubWLModelReq req,
                                                            OaUserBO oaUserBO, List<Integer> smallproIds) {
        R<Integer> wuliuResult = wuliuCloud.saveWuLiu(req, oaUserBO.getAreaId(), oaUserBO.getUserName());
        Integer wuliuId = null;
        if (wuliuResult.getCode() == 0) {
            wuliuId = wuliuResult.getData();
        } else {
            result.setCode(NumberConstant.FIVE_HUNDRED);
            result.setMessage("物流单生成失败！");
            return null;
        }
        for (Integer smallproId : smallproIds) {
            smallproLogService.addLogs(smallproId,
                    "[在途] PDA扫描操作，生成小件发往D1物流单【<a href=\"/addOrder/wuliu?wuliuid=" + wuliuId + "\">" + wuliuId + "</a>】",
                    oaUserBO.getUserName(), 0);
        }

        UpdateWrapper<Smallpro> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().set(Smallpro::getWuliuId, wuliuId).in(Smallpro::getId, smallproIds);
        boolean updateFlag = smallproService.update(updateWrapper);
        if (!updateFlag) {
            result.setCode(NumberConstant.FIVE_HUNDRED);
            result.setMessage("物流单绑定小件单失败！");
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            wuliuCloud.delWuliuById(wuliuId);
            return null;
        }
        result.setCode(0);
        result.setMessage(String.valueOf(wuliuId));
        return wuliuId;
    }


    // endregion

}
