package com.jiuji.oa.afterservice.bigpro.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.afterservice.bigpro.dao.ShouhouCommentMapper;
import com.jiuji.oa.afterservice.bigpro.dao.ShouhouToareaMapper;
import com.jiuji.oa.afterservice.bigpro.enums.OaMesTypeEnum;
import com.jiuji.oa.afterservice.bigpro.po.Shouhou;
import com.jiuji.oa.afterservice.bigpro.po.ShouhouComment;
import com.jiuji.oa.afterservice.bigpro.po.ShouhouToarea;
import com.jiuji.oa.afterservice.bigpro.service.ShouhouCommentService;
import com.jiuji.oa.afterservice.bigpro.service.ShouhouService;
import com.jiuji.oa.afterservice.bigpro.service.SmsService;
import com.jiuji.oa.afterservice.bigpro.vo.req.ShouhouCommentReq;
import com.jiuji.oa.afterservice.common.bo.OaUserBO;
import com.jiuji.oa.afterservice.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.afterservice.common.enums.MTableInfoEnum;
import com.jiuji.oa.afterservice.common.util.CommenUtil;
import com.jiuji.oa.afterservice.other.bo.AreaInfoSimpleBO;
import com.jiuji.oa.afterservice.other.service.AreainfoService;
import com.jiuji.oa.orginfo.areainfo.client.AreaInfoClient;
import com.jiuji.oa.orginfo.areainfo.vo.AreaInfo;
import com.jiuji.oa.orginfo.userinfo.client.UserInfoClient;
import com.jiuji.oa.orginfo.userinfo.vo.Ch999UserVo;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.common.vo.ResultCode;
import com.jiuji.tc.foundation.message.send.service.MessageSendService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;


@Service("shouhouCommentService")
public class ShouhouCommentServiceImpl extends ServiceImpl<ShouhouCommentMapper, ShouhouComment> implements ShouhouCommentService {

    @Resource
    private AbstractCurrentRequestComponent currentRequestComponent;
    @Lazy
    @Autowired
    private ShouhouService shouhouService;
    @Autowired
    private SmsService smsService;
    @Autowired
    private AreainfoService areainfoService;
    @Autowired
    private ShouhouToareaMapper shouhouToareaMapper;
    @Autowired
    private UserInfoClient userInfoClient;
    @Autowired
    private AreaInfoClient areaInfoClient;
    @Autowired
    private MessageSendService messageSendService;

    @Override
    public R<List<ShouhouComment>> getShouhouComment(Integer shouhouId) {
        List<ShouhouComment> commentList = null;
        commentList = super.list(new LambdaQueryWrapper<ShouhouComment>().eq(ShouhouComment::getShouhouId, shouhouId).orderByDesc(ShouhouComment::getId));
        return R.success(commentList);
    }

    @Override
    public R<Boolean> reply(ShouhouCommentReq req) {
        OaUserBO userBO = currentRequestComponent.getCurrentStaffId();
        if (userBO == null) {
            return R.error("请先登录");
        }
        ShouhouComment comment = new ShouhouComment();
        comment.setId(req.getId());
        comment.setReply(req.getComment());
        comment.setReplyer(userBO.getUserName());
        comment.setReplytime(LocalDateTime.now());
        super.updateById(comment);

        if (req.getShoushouId() != 0) {
            //Shouhou shouhou = shouhouService.getById(req.getShoushouId());
            Shouhou shouhou = CommenUtil.autoQueryHist(() ->shouhouService.getById(req.getShoushouId()), MTableInfoEnum.SHOUHOU, req.getShoushouId());
            if (shouhou.getIszy() != null && shouhou.getIszy()) {
                String sendMsg = String.format("您留言的维修单%s有新回复：" + req.getComment(), req.getShoushouId());
                Integer userId = shouhou.getUserid().intValue();
                String wxName = shouhou.getName();
                AreaInfoSimpleBO areainfo = areainfoService.getAreaInfoSimpleByAreaId(shouhou.getAreaid() == null ? 0 : shouhou.getAreaid());
                String url = "http://fix.999buy.com/orderDetail/" + req.getShoushouId() + "/1";
                smsService.sendZyWeixinMsg(req.getShoushouId(), userId, "售后维修留言回复提醒", wxName, sendMsg, url, areainfo == null ? 0 : areainfo.getCityId());
            }
        }
        return R.success(true);
    }

    /**
     * 转地区3天未接收转地区的提交人做oa消息推送
     */
    @Override
    public void pushMsgOa() {
        List<ShouhouToarea> shouhouToareas = shouhouToareaMapper.getShouhouToarea();
        List<Ch999UserVo> ch999Ids =  Collections.emptyList();
        List<AreaInfo> areas = Collections.emptyList();
        if (CollectionUtils.isNotEmpty(shouhouToareas)){
            R<List<Ch999UserVo>> ch999UserVoList = userInfoClient.getCh999UserByUserNames(shouhouToareas.parallelStream().map(ShouhouToarea::getInuser).distinct().collect(Collectors.toList()));
            if (ch999UserVoList.getCode() == ResultCode.SUCCESS && ch999UserVoList.getData() != null) {
                 ch999Ids = ch999UserVoList.getData();
            }
            R<List<AreaInfo>> areaInfoRet = areaInfoClient.listAreaInfo(shouhouToareas.parallelStream().map(ShouhouToarea::getToareaid).distinct().collect(Collectors.toList()));
            if (areaInfoRet.getCode() == ResultCode.SUCCESS && areaInfoRet.getData() != null) {
                areas = areaInfoRet.getData();
            }
            for (ShouhouToarea s: shouhouToareas){
               for (AreaInfo a :areas){
                   if (s.getToareaid().equals(a.getId())){
                       s.setArea(a.getArea());
                   }
               }
               for (Ch999UserVo c:ch999Ids){
                   if (s.getInuser().equals(c.getCh999Name())){
                       s.setInuserId(c.getCh999Id()+"");
                   }
               }
                smsService.sendOaMsg("维修单"+s.getShouhouId()+"未在预计时间达到"+s.getArea()+"店，请尽快核实情况，以免延长客户送修时间","",s.getInuserId(), OaMesTypeEnum.SHTZ);
                shouhouService.saveShouhouLog(s.getShouhouId(),"维修单"+s.getShouhouId()+"未在预计时间达到"+s.getArea()+"店，请尽快核实情况，以免延长客户送修时间",s.getInuser(),null,false);
            }
        }

    }
}