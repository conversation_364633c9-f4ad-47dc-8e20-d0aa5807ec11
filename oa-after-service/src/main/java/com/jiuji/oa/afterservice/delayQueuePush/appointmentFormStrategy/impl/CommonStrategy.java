package com.jiuji.oa.afterservice.delayQueuePush.appointmentFormStrategy.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.jiuji.oa.afterservice.bigpro.enums.OaMesTypeEnum;
import com.jiuji.oa.afterservice.bigpro.po.ShouhouYuyue;
import com.jiuji.oa.afterservice.bigpro.po.WXSmsReceiver;
import com.jiuji.oa.afterservice.bigpro.service.KaoqinService;
import com.jiuji.oa.afterservice.bigpro.service.ShouhouYuyueService;
import com.jiuji.oa.afterservice.bigpro.service.SmsService;
import com.jiuji.oa.afterservice.bigpro.service.WXSmsReceiverService;
import com.jiuji.oa.afterservice.common.enums.SmsReceiverClassfyEnum;
import com.jiuji.oa.afterservice.delayQueuePush.vo.AnalysisResultBO;
import com.jiuji.oa.afterservice.delayQueuePush.vo.AppointmentFormPush;
import com.jiuji.oa.afterservice.delayQueuePush.vo.TimeInfo;
import com.jiuji.oa.afterservice.sys.service.SysConfigService;
import com.jiuji.tc.utils.constants.NumberConstant;
import com.jiuji.tc.utils.constants.SysConfigConstant;
import com.meitu.platform.lmstfy.Job;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class CommonStrategy {

    @Resource
    private SmsService smsService;

    @Resource
    private SysConfigService sysConfigService;

    @Resource
    private ShouhouYuyueService shouhouYuyueService;


    public static final Integer THIRTY_MINUTE = 30*60;
    public static final Integer ONE_HOUR = 60*60;
    public static final Integer SEVENTY_TWO_HOUR = 72*60*60;
    /**
     * 判断当前是否为消息堆积时间（0点到9点30之间）
     * @return
     */
    public Boolean isAccumulationTime(){
        LocalDateTime now = LocalDateTime.now();
        if(now.isAfter(getTimeInfo().getStartTime()) && now.isBefore(getTimeInfo().getEndTime())){
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }


    public static TimeInfo getTimeInfo(){
        TimeInfo timeInfo = new TimeInfo();
        //停止推送开始时间 当天0点
        timeInfo.setStartTime(LocalDateTime.of(LocalDate.now(), LocalTime.MIN));
        //停止推送结束时间 当天9点30
        timeInfo.setEndTime(LocalDateTime.of(LocalDate.now(), LocalTime.MIN)
                .plusHours(NumberConstant.NINE)
                .plusMinutes(NumberConstant.THIRTY));
        return timeInfo;
    }

    /**
     * 通过areaid获取ch999Id
     *
     * @param classifyEnum 配置类型
     * @param areaId  门店id
     * @return
     */
    public List<String> getUserIdByAreaId(SmsReceiverClassfyEnum classifyEnum, Integer areaId){
        //消息类型为空或者门店id为空返回空
        if(classifyEnum == null || ObjectUtil.defaultIfNull(areaId,0) <= 0){
            return Collections.emptyList();
        }
        //根据类型获取发送配置信息
        Optional<WXSmsReceiver> srOpt = SpringUtil.getBean(WXSmsReceiverService.class).lambdaQuery().eq(WXSmsReceiver::getClassify, classifyEnum.getCode())
                .orderByDesc(WXSmsReceiver::getId).list().stream().findFirst();
        if(!srOpt.isPresent()){
            log.warn("没有{}对应的配置信息",classifyEnum.getMessage());
            return Collections.emptyList();
        }
        WXSmsReceiver sr = srOpt.get();
        List<Integer> ch999Ids = StrUtil.splitTrim(sr.getCh999ids(), StringPool.COMMA).stream().map(Convert::toInt).collect(Collectors.toList());
        List<Integer> zhiWuIds = StrUtil.splitTrim(sr.getZhiWus(), StringPool.COMMA).stream().map(Convert::toInt).collect(Collectors.toList());
        List<Integer> roleIds = StrUtil.splitTrim(sr.getRoleIds(), StringPool.COMMA).stream().map(Convert::toInt).collect(Collectors.toList());
        //根据配置获取工号/职务/角色 是或关系
        List<Integer> result = new LinkedList<>();
        KaoqinService kaoqinService = SpringUtil.getBean(KaoqinService.class);
        if(!ch999Ids.isEmpty()){
            result.addAll(kaoqinService.getCurrentWorkByAreaIdAndCh999Ids(areaId, ch999Ids));
        }
        if(!zhiWuIds.isEmpty()){
            result.addAll(kaoqinService.getCurrentWorkByAreaIdAndZhiWuIds(areaId,zhiWuIds));
        }
        if(!roleIds.isEmpty()){
            result.addAll(kaoqinService.getCurrentWorkByAreaIdAndRoleIds(areaId,roleIds));
        }
        if(result.isEmpty() && !ch999Ids.isEmpty()){
            //兜底逻辑
            result.addAll(kaoqinService.getCurrentWork(ch999Ids));
        }
        return result.stream().distinct().map(Convert::toStr).collect(Collectors.toList());
    }

    /**
     * 直接推送售后OA信息
     * @param appointmentFormPush
     */
    public void pushDirectMsg(AppointmentFormPush appointmentFormPush,SmsReceiverClassfyEnum classifyEnum){
        //通过门店id查询到推送人的
        List<String> userIdList = getUserIdByAreaId(classifyEnum,appointmentFormPush.getAreaId());
        StringJoiner stringJoiner = new StringJoiner(",");
        if(CollectionUtils.isNotEmpty(userIdList)){
            userIdList.forEach(stringJoiner::add);
        }
        String host = sysConfigService.getValueByCode(SysConfigConstant.MOA_URL);
        String url = String.format("<a href= %s/new/#/afterService/detail/%s>%s</a>",host,appointmentFormPush.getId(),appointmentFormPush.getId());
        String msg="您所负责的门店有网络预约订单："+url+"需要跟进确认";
        //OA消息推送
        sendOaShouhouMsg(stringJoiner,msg,appointmentFormPush.getId(),appointmentFormPush);
    }


    /**
     * 优先取AppointmentFormPush对对象里面delaySecond字段为延迟时间，如果过该字段为空进行下面计算
     * 计算当前时间是不是在0点到9点30之间，如果是那就进行时间差计算，如果不是那就取30分钟
     * 并且通过AppointmentFormPush对象里面pushNumber字段统计推送次数
     * 计算延迟队列
     * @param appointmentFormPush
     * @return
     */
    public Integer calculationDelaySecond(AppointmentFormPush appointmentFormPush){
        LocalDateTime now = LocalDateTime.now();
        //判断当前时间在当天0点到9点30之间
        int calculationDelaySecond;
        if(now.isAfter(getTimeInfo().getStartTime()) && now.isBefore(getTimeInfo().getEndTime())){
            //计算当前时间到9点30相差几秒
            calculationDelaySecond = (int) Math.abs(getTimeInfo().getEndTime().until(now, ChronoUnit.SECONDS));
        } else {
            //取值30分钟
            calculationDelaySecond=THIRTY_MINUTE;
        }
        //推送次数需要加一用来统计同一个预约单被推送了几次
        appointmentFormPush.setPushNumber(appointmentFormPush.getPushNumber()+ NumberConstant.ONE);
        //传延迟时间如果为空那就取值calculationDelaySecond
        return Optional.ofNullable(appointmentFormPush.getDelaySecond()).orElse(calculationDelaySecond);
    }

    /**
     * 售后信息推送
     * @param ch999IdsJoiner
     * @param msg
     * @param shouhouYuyueId
     */
    public void sendOaShouhouMsg(StringJoiner ch999IdsJoiner, String msg, Integer shouhouYuyueId, AppointmentFormPush appointmentFormPush){
        Boolean isPush = Optional.ofNullable(appointmentFormPush.getIsPush()).orElse(Boolean.TRUE);
        //判断相关负责人不为空,消息推送给相关负责人
        log.warn("前置判断售后预约OA推送人员：{},订单号：{},appointmentFormPush参数为：{}",ch999IdsJoiner.toString(),shouhouYuyueId,appointmentFormPush.toString());
        if(ch999IdsJoiner.length()>NumberConstant.ZERO && isPush){
            String host = sysConfigService.getValueByCode(SysConfigConstant.MOA_URL);
            String link = host + "/Mshouhouyuyue/yuyueadd/" + shouhouYuyueId;
            smsService.sendOaMsg(msg,link,ch999IdsJoiner.toString(), OaMesTypeEnum.SHTZ);
            log.warn("售后预约OA推送人员：{},订单号：{},appointmentFormPush参数为：{}",ch999IdsJoiner.toString(),shouhouYuyueId,appointmentFormPush.toString());
        }
    }


    /**
     * 根据消费队列获取售后预约单
     * @param job
     * @return
     */
    public AnalysisResultBO getAnalysisResult(Job job){
        AnalysisResultBO analysisResultBO = new AnalysisResultBO();
        //预防调用方法获取使用的时候出现空指针异常
        analysisResultBO.setAppointmentFormPush(new AppointmentFormPush())
                .setShouhouYuyue(new ShouhouYuyue());
        String data = Optional.ofNullable(job).orElse(new Job()).getData();
        if(StringUtils.isEmpty(data)){
            return analysisResultBO;
        }
        AppointmentFormPush appointmentFormPush;
        try {
            appointmentFormPush = JSONUtil.toBean(data, AppointmentFormPush.class);
            analysisResultBO.setAppointmentFormPush(appointmentFormPush);
        } catch (Exception e){
            log.error("消费售后预约单延迟队列反序列化失败，反序列数据为{}",data,e);
            return analysisResultBO;
        }
        Integer id = appointmentFormPush.getId();
        ShouhouYuyue shouhouYuyue = shouhouYuyueService.getById(id);
        analysisResultBO.setShouhouYuyue(shouhouYuyue);
        return analysisResultBO;
    }
}
