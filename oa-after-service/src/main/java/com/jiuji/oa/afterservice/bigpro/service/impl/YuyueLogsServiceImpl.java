package com.jiuji.oa.afterservice.bigpro.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.afterservice.apollo.ApolloEntity;
import com.jiuji.oa.afterservice.bigpro.dao.YuyueLogsMapper;
import com.jiuji.oa.afterservice.bigpro.enums.OaMesTypeEnum;
import com.jiuji.oa.afterservice.bigpro.po.ShouhouYuyue;
import com.jiuji.oa.afterservice.bigpro.po.YuyueLogs;
import com.jiuji.oa.afterservice.bigpro.service.ShouhouYuyueService;
import com.jiuji.oa.afterservice.bigpro.service.SmsService;
import com.jiuji.oa.afterservice.bigpro.service.WeixinUserService;
import com.jiuji.oa.afterservice.bigpro.service.YuyueLogsService;
import com.jiuji.oa.afterservice.bigpro.vo.req.YuyueLogsAddReq;
import com.jiuji.oa.afterservice.cloud.service.IMCloud;
import com.jiuji.oa.afterservice.common.bo.OaUserBO;
import com.jiuji.oa.afterservice.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.afterservice.common.enums.ESmsChannelTypeEnum;
import com.jiuji.oa.afterservice.common.enums.XtenantEnum;
import com.jiuji.oa.afterservice.common.util.CommenUtil;
import com.jiuji.oa.afterservice.common.util.DateUtil;
import com.jiuji.oa.afterservice.sys.service.SysConfigService;
import com.jiuji.oa.nc.SmsConfigCloud;
import com.jiuji.oa.nc.sms.SmsConfigCodeEnum;
import com.jiuji.oa.nc.sms.SmsConfigVO;
import com.jiuji.oa.nc.sms.SmsPushMethodEnum;
import com.jiuji.oa.orginfo.userinfo.client.UserInfoClient;
import com.jiuji.oa.orginfo.userinfo.vo.Ch999UserVo;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.common.vo.ResultCode;
import com.jiuji.tc.utils.common.CommonUtils;
import com.jiuji.tc.utils.constants.SysConfigConstant;
import com.jiuji.tc.utils.xtenant.Namespaces;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-13
 */
@Service
public class YuyueLogsServiceImpl extends ServiceImpl<YuyueLogsMapper, YuyueLogs> implements YuyueLogsService {

    @Resource
    private AbstractCurrentRequestComponent currentRequestComponent;
    @Autowired
    private SmsService smsService;
    @Autowired
    @Lazy
    private ShouhouYuyueService shouhouYuyueService;
    @Resource
    private UserInfoClient userInfoClient;
    @Resource
    private IMCloud imCloud;
    @Autowired
    private WeixinUserService weixinUserService;
    @Autowired
    private SysConfigService sysConfigService;
    @Resource
    private SmsConfigCloud smsConfigCloud;
    @Resource
    private ApolloEntity apolloEntity;

    @Override
    public List<YuyueLogs> getYuyueLogs(Integer yuyueId) {
        return baseMapper.selectList(new LambdaQueryWrapper<YuyueLogs>().eq(YuyueLogs::getYuyueId, yuyueId).orderByAsc(YuyueLogs::getId));
    }

    @Override
    public Boolean yuyueLogsAdd(Integer yuyueId, String comment, String inUser, Integer viewType) {
        if (viewType == null) {
            viewType = 0;
        }
        YuyueLogs yuyueLogs = new YuyueLogs();
        yuyueLogs.setYuyueId(yuyueId);
        yuyueLogs.setInuser(inUser);
        yuyueLogs.setComment(comment);
        yuyueLogs.setViewType(viewType);
        yuyueLogs.setDtime(LocalDateTime.now());
        return super.save(yuyueLogs);
    }

    @Override
    public R<Boolean> yuyueLogsAdd(YuyueLogsAddReq req) {
        OaUserBO oaUserBO = currentRequestComponent.getCurrentStaffId();
        String host = sysConfigService.getValueByCode(SysConfigConstant.MOA_URL);
        if (req.getNotice() == null){
            //前端不给我传参数
            req.setNotice(new YuyueLogsAddReq.YuyueNotice().setToUser("").setToEmail(Boolean.FALSE).setToSms(Boolean.FALSE).setToWechat(Boolean.FALSE));
        }
        if (null != req.getNotice()) {
            String comment = "（";
            //微信，OAAPP 通知员工
            String link = host + "/Mshouhouyuyue/yuyueadd/" + req.getYuyueId();
            ShouhouYuyue yuyueInfo = shouhouYuyueService.getById(req.getYuyueId());
            String productName = yuyueInfo.getName();

            String msg =
                    "售后预约：<a href='" + host + "/Mshouhouyuyue/yuyueadd/" + req.getYuyueId().toString() + "'>" + req.getYuyueId().toString() + "</a>" + "，商品(" + productName + ")，" + req.getComment();
            Ch999UserVo ch999User = null;
            R<Ch999UserVo> ch999UserVoR = userInfoClient.getCh999UserByUserName(req.getNotice().getToUser());
            if (ch999UserVoR.getCode() == ResultCode.SUCCESS && ch999UserVoR.getData() != null) {
                ch999User = ch999UserVoR.getData();
            }
            if (req.getNotice().getToEmail() != null && req.getNotice().getToEmail()) {
                comment += "内部邮件 ";
                msg = "售后预约：" + req.getYuyueId().toString() + "，商品(" + productName + ")，" + req.getComment();
                if (ch999User != null) {
                    smsService.sendOaMsg(msg, link, ch999User.getCh999Id().toString(), OaMesTypeEnum.SHTZ.getCode().toString());
                }
            }

            if (req.getNotice().getToSms() != null && req.getNotice().getToSms()) {
                if (ch999User != null) {
                    comment += "短信 ";
                    long xtenant = Namespaces.get();
                    String openXtenantStr = apolloEntity.getSmsConfigOpenXtenant();
                    if (XtenantEnum.isSaasXtenant()
                            && StringUtils.isNotEmpty(openXtenantStr)
                            && CommonUtils.covertIdStr(openXtenantStr).contains((int) xtenant)) {
                        R<SmsConfigVO> smsConfigResult = smsConfigCloud.getConfigByCode(xtenant,
                                SmsConfigCodeEnum.CODE_104.getCode());
                        if (CommonUtils.isRequestSuccess(smsConfigResult)) {
                            SmsConfigVO smsConfig = smsConfigResult.getData();
                            // 消息内容
                            List<SmsConfigVO.SmsField> fields = smsConfig.getFields();
                            String message = smsConfig.getTemplate();
                            if (StringUtils.isNotEmpty(message)
                                    && org.apache.commons.collections4.CollectionUtils.isNotEmpty(fields)) {
                                for (SmsConfigVO.SmsField field : fields) {
                                    if ("<subId>".equals(field.getValue())) {
                                        String yuyueId = String.format("<a href='%s/Mshouhouyuyue/yuyueadd/%s'>%s</a>", host, req.getYuyueId(), req.getYuyueId());
                                        message = message.replace(field.getValue(), yuyueId);
                                    }
                                    if ("<productName>".equals(field.getValue())) {
                                        message = message.replace(field.getValue(), productName);
                                    }
                                    if ("<comment>".equals(field.getValue())) {
                                        message = message.replace(field.getValue(), req.getComment());
                                    }
                                }
                            }
                            // 推送方式
                            List<Integer> pushMethods = smsConfig.getPushMethod();
                            // sms消息
                            if (CollectionUtils.isNotEmpty(pushMethods)
                                    && pushMethods.contains(SmsPushMethodEnum.SMS.getCode())) {
                                msg = message;
                            }
                        }
                    }
                    String url = host + "/Mshouhouyuyue/yuyueadd/" + req.getYuyueId().toString();
                    String shortUrl = smsService.getShortUrl(0L, url, "");
                    smsService.sendSms(ch999User.getMobile(), msg + "," + shortUrl, DateUtil.localDateTimeToString(LocalDateTime.now()), oaUserBO.getUserName(), smsService.getSmsChannelByTenant(oaUserBO.getAreaId(), ESmsChannelTypeEnum.YZMTD));
                }
            }
            if (req.getNotice().getToWechat() != null && req.getNotice().getToWechat()) {
                if (ch999User != null) {

                    comment += "微信(app) ";
                    msg = "售后预约：" + req.getYuyueId().toString() + "，商品(" + productName + ")，" + req.getComment();
                    long xtenant = Namespaces.get();
                    String openXtenantStr = apolloEntity.getSmsConfigOpenXtenant();
                    if (XtenantEnum.isSaasXtenant()
                            && StringUtils.isNotEmpty(openXtenantStr)
                            && CommonUtils.covertIdStr(openXtenantStr).contains((int) xtenant)) {
                        R<SmsConfigVO> smsConfigResult = smsConfigCloud.getConfigByCode(xtenant,
                                SmsConfigCodeEnum.CODE_104.getCode());
                        if (CommonUtils.isRequestSuccess(smsConfigResult)) {
                            SmsConfigVO smsConfig = smsConfigResult.getData();
                            // 消息内容
                            List<SmsConfigVO.SmsField> fields = smsConfig.getFields();
                            String message = smsConfig.getTemplate();
                            if (StringUtils.isNotEmpty(message)
                                    && org.apache.commons.collections4.CollectionUtils.isNotEmpty(fields)) {
                                for (SmsConfigVO.SmsField field : fields) {
                                    if ("<subId>".equals(field.getValue())) {
                                        message = message.replace(field.getValue(), String.valueOf(req.getYuyueId()));
                                    }
                                    if ("<productName>".equals(field.getValue())) {
                                        message = message.replace(field.getValue(), productName);
                                    }
                                    if ("<comment>".equals(field.getValue())) {
                                        message = message.replace(field.getValue(), req.getComment());
                                    }
                                }
                            }
                            // 推送方式
                            List<Integer> pushMethods = smsConfig.getPushMethod();
                            // 微信消息
                            if (pushMethods.contains(SmsPushMethodEnum.WECHAT_MSG.getCode())) {
                                msg = message;
                            }
                        }
                    }
                    List<String> openId = weixinUserService.getOpenIdByCh999IdList(Arrays.asList(ch999User.getCh999Id()));
                    if (CollectionUtils.isNotEmpty(openId)) {
                        imCloud.sendAfterServiceProgressMsg(openId.get(0), link, msg, "预约进程通知", "内部通知", DateUtil.localDateTimeToString(LocalDateTime.now()), req.getComment(), msg, null, 0L);
                    }
                }
            }
            if (CommenUtil.isNotNullZero(req.getViewType())) {
                comment += "已对接通知：" + req.getNotice().getToUser() + "）";
            }
            //修复显示bug
            if (Objects.equals("（", comment)) {
                comment = "";
            }
            Boolean flag = this.yuyueLogsAdd(req.getYuyueId(), req.getComment() + comment, oaUserBO.getUserName(), req.getViewType());
            if (!flag) {
                return R.error("添加进程失败");
            }
        }

        return R.success("操作成功");
    }
}
