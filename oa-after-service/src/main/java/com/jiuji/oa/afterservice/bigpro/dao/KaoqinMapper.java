package com.jiuji.oa.afterservice.bigpro.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jiuji.oa.afterservice.bigpro.bo.AttendanceStatusBO;
import com.jiuji.oa.afterservice.bigpro.po.Kaoqin;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-18
 */
@Mapper
public interface KaoqinMapper extends BaseMapper<Kaoqin> {

    /**
     * 查询正在上班的用户
     * @param ch999Ids
     * @return
     */
    List<Integer> getCurrentWork(@Param("ch999Ids") List<Integer> ch999Ids);

    Collection<Integer> getCurrentWorkByAreaIdAndCh999Ids(@Param("areaId") Integer areaId, @Param("ch999Ids") List<Integer> ch999Ids);

    Collection<Integer> getCurrentWorkByAreaIdAndRoleIds(@Param("areaId") Integer areaId, @Param("roleIds") List<Integer> roleIds);

    Collection<Integer> getCurrentWorkByAreaIdAndZhiWuIds(@Param("areaId") Integer areaId, @Param("zhiWuIds") List<Integer> zhiWuIds);

    /**
     * 查询业务确认人的考勤和暂离状态
     * @param ch999Id 用户ID
     * @param sTime 开始时间
     * @param eTime 结束时间
     * @return 考勤状态信息
     */
    AttendanceStatusBO getAttendanceStatus(@Param("ch999Id") Integer ch999Id,
                                          @Param("sTime") LocalDateTime sTime,
                                          @Param("eTime") LocalDateTime eTime);
}
