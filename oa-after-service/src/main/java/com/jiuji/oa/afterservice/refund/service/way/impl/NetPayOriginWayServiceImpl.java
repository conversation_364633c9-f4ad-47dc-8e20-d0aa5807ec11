package com.jiuji.oa.afterservice.refund.service.way.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.jiuji.oa.afterservice.bigpro.dao.ShouhouRefundMapper;
import com.jiuji.oa.afterservice.bigpro.po.refund.ShouhouTuiHuanPo;
import com.jiuji.oa.afterservice.bigpro.service.ShouhouRefundService;
import com.jiuji.oa.afterservice.common.constant.RedisKeys;
import com.jiuji.oa.afterservice.common.constant.RequestAttrKeys;
import com.jiuji.oa.afterservice.common.constant.RequestCacheKeys;
import com.jiuji.oa.afterservice.common.enums.MTableInfoEnum;
import com.jiuji.oa.afterservice.common.enums.TuihuanKindEnum;
import com.jiuji.oa.afterservice.common.enums.XtenantEnum;
import com.jiuji.oa.afterservice.common.exception.CustomizeException;
import com.jiuji.oa.afterservice.common.util.CommenUtil;
import com.jiuji.oa.afterservice.common.util.SpringContextUtil;
import com.jiuji.oa.afterservice.other.po.NetPayRefundInfo;
import com.jiuji.oa.afterservice.other.service.NetPayRefundInfoService;
import com.jiuji.oa.afterservice.refund.bo.DetailParamBo;
import com.jiuji.oa.afterservice.refund.bo.PayGatewayMchBo;
import com.jiuji.oa.afterservice.refund.bo.RefundSubInfoBo;
import com.jiuji.oa.afterservice.refund.dao.way.NetPayOriginWayMapper;
import com.jiuji.oa.afterservice.refund.enums.ThirdRefundTypeEnum;
import com.jiuji.oa.afterservice.refund.enums.TuiGroupEnum;
import com.jiuji.oa.afterservice.refund.enums.TuiWayMatchDbEnum;
import com.jiuji.oa.afterservice.refund.enums.ZheJiaPayEnum;
import com.jiuji.oa.afterservice.refund.po.ShouhouTuihuanDetailPo;
import com.jiuji.oa.afterservice.refund.service.BaseRefundService;
import com.jiuji.oa.afterservice.refund.service.impl.BaseRefundServiceImpl;
import com.jiuji.oa.afterservice.refund.service.way.DeductionRefundService;
import com.jiuji.oa.afterservice.refund.service.way.NetPayOriginWayService;
import com.jiuji.oa.afterservice.refund.utils.RefundMoneyUtil;
import com.jiuji.oa.afterservice.refund.vo.req.AlipayInfoReqVo;
import com.jiuji.oa.afterservice.refund.vo.req.GroupTuihuanFormVo;
import com.jiuji.oa.afterservice.refund.vo.res.RefundMoneyDetailVo;
import com.jiuji.oa.afterservice.refund.vo.res.RefundWayDetailVo;
import com.jiuji.oa.afterservice.refund.vo.res.way.NetPayOriginRefundVo;
import com.jiuji.oa.afterservice.refund.vo.res.way.OtherRefundVo;
import com.jiuji.oa.afterservice.smallpro.service.NetpayRecordService;
import com.jiuji.oa.pay.sdk.dto.channelconfig.ChannelConfigDto;
import com.jiuji.oa.pay.sdk.dto.channelconfig.SimpleChannelConfigDto;
import com.jiuji.oa.pay.sdk.dto.req.config.ConfigListQueryByIdsReq;
import com.jiuji.oa.pay.sdk.dto.req.config.ConfigQueryReq;
import com.jiuji.oa.pay.sdk.dto.res.config.ConfigListQueryByIdsRes;
import com.jiuji.oa.pay.sdk.execute.ApiRes;
import com.jiuji.oa.pay.sdk.execute.ClientExecute;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.utils.common.DecideUtil;
import com.jiuji.tc.utils.common.build.LambdaBuild;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 原路径退款服务接口
 * <AUTHOR>
 * @since 2022/7/19 14:32
 */
@Service
@Slf4j
public class NetPayOriginWayServiceImpl extends BaseRefundServiceImpl<NetPayOriginRefundVo> implements NetPayOriginWayService {
    @Resource
    @Lazy
    private NetPayOriginWayService netPayOriginWayService;
    @Resource
    private NetPayOriginWayMapper netPayOriginWayMapper;

    @Resource
    private NetPayRefundInfoService netPayRefundInfoService;

    @Resource
    private NetpayRecordService netpayRecordService;

    @Override
    protected void setGroupDataByRefundWays(DetailParamBo detailParamBo, RefundWayDetailVo<NetPayOriginRefundVo> refundWayDetailVo, R<RefundMoneyDetailVo> result) {
        RefundMoneyDetailVo detailVo = result.getData();
        Optional.of(detailVo.getRefundWays().stream().filter(this::containMyGroup).map(RefundMoneyDetailVo.RefundWayVo::getName)
                .flatMap(this::tuiWayMatchDb)
                .collect(Collectors.toSet())).filter(CollUtil::isNotEmpty)
                .map(originWays -> {
                    List<NetPayOriginRefundVo> alipayPayInfos = netPayOriginWayService.getAlipayPayInfo(new AlipayInfoReqVo()
                            .setSubId(detailParamBo.getRefundSubInfo().getOrderId()).setTuiWays(originWays)
                            .setTuihuanKind(detailParamBo.getTuihuanKindEnum()));
                    return alipayPayInfos.stream().filter(filterAlipayPayInfoFun(detailVo)).collect(Collectors.toList());
                })
                .ifPresent(refundWayDetailVo::setGroupData);
        refundWayDetailVo.setDataTemplate(new NetPayOriginRefundVo().setGroupCode(getMyGroup().getCode()));
    }
    /**
     * 过滤ali支付信息函数
     */
    private Predicate<NetPayOriginRefundVo> filterAlipayPayInfoFun(RefundMoneyDetailVo detailVo) {
        return api -> {
            if (ThirdRefundTypeEnum.onlyOriginRefund(api.getRefundType(), null)) {
                // 仅支持原路径退款
                return true;
            }
            if(Objects.equals(api.getBuTieKind(), ZheJiaPayEnum.GUO_JIA_BUTIE.getCode())&& detailVo != null &&  ObjectUtil.notEqual(api.getSubId(), detailVo.getOrderId())){
                SpringContextUtil.addRequestKeyMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG,
                        "订单[{}]国补收银:[{}],网络支付记录id:[{}],不允许在当前订单进行退款", api.getSubId(),
                        api.getReturnWayName(), api.getNetPayRecordId());
                detailVo.getRefundWays().removeIf(rw -> tuiWayMatchDb(rw.getName()).anyMatch(r -> Objects.equals(api.getReturnWayName(), r)));
                return false;
            }

            if (XtenantEnum.isSaasXtenant()) {
                return true;
            }

            // 获取支付网关配置
            PayGatewayMchBo payGatewayMch = netPayOriginWayMapper.getPayGatewayMch();
            if (payGatewayMch == null && detailVo != null) {
                SpringContextUtil.addRequestKeyMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG,
                        "九机网上支付：支付网关商户信息不存在");
                // 不允许原路径
                detailVo.getRefundWays().removeIf(rw -> tuiWayMatchDb(rw.getName()).anyMatch(r -> Objects.equals(api.getReturnWayName(), r)));
                return false;
            }

            // 初始化执行客户端
            ClientExecute execute = new ClientExecute(
                    payGatewayMch.getGatewayHost(),
                    payGatewayMch.getMchNo(),
                    payGatewayMch.getMchPrivateKey()
            );
            // 获取渠道配置信息
            String channelConfigId = netPayOriginWayMapper.getChannelConfigIdByNetPayRecordId(api.getNetPayRecordId());
            Integer day = 90; // 默认兜底90天

            if (StrUtil.isNotBlank(channelConfigId) && NumberUtil.isNumber(channelConfigId)) {
                try {
                    ConfigQueryReq req = new ConfigQueryReq();
                    req.setId(Long.valueOf(channelConfigId));

                    ApiRes res = execute.configGetOneExecute(req);
                    if ("SUCCESS".equals(res.getCode())) {
                        if (Objects.nonNull(res.getData())) {
                            day = ((ChannelConfigDto) res.getData()).getRefundDaysLimit();
                            if (Objects.isNull(day)) {
                                SpringContextUtil.addRequestKeyMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG,
                                        "九机网上支付：原路径退款限制天数为空,兜底90天");
                                day = 90;
                            }
                        } else {
                            day = null;
                        }
                    } else {
                        SpringContextUtil.addRequestKeyMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG,
                                "九机网上支付：获取支付配置失败，Code[{}] Msg[{}]，渠道配置ID[{}]，原路径退款已禁止",
                                res.getCode(), res.getMessage(), channelConfigId);
                        day = null;
                    }
                } catch (Exception e) {
                    SpringContextUtil.addRequestKeyMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG,
                            "九机网上支付：配置查询异常[{}]，渠道配置ID[{}]，原路径退款已禁止",
                            e.getMessage(), channelConfigId);
                    day = null;
                }
            } else {
                SpringContextUtil.addRequestKeyMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG,
                        "九机网上支付：无效渠道配置ID[{}],兜底90天", channelConfigId);
            }

            if (CommenUtil.isNullOrZero(day)&& detailVo != null) {
                // 不允许原路径
                detailVo.getRefundWays().removeIf(rw -> tuiWayMatchDb(rw.getName()).anyMatch(r -> Objects.equals(api.getReturnWayName(), r)));
                return false;
            }

            // 只做九机 网上支付：超过支付配置的限制天数后不能原路径退款
            LocalDateTime startTime = Optional.ofNullable(api.getDtime()).orElse(LocalDateTime.MIN);
            LocalDateTime endTime = LocalDateTime.now();
            boolean isOverTime = startTime.isBefore(endTime.minusDays(day));
            if (isOverTime && detailVo != null) {
                // 主动移除的需要将退款方式处理掉
                detailVo.getRefundWays().removeIf(rw -> tuiWayMatchDb(rw.getName()).anyMatch(r -> Objects.equals(api.getReturnWayName(), r)));
                SpringContextUtil.addRequestKeyMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG,
                        "九机网上支付：超出{}天退款限制，原路径退款已禁止:[{}],收银时间：[{}],网络支付记录id:[{}],渠道配置ID：[{}]", day, api.getReturnWayName(), startTime, api.getNetPayRecordId(), channelConfigId);
            }
            return !isOverTime;
        };
    }

    @Override
    protected List<NetPayOriginRefundVo> toGroupDataByDetail(DetailParamBo detailParamBo, List<ShouhouTuihuanDetailPo> myTuihuanDetailPos) {
        Map<Integer, ShouhouTuihuanDetailPo> tuihuanDetailRecordIdMaps = myTuihuanDetailPos.stream()
                .collect(Collectors.toMap(ShouhouTuihuanDetailPo::getRecordId, Function.identity(), (k1, k2) -> k1));
        return netPayOriginWayMapper.listNetPayRecordInfoByIds(tuihuanDetailRecordIdMaps.keySet()).stream()
                .map(npri -> convertNetPayOriginRefund(npri,tuihuanDetailRecordIdMaps))
                .filter(Objects::nonNull).collect(Collectors.toList());
    }

    /**
     * 根据详情转换为原路径退款对象
     * @param npri
     * @param tuihuanDetailRecordIdMaps
     * @return
     */
    private NetPayOriginRefundVo convertNetPayOriginRefund(NetPayOriginRefundVo npri, Map<Integer, ShouhouTuihuanDetailPo> tuihuanDetailRecordIdMaps) {
        ShouhouTuihuanDetailPo stDetail = tuihuanDetailRecordIdMaps.get(npri.getNetPayRecordId());
        if(stDetail == null){
            return null;
        }
        npri.setId(stDetail.getId());
        npri.setRefundPrice(stDetail.getRefundPrice());
        //需要减去当前退款金额
        npri.setRefundedPrice(npri.getRefundedPrice().subtract(stDetail.getRefundPrice()));
        npri.setGroupCode(stDetail.getTuiGroup());
        npri.setRefundBusinessType(stDetail.getRefundBusinessType());
        return npri;
    }


    /**
     * 获取网络支付信息
     * @param alipayInfoReq
     * @return
     */
    @Override
    public List<NetPayOriginRefundVo> getAlipayPayInfo(AlipayInfoReqVo alipayInfoReq){
        return SpringContextUtil.reqCache(()-> invokeGetAlipayPayInfo(alipayInfoReq), RequestCacheKeys.GET_ALIPAY_PAY_INFO, JSON.toJSONString(alipayInfoReq));
    }

    @Override
    public ZheJiaPayEnum getZheJiaPayEnum(Integer orderId, TuihuanKindEnum tuihuanKindEnum) {
        List<NetPayOriginRefundVo> refundVos = netPayOriginWayService.listAll(orderId, tuihuanKindEnum);
        boolean isGuoBu = refundVos.stream().anyMatch(r -> ObjectUtil.equals(r.getSubId(), orderId)
                && Objects.equals(r.getBuTieKind(), ZheJiaPayEnum.GUO_JIA_BUTIE.getCode()));
        if(isGuoBu){
            return ZheJiaPayEnum.GUO_JIA_BUTIE;
        }
        return null;
    }


    /**
     * 获取网络支付信息
     * @param alipayInfoReq
     * @return
     */
    private List<NetPayOriginRefundVo> invokeGetAlipayPayInfo(AlipayInfoReqVo alipayInfoReq){
        if (ObjectUtil.defaultIfNull(alipayInfoReq.getSubId(),0) <= 0){
            throw new CustomizeException("单号错误");
        }
        if (alipayInfoReq.getTuiWays() == null){
            alipayInfoReq.setTuiWays(Collections.emptySet());
        }
        Integer type = getNetPayType(Optional.ofNullable(alipayInfoReq.getTuihuanKind()).orElseThrow(() -> new CustomizeException("退款类型不能为空")));
        Collection<Integer> subIds = RefundMoneyUtil.parentChildOrderIds(alipayInfoReq.getSubId(), alipayInfoReq.getTuihuanKind());

        //处理tuiWay
        List<String> tuiWays = alipayInfoReq.getTuiWays().stream().flatMap(this::tuiWayMatchDb).collect(Collectors.toList());
        //获取支付记录信息
        List<NetPayOriginRefundVo> npoRefundVos = CommenUtil.autoQueryHist(() -> netPayOriginWayMapper.listNetPayRecordInfo(subIds, type, tuiWays),
                MTableInfoEnum.NET_PAY_RECORD, alipayInfoReq.getSubId(), type);
        npoRefundVos.stream()
                // 设置支持退款类型
                .peek(npor -> {
                    ThirdRefundTypeEnum refundTypeEnum = ThirdRefundTypeEnum.ORIGIN_WAY_OR_OTHER_WAY_REFUND;

                    if(ZheJiaPayEnum.GUO_JIA_BUTIE.getCode().equals(npor.getBuTieKind())){
                        refundTypeEnum = ThirdRefundTypeEnum.ORIGIN_WAY_FULL_REFUND;
                    }

                    npor.setRefundType(refundTypeEnum.getCode());
                    npor.setRefundTypeName(refundTypeEnum.getMessage());
                })
                // 设置分组编码
                .forEach(npor -> npor.setGroupCode(getMyGroup().getCode()));
        return npoRefundVos;
    }

    private Integer getNetPayType(TuihuanKindEnum tuiHuanKind) {
        Integer type;
        switch (tuiHuanKind){
            case TDJ_LP:
            case TK_LP:
                //良品
                type = 3;
                break;
            case TWXF:
            case TDJ_WXF:
                //维修
                type = 2;
                break;
            case SMALL_PRO_REFUND_REPAIR_FEE:
                //小件维修费
                type = 10;
                break;
            case SMALL_PRO_REFUND:
                throw new CustomizeException("小件换货没有支付记录,不应该存在这个退款类型");
            default:
                //订单
                type = 1;
                break;
        }
        return type;
    }

    /**
     * 程序退款方式匹配库退款方式
     * @param tuiWayParam
     * @return
     */
    @Override
    protected Stream<String> tuiWayMatchDb(String tuiWayParam) {
        String tuiWay = StrUtil.removeSuffix(tuiWayParam, "返回");
        return Arrays.stream(TuiWayMatchDbEnum.values()).filter(twm -> Objects.equals(twm.getCode(), tuiWay))
                .map(twd -> twd.getDbCodeList().stream())
                .findFirst().orElseGet(() -> Stream.of(tuiWay));
    }

    @Override
    protected String dbMatchTuiWay(String dbTuiWay) {
        return Arrays.stream(TuiWayMatchDbEnum.values()).filter(twm -> twm.getDbCodeList().contains(dbTuiWay))
                .map(TuiWayMatchDbEnum::getCode)
                .findFirst().orElse(dbTuiWay);
    }

    @Override
    protected RefundWayDetailVo<NetPayOriginRefundVo> initRefundWayDetailVo(DetailParamBo detailParamBo) {
        boolean isEdit = detailParamBo.getTuiHuanPo() == null;
        String inputOrText = DecideUtil.iif(isEdit, RefundWayDetailVo.GroupColumnTypeEnum.INPUT.getCode(), RefundWayDetailVo.GroupColumnTypeEnum.TEXT.getCode());
        LambdaBuild<LinkedList<RefundWayDetailVo.GroupColumns>> columns = LambdaBuild.create(new LinkedList<RefundWayDetailVo.GroupColumns>())
                .set(List::add, new RefundWayDetailVo.GroupColumns().setDataIndex("returnWayName").setTitle(TuiGroupEnum.NET_PAY_ORIGIN_WAY.getMessage()).setType(RefundWayDetailVo.GroupColumnTypeEnum.TEXT.getCode()))
                .set(List::add, new RefundWayDetailVo.GroupColumns().setDataIndex("actualPayPrice").setTitle("已付金额").setType(RefundWayDetailVo.GroupColumnTypeEnum.TEXT.getCode()))
                ;
        if(Optional.ofNullable(detailParamBo).map(DetailParamBo::getTuiHuanPo).map(ShouhouTuiHuanPo::getCheck3)
                .map(Boolean.FALSE::equals).orElse(Boolean.TRUE)){
            columns.set(List::add, new RefundWayDetailVo.GroupColumns().setDataIndex("refundedPrice").setTitle("已退款金额")
                    .setType(RefundWayDetailVo.GroupColumnTypeEnum.TEXT.getCode()));
        }
        columns.set(List::add, new RefundWayDetailVo.GroupColumns().setDataIndex("refundPrice").setTitle("本次退款金额").setType(inputOrText))
                .set(List::add, new RefundWayDetailVo.GroupColumns().setDataIndex("tradeNo").setTitle("流水号").setType(RefundWayDetailVo.GroupColumnTypeEnum.TEXT.getCode()));
        return new RefundWayDetailVo<NetPayOriginRefundVo>().setGroupColumns(columns.build());
    }

    @Override
    public boolean isMyGroup(Integer tuiGroup) {
        return Objects.equals(tuiGroup,getMyGroup().getCode());
    }

    @Override
    public TuiGroupEnum getMyGroup() {
        return TuiGroupEnum.NET_PAY_ORIGIN_WAY;
    }

    @Override
    protected BaseRefundService<NetPayOriginRefundVo> getMyService() {
        return netPayOriginWayService;
    }

    @Override
    protected void doAssertCheckSave(GroupTuihuanFormVo tuihuanForm, List<NetPayOriginRefundVo> tuiWayDetails) {
        StringRedisTemplate stringRedisTemplate = SpringUtil.getBean(StringRedisTemplate.class);
        List<Integer> alipayPpriceIds = StrUtil.splitTrim(stringRedisTemplate.opsForValue().get(RedisKeys.ALIPAY_YOUHUI_PPRICEID), StringPool.COMMA).stream()
                .map(Convert::toInt).collect(Collectors.toList());
        Assert.isFalse(CollUtil.isNotEmpty(alipayPpriceIds)
                && netPayOriginWayMapper.countAlipayYouHui(tuihuanForm.getSubId(),alipayPpriceIds)
                        > tuiWayDetails.stream().filter(tw -> Objects.equals(tw.getReturnWayName(),NetPayOriginWayService.REFUND_WAY_ALIPAY1)).count(),
                "支付宝(pay1)只能原路径退款！");
        //校验原路径最大可退金额
        List<NetPayOriginRefundVo> alipayPayInfos = netPayOriginWayService.getAlipayPayInfo(new AlipayInfoReqVo()
                .setSubId(tuihuanForm.getSubId()).setTuihuanKind(tuihuanForm.getTuihuanKindEnum()).setTuiWays(Collections.emptySet()));
        Set<NetPayOriginRefundVo> overMaxRefunds = alipayPayInfos.stream()
                .filter(appi -> tuiWayDetails.stream().filter(twd -> Objects.equals(twd.getNetPayRecordId(), appi.getNetPayRecordId()))
                .anyMatch(twd -> twd.getRefundPrice().compareTo(appi.getActualPayPrice().subtract(appi.getRefundedPrice())) > 0))
                .collect(Collectors.toSet());
        Set<NetPayOriginRefundVo> notFindRecords = tuiWayDetails.stream()
                .filter(twd -> alipayPayInfos.stream().noneMatch(appi -> Objects.equals(appi.getNetPayRecordId(), twd.getNetPayRecordId())))
                .collect(Collectors.toSet());
        Assert.isTrue(overMaxRefunds.isEmpty(),overMaxRefunds.stream()
                .map(omr -> StrUtil.format("交易号[{}]退款金额不能大于{}",omr.getTradeNo(),omr.getActualPayPrice().
                        subtract(omr.getRefundedPrice()))).collect(Collectors.joining(StringPool.SLASH)));
        Assert.isTrue(notFindRecords.isEmpty(),notFindRecords.stream()
                .map(nfr -> StrUtil.format("交易号[{}]找不到支付记录",nfr.getTradeNo())).collect(Collectors.joining(StringPool.SLASH)));
        //整单退校验
        Set<NetPayOriginRefundVo> notFullRefunds = alipayPayInfos.stream()
                .filter(appi -> tuiWayDetails.stream().filter(twd -> Objects.equals(appi.getNetPayRecordId(), twd.getNetPayRecordId()))
                        .anyMatch(twd -> ThirdRefundTypeEnum.fullRefund(appi.getRefundType())
                                && twd.getRefundPrice().compareTo(appi.getActualPayPrice().subtract(appi.getRefundedPrice())) < 0))
                .collect(Collectors.toSet());
        Assert.isTrue(notFullRefunds.isEmpty(),"{} {}",getMyGroup().getMessage(),notFullRefunds.stream()
                .map(nfr -> StrUtil.format("编号[{}]必须整笔退{}",nfr.getTradeNo(),nfr.getActualPayPrice().
                        subtract(nfr.getRefundedPrice()))).collect(Collectors.joining(StringPool.SLASH)));
        //可以通过其他退款退的金额处理
        if(XtenantEnum.isJiujiXtenant()){
            assertCheckOtherRefund(tuihuanForm, tuiWayDetails, alipayPayInfos);
        }

    }

    private void assertCheckOtherRefund(GroupTuihuanFormVo tuihuanForm, List<NetPayOriginRefundVo> tuiWayDetails, List<NetPayOriginRefundVo> alipayPayInfos) {
        Set<String> myTuiWayNames = tuihuanForm.getRefundWays().stream().filter(this::containMyGroup).map(RefundMoneyDetailVo.RefundWayVo::getName)
                .flatMap(this::tuiWayMatchDb)
                .collect(Collectors.toSet());
        List<NetPayOriginRefundVo> onlyOriginRefundList = alipayPayInfos.stream().filter(filterAlipayPayInfoFun(null))
                .filter(tor -> myTuiWayNames.contains(tor.getReturnWayName())).collect(Collectors.toList());
        //仅支持原路径退目前可退总和 过滤掉已经过期的原路径
        BigDecimal onlyOriginRefundPrice =onlyOriginRefundList.stream().map(NetPayOriginRefundVo::getRefundPrice).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        //当前仅支持原路径退总金额
        BigDecimal currOnlyOriginRefundPrice = tuiWayDetails.stream().map(NetPayOriginRefundVo::getRefundPrice).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        //换其他型号冲抵金额
        boolean isHqtxh = Stream.of(TuihuanKindEnum.HQTXH, TuihuanKindEnum.SMALL_PRO_HQTXH).anyMatch(tuiEnum -> tuiEnum == tuihuanForm.getTuihuanKindEnum());
        BigDecimal hqtxhRefundPrice = BigDecimal.ZERO;
        if(isHqtxh){
            hqtxhRefundPrice = SpringUtil.getBean(DeductionRefundService.class).getMyClassDetailList(tuihuanForm.getRefundWayDetails())
                    .stream().map(OtherRefundVo::getRefundPrice).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        }

        //总最大可退金额(包括父子订单)
        RefundSubInfoBo subInfoBo = tuihuanForm.getSubInfoBo();
        BigDecimal allSubMaxRefundPrice = subInfoBo.getAllYifuM().subtract(subInfoBo.getNotRefundPrice());
        //当前退款金额
        BigDecimal refundPrice = tuihuanForm.getRefundPrice();
        //仅支持原路径退的,如果没有通过原路径退,总金额要进行扣减
        //计算公式: otherRefundPrice - currOtherOriginRefundPrice + onlyOriginRefundPrice > subMaxRefundPrice
        boolean limitOtherRefund = !Boolean.TRUE.equals(SpringUtil.getBean(StringRedisTemplate.class)
                .hasKey(StrUtil.format(RedisKeys.NETPAY_OTHER_REFUND_ALLOWED_ORDER_ID_KEY, subInfoBo.getOrderId())));
        Assert.isFalse(limitOtherRefund && ObjectUtil.defaultIfNull(tuihuanForm.getOtherRefundPrice(), BigDecimal.ZERO).compareTo(BigDecimal.ZERO)>0
                        && refundPrice.subtract(currOnlyOriginRefundPrice.add(hqtxhRefundPrice)).add(onlyOriginRefundPrice).compareTo(allSubMaxRefundPrice)>0,
                "{}网上支付金额，只能使用原路径退款，不能使用其他方式退款，请重新核对退款金额后再做提交。",
                onlyOriginRefundList.stream().map(NetPayOriginRefundVo::getReturnWayName).distinct().filter(StringUtils::isNotEmpty).collect(Collectors.joining(",")));
    }

    @Override
    public void preHandleSaveData(GroupTuihuanFormVo tuihuanForm, List<JSONObject> tuihuanDetailJsons) {
        List<NetPayOriginRefundVo> alipayPayInfos = netPayOriginWayService.getAlipayPayInfo(new AlipayInfoReqVo()
                .setSubId(tuihuanForm.getSubId()).setTuihuanKind(tuihuanForm.getTuihuanKindEnum()).setTuiWays(Collections.emptySet()));
        List<NetPayOriginRefundVo> tuiWayDetails = getMyClassDetailList(tuihuanDetailJsons);
        handleOtherRefund(tuihuanForm, alipayPayInfos,tuiWayDetails);
    }

    private void handleOtherRefund(GroupTuihuanFormVo tuihuanForm, List<NetPayOriginRefundVo> alipayPayInfos, List<NetPayOriginRefundVo> tuiWayDetails) {
        //支持其他退款方式, 剩余可退金额
        List<NetPayOriginRefundVo> otherRefundList = alipayPayInfos.stream()
                .filter(tor -> ThirdRefundTypeEnum.otherRefund(tor.getRefundType()))
                // 复制对象, 需要调整可退金额
                .map(NetPayOriginRefundVo::copy)
                // 找到对应原路径退,进行扣减
                .peek(tor -> tuiWayDetails.stream().filter(twd -> ObjectUtil.equal(twd.getNetPayRecordId(),tor.getNetPayRecordId()))
                        .findFirst().map(twd -> tor.getRefundPrice().subtract(twd.getRefundPrice())).ifPresent(tor::setRefundPrice))
                .collect(Collectors.toList());
        //其他退款方式剩余可退金额总和
        BigDecimal otherRefundPriceSum = otherRefundList.stream()
                .map(NetPayOriginRefundVo::getRefundPrice).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        BigDecimal currOtherRefundPrice = NumberUtil.min(otherRefundPriceSum,tuihuanForm.getOtherRefundPrice());
        //按退款金额增加记录
        while(!otherRefundList.isEmpty() && currOtherRefundPrice.compareTo(BigDecimal.ZERO)>0) {
            //每次都获取距离最近的退款金额,来进行扣减
            NetPayOriginRefundVo appi = findNearestRefund(otherRefundList, currOtherRefundPrice)
                    .orElseThrow(() -> new CustomizeException("获取不到最接近的网络原路径退款记录(不可能发生)"));
            //每个只能扣减一次, 需要从列表中移除
            otherRefundList.remove(appi);
            NetPayOriginRefundVo temp = appi.copy();
            temp.setRefundPrice(NumberUtil.min(currOtherRefundPrice, temp.getRefundPrice()));
            temp.setIsDel(Boolean.TRUE);
            temp.setGroupCode(getMyGroup().getCode());
            //增加到提交列表
            tuihuanForm.getRefundWayDetails().add(JSON.parseObject(JSON.toJSONString(temp)));
            //更新当前剩余其他方式退款金额
            currOtherRefundPrice = currOtherRefundPrice.subtract(temp.getRefundPrice());
            //被其他方式退了的需要进行扣减
            tuihuanForm.setOtherRefundPrice(tuihuanForm.getOtherRefundPrice().subtract(temp.getRefundPrice()));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void doSave(GroupTuihuanFormVo tuihuanForm, List<NetPayOriginRefundVo> myTuiWayDetails) {
        for (NetPayOriginRefundVo myTuiWayDetail : myTuiWayDetails) {
            if(!Boolean.TRUE.equals(myTuiWayDetail.getIsDel())){
                //非删除才需要增加返回
                myTuiWayDetail.setReturnWayName(StrUtil.addSuffixIfNot(myTuiWayDetail.getReturnWayName(), ShouhouRefundService.REFUND_WAY_ORIGIN));
            }
        }
        //插入明细信息
        int detailInsertNum = netPayOriginWayMapper.batchInsert(tuihuanForm, myTuiWayDetails);
        Assert.isTrue(detailInsertNum == myTuiWayDetails.size(),"{}插入明细失败",getMyGroup().getMessage());
        //查询明细信息
        List<ShouhouTuihuanDetailPo> netpayDetailPos = getCommonService().listTuihuanDetail(tuihuanForm.getTuihuanId(), getMyGroup().getCode(), true);
        //相同的记录,确保金额顺序一致,避免挂错退款详情id
        netpayDetailPos.stream().sorted(Comparator.comparing(ShouhouTuihuanDetailPo::getRefundPrice))
                .forEach(npd -> myTuiWayDetails.stream().sorted(Comparator.comparing(NetPayOriginRefundVo::getRefundPrice))
                .filter(twd -> Objects.equals(twd.getNetPayRecordId(),npd.getRecordId()) && Objects.equals(twd.getReturnWayName(),npd.getTuiWay()))
                .filter(twd -> ObjectUtil.isNull(twd.getId()))
                .findFirst().ifPresent(twd -> twd.setId(npd.getId())));
        //插入插入原路径退款信息
        List<NetPayOriginRefundVo> insertNetReturnInfos = myTuiWayDetails.stream()
                //其他方式退的不需要写入退款信息
                .filter(twd -> ObjectUtil.notEqual(Boolean.TRUE,twd.getIsDel()))
                .collect(Collectors.toList());
        if(!insertNetReturnInfos.isEmpty()){
            int netPayInsertNum = netPayOriginWayMapper.batchInsertNetPayRefundInfo(tuihuanForm, insertNetReturnInfos);
            //插入网络支付信息错误
            Assert.isTrue(netPayInsertNum == insertNetReturnInfos.size(),"{}插入原路径退款信息错误",getMyGroup().getMessage());
        }
        //更新netpayinfo的金额
        Map<Integer, NetPayOriginRefundVo> updateNetPayMap = myTuiWayDetails.stream()
                .collect(Collectors.toMap(NetPayOriginRefundVo::getNetPayRecordId,
                        mtd -> new NetPayOriginRefundVo().setId(mtd.getId()).setTradeNo(mtd.getTradeNo())
                                .setRefundPrice(ObjectUtil.defaultIfNull(mtd.getRefundPrice(), BigDecimal.ZERO))
                                .setNetPayRecordId(mtd.getNetPayRecordId()).setGroupCode(mtd.getGroupCode()),
                        (v1,v2)->v1.setRefundPrice(v1.getRefundPrice().add(v2.getRefundPrice()))));
        int netPayUpdateNum = netPayOriginWayMapper.batchUpdateNetPayInfo(updateNetPayMap.values());
        Assert.isTrue(netPayUpdateNum == updateNetPayMap.size(),"{}更新网络支付退款金额失败",getMyGroup().getMessage());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void doCancelRefund(Integer tuihuanId, List<ShouhouTuihuanDetailPo> myTuihuanDetails) {
        //删除支付批次
        ShouhouRefundMapper shouhouRefundMapper = SpringUtil.getBean(ShouhouRefundMapper.class);
        shouhouRefundMapper.cancelNetpayRecord(tuihuanId);
        //恢复退款金额
        Map<Integer, NetPayOriginRefundVo> updateNetPayMap = myTuihuanDetails.stream()
                .collect(Collectors.toMap(ShouhouTuihuanDetailPo::getRecordId,
                        mtd -> new NetPayOriginRefundVo().setId(mtd.getId()).setTradeNo(null)
                                .setRefundPrice(ObjectUtil.defaultIfNull(mtd.getRefundPrice(), BigDecimal.ZERO).negate())
                                .setNetPayRecordId(mtd.getRecordId()).setGroupCode(mtd.getTuiGroup()),
                        (v1,v2)->v1.setRefundPrice(v1.getRefundPrice().add(v2.getRefundPrice()))));
        int netPayUpdateNum = netPayOriginWayMapper.batchUpdateNetPayInfo(updateNetPayMap.values());
        Assert.isTrue(netPayUpdateNum == updateNetPayMap.size(),"{}恢复网络支付退款金额失败",getMyGroup().getMessage());
        List<Integer> detailIds = myTuihuanDetails.stream().map(ShouhouTuihuanDetailPo::getId).collect(Collectors.toList());
        //批量获取退款信息
        List<NetPayRefundInfo> payRefundInfos = netPayRefundInfoService.lambdaQuery()
                .in(NetPayRefundInfo::getTuihuanDetailId, detailIds)
                .list();
        //恢复分账金额
        for (NetPayRefundInfo netPayRefundInfo : payRefundInfos) {
            if(ObjectUtil.defaultIfNull(netPayRefundInfo.getRefundSplitServiceFee(), BigDecimal.ZERO).compareTo(BigDecimal.ZERO) <= 0){
                continue;
            }
            netPayOriginWayMapper.updateRefundSplitServiceFee(netPayRefundInfo.getNetRecordId(), netPayRefundInfo.getRefundSplitServiceFee());
        }
    }

    @Override
    protected boolean isDoAssertCheckSave(List<NetPayOriginRefundVo> myClassList) {
        //网络支付需要一直都要检测,因为存在其他退款方式退款的情况
        return true;
    }

    @Override
    public boolean isOriginWay() {
        return true;
    }

    @Override
    public List<NetPayOriginRefundVo> listAll(Integer orderId, TuihuanKindEnum tuihuanKindEnum) {
        return netPayOriginWayService.getAlipayPayInfo(new AlipayInfoReqVo()
                .setSubId(orderId).setTuihuanKind(tuihuanKindEnum).setTuiWays(Collections.emptySet()));
    }


}
