package com.jiuji.oa.afterservice.patchsearch.dao;

import com.jiuji.oa.afterservice.patchsearch.vo.req.PatchSearchReq;
import com.jiuji.oa.afterservice.patchsearch.vo.res.PatchSearchRes;
import com.jiuji.oa.afterservice.patchsearch.vo.res.PatchSearchResV2;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Auther: qiweiqing
 * @Date: 2020/03/05
 * @Description:
 */
@Mapper
public interface PatchSearchDao{

    List<PatchSearchRes> getBasketPage(@Param("req") PatchSearchReq personChangesReq);

    List<PatchSearchRes> getBasketSub(@Param("subIdList") List<Integer> subIdList);

    List<PatchSearchRes> getBasketSmall(@Param("smallProIdIdList") List<Integer> smallProIdIdList);

     List<PatchSearchResV2.PatchGive> listGive(@Param("basketId") Integer basketId, @Param("transferCodeList") List<String> transferCodeList);
}
