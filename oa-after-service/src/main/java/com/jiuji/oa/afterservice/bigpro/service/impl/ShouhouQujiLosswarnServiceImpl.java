package com.jiuji.oa.afterservice.bigpro.service.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.jiuji.cloud.after.enums.BaoXiuTypeEnum;
import com.jiuji.cloud.after.enums.ProductTypeEnum;
import com.jiuji.oa.afterservice.bigpro.bo.ShouhouLossWarnBo;
import com.jiuji.oa.afterservice.bigpro.bo.shouhou.RProductResultBo;
import com.jiuji.oa.afterservice.bigpro.dao.ShouHouPjMapper;
import com.jiuji.oa.afterservice.bigpro.dao.ShouhouMapper;
import com.jiuji.oa.afterservice.bigpro.enums.BaoxiuStatusEnum;
import com.jiuji.oa.afterservice.bigpro.po.*;
import com.jiuji.oa.afterservice.bigpro.service.*;
import com.jiuji.oa.afterservice.common.config.properties.JiujiSystemProperties;
import com.jiuji.oa.afterservice.common.constant.DataSourceConstants;
import com.jiuji.oa.afterservice.common.enums.MTableInfoEnum;
import com.jiuji.oa.afterservice.common.util.CommenUtil;
import com.jiuji.oa.afterservice.lossReview.enums.AuditStatsEnum;
import com.jiuji.oa.afterservice.lossReview.enums.ErrorKindEnum;
import com.jiuji.oa.afterservice.lossReview.enums.ErrorTypeEnum;
import com.jiuji.oa.afterservice.lossReview.po.LossReviewAudit;
import com.jiuji.oa.afterservice.lossReview.po.LossReviewAuditDetail;
import com.jiuji.oa.afterservice.lossReview.service.ILossReviewAuditDetailService;
import com.jiuji.oa.afterservice.lossReview.service.ILossReviewAuditService;
import com.jiuji.oa.afterservice.lossReview.vo.req.QujiLosswarnReq;
import com.jiuji.oa.afterservice.other.po.Areainfo;
import com.jiuji.oa.afterservice.other.service.AreainfoService;
import com.jiuji.oa.afterservice.sub.service.CategoryService;
import com.jiuji.oa.orginfo.areainfo.vo.AreaInfo;
import com.jiuji.tc.utils.enums.EnumUtil;
import com.jiuji.tc.utils.transaction.MultipleTransaction;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 售后亏损审核
 *
 * <AUTHOR>
 * @since 2023/7/27 20:56
 */
@Service
@Slf4j
public class ShouhouQujiLosswarnServiceImpl implements ShouhouQujiLosswarnService {
    @Autowired
    private WxkcoutputService wxkcoutputService;
    @Resource
    private JiujiSystemProperties jiujiSystemProperties;
    @Autowired
    private ILossReviewAuditService lossReviewAuditService;
    @Autowired
    private ILossReviewAuditDetailService lossReviewAuditDetailService;
    @Autowired
    private ShouhouHuishouService shouhouHuishouService;
    @Autowired
    private ProductinfoService productinfoService;

    @Autowired
    private ShouhouService shouhouService;
    @Autowired
    private AreainfoService areainfoService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void qujiLosswarn(QujiLosswarnReq req) {
        Integer shouhouId = req.getShouhouId();
        Integer areaId = req.getAreaId();
        //Shouhou shouhou = shouhouService.getById(shouhouId);
        Shouhou shouhou = CommenUtil.autoQueryHist(() ->shouhouService.getById(shouhouId), MTableInfoEnum.SHOUHOU,shouhouId);
        Areainfo areainfo = areainfoService.getById(areaId);
        com.jiuji.oa.orginfo.areainfo.vo.AreaInfo temp = new AreaInfo();
        BeanUtils.copyProperties(areainfo, temp);
        this.qujiLosswarn(shouhou, temp);
        return;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void qujiLosswarn(Shouhou shouhou, AreaInfo currAreaInfo) {
        Integer shouhouId = shouhou.getId();
        if (!Boolean.TRUE.equals(shouhou.getIsquji())) {
            log.warn("售后单[{}]未取机不进行亏损审核", shouhouId);
            return;
        }

        // region 构建参数
        List<LossReviewAuditDetail> lossReasons = new LinkedList<>();
        // 维修配件信息
        List<Wxkcoutput> wxkcoutputs = CommenUtil.autoQueryHist(()-> wxkcoutputService.lambdaQuery().eq(Wxkcoutput::getWxid, shouhouId)
                .and(cnd -> cnd.ne(Wxkcoutput::getPrice1, 0).or().isNull(Wxkcoutput::getPpriceid).or().eq(Wxkcoutput::getPpriceid, 0))
                .and(cnd -> cnd.ne(Wxkcoutput::getStats, Wxkcoutput.StatusEnum.CANCELED.getCode())
                        .or().eq(Wxkcoutput::getTuiStatus, Wxkcoutput.TuiStatusEnum.REFUND_ONLY.getCode())).list(), MTableInfoEnum.WXKCOUTPUT_WXID, shouhouId);
//        List<Wxkcoutput> wxkcoutputs = wxkcoutputService.lambdaQuery().eq(Wxkcoutput::getWxid, shouhouId)
//                .and(cnd -> cnd.ne(Wxkcoutput::getPrice1, 0).or().isNull(Wxkcoutput::getPpriceid).or().eq(Wxkcoutput::getPpriceid, 0))
//                .and(cnd -> cnd.ne(Wxkcoutput::getStats, Wxkcoutput.StatusEnum.CANCELED.getCode())
//                        .or().eq(Wxkcoutput::getTuiStatus, Wxkcoutput.TuiStatusEnum.REFUND_ONLY.getCode())).list();
        // 所有配件都没有亏损
        if (wxkcoutputs.stream().allMatch(wxkc -> ObjectUtil.defaultIfNull(wxkc.getPrice(), BigDecimal.ZERO)
                .subtract(ObjectUtil.defaultIfNull(wxkc.getRefundedPrice(), BigDecimal.ZERO))
                .subtract(ObjectUtil.defaultIfNull(wxkc.getInprice(), BigDecimal.ZERO)).compareTo(BigDecimal.ZERO) >= 0)) {
            // 没有配件或配件都不亏本,直接返回
            return;
        }
        // 获取ch999的用户信息
        String memberCh999UserName = SpringUtil.getBean(ShouhouMapper.class).getCh999User(shouhou.getMobile(), jiujiSystemProperties.getOfficeName());
        BigDecimal hexiaoAmount = SpringUtil.getBean(ShouhouExService.class).getHeXiaoJE(shouhouId);
        BigDecimal allhs = shouhouHuishouService.getHuishouListBy(shouhouId).stream().map(ShouhouHuishou::getPrice)
                .filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal totalLossPrice = shouhou.getFeiyong().subtract(shouhou.getCostprice()).subtract(allhs).subtract(hexiaoAmount);
        ShouhouLossWarnBo lossWarnBo = ShouhouLossWarnBo.builder().shouhou(shouhou).currAreaInfo(currAreaInfo)
                .wxkcoutputs(wxkcoutputs).memberCh999UserName(memberCh999UserName).totalLossPrice(totalLossPrice).build();
        // endregion
        //添加服务亏损审核
        addServiceAccidentLoss(lossReasons, lossWarnBo);
        //添加换货审核
        addExchangeLoss(lossReasons, lossWarnBo);
        //添加其他审核
        addOtherLoss(lossReasons, lossWarnBo);
        //添加仅退款的审核
        addOnlyRefundLoss(lossReasons, lossWarnBo);
        // region 构建审批记录
        // 已经存在审核记录,保证密等,进行更新对应的审核
        Optional<LossReviewAudit> lossReviewAuditOpt = lossReviewAuditService.lambdaQuery().eq(LossReviewAudit::getShouhouId, shouhouId).list().stream().findFirst();
        if (lossReasons.isEmpty()) {
            log.warn("售后单[{}]没有异常,不需要审核", shouhouId);
            lossReviewAuditOpt.ifPresent(lra -> lossReviewAuditService.lambdaUpdate().eq(LossReviewAudit::getId, lra.getId())
                    .ne(LossReviewAudit::getAuditStatus, AuditStatsEnum.FOUR.getCode())
                    .set(LossReviewAudit::getIsDeleted, 1).update());
            return;
        }
        BigDecimal lossLine = new BigDecimal("300");
        Integer auditStats = AuditStatsEnum.TWO.getCode();
        LossReviewAudit lossReviewAudit = new LossReviewAudit().setAuditStatus(auditStats)
                .setLossAmountSum(totalLossPrice).setIsDeleted(0).setUpdateTime(LocalDateTime.now())
                .setCreateTime(LocalDateTime.now()).setShouhouId(Convert.toLong(shouhouId));
        if (totalLossPrice.compareTo(BigDecimal.ZERO) > 0 || totalLossPrice.abs().compareTo(lossLine) <= 0) {
            // 小于基准或者总金额没亏损 二审免审
            lossReviewAudit.setCheckUser2(AuditStatsEnum.ONE.getMessage());
        }
        List<LossReviewAuditDetail> lossReviewAuditDetails = lossReviewAuditOpt
                .map(lra -> lossReviewAuditDetailService.lambdaQuery().eq(LossReviewAuditDetail::getLossReviewAuditId, lra.getId()).list())
                .orElse(Collections.emptyList());
        Map<Integer,LossReviewAuditDetail> lossReviewAuditDetailMap = lossReviewAuditDetails.stream()
                // 先标记为删除
                .peek(lrad -> lrad.setIsDeleted(1))
                .collect(Collectors.toMap(LossReviewAuditDetail::getErrorType, Function.identity(), (v1, v2) -> v1));
        if(lossReviewAuditOpt.isPresent()){
            //亏损金额增加,需要重新审核
           Function<List<LossReviewAuditDetail>, BigDecimal> detailTotalLossFun = details -> details.stream()
                   .map(LossReviewAuditDetail::getLossAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO,BigDecimal::add);
            if(lossReviewAuditDetailMap.size()>lossReasons.size()
                || detailTotalLossFun.apply(lossReviewAuditDetails).compareTo(detailTotalLossFun.apply(lossReasons))>0){
                LossReviewAudit lra = lossReviewAuditOpt.get();
                lossReviewAudit.setId(lra.getId());
                lossReviewAuditService.lambdaUpdate().eq(LossReviewAudit::getId, lra.getId())
                        .set(LossReviewAudit::getAuditStatus, auditStats)
                        .set(LossReviewAudit::getUpdateTime, LocalDateTime.now())
                        .update();
                lossReasons.forEach(ll -> {
                    ll.setErrorReason(StringUtils.trim(ll.getErrorReason()));
                    ll.setLossReviewAuditId(lossReviewAudit.getId());
                    LossReviewAuditDetail detail = lossReviewAuditDetailMap.get(ll.getErrorType());
                    if(detail == null){
                        lossReviewAuditDetailService.save(ll);
                    }else{
                        detail.setIsDeleted(0);
                        lossReviewAuditDetailService.lambdaUpdate().eq(LossReviewAuditDetail::getId, detail.getId())
                                .set(LossReviewAuditDetail::getLossAmount, ll.getLossAmount())
                                .set(LossReviewAuditDetail::getUpdateTime, LocalDateTime.now())
                                .update();
                    }
                });
                //删除已经没有的亏损
                List<Integer> delIds = lossReviewAuditDetails.stream().filter(lrad -> ObjectUtil.equals(1, lrad.getIsDeleted()))
                        .map(LossReviewAuditDetail::getId).collect(Collectors.toList());
                if(!delIds.isEmpty()){
                    lossReviewAuditDetailService.lambdaUpdate()
                            .in(LossReviewAuditDetail::getId, delIds)
                            .set(LossReviewAuditDetail::getIsDeleted, 1)
                            .set(LossReviewAuditDetail::getUpdateTime, LocalDateTime.now())
                            .update();
                }
            }
        }else{
            lossReviewAuditService.save(lossReviewAudit);
            lossReasons.forEach(ll -> {
                ll.setErrorReason(StringUtils.trim(ll.getErrorReason()));
                ll.setLossReviewAuditId(lossReviewAudit.getId());
                lossReviewAuditDetailService.save(ll);
            });
        }


        // endregion
    }

    @Override
    public void addServiceAccidentLoss(List<LossReviewAuditDetail> lossReasons, ShouhouLossWarnBo lossWarnBo) {

        Shouhou shouhou = lossWarnBo.getShouhou();
        // 1.维修单出险九机服务
        Integer serviceType = shouhou.getServiceType();
        if (ObjectUtil.defaultIfNull(serviceType, 0) <= 0) {
            //非服务出险
            return;
        }
        //出险配件
        List<Wxkcoutput> serviceWxkcoutputs = lossWarnBo.getWxkcoutputs().stream()
                .filter(wxkc -> ObjectUtil.defaultIfNull(wxkc.getServiceType(), 0) > 0)
                .collect(Collectors.toList());
        // 2.出险九机服务的配件产生了亏损
        BigDecimal serviceLossAmount = serviceWxkcoutputs.stream().map(wxkc -> ObjectUtil.defaultIfNull(wxkc.getPrice(), BigDecimal.ZERO)
                .subtract(ObjectUtil.defaultIfNull(wxkc.getInprice(), BigDecimal.ZERO)))
                .filter(amount -> amount.compareTo(BigDecimal.ZERO) < 0)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        if (serviceLossAmount.compareTo(BigDecimal.ZERO) >= 0) {
            // 没有亏损
            return;
        }

        // region 基础数据处理
        //出险配件的分类id
        List<Integer> servicePpids = serviceWxkcoutputs.stream().map(Wxkcoutput::getPpriceid)
                .filter(ppid -> ObjectUtil.defaultIfNull(ppid, 0) > 0).collect(Collectors.toList());
        List<Integer> serviceWxkcCids = Collections.emptyList();
        if (!servicePpids.isEmpty()) {
            serviceWxkcCids = productinfoService.lambdaQuery()
                    .in(Productinfo::getPpriceid, servicePpids)
                    .select(Productinfo::getCid).list().stream().map(Productinfo::getCid).distinct()
                    .collect(Collectors.toList());
        }
        // 获取出险的服务记录
        ServiceRecordService serviceRecordService = SpringUtil.getBean(ServiceRecordService.class);
        Optional<ServiceRecord> serviceRecordOpt = serviceRecordService.lambdaQuery()
                .eq(ServiceRecord::getServerShouhouId, shouhou.getId()).list().stream().findFirst();
        // 获取服务的配件分类
        Optional<BaoXiuTypeEnum> baoXiuTypeEnumOpt = Optional.ofNullable(EnumUtil.getEnumByCode(BaoXiuTypeEnum.class, serviceType));
        Optional<List<Integer>> servicePartCidsOpt = baoXiuTypeEnumOpt.map(bt -> SpringUtil.getBean(CategoryService.class).getProductChildCidList(bt.getPartCids()));
        Optional<List<Integer>> excludePartCidsOpt = baoXiuTypeEnumOpt.map(bt -> SpringUtil.getBean(CategoryService.class).getProductChildCidList(bt.getExcludePartCids()));
        // endregion
        // 3.维修单的userid 和销售单的userid是同一个人
        boolean isSameUseId = serviceRecordOpt.filter(sr -> ObjectUtil.equal(Convert.toInt(shouhou.getUserid()), sr.getUserid())).isPresent();
        // 4. 维修单会员是员工号码
        boolean isCh999User = StrUtil.isNotBlank(lossWarnBo.getMemberCh999UserName());
        // 5.出险设备购买过服务
        boolean isBuyService = serviceRecordOpt.isPresent();
        // 6.出险九机服务的配件和服务是对应关系
        boolean isServicePart = serviceWxkcCids.stream().allMatch(swCid ->
                // 在服务包含配件里面
                servicePartCidsOpt.map(spCids -> spCids.contains(swCid)).orElse(Boolean.TRUE)
                        // 不在排除配件里面
                        && excludePartCidsOpt.map(epCids -> !epCids.contains(swCid)).orElse(Boolean.TRUE));

        LossReviewAuditDetail template = new LossReviewAuditDetail().setCreateTime(LocalDateTime.now()).setUpdateTime(LocalDateTime.now())
                .setIsDeleted(0).setKind(ErrorKindEnum.FU_WU_CHU_XIAN_KUI_SUN.getCode()).setLossAmount(serviceLossAmount);

        // region 生成对应的异常信息
        if (isSameUseId && !isCh999User && isBuyService && isServicePart) {
            // 正常出险亏损
            ErrorTypeEnum errorTypeEnum = ErrorTypeEnum.ZHENG_CHANG_CHU_XIAN;
            lossReasons.add(template.copy().setErrorType(errorTypeEnum.getCode()).setErrorReason(errorTypeEnum.getMessage()));
        }
        if (!isSameUseId && !isCh999User && isBuyService && isServicePart) {
            // 不同用户出险
            ErrorTypeEnum errorTypeEnum = ErrorTypeEnum.BU_TONG_YONG_HU_CHU_XIAN;
            lossReasons.add(template.copy().setErrorType(errorTypeEnum.getCode()).setErrorReason(errorTypeEnum.getMessage()));
        }
        if (isBuyService && !isServicePart) {
            // 出险配件异常
            ErrorTypeEnum errorTypeEnum = ErrorTypeEnum.CHU_XIAN_PEI_JIAN_YI_CHANG;
            lossReasons.add(template.copy().setErrorType(errorTypeEnum.getCode()).setErrorReason(errorTypeEnum.getMessage()));
        }
        if (!isBuyService) {
            // 特殊出险
            ErrorTypeEnum errorTypeEnum = ErrorTypeEnum.TE_SHU_CHU_XIAN;
            lossReasons.add(template.copy().setErrorType(errorTypeEnum.getCode()).setErrorReason(errorTypeEnum.getMessage()));
        }
        // endregion
    }

    @Override
    public void addExchangeLoss(List<LossReviewAuditDetail> lossReasons, ShouhouLossWarnBo lossWarnBo) {
        Integer shouhouId = lossWarnBo.getShouhou().getId();
        List<ShouhouHuishou> huanhuoHuishous = shouhouHuishouService.lambdaQuery()
                .eq(ShouhouHuishou::getShouhouId, shouhouId).eq(ShouhouHuishou::getIshuanhuo, 1)
                .and(s -> s.eq(ShouhouHuishou::getIsdel, Boolean.FALSE).or().isNull(ShouhouHuishou::getIsdel)).list();
        if (huanhuoHuishous.isEmpty()) {
            //1.维修配件走换货流程
            return;
        }
        //2.换货的维修配件产生亏损
        List<Wxkcoutput> huanhuoLossWxkcList = lossWarnBo.getWxkcoutputs().stream()
                .filter(wxkc -> huanhuoHuishous.stream().anyMatch(hhh -> Objects.equals(hhh.getWxkcid(), wxkc.getId())))
                .filter(wxkc -> ObjectUtil.defaultIfNull(wxkc.getPrice(), BigDecimal.ZERO)
                        .compareTo(ObjectUtil.defaultIfNull(wxkc.getInprice(), BigDecimal.ZERO)) < 0)
                .collect(Collectors.toList());
        if (huanhuoLossWxkcList.isEmpty()) {
            return;
        }

        LossReviewAuditDetail detail = new LossReviewAuditDetail().setCreateTime(LocalDateTime.now()).setUpdateTime(LocalDateTime.now())
                .setIsDeleted(0).setKind(ErrorKindEnum.HUAN_HUO_SHEN_HE.getCode())
                .setLossAmount(getLossAmount(huanhuoLossWxkcList));
        // 换货商品亏损
//        ErrorTypeEnum errorTypeEnum = ErrorTypeEnum.HUAN_HUO_SHANG_PIN_KUI_SUN;
//        lossReasons.add(detail.setErrorType(errorTypeEnum.getCode()).setErrorReason(errorTypeEnum.getMessage()));
    }


    @Override
    public void addOnlyRefundLoss(List<LossReviewAuditDetail> lossReasons, ShouhouLossWarnBo lossWarnBo) {
        //1.换货的维修配件产生亏损
        List<Wxkcoutput> onlyLossWxkcList = lossWarnBo.getWxkcoutputs().stream()
                .filter(wxkc -> Wxkcoutput.TuiStatusEnum.REFUND_ONLY.getCode().equals(wxkc.getTuiStatus()))
                .filter(wxkc -> ObjectUtil.defaultIfNull(wxkc.getPrice(), BigDecimal.ZERO)
                        .subtract(ObjectUtil.defaultIfNull(wxkc.getRefundedPrice(), BigDecimal.ZERO))
                        .compareTo(ObjectUtil.defaultIfNull(wxkc.getInprice(), BigDecimal.ZERO)) < 0)
                .collect(Collectors.toList());
        if (onlyLossWxkcList.isEmpty()) {
            return;
        }

        LossReviewAuditDetail detail = new LossReviewAuditDetail().setCreateTime(LocalDateTime.now()).setUpdateTime(LocalDateTime.now())
                .setIsDeleted(0).setKind(ErrorKindEnum.JIN_TUI_KUAN_WEI_XIU_PEI_JIAN.getCode())
                .setLossAmount(getLossAmount(onlyLossWxkcList));
        // 仅退款维修配件
        ErrorTypeEnum errorTypeEnum = ErrorTypeEnum.JIN_TUI_KUAN_WEI_XIU_PEI_JIAN;
        lossReasons.add(detail.setErrorType(errorTypeEnum.getCode()).setErrorReason(errorTypeEnum.getMessage()));
    }

    @Override
    public BigDecimal getLossAmount(List<Wxkcoutput> lossWxkcList) {
        return lossWxkcList.stream()
                .map(wxkc -> ObjectUtil.defaultIfNull(wxkc.getPrice(), BigDecimal.ZERO)
                        .subtract(ObjectUtil.defaultIfNull(wxkc.getRefundedPrice(), BigDecimal.ZERO))
                        .subtract(ObjectUtil.defaultIfNull(wxkc.getInprice(), BigDecimal.ZERO)))
                .filter(amount -> amount.compareTo(BigDecimal.ZERO) < 0)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    @Override
    public void addOtherLoss(List<LossReviewAuditDetail> lossReasons, ShouhouLossWarnBo lossWarnBo) {
        LossReviewAuditDetail template = new LossReviewAuditDetail().setCreateTime(LocalDateTime.now()).setUpdateTime(LocalDateTime.now())
                .setIsDeleted(0);
        Shouhou shouhou = lossWarnBo.getShouhou();

        List<Wxkcoutput> wxkcoutputs = lossWarnBo.getWxkcoutputs();
        BigDecimal punishLossAmount = getLossAmount(wxkcoutputs.stream().filter(wxkc -> StrUtil.isNotBlank(wxkc.getPunishSub()))
                .collect(Collectors.toList()));
        if (punishLossAmount.compareTo(BigDecimal.ZERO) < 0) {
            // 员工赔付商品
            ErrorTypeEnum errorTypeEnum = ErrorTypeEnum.YUAN_GONG_PEI_FU_SHANG_PIN;
            lossReasons.add(template.copy().setKind(ErrorKindEnum.YUAN_GONG_PEI_FU.getCode()).setLossAmount(punishLossAmount)
                    .setErrorType(errorTypeEnum.getCode()).setErrorReason(errorTypeEnum.getMessage()));
        }

        BigDecimal specialQualityLossAmount = getLossAmount(wxkcoutputs.stream()
                .filter(wxkc -> ObjectUtil.defaultIfNull(wxkc.getSpecialQualityAssurance(), 0) > 0)
                .collect(Collectors.toList()));
        if (specialQualityLossAmount.compareTo(BigDecimal.ZERO) < 0) {
            // 特殊质保审批
            ErrorTypeEnum errorTypeEnum = ErrorTypeEnum.TE_SHU_ZHI_BAO_SHEN_PI;
            lossReasons.add(template.copy().setKind(ErrorKindEnum.TE_SHU_ZHI_BAO.getCode()).setLossAmount(specialQualityLossAmount)
                    .setErrorType(errorTypeEnum.getCode()).setErrorReason(errorTypeEnum.getMessage()));
        }

        BigDecimal lossAmount = getLossAmount(wxkcoutputs);
        boolean isBaoxiu = BaoxiuStatusEnum.Z.getCode().equals(shouhou.getBaoxiu());
        ProductTypeEnum productTypeEnum = SpringUtil.getBean(ShouhouService.class).getProductTypeEnum(shouhou);

        // region 商品类型亏损
        if (isBaoxiu && Objects.equals(productTypeEnum, ProductTypeEnum.NEW_MACHINE) && lossAmount.compareTo(BigDecimal.ZERO) < 0) {
            // 在保亏损
            ErrorTypeEnum errorTypeEnum = ErrorTypeEnum.YONG_HU_ZAI_BAO_KUI_SUN_YI_CHANG;
            lossReasons.add(template.copy().setKind(ErrorKindEnum.ZAI_BAO_KUI_SUN.getCode()).setLossAmount(lossAmount)
                    .setErrorType(errorTypeEnum.getCode()).setErrorReason(errorTypeEnum.getMessage()));
        }

        if (isBaoxiu && Objects.equals(productTypeEnum, ProductTypeEnum.EXCELLENT_PRODUCT) && lossAmount.compareTo(BigDecimal.ZERO) < 0) {
            // 优品在保
            ErrorTypeEnum errorTypeEnum = ErrorTypeEnum.YOU_PIN_ZAI_BAO_YONG_HU_ZAI_BAO_KUI_SUN_YI_CHANG;
            lossReasons.add(template.copy().setKind(ErrorKindEnum.YOU_PIN_ZAI_BAO.getCode()).setLossAmount(lossAmount)
                    .setErrorType(errorTypeEnum.getCode()).setErrorReason(errorTypeEnum.getMessage()));
        }

        if (isBaoxiu && Objects.equals(productTypeEnum, ProductTypeEnum.GOOD_PRODUCT) && lossAmount.compareTo(BigDecimal.ZERO) < 0) {
            // 良品在保
            ErrorTypeEnum errorTypeEnum = ErrorTypeEnum.LIANG_PIN_ZAI_BAO_YONG_HU_ZAI_BAO_KUI_SUN_YI_CHANG;
            lossReasons.add(template.copy().setKind(ErrorKindEnum.LIANG_PIN_ZAI_BAO.getCode()).setLossAmount(lossAmount)
                    .setErrorType(errorTypeEnum.getCode()).setErrorReason(errorTypeEnum.getMessage()));
        }
        // endregion
        List<Integer> ppids = wxkcoutputs.stream().map(Wxkcoutput::getPpriceid).filter(ppid -> ObjectUtil.defaultIfNull(ppid, 0) > 0)
                .distinct().collect(Collectors.toList());
        List<RProductResultBo> productResultBos = Collections.emptyList();
        if (!ppids.isEmpty()) {
            List<Integer> pjProductIds = productinfoService.lambdaQuery().in(Productinfo::getPpriceid, ppids).select(Productinfo::getProductId)
                    .list().stream().map(Productinfo::getProductId).collect(Collectors.toList());
            productResultBos = MultipleTransaction.query(DataSourceConstants.WEB999, () -> SpringUtil.getBean(ShouHouPjMapper.class)
                    .listPjRproduct(Convert.toInt(shouhou.getProductId()), pjProductIds));
        }
        if (!productResultBos.isEmpty() && lossAmount.compareTo(BigDecimal.ZERO) < 0
                && productResultBos.stream().anyMatch(pr -> ObjectUtil.notEqual(pr.getPpid(), Convert.toInt(shouhou.getProductId())))) {
            // 配件出库异常
            ErrorTypeEnum errorTypeEnum = ErrorTypeEnum.PEI_JIAN_CHU_KU_YI_CHANG;
            lossReasons.add(template.copy().setKind(ErrorKindEnum.PEI_JIAN_CHU_KU_YI_CHANG.getCode()).setLossAmount(lossAmount)
                    .setErrorType(errorTypeEnum.getCode()).setErrorReason(errorTypeEnum.getMessage()));
        }

        if (StrUtil.isNotBlank(lossWarnBo.getMemberCh999UserName()) && lossAmount.compareTo(BigDecimal.ZERO) < 0) {
            // 员工亏损维修单
            ErrorTypeEnum errorTypeEnum = ErrorTypeEnum.YUAN_GONG_KUI_SUN_WEI_XIU_DAN;
            lossReasons.add(template.copy().setKind(ErrorKindEnum.YUAN_GONG_KUI_SUN.getCode()).setLossAmount(lossAmount)
                    .setErrorType(errorTypeEnum.getCode()).setErrorReason(errorTypeEnum.getMessage()));
        }
    }
}
