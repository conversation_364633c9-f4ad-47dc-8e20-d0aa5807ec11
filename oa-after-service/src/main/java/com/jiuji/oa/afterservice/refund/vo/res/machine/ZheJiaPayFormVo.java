package com.jiuji.oa.afterservice.refund.vo.res.machine;

import com.jiuji.oa.afterservice.refund.enums.ZheJiaPayEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 折价支付表单实体
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("折价支付表单实体")
public class ZheJiaPayFormVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 售后ID
     */
    @ApiModelProperty("售后ID")
    @NotNull(message = "售后ID不能为空")
    private Integer shouhouId;

    /**
     * @see ZheJiaPayEnum
     */
    @ApiModelProperty("折价类型编码")
    @NotNull(message = "折价类型编码")
    private Integer zheJiaPayCode;

    /**
     * 折价金额
     */
    @ApiModelProperty("折价应支付金额")
    @NotNull(message = "折价应支付金额不能为空")
    @DecimalMin(value="0", message = "折价应支付金额不能小于0")
    private BigDecimal zheJiaPayPrice;

    /**
     * 折价支付原因
     */
    @ApiModelProperty("折价支付原因")
    @NotBlank(message = "折价支付原因不能为空")
    private String zheJiaPayReason;
}
