package com.jiuji.oa.afterservice.bigpro.bo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 考勤状态查询结果BO
 * <AUTHOR>
 */
@Data
@ApiModel(description = "考勤状态查询结果BO")
public class AttendanceStatusBO {

    @ApiModelProperty(value = "是否在岗 1-在岗 0-未在岗")
    private Integer working;

    @ApiModelProperty(value = "暂离次数")
    private Integer lgCount;

    /**
     * 判断是否需要推送消息
     * 当working!=1或者lgCount>0时需要推送
     * @return true-需要推送 false-不需要推送
     */
    public boolean shouldPushMessage() {
        return !Integer.valueOf(1).equals(working) || (lgCount != null && lgCount > 0);
    }

    /**
     * 获取状态描述
     * @return 状态描述
     */
    public String getStatusDescription() {
        if (lgCount != null && lgCount > 0) {
            return "暂离";
        } else if (!Integer.valueOf(1).equals(working)) {
            return "未在岗";
        }
        return "在岗";
    }
}
