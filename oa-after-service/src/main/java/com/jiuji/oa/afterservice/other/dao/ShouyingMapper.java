package com.jiuji.oa.afterservice.other.dao;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.jiuji.oa.afterservice.bigpro.statistics.vo.req.StatisticsSaleShouYinQuery;
import com.jiuji.oa.afterservice.bigpro.statistics.vo.res.SaleShouYinProjectVo;
import com.jiuji.oa.afterservice.common.constant.DataSourceConstants;
import com.jiuji.oa.afterservice.other.po.Shouying;
import com.jiuji.oa.afterservice.statistics.vo.req.IncomeAndExpenditureReq;
import com.jiuji.oa.afterservice.statistics.vo.res.IncomeAndExpenditureVO;
import com.jiuji.tc.foundation.db.annotation.DS;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-25
 */
@Mapper
public interface ShouyingMapper extends BaseMapper<Shouying> {

    /**
     * description: <SqlServer-list>
     * translation: <SqlServer-list>
     *
     * @param wrapper 筛选条件
     * @return java.util.List<com.jiuji.oa.afterservice.other.po.Shouying>
     * <AUTHOR>
     * @date 9:36 2020/3/25
     * @since 1.0.0
     **/
    @Select("select * from shouying with(nolock) ${ew.customSqlSegment}")
    List<Shouying> listSqlServer(@Param(Constants.WRAPPER) Wrapper wrapper);

    /**
     * description: <SqlServer-getById>
     * translation: <SqlServer-getById>
     *
     * @param id 主键
     * @return com.jiuji.oa.afterservice.other.po.Shouying
     * <AUTHOR>
     * @date 9:36 2020/3/25
     * @since 1.0.0
     **/
    @Select("select * from shouying with(nolock) where id=${id}")
    Shouying getByIdSqlServer(@Param("id") Integer id);

    /**
     * 统计销售收银报表
     *
     * @param query@return
     */
    @DS(DataSourceConstants.CH999_OA_NEW_REPORT)
    List<SaleShouYinProjectVo> statisticsSaleShouYin(@Param("query") StatisticsSaleShouYinQuery query);

    /**
     * 收支报表统计
     *
     * @param req
     * @return
     */
    @DS(DataSourceConstants.CH999_OA_NEW_REPORT)
    List<IncomeAndExpenditureVO> saleIncomeAndExpenditure(@Param("req") IncomeAndExpenditureReq req);

    /**
     * 获取三方退款的名称
     *
     * @return
     */
    List<String> getThreePartyName();

    /**
     * 获取分期退款的名称
     *
     * @return
     */
    List<String> getInstallmentName();

    /**
     * 获取pos退款的名称
     *
     * @return
     */
    List<String> getPosName();

}
