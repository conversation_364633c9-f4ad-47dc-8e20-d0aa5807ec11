package com.jiuji.oa.afterservice.refund.service.kind.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSONObject;
import com.jiuji.oa.afterservice.bigpro.service.SmsService;
import com.jiuji.oa.afterservice.bigpro.vo.res.DouYinCouponLogRes;
import com.jiuji.oa.afterservice.common.constant.RequestCacheKeys;
import com.jiuji.oa.afterservice.common.enums.TuihuanKindEnum;
import com.jiuji.oa.afterservice.common.enums.XtenantEnum;
import com.jiuji.oa.afterservice.common.exception.CustomizeException;
import com.jiuji.oa.afterservice.common.exception.RRExceptionHandler;
import com.jiuji.oa.afterservice.common.util.SpringContextUtil;
import com.jiuji.oa.afterservice.other.po.Basket;
import com.jiuji.oa.afterservice.other.service.BasketService;
import com.jiuji.oa.afterservice.refund.bo.DetailParamBo;
import com.jiuji.oa.afterservice.refund.bo.RefundSubInfoBo;
import com.jiuji.oa.afterservice.refund.dao.OperatorBusinessRefundMapper;
import com.jiuji.oa.afterservice.refund.dao.ShouhouRefundMoneyMapper;
import com.jiuji.oa.afterservice.refund.enums.RefundBusinessEnum;
import com.jiuji.oa.afterservice.refund.service.RefundMoneyService;
import com.jiuji.oa.afterservice.refund.service.kind.SubTuiHuanKindService;
import com.jiuji.oa.afterservice.refund.service.way.WechatAlipaySecondsRefundService;
import com.jiuji.oa.afterservice.refund.vo.req.GroupTuihuanFormVo;
import com.jiuji.oa.afterservice.refund.vo.res.BusinessRefundVo;
import com.jiuji.oa.afterservice.refund.vo.res.RefundMoneyDetailVo;
import com.jiuji.oa.afterservice.refund.vo.res.TuiHuanCheckVo;
import com.jiuji.oa.afterservice.refund.vo.res.way.Refund;
import com.jiuji.oa.afterservice.refund.vo.res.way.ThirdOriginRefundVo;
import com.jiuji.oa.loginfo.order.vo.req.SubLogsNewReq;
import com.jiuji.oa.oacore.csharp.cloud.CsharpCloud;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.utils.common.build.LambdaBuild;
import com.jiuji.tc.utils.constants.BasketTypeEnum;
import com.jiuji.tc.utils.constants.NumberConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * 小件退换的处理类
 * <AUTHOR>
 * @since 2022/12/1 10:41
 */
@Service
@Slf4j
public class SubTuiHuanKindServiceImpl extends ParentTuiHuanKindServiceImpl implements SubTuiHuanKindService {

    @Resource
    private OperatorBusinessRefundMapper operatorBusinessRefundMapper;
    @Resource
    private BasketService basketService;

    @Override
    public R<Integer> assertCheckSaveAndSet(GroupTuihuanFormVo tuihuanForm) {
        Integer tuihuanKind = tuihuanForm.getTuihuanKind();

        R<Integer> checkTdjR = checkTdj(tuihuanForm, tuihuanKind);
        if (!checkTdjR.isSuccess()){
            return checkTdjR;
        }

        return super.assertCheckSaveAndSet(tuihuanForm);
    }

    /**
     * 校验退订金接口
     * @param tuihuanForm
     * @param tuihuanKind
     * @return
     */
    private R<Integer> checkTdj(GroupTuihuanFormVo tuihuanForm, Integer tuihuanKind) {
        // 判断只有是退定金的情况下才进行处理
        if(!TuihuanKindEnum.TDJ.getCode().equals(tuihuanKind)){
            return R.success(null);
        }
        //判断是不是返现订单
        List<BusinessRefundVo> businessRefundVos = SpringContextUtil.reqCache(()-> operatorBusinessRefundMapper.selectOperatorBusiness(tuihuanForm.getSubId()),
                RequestCacheKeys.SELECT_OPERATOR_BUSINESS, tuihuanForm.getSubId());
        if (CollectionUtils.isNotEmpty(businessRefundVos)) {
            List<JSONObject> refundWayDetails = tuihuanForm.getRefundWayDetails();
            //勾选运营商总金额
            BigDecimal selectRecurrence = refundWayDetails.stream()
                    .filter(item -> RefundBusinessEnum.SELECT_RECURRENCE.getCode() == item.getIntValue(Refund.REFUND_BUSINESS_TYPE))
                    .map(item -> item.getBigDecimal(Refund.REFUND_PRICE_KEY))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            RefundSubInfoBo subInfoBo = Optional.ofNullable(tuihuanForm.getSubInfoBo()).orElse(new RefundSubInfoBo());
            //已付款
            BigDecimal yifuM = Optional.ofNullable(subInfoBo.getYifuM()).orElse(BigDecimal.ZERO);
            //应付款
            BigDecimal yingfuM = Optional.ofNullable(subInfoBo.getYingfuM()).orElse(BigDecimal.ZERO);
            BigDecimal subtractPrice = yifuM.subtract(yingfuM);
            if(subtractPrice.compareTo(selectRecurrence)<0){
                return R.error("不能操作运营商返现，原因：运营商返现金额大于运营商收银抵扣收款可返现金额"+subtractPrice);
            }
        }
        return R.success(null);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(GroupTuihuanFormVo tuihuanForm) {

    }

    @Override
    public List<TuihuanKindEnum> myKind() {
        return Arrays.asList(TuihuanKindEnum.TDJ,TuihuanKindEnum.TDJ_LP);
    }

    @Override
    protected RefundSubInfoBo getSuInfoWithMaxRefundPrice(Integer subId, TuihuanKindEnum tuihuanKindEnum) {
        ShouhouRefundMoneyMapper shouhouRefundMoneyMapper = SpringUtil.getBean(ShouhouRefundMoneyMapper.class);
        Integer tuihuanKind = tuihuanKindEnum.getCode();
        RefundSubInfoBo maxRefundPriceSubInfo;
        switch (tuihuanKindEnum){
            case TDJ:
                maxRefundPriceSubInfo = shouhouRefundMoneyMapper.getSubMaxRefundPrice(subId, tuihuanKind);
                break;
            case TDJ_LP:
                maxRefundPriceSubInfo = shouhouRefundMoneyMapper.getRecoverMaxRefundPrice(subId,tuihuanKind);
                break;
            default:
                throw new CustomizeException(StrUtil.format("不支持{}获取最大可退金额",tuihuanKindEnum.getMessage()));
        }
        return maxRefundPriceSubInfo;
    }

    @Override
    public R<Integer> checkSubmitCheckAndSet(TuiHuanCheckVo tuiHuanCheckVo) {
        switch (tuiHuanCheckVo.getTuihuanKindEnum()){
            case TDJ:
                switch (tuiHuanCheckVo.getProcessStatusEnum()){
                    case CHECK3:
                        R<Boolean> srlCheck = SpringUtil.getBean(WechatAlipaySecondsRefundService.class).secondsRefundLimit(tuiHuanCheckVo);
                        if(!srlCheck.isSuccess()){
                            //校验不通过过记录日志
                            SpringUtil.getBean(RefundMoneyService.class).addLogs(tuiHuanCheckVo.getTuihuanKindEnum(),
                                    Collections.singletonList(LambdaBuild.create(new SubLogsNewReq())
                                            .set(SubLogsNewReq::setSubId, tuiHuanCheckVo.getTuiHuanPo().getSubId())
                                            .set(SubLogsNewReq::setComment, srlCheck.getUserMsg()).set(SubLogsNewReq::setDTime, LocalDateTime.now())
                                            .set(SubLogsNewReq::setInUser, tuiHuanCheckVo.getCurrUser().getUserName()).set(SubLogsNewReq::setShowType, Boolean.FALSE)
                                            .set(SubLogsNewReq::setType, 1)
                                            .build()));
                            return R.error(srlCheck.getUserMsg());
                        }
                        break;
                    default:
                        break;
                }
                break;
            default:
                break;
        }
        return super.checkSubmitCheckAndSet(tuiHuanCheckVo);
    }

    @Override
    public void forEachThirdRefund(ThirdOriginRefundVo tor, TuihuanKindEnum tuihuanKindEnum) {
        super.forEachThirdRefund(tor, tuihuanKindEnum);
        DouYinCouponLogRes.SubKindEnum subKindsEnum = null;
        switch (tuihuanKindEnum){
            case TDJ:
                subKindsEnum = DouYinCouponLogRes.SubKindEnum.ORDINARY;
                break;
            case TDJ_LP:
                subKindsEnum = DouYinCouponLogRes.SubKindEnum.QUALITY;
                break;
            default:
                break;
        }
        // 退订不管抖音有没有可退金额,都不允许退
        setDouYinNotRefund(tor, subKindsEnum, false);

    }

    @Override
    public void doDetailChain(DetailParamBo detailParamBo, R<RefundMoneyDetailVo> result) {
        TuihuanKindEnum tuihuanKindEnum = detailParamBo.getTuihuanKindEnum();
        if(TuihuanKindEnum.TDJ.getCode().equals(tuihuanKindEnum.getCode())){
           if(XtenantEnum.isJiujiXtenant()){
               //判断订单为DIy
               Optional.ofNullable(result.getData()).ifPresent(item->{
                   Integer subId =detailParamBo.getOrderId();
                   Integer count = Optional.ofNullable(basketService.lambdaQuery().eq(Basket::getSubId, subId).eq(Basket::getType, BasketTypeEnum.DIY.getCode()).apply("isnull(isdel,0)=0").count())
                           .orElse(NumberConstant.ZERO);
                   if(count>0){
                       List<String> notes = item.getNotes();
                       notes.add("* 特殊商品（diy保护壳）！请先对接采销同事再进行退款。");
                   }
               });
           }
        }
    }

    @Override
    public void saveSuccess(GroupTuihuanFormVo tuihuanForm) {
        //调用退款接口
        if(XtenantEnum.isJiujiXtenant()){
            R cstsfR = SpringUtil.getBean(CsharpCloud.class).calculateSubTuiServicesFee(tuihuanForm.getTuihuanId());
            if(!cstsfR.isSuccess()){
                RRExceptionHandler.logError(StrUtil.format("退款申请退款手续费计算,tuihuanId: {}", tuihuanForm.getTuihuanId()),
                        tuihuanForm,new CustomizeException(cstsfR), SpringUtil.getBean(SmsService.class)::sendOaMsgTo9JiMan);
            }
        }
    }
}
