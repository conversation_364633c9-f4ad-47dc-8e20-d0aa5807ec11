package com.jiuji.oa.afterservice.stock.controller;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jiuji.oa.afterservice.common.bo.OaUserBO;
import com.jiuji.oa.afterservice.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.afterservice.common.util.NewExcelUtils;
import com.jiuji.oa.afterservice.stock.dao.ProductMkcMapper;
import com.jiuji.oa.afterservice.stock.enums.DiaoboStatusEnum;
import com.jiuji.oa.afterservice.stock.enums.KcCheckEnum;
import com.jiuji.oa.afterservice.stock.enums.StockQueryByEnum;
import com.jiuji.oa.afterservice.stock.service.ProductMkcService;
import com.jiuji.oa.afterservice.stock.vo.AggregatedStockVO;
import com.jiuji.oa.afterservice.stock.vo.ShouhouMkcPageVO;
import com.jiuji.oa.afterservice.stock.vo.req.*;
import com.jiuji.oa.afterservice.stock.vo.res.MkcDelQueryRes;
import com.jiuji.oa.afterservice.stock.vo.res.MkcInpriceInfoRes;
import com.jiuji.oa.orginfo.areainfo.client.AreaInfoClient;
import com.jiuji.oa.orginfo.areainfo.vo.AreaInfo;
import com.jiuji.oa.orginfo.departinfo.client.DepartInfoClient;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.common.vo.ResultCode;
import com.jiuji.tc.utils.common.CommonUtils;
import com.jiuji.tc.utils.enums.EnumUtil;
import com.jiuji.tc.utils.enums.EnumVO;
import com.jiuji.tc.utils.xtenant.Namespaces;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @description 库存-售后转出
 * @since 2020/3/23
 */
@RestController
@RequestMapping("/api/shouhouMkc/")
@Api(tags = "售后转出")
@Slf4j
public class ShouhouMkcController {
    @Resource
    private ProductMkcService productMkcService;
    @Resource
    private AbstractCurrentRequestComponent requestComponent;
    @Autowired
    private AreaInfoClient areaInfoClient;
    @Resource
    private AbstractCurrentRequestComponent abstractCurrentRequestComponent;
    @Resource
    private ProductMkcMapper productMkcMapper;
    @Resource
    private DepartInfoClient departInfoClient;


    @PostMapping("search")
    @ApiOperation(value = "搜索", httpMethod = "POST")
    public R<Page<ShouhouMkcPageVO>> search(@RequestBody ShouhouMkcQuery req) {
        OaUserBO inUser = requestComponent.getCurrentStaffId();
        if (inUser == null) {
            return R.error(ResultCode.UNLOGIN, "请登录后再操作");
        }
        if (req == null) {
            return R.error(ResultCode.PARAM_ERROR, "参数为空");
        }
        req.setAuthorizeId(inUser.getAuthorizeId());

        if (req.getCurrent() <= 0) {
            req.setCurrent(1);
        }
        if (req.getSize() == null || req.getSize() == 0) {
            req.setSize(10);
        }

        Page<ShouhouMkcPageVO> page = productMkcService.pageShouhouMkc(req);
        if (page != null && CollectionUtils.isNotEmpty(page.getRecords())) {
            for (ShouhouMkcPageVO record : page.getRecords()) {
                if (record.getBasketId() == null || record.getBasketId() == 0) {
                    record.setKind("现货");
                } else if (record.getBasketId() > 0) {
                    record.setKind("订货");
                } else if (record.getBasketId() == -1) {
                    record.setKind("陈列锁定");
                }
                record.setKcCheck(EnumUtil.getMessageByCode(KcCheckEnum.class, record.getKcCheckCode()));
            }
        }
        return R.success(page);
    }

    @PostMapping("batchZx")
    @ApiOperation(value = "批量售后转现", httpMethod = "POST")
    public R<Boolean> batchZx(@RequestBody ShouhouZxReq req) {
        OaUserBO inUser = requestComponent.getCurrentStaffId();
        if (inUser == null) {
            return R.error(ResultCode.UNLOGIN, "请登录后再操作");
        }
        if (req == null) {
            return R.error(ResultCode.PARAM_ERROR, "参数为空");
        }
        if (CollectionUtils.isEmpty(req.getBatchReturnMkcId())) {
            return R.error(ResultCode.PARAM_ERROR, "请选择转现MKC");
        }
        if (StringUtils.isEmpty(req.getReason())) {
            return R.error(ResultCode.PARAM_ERROR, "请输入转现原因");
        }
        return R.success(productMkcService.batchZx(req, inUser));
    }

    @PostMapping("stockAggregation")
    @ApiOperation(value = "库存聚合分页查询", httpMethod = "POST")
    public R<Page<AggregatedStockVO>> stockAggregation(@RequestBody AggregatedStockHttpReq req) {
        req.setVendor(getCompanyName());
        if (req.getStockQueryByEnum().equals(StockQueryByEnum.ID)
                || req.getStockQueryByEnum().equals(StockQueryByEnum.PPID)
                && !org.apache.commons.lang3.StringUtils.isEmpty(req.getMainkey())) {
            try {
                Integer.valueOf(req.getMainkey());
            } catch (NumberFormatException e) {
                log.error("所选查询分类查询内容非数字ID");
                return R.error("所选查询分类查询内容非数字ID");
            }
        }
        return R.success(this.productMkcService.pageAggregatedStock(this.dealInput(req)));
    }

    /**
     * 导出报表
     */
    @PostMapping(value = "/stockAggregation/export")
    @ApiOperation(value = "导出excel报表", httpMethod = "POST")
    public R export(@RequestBody AggregatedStockHttpReq req,
                    HttpServletResponse response) {
        log.warn("聚合库存报表导出条件: {}", JSON.toJSONString(req));
        dealInput(req);
        req.setVendor(getCompanyName());
        if (req.getStockQueryByEnum().equals(StockQueryByEnum.ID)
                || req.getStockQueryByEnum().equals(StockQueryByEnum.PPID)
                && !org.apache.commons.lang3.StringUtils.isEmpty(req.getMainkey())) {
            try {
                Integer.valueOf(req.getMainkey());
            } catch (NumberFormatException e) {
                log.error("所选查询分类查询内容非数字ID");
                return R.error("所选查询分类查询内容非数字ID");
            }
        }
        req.setPageQuery(false);
        List<AggregatedStockVO> vos = productMkcService.listAggregatedStock(req);
        if (vos.size() > 60000) {
            vos = vos.subList(0, 59999);
        }
        ArrayList<String> titles = new ArrayList<>(Arrays.asList("门店", "ppid", "商品名称", "规格", "品牌", "供应商", "会员价", "库存数量", "库存金额", "在途数量",
                "在途金额", "欠款数量", "欠款金额", "合计数量", "合计金额"));
        List<List<Object>> datas = vos.stream().map(aggregatedStockVO -> {
            List<Object> cells = new ArrayList<>();
            cells.add(aggregatedStockVO.getAreaName());
            cells.add(aggregatedStockVO.getPpid() + "");
            cells.add(aggregatedStockVO.getPName());
            cells.add(aggregatedStockVO.getSpecs());
            cells.add(aggregatedStockVO.getBrand());
            cells.add(aggregatedStockVO.getVendor());
            cells.add(aggregatedStockVO.getMemberPrice().toString());
            cells.add(aggregatedStockVO.getStockCount() + "");
            cells.add(aggregatedStockVO.getStockAmount().toString());
            cells.add(aggregatedStockVO.getTransportCount() + "");
            cells.add(aggregatedStockVO.getTransportAmount().toString());
            cells.add(aggregatedStockVO.getDebtCount() + "");
            cells.add(aggregatedStockVO.getDebtAmount().toString());
            cells.add(aggregatedStockVO.getTotalCount() + "");
            cells.add(aggregatedStockVO.getTotalAmount().toString());
            return cells;
        }).collect(Collectors.toList());
        String fileName = "聚合库存报表（"
                + req.getDate().getYear()
                + "-" + req.getDate().getMonthValue()
                + "-" + req.getDate().getDayOfMonth()
                + "）.xlsx";

        try {
            NewExcelUtils.export(response, datas, titles, null,
                    fileName, 1);
        } catch (IOException e) {
            log.error(e.getMessage());
        }
        return R.success(null);
    }


    /**
     * 库存枚举查询
     */
    @GetMapping("stockQueryEnums")
    @ApiOperation(value = "库存聚合枚举查询", httpMethod = "GET")
    public R<Map<String, List<EnumVO>>> getStockQueryEnums() {
        Map<String, List<EnumVO>> map = new HashMap<>();
        map.put("areaLevelEnums", productMkcService.getAreaLevelEnums());
        map.put("stockQueryByEnums", productMkcService.getStockQueryByEnums());
        map.put("stockQueryEnums", productMkcService.getStockQueryEnums());
        return R.success(map);
    }

    /**
     * 参数处理
     *
     * @param req
     */
    private AggregatedStockHttpReq dealInput(AggregatedStockHttpReq req) {
        req.setCurUser(abstractCurrentRequestComponent.getCurrentStaffId());
//        {
//        只查看自己地区的订单
        req.setUserOwnAreas(req.getCurUser().getAreas());
        if (Objects.equals(null, req.getCurrent()) || req.getCurrent() < 1) {
            req.setCurrent(1);
        }
        if (Objects.equals(null, req.getSize()) || req.getSize() > 999 || req.getSize() < 10) {
            req.setSize(10);

        }
        if (req.getCateCodes().size() > 0) {
            req.setCids(String.join(",", req.getCateCodes()));
        }
//        if (!req.getRankList().contains("80")
//                && !req.getRankList().contains("777")
//                && req.getAreaKind1() != 3
//        )
//            req.setTop20(true);
//        if (Objects.equals(null, req.getStockQueryEnum()))
//            req.setStockQueryEnum(StockQueryEnum.DA_JIAN);


        if (Objects.equals(null, req.getDate()) || req.getDate().isAfter(LocalDate.now().plusDays(-1L))) {
            req.setDate(LocalDate.now().plusDays(-1L));
        }

        if (CollectionUtils.isNotEmpty(req.getAreaCodes())) {
            List<AreaInfo> areaInfos = areaInfoClient.listAll().getData();
            req.setAreaIds(this.dealAreaCodeListToIdList(req.getAreaCodes(), areaInfos));
        }
//        if (!Objects.equals(null, req.getUserOwnAreas()))
//            req.setUserOwnAreaIds(departInfoVOS.stream().filter(departInfoVO ->
//                    req.getUserOwnAreas().contains(departInfoVO.getCode()))
//                    .map(DepartInfoVO::getId).collect(Collectors.toList()));
        return req;
    }

    // todo 该方法挪到org项目

    /**
     * 入参为A1001 或者 524 之类
     *
     * @param areaCodes
     * @param areaInfos
     * @return areaIds
     */
    private List<Integer> dealAreaCodeListToIdList(List<String> areaCodes, List<AreaInfo> areaInfos) {

        return areaCodes.stream().flatMap(areaCode -> {
            if (areaCode.startsWith("a")) {
                String prefixCode = areaCode.substring(1);
                R<List<Integer>> allLowNodeR = departInfoClient.getAllLowNode(prefixCode);
                List<Integer> allLowNode = CommonUtils.isRequestSuccess(allLowNodeR) ? allLowNodeR.getData() : Collections.emptyList();
                return areaInfos.stream().filter(areaInfo ->
                        allLowNode.contains(areaInfo.getDepartId())
                ).map(AreaInfo::getId);
            } else {
                return Stream.of(Integer.parseInt(areaCode));
            }
        }).distinct().sorted().collect(Collectors.toList());
    }

    private String getCompanyName() {
        if (Namespaces.get() == 0) {
            return "九机";
        }
        return Optional.of(productMkcMapper.company()).orElse(" ");
    }


    /**
     * 售后现货转出
     * description C#方法 mkcDel
     */
    @PostMapping("/mkcDeal")
    @ApiOperation(value = "售后现货转出（瑕疵机转出未实现）", httpMethod = "POST")
    public R<MkcDelQueryRes> mkcDeal(@RequestBody @Validated MkcDelReq req) {
        return productMkcService.mkcDeal(req);
    }


    @GetMapping("/getEnums")
    @ApiOperation(value = "获取库存相关枚举选项")
    public R<Map<String, List<EnumVO>>> listAllEnum() {

        Map<String, List<EnumVO>> enumMap = new HashMap<>();
        List<EnumVO> kcCheckStatusEnum = EnumUtil.toEnumVOList(KcCheckEnum.class);
        List<EnumVO> diaoBoStatusEnum = EnumUtil.toEnumVOList(DiaoboStatusEnum.class);
        enumMap.put("kcCheckStatusEnum", kcCheckStatusEnum);
        enumMap.put("diaoBoStatusEnum", diaoBoStatusEnum);
        return R.success(enumMap);
    }

    @GetMapping("/getInPriceByPpid")
    @ApiOperation(value = "获取库存成本价")
    public R<MkcInpriceInfoRes> getInPriceByPpid(@RequestParam(value = "ppid") Integer ppid) {
        return productMkcService.getInPriceByPpid(ppid);
    }

    /**
     * 售后现货转出
     * description C#方法 mkcDel "save"模块
     */
    @PostMapping("/mkcDealSave")
    @ApiOperation(value = "售后转出提交", httpMethod = "POST")
    public R<Integer> mkcDealSave(@RequestBody @Validated MkcDelReq req) {
        return productMkcService.mkcDealSave(req);
    }


    /**
     * 售后现货转出审核1
     * description C#方法 mkcDel "acept1"模块
     */
    @PostMapping("/mkcDealCheck1")
    @ApiOperation(value = "售后转出审核1", httpMethod = "POST")
    public R<String> mkcDealCheck1(@RequestBody @Validated MkcDelReq req) {
        return productMkcService.mkcDealCheck1(req);
    }


    /**
     * 售后现货转出审核2
     * description C#方法 mkcDel "acept2"模块
     */
    @PostMapping("/mkcDealCheck2")
    @ApiOperation(value = "售后转出审核2", httpMethod = "POST")
    public R<String> mkcDealCheck2(@RequestBody @Validated MkcDelCheck2Req req) {
        return productMkcService.mkcDealCheck2(req);
    }

    /**
     * 售后现货转出 删除
     * description C#方法 mkcDel "del"模块
     */
    @GetMapping("/mkcDealDel")
    @ApiOperation(value = "售后转出删除", httpMethod = "GET")
    public R<String> mkcDealDel(@RequestParam(value = "id") Integer id) {
        return productMkcService.mkcDealDel(id);
    }

}
