package com.jiuji.oa.afterservice.refund.dao.way;

import com.jiuji.oa.afterservice.refund.bo.ParentChildSubBo;
import com.jiuji.oa.afterservice.refund.bo.PayGatewayMchBo;
import com.jiuji.oa.afterservice.refund.vo.req.GroupTuihuanFormVo;
import com.jiuji.oa.afterservice.refund.vo.res.way.NetPayOriginRefundVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * 原路径退款
 * <AUTHOR>
 * @since 2022/7/27 15:32
 */
@Mapper
public interface NetPayOriginWayMapper {
    List<NetPayOriginRefundVo> listNetPayRecordInfo(@Param("subIds") Collection<Integer> subIds, @Param("type") Integer type, @Param("tuiWays") List<String> tuiWays);

    List<NetPayOriginRefundVo> listNetPayRecordInfoByIds(@Param("recordIds") Set<Integer> recordIds);

    long countAlipayYouHui(@Param("subId") Integer subId, @Param("ppriceids") List<Integer> ppriceids);

    int batchInsert(@Param("tuihuanForm") GroupTuihuanFormVo tuihuanForm, @Param("tuiWayDetails") List<NetPayOriginRefundVo> tuiWayDetails);

    int batchInsertNetPayRefundInfo(@Param("tuihuanForm") GroupTuihuanFormVo tuihuanForm, @Param("tuiWayDetails") List<NetPayOriginRefundVo> tuiWayDetails);

    /**
     * 批量更新网络支付信息
     * @param tuiWayDetails
     * @return
     */
    int batchUpdateNetPayInfo(@Param("tuiWayDetails") Collection<NetPayOriginRefundVo> tuiWayDetails);

    ParentChildSubBo getSubParentId(@Param("subId") Integer subId);

    List<ParentChildSubBo> listSubChildId(@Param("subPId") Integer subPId);

    ParentChildSubBo getLpSubParentId(@Param("subId") Integer subId);

    List<ParentChildSubBo> listLpSubChildId(@Param("subPId") Integer subPId);

    /**
     * 更新分账的金额
     * @param netRecordId
     * @param refundSplitServiceFee
     */
    void updateRefundSplitServiceFee(@Param("netRecordId") Integer netRecordId,
                                     @Param("refundSplitServiceFee") BigDecimal refundSplitServiceFee);

    PayGatewayMchBo getPayGatewayMch();

    String getChannelConfigIdByNetPayRecordId(@Param("netPayRecordId") Integer netPayRecordId);
}
