package com.jiuji.oa.afterservice.stock.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.afterservice.bigpro.bo.OperateProductKcPara;
import com.jiuji.oa.afterservice.bigpro.bo.productkc.OperateProductKcRes;
import com.jiuji.oa.afterservice.bigpro.bo.productkc.ProductLeftKcInfoBo;
import com.jiuji.oa.afterservice.bigpro.po.BasketOther;
import com.jiuji.oa.afterservice.bigpro.po.Productinfo;
import com.jiuji.oa.afterservice.bigpro.service.BasketOtherService;
import com.jiuji.oa.afterservice.bigpro.service.ProductinfoService;
import com.jiuji.oa.afterservice.bigpro.service.SmsService;
import com.jiuji.oa.afterservice.common.aspect.RepeatSubmitCheck;
import com.jiuji.oa.afterservice.common.config.rabbimq.RabbitMqConfig;
import com.jiuji.oa.afterservice.common.constant.RequestAttrKeys;
import com.jiuji.oa.afterservice.common.constant.ShouhouConstants;
import com.jiuji.oa.afterservice.common.enums.XtenantEnum;
import com.jiuji.oa.afterservice.common.event.bo.AfterRollBackEvent;
import com.jiuji.oa.afterservice.common.exception.RRExceptionHandler;
import com.jiuji.oa.afterservice.common.util.CommenUtil;
import com.jiuji.oa.afterservice.common.util.SpringContextUtil;
import com.jiuji.oa.afterservice.common.vo.res.OaQuequRes;
import com.jiuji.oa.afterservice.smallpro.bo.SmallproKcReqBO;
import com.jiuji.oa.afterservice.smallpro.service.SmallproForwardExService;
import com.jiuji.oa.afterservice.stock.bo.LockKcInfoBO;
import com.jiuji.oa.afterservice.stock.dao.NaHuoMapper;
import com.jiuji.oa.afterservice.stock.dao.ProductKcMapper;
import com.jiuji.oa.afterservice.stock.po.ProductKc;
import com.jiuji.oa.afterservice.stock.po.ProductKcOrderCountLog;
import com.jiuji.oa.afterservice.stock.po.ProductKclogs;
import com.jiuji.oa.afterservice.stock.po.StockStatusCountBo;
import com.jiuji.oa.afterservice.stock.service.ProductKcOrderCountLogService;
import com.jiuji.oa.afterservice.stock.service.ProductKcService;
import com.jiuji.oa.afterservice.stock.service.ProductKclogsService;
import com.jiuji.oa.afterservice.sys.service.AuthConfigService;
import com.jiuji.oa.orginfo.areainfo.client.AreaInfoClient;
import com.jiuji.oa.orginfo.areainfo.vo.AreaInfo;
import com.jiuji.oa.orginfo.departinfo.client.DepartInfoClient;
import com.jiuji.oa.orginfo.departinfo.vo.DepartInfoVO;
import com.jiuji.oa.orginfo.sysconfig.client.SysConfigClient;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.common.vo.ResultCode;
import com.jiuji.tc.utils.common.CommonUtils;
import com.jiuji.tc.utils.constants.NumberConstant;
import com.jiuji.tc.utils.constants.SignConstant;
import com.jiuji.tc.utils.constants.SysConfigConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-17
 */
@Service
@Slf4j
public class ProductKcServiceImpl extends ServiceImpl<ProductKcMapper, ProductKc> implements ProductKcService {

    @Autowired
    private ProductinfoService productinfoService;
    @Autowired
    private BasketOtherService basketOtherService;
    @Resource
    private AreaInfoClient areaInfoClient;
    @Lazy
    @Autowired
    private SmsService smsService;
    @Autowired
    private ProductKclogsService productKclogsService;
    @Resource
    private RabbitTemplate rabbitTemplate;

    @Autowired
    private ShouhouConstants shouhouConstants;
    @Resource
    private SysConfigClient sysConfigClient;
    @Resource
    private ProductKcOrderCountLogService prodOrderCountLogService;
    @Resource
    private NaHuoMapper naHuoMapper;

    @Lazy
    @Resource
    private ProductKcService productKcService;

    public static final Integer MAX_FROM_SOURCE_SIZE = 950;

    @Override
    public Integer getKcCount(Integer ppid, Integer areaId) {
        if (ppid == null || ppid == 0 || areaId == null || areaId == 0) {
            return 0;
        }
        return baseMapper.getKcCount(ppid, areaId);
    }

    @Override
    public Map<Integer, Integer> listKcCount(Collection<Integer> ppidList, Integer areaId) {
        if(CollUtil.isEmpty(ppidList)){
            return Collections.emptyMap();
        }
        return baseMapper.listKcCount(ppidList, areaId).stream()
                .collect(Collectors.toMap(ProductKc::getPpriceid,ProductKc::getLeftCount, (v1,v2) -> v1));
    }

    @Override
    public Map<Integer, ProductKc> listKcCountMap(Collection<Integer> ppidList, Integer areaId) {
        if(CollUtil.isEmpty(ppidList)){
            return Collections.emptyMap();
        }
        return baseMapper.listKcCount(ppidList, areaId).stream()
                .collect(Collectors.toMap(ProductKc::getPpriceid, Function.identity(), (v1, v2) -> v2));
    }

    @Override
    public Boolean lockKc(Integer ppid, Integer areaId) {
        return productKcService.lockKc(ppid, areaId, 1);
    }

    @Override
    public Boolean lockKc(Integer ppid, Integer areaId, Integer orderCount) {
        if (checkNullPpidAndAreaId(ppid, areaId)) {
            return false;
        }
        boolean flag;
        try {
            //锁单操作 并且返回锁单信息
            LockKcInfoBO lockKcInfoBO = baseMapper.lockKcNew(ppid, areaId, orderCount);
            if(ObjectUtil.isNull(lockKcInfoBO)){
                flag = false || negativeInventory(ppid) || SpringContextUtil.getRequest().map(req -> req.getAttribute(RequestAttrKeys.IS_SHOUHOU_BIND_PEIJIAN))
                        .map(Convert::toBool).orElse(false);
            } else {
                ProductKcOrderCountLog productKcOrderCountLog = getProductKcOrderCountLog(ppid, areaId, lockKcInfoBO,orderCount);
                flag = prodOrderCountLogService.save(productKcOrderCountLog);
            }
        } catch (Exception e) {
            Integer b = Optional.ofNullable(baseMapper.lockKc(ppid, areaId, orderCount)).orElse(NumberConstant.ZERO);
            flag = (b > NumberConstant.ZERO) || negativeInventory(ppid);
            String comment = String.format("小件库存 锁 单流水记录异常ppid:%s,areaId:%s", ppid, areaId);
            //记录异常并且通知
            RRExceptionHandler.logError("小件库存 锁 单流水记录异常",comment,e,smsService::sendOaMsgTo9JiMan);
        }
        return flag;
    }


    /**
     * 允许负库存出库
     * @param ppid
     * @return
     */
    @Override
    public Boolean negativeInventory(Integer ppid) {
        Productinfo productinfo = Optional.ofNullable(productinfoService.getProductinfoByPpid(ppid)).orElse(new Productinfo());
        List<Integer> cidList = getCidList();
        List<Integer> ppidList = getPpidList();
        return cidList.contains(productinfo.getCid()) || ppidList.contains(ppid);
    }

    /**
     * 获取锁单流水记录
     * @param ppid
     * @param areaId
     * @param lockKcInfoBO
     * @return
     */
    private ProductKcOrderCountLog getProductKcOrderCountLog(Integer ppid, Integer areaId,LockKcInfoBO lockKcInfoBO,Integer changeValue) {
        ProductKcOrderCountLog productKcOrderCountLog = new ProductKcOrderCountLog();
        //获取当前线程调用的堆栈信息
        Thread currentThread = Thread.currentThread();
        StackTraceElement[] stackTraces = currentThread.getStackTrace();
        List<StackTraceElement> list = Arrays.asList(stackTraces);
        StringJoiner joiner = new StringJoiner(",");
        if(CollectionUtils.isNotEmpty(list)){
            list.stream()
                    .filter(obj -> !obj.getClassName().contains("com.jiuji.oa.afterservice.common") && obj.getClassName().startsWith("com.jiuji.oa.afterservice") && Optional.of(obj.getLineNumber()).orElse(NumberConstant.ZERO) != -1)
                    .map(item ->  item.getMethodName() + ":" + item.getLineNumber())
                    .forEach(joiner::add);
        }
        String comment = joiner.toString().substring(0, NumberUtil.min(joiner.toString().length(), MAX_FROM_SOURCE_SIZE));
        productKcOrderCountLog.setAreaId(areaId)
                .setPpid(ppid)
                .setFromSource(comment)
                .setChangeValue(changeValue)
                .setKcId(lockKcInfoBO.getId())
                .setNewValue(Optional.ofNullable(lockKcInfoBO.getNewValue()).orElse(NumberConstant.ZERO))
                .setOldValue(Optional.ofNullable(lockKcInfoBO.getOldValue()).orElse(NumberConstant.ZERO));
        log.warn("小件库存处理日志信息：{}",JSON.toJSONString(productKcOrderCountLog));
        return productKcOrderCountLog;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean unlockKc(Integer ppid, Integer areaId) {
        if (checkNullPpidAndAreaId(ppid, areaId)) {
            return false;
        }
        boolean flag;
        try {
            //锁单操作 并且返回锁单信息
            LockKcInfoBO lockKcInfoBO = baseMapper.unlockKcNew(ppid, areaId);
            if(ObjectUtil.isNull(lockKcInfoBO)){
                flag =  false;
            } else {
                ProductKcOrderCountLog productKcOrderCountLog = getProductKcOrderCountLog(ppid, areaId, lockKcInfoBO,-NumberConstant.ONE);
                flag = prodOrderCountLogService.save(productKcOrderCountLog);
            }
        }catch (Exception e) {
            Integer upCount = Optional.ofNullable(baseMapper.unlockKc(ppid, areaId)).orElse(NumberConstant.ZERO);
            String comment = String.format("小件库存 解锁 单流水记录异常ppid:%s,areaId:%s", ppid, areaId);
            //记录异常并且通知
            RRExceptionHandler.logError("小件库存 解锁 单流水记录异常",comment,e,smsService::sendOaMsgTo9JiMan);
            flag = upCount > 0;
        }
        return flag;
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRES_NEW)
    public Boolean unlockKcNewTrans(Integer ppid, Integer areaId) {
        if (checkNullPpidAndAreaId(ppid, areaId)) {
            return false;
        }
        Integer upCount = baseMapper.unlockKc(ppid, areaId);
        return upCount > 0;
    }

    /**
     * 检查ppid和门店id是否为空
     *
     * @param ppid
     * @param areaId
     * @return
     */
    private Boolean checkNullPpidAndAreaId(Integer ppid, Integer areaId) {
        return ppid == null || ppid == 0 || areaId == null || areaId == 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<OperateProductKcRes> newOperateProductKc(OperateProductKcPara para) {
        SmallproForwardExService forwardExService = SpringUtil.getBean(SmallproForwardExService.class);
        SmallproKcReqBO smallproKcReq = new SmallproKcReqBO().setAreaId(para.getAreaId()).setBasketId(Convert.toInt(para.getBasketId()))
                .setCheck1(Convert.toInt(para.getCheck1())).setCheck2(Convert.toInt(para.getCheck2())).setComment(para.getComment())
                .setCount(para.getCount()).setDiaoboFlag(para.getDiaoboFlag()).setInPrice(para.getInprice()).setInsource(para.getInsource())
                .setIsLp(para.getIsLp()).setPpriceId(para.getPpid()).setShouhouId(Convert.toInt(para.getShouhouId()))
                .setUserName(para.getInuser());
        R<OperateProductKcRes> sncR = forwardExService.stockOperationsV2(smallproKcReq);
        if (sncR.isSuccess()){
            //发布回滚补偿事件
            SmallproKcReqBO rollSmallproKcReq = smallproKcReq.copy();
            rollSmallproKcReq.setCount(rollSmallproKcReq.getCount()*-1).setComment(ProductKcService.TRANS_ROLLBACK_COMMENT_START + rollSmallproKcReq.getComment());
            Optional.ofNullable(sncR.getData()).map(OperateProductKcRes::getInprice).ifPresent(rollSmallproKcReq::setInPrice);

            SpringUtil.getApplicationContext().publishEvent(new AfterRollBackEvent(this,rollSmallproKcReq,
                    ()->forwardExService.stockOperationsV2(rollSmallproKcReq)));
        }
        return sncR;
    }

    /**
     * 改为调用C#的接口
     * @param para
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @RepeatSubmitCheck()
    public R<OperateProductKcRes> operateProductKc(OperateProductKcPara para) {
        //默认值处理
        Boolean transFlag = para.getTransFlag() == null || para.getTransFlag();
        Boolean isLp = para.getIsLp() != null && para.getIsLp();
        Boolean diaoboFlag = para.getDiaoboFlag() != null && para.getDiaoboFlag();

        Boolean transferFlag = ("调拨".equals(para.getComment()) && para.getCount() > 0 && transFlag);

        if (StringUtils.isEmpty(para.getInuser())) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return R.error("库存操作人不能为空");
        }
        if (CommenUtil.isNullOrZero(para.getCount())) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return R.error("库存操作数量不能为空");
        }
        if (CommenUtil.isNullOrZero(para.getPpid())) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return R.error("ppid为空");
        }

        log.info("配件库存出入库信息：" + JSONObject.toJSONString(para));
        Integer cid = 0;
        Integer vendor = 0;
        Integer orderCount = 0;
        Boolean isDone = false;
        Integer ppidOther = 0;
        Integer areaOther = 0;

        if (para.getCount() < 0) {
            //出库
            List<Productinfo> pDt = productinfoService.list(new LambdaQueryWrapper<Productinfo>().eq(Productinfo::getPpriceid, para.getPpid()));
            if (CollectionUtils.isNotEmpty(pDt)) {
                cid = pDt.get(0).getCid();
                vendor = pDt.get(0).getVendor();
                cid = cid == null ? 0 : cid;
                vendor = vendor == null ? 0 : vendor;
            }
        }
        if (("欠款".equals(para.getComment()) || "欠款转出".equals(para.getComment())) && !isLp && !Arrays.asList(16386, 59830, 65752, 16387).contains(para.getPpid())) {

            BasketOther bo = basketOtherService.getBasketOtherInfoByIdAndIsDone(para.getBasketId(), "欠款".equals(para.getComment()) ? 0 : 1);
            if (bo != null) {
                orderCount = bo.getLcount() == null ? 0 : bo.getLcount();
                isDone = bo.getIsDone() != null && bo.getIsDone();
                ppidOther = bo.getPpriceid();
                areaOther = bo.getAreaid();
            }
        }

        List<ProductKc> productKcList = super.list(new LambdaQueryWrapper<ProductKc>().eq(ProductKc::getAreaid, para.getAreaId()).eq(ProductKc::getPpriceid, para.getPpid()));
        Integer oldCount = 0;
        Integer countE = 0;
        Integer oLcount = 0;
        BigDecimal inpriceE = BigDecimal.valueOf(-1);
        BigDecimal oldInprice = BigDecimal.ZERO;
        Integer panCount = 0;

        if (CollectionUtils.isNotEmpty(productKcList)) {
            ProductKc pk = productKcList.get(0);
            oldCount = pk.getLeftCount() == null ? 0 : pk.getLeftCount();
            inpriceE = pk.getInprice() == null ? BigDecimal.ZERO : pk.getInprice();
            countE = pk.getLcount() == null ? 0 : pk.getLcount();
//            if (pk.getOrderCount().equals(0) && para.getCount() < 0){
//                return R.error("当前地区备货锁定库存量已为0，不可再撤销");
//            }
            oLcount = countE;
            if (CommenUtil.isNotNullZero(pk.getPanCount())) {
                panCount = pk.getPanCount();
            }
            oldInprice = inpriceE;
        }

        //其他
        if (para.getPpid().equals(10754)) {
            oldInprice = para.getInprice();
        }

        List<Integer> ppidList = getPpidList();
        List<Integer> cidList = getCidList();
        if (para.getCount() < NumberConstant.ZERO
                && (oldCount + orderCount + para.getCount()) < NumberConstant.ZERO
                && !ppidList.contains(para.getPpid())
                && !cidList.contains(cid)) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return R.error("负库存不允许出库");
        }

        //调拨 负库存不允许调拨
//        if (para.getCount() < 0 && (oldCount + orderCount + para.getCount()) < 0 &&
//                ((!shouhouConstants.getOtherPPid().contains(para.getPpid()) && !shouhouConstants.getOtherCid().contains(cid)
//                        && !shouhouConstants.getMaskPPriceids().contains(para.getPpid()) && !shouhouConstants.getInstallServicesPpriceid().contains(para.getPpid()))
//                        || "调拨".equals(para.getComment())) && vendor.equals(0)) {
//            //负库存不允许出库
//            //vendor为0为九机商品，否则为第三方发货产品，第三方发货商品允许负库存出库
//            return R.error("负库存不允许出库");
//        }

        Boolean dcFlag = false;
        R<AreaInfo> areaInfoR = areaInfoClient.getAreaInfoById(para.getAreaId());
        if (areaInfoR.getCode() != ResultCode.SUCCESS || areaInfoR.getData() == null) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return R.error("库存操作地区信息不存在");
        }

        AreaInfo areaInfo = areaInfoR.getData();
        int shortXtenant = CommonUtils.toShotWebXtenant(XtenantEnum.getXtenant());
        int xtenant = XtenantEnum.getXtenant();
        Optional<DepartInfoVO> departInfoOpt = Optional.ofNullable(areaInfo).map(AreaInfo::getDepartId)
                .map(SpringUtil.getBean(DepartInfoClient.class)::getByDepartId).map(R::getData);
        List<Integer> departPathList = StrUtil.splitTrim(departInfoOpt.map(DepartInfoVO::getDepartPath).orElse(null), StringPool.COMMA)
                .stream().map(Convert::toInt).collect(Collectors.toList());
        boolean isAuthPart = Boolean.TRUE.equals(SpringUtil.getBean(AuthConfigService.class).isAuthPart(para.getAreaId(), XtenantEnum.getXtenant()));

        if (para.getInprice().compareTo(BigDecimal.ZERO) >= 0 && para.getCount() > 0) {
            if (transferFlag) {
                inpriceE = para.getInprice();
            } else {
                //自营（共享大仓成本）
                Integer count1 = 0;
                Boolean inpriceFlag = true;
                if (areaInfo.getKind1().equals(1)) {
                    ProductLeftKcInfoBo leftKcInfo = baseMapper.getProductLeftKcInfoByPpid(shortXtenant,xtenant,departPathList,
                            isAuthPart,areaInfo.getAuthorizeId(),para.getPpid());
                    if (leftKcInfo != null) {
                        dcFlag = true;
                        inpriceE = leftKcInfo.getInprices() == null ? BigDecimal.ZERO : leftKcInfo.getInprices();
                        count1 = leftKcInfo.getLcounts() == null ? 0 : leftKcInfo.getLcounts();
                    }

                    if (para.getComment().contains("采购单")) {
                        ProductLeftKcInfoBo diaoBoLeftKcInfo = baseMapper.getDiaoBoBasketLeftKcInfoByPpid(shortXtenant,xtenant,departPathList,
                                isAuthPart,areaInfo.getAuthorizeId(),para.getPpid());
                        if (diaoBoLeftKcInfo != null) {
                            dcFlag = true;
                            inpriceE = inpriceE.add(diaoBoLeftKcInfo.getInprices() == null ? BigDecimal.ZERO : diaoBoLeftKcInfo.getInprices());
                            count1 = count1 + (diaoBoLeftKcInfo.getLcounts() == null ? 0 : diaoBoLeftKcInfo.getLcounts());
                        }
                    }

                }

                if (inpriceE.compareTo(BigDecimal.valueOf(-1)) == 0) {
                    //如果库存成本为0 以当前采购价为成本价
                    inpriceE = para.getInprice();
                } else if (inpriceE.compareTo(BigDecimal.ZERO) >= 0 && ((countE > 0 && !dcFlag) || (count1 > 0 && dcFlag))) {
                    //还有库存 库存 加权平均计算成本
                    if (dcFlag) {
                        inpriceE = (inpriceE.add(para.getInprice().multiply(BigDecimal.valueOf(para.getCount())))).divide(BigDecimal.valueOf(Long.valueOf(count1) + Long.valueOf(para.getCount())), 4);
                    } else {
                        inpriceE = ((inpriceE.multiply(BigDecimal.valueOf(countE)).add(para.getInprice().multiply(BigDecimal.valueOf(para.getCount()))))).divide(BigDecimal.valueOf(Long.valueOf(countE) + Long.valueOf(para.getCount())), 4);
                    }
                } else {
                    inpriceE = para.getInprice();
                }

            }
        }

        if (inpriceE.compareTo(BigDecimal.valueOf(-1)) == 0) {
            inpriceE = para.getInprice();
        }

        countE = countE + para.getCount();

        Integer pCount = panCount + para.getCount();
        Integer kcCount = 0;
        Integer kcLogCount = 0;

        Boolean flag = false;

        if (CollectionUtils.isEmpty(productKcList)) {
            //不存在记录
            ProductKc pk = new ProductKc();
            pk.setAreaid(para.getAreaId());
            pk.setLcount(countE);
            pk.setInprice(inpriceE);
            pk.setPpriceid(para.getPpid());
            flag = super.save(pk);
        } else {
            flag = super.update(new LambdaUpdateWrapper<ProductKc>()
                    .set(ProductKc::getInprice, inpriceE)
                    .set(ProductKc::getLcount, countE)
                    .set(panCount != null, ProductKc::getPanCount, panCount)
                    .eq(ProductKc::getAreaid, para.getAreaId()).eq(ProductKc::getPpriceid, para.getPpid()).eq(ProductKc::getLcount, oLcount));
        }

        if (!flag) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return R.error("更新库存失败");
        }

        kcCount = oLcount;

        if (orderCount > 0) {
            //ppriceid发生变化 不能执行
            if (!ppidOther.equals(para.getPpid()) || !areaOther.equals(para.getAreaId())) {
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return R.error("操作失败,ppid发生改变");
            }

            flag = super.update(new LambdaUpdateWrapper<ProductKc>().setSql(StrUtil.format("orderCount=case when orderCount{sign}{orderCount} >= 0 " +
                            "then orderCount{sign}{orderCount} else 0 end",
                    Dict.create().set("sign", (isDone ? "+" : "-")).set("orderCount", orderCount)))
                    .eq(ProductKc::getAreaid, para.getAreaId()).eq(ProductKc::getPpriceid, para.getPpid()));

            if (!flag) {
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return R.error("更新订单数量失败");
            }
            flag = basketOtherService.update(new LambdaUpdateWrapper<BasketOther>()
                    .set(BasketOther::getIsDone, !isDone).eq(BasketOther::getBasketId, para.getBasketId()).eq(BasketOther::getIsDone, isDone).eq(BasketOther::getPpriceid, para.getPpid()));
            if (!flag) {
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return R.error("更新订单关联信息表失败");
            }

        }

        //更新自营大仓平均成本
        if (dcFlag && !transferFlag) {
            Integer count = baseMapper.updateDcCostPrice(shortXtenant,xtenant,departPathList, isAuthPart,
                    areaInfo.getAuthorizeId(),inpriceE, para.getPpid());
            if (CommenUtil.isNullOrZero(count)) {
                smsService.sendOaMsgTo9JiMan("大仓平均成本更新失败：ppriceid[" + para.getPpid() + "],area[" + para.getAreaId() + "]");
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return R.error("大盘平均成本更新失败！");
            }
        }

        ProductKclogs kclogs = new ProductKclogs();
        kclogs.setPpriceid(para.getPpid());
        kclogs.setCount(para.getCount());
        kclogs.setLastcount(countE);
        kclogs.setInprice(inpriceE);
        kclogs.setInputPrice(para.getInprice());
        kclogs.setAreaid(para.getAreaId());
        kclogs.setInuser(para.getInuser());
        kclogs.setInsource(para.getInsource());
        kclogs.setComment(para.getComment());
        kclogs.setBasketId(para.getBasketId());
        kclogs.setShouhouId(para.getShouhouId());

        Boolean check1 = para.getCheck1() != null && para.getCheck1();
        Boolean check2 = para.getCheck2() != null && para.getCheck2();
        kclogs.setCheck1(check1);
        kclogs.setCheck2(check2);
        kclogs.setCheck1dtime(check1 ? LocalDateTime.now() : null);
        kclogs.setCheck2dtime(check2 ? LocalDateTime.now() : null);
        kclogs.setDtime(LocalDateTime.now());

        boolean saveKcLogR = productKclogsService.save(kclogs);
        if (!saveKcLogR) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return R.error("库存日志保存失败");
        }
        kcLogCount = countE;
//        if (kcCount != kcLogCount){
//            log.error("库存与日志不匹配");
//            smsService.sendOaMsg("库存与日志不匹配：ppriceid["+para.getPpid()+"],area["+para.getAreaId()+"]",null,"10722", OaMesTypeEnum.YCTZ.getCode().toString());
//            return R.error("库存与日志不匹配！");
//        }


        //出入库记录检测 自动备货
        //正式环境，rabbitMQ推送(要放在出其他配件逻辑前面，不然ppid会被改掉
        Map<String, String> data = new HashMap<>();
        data.put("areaid", String.valueOf(para.getAreaId()));
        data.put("ppriceid", String.valueOf(para.getPpid()));
        OaQuequRes oaQuequRes = new OaQuequRes("autoKcPush", data);


        Integer[] s1 = {1, 2, 3, 4, 5, 6, 7, 8, 9};
        int expire = s1[new Random().nextInt(s1.length)] * 300;
        rabbitTemplate.convertAndSend("", RabbitMqConfig.autoKcPushQueue, JSON.toJSONString(oaQuequRes), message -> {
            message.getMessageProperties().setExpiration(String.valueOf(expire));
            return message;
        });
//            smsService.oaRabbitMQWorkQueue(JSON.toJSONString(oaQuequRes), RabbitMqConfig.autoKcPushQueue);

        OperateProductKcRes res = new OperateProductKcRes();
        res.setCount(countE);
        res.setInprice(oldInprice);

        return R.success("出库成功", res);
    }

    /**
     * 允许负库存出库的cid
     *
     * @return
     */
    public List<Integer> getCidList() {
        return Optional.ofNullable(sysConfigClient.getValueByCode(SysConfigConstant.NEGATIVE_STOCK_CID)).filter(R::isSuccess)
                .map(R::getData).filter(StringUtils::isNotEmpty).map(str -> StrUtil.splitTrim(str, SignConstant.COMMA).stream()
                        .map(Convert::toInt).filter(Objects::nonNull)
                        .collect(Collectors.toList()))
                .orElse(Collections.emptyList());
    }

    /**
     * 允许负库存出库的ppid
     *
     * @return
     */
    public List<Integer> getPpidList() {
        return Optional.ofNullable(sysConfigClient.getValueByCode(SysConfigConstant.NEGATIVE_STOCK_PPID)).filter(R::isSuccess)
                .map(R::getData).filter(StringUtils::isNotEmpty).map(str -> StrUtil.splitTrim(str, SignConstant.COMMA).stream()
                        .map(Convert::toInt).filter(Objects::nonNull)
                        .collect(Collectors.toList()))
                .orElse(Collections.emptyList());
    }


    @Override
    public Integer hasProductMKC(Integer mkcId, String imei) {
        return baseMapper.hasProductMKC(mkcId, imei);
    }

    @Transactional(rollbackFor =Exception.class)
    @Override
    public Boolean updateProductKcNew(Integer count, Long kcid) {
        if (ObjectUtil.isNull(count) || ObjectUtil.isNull(kcid)) {
            return false;
        }
        boolean flag;
        try {
            //锁单操作 并且返回锁单信息
            LockKcInfoBO lockKcInfoBO = this.baseMapper.updateProductKcNew(count, kcid);
            ProductKc productKc = Optional.ofNullable(this.getById(kcid)).orElse(new ProductKc());
            ProductKcOrderCountLog productKcOrderCountLog = getProductKcOrderCountLog(productKc.getPpriceid(), productKc.getAreaid(), lockKcInfoBO,NumberConstant.ONE);
            flag = prodOrderCountLogService.save(productKcOrderCountLog);
        } catch (Exception e) {
            Integer updateProductkcCount = Optional.ofNullable(naHuoMapper.updateProductKc(count, kcid)).orElse(NumberConstant.ZERO);
            flag = updateProductkcCount > NumberConstant.ZERO;
            String comment = String.format("订单拿货  锁 单流水记录异常kcid:%s,count:%s", kcid, count);
            //记录异常并且通知
            RRExceptionHandler.logError("订单拿货 锁 单流水记录异常",comment,e,smsService::sendOaMsgTo9JiMan);
        }
        return flag;

    }

    @Override
    public List<StockStatusCountBo> listStockStatusCount(Collection<Integer> basketIds, Integer basketType) {
        return CommonUtils.bigDataInQuery(200,basketIds,ids -> baseMapper.listStockStatusCount(basketIds, basketType));
    }

}
