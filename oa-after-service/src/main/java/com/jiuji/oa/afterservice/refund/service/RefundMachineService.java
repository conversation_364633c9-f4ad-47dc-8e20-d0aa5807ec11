package com.jiuji.oa.afterservice.refund.service;

import com.jiuji.cloud.after.vo.refund.RecoverNoReasonReduceVo;
import com.jiuji.oa.afterservice.bigpro.po.Shouhou;
import com.jiuji.oa.afterservice.bigpro.po.refund.ShouhouTuiHuanPo;
import com.jiuji.oa.afterservice.common.bo.OaUserBO;
import com.jiuji.oa.afterservice.common.enums.TuihuanKindEnum;
import com.jiuji.oa.afterservice.other.po.ShouhouTuihuan;
import com.jiuji.oa.afterservice.other.po.TuihuanConfig;
import com.jiuji.oa.afterservice.refund.bo.BuyPriceSubInfoBo;
import com.jiuji.oa.afterservice.refund.vo.req.OperatorByBasketIdReq;
import com.jiuji.oa.afterservice.refund.vo.req.machine.HuanInfoReq;
import com.jiuji.oa.afterservice.refund.vo.req.machine.RefundMachineFormVo;
import com.jiuji.oa.afterservice.refund.vo.req.machine.ZheJiaMachineFormVo;
import com.jiuji.oa.afterservice.refund.vo.res.OperatorByBasketIdRes;
import com.jiuji.oa.afterservice.refund.vo.res.RefundMoneyDetailVo;
import com.jiuji.oa.afterservice.refund.vo.res.TuiGiftBasketListVo;
import com.jiuji.oa.afterservice.refund.vo.res.TuiHuanCheckVo;
import com.jiuji.oa.afterservice.refund.vo.res.machine.*;
import com.jiuji.oa.orginfo.areainfo.vo.AreaInfo;
import com.jiuji.tc.common.vo.R;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;
import reactor.util.function.Tuple2;

import javax.validation.Valid;
import java.util.List;
import java.util.Set;

/**
 * @see https://test01.oa.saas.ch999.cn/shouhou/huan/10156?is_iframe=1
 * oa999UI\Controllers\ShouhouController.cs:Huan 退换机管理详情展示
 * 退换机管理(大件售后)
 * <AUTHOR>
 * @since 2022/7/19 16:00
 */
public interface RefundMachineService {

    String ZHENG_CHANG_TUI_HUAN = "正常退换";
    // 良品无理由对换次数
    int LIANG_PIN_NO_REASON_ALL_COUNT = 3;

OperatorByBasketIdRes getOperatorByBasketId(OperatorByBasketIdReq req);

    R<RefundMachineDetailVo> detail(Integer shouhouId, Integer basketId);

    R<HuanInfoVo> huanInfo(HuanInfoReq huanInfo);

    /**
     * 获取退货配置信息
     *
     * @param tradeType
     * @param tradeDay
     * @param xtenant
     * @param ppriceid
     * @return
     */
    List<TuihuanConfig> getTuihuanConfigs(Integer tradeType, Integer tradeDay, Integer xtenant, Integer ppriceid);

    /**
     * 折价计算接口
     * @param zheJiaFormVo
     * @return
     */
    R<ZheJiaMachineVo> zheJia(ZheJiaMachineFormVo zheJiaFormVo);

    /**
     * /获取购买价格 oa999DAL\ShouhouTuihuan.cs::GetTuihuanBuyPrice
     * @param shouhouId 订单号
     *
     * @param tuihuanKindEnum
     * @return BuyPriceSubInfoBo
     */
    BuyPriceSubInfoBo getBuyPriceAndSubInfo(Integer shouhouId, TuihuanKindEnum tuihuanKindEnum);

    @Transactional(rollbackFor = Exception.class)
    void saveLpAccessories(RefundMachineFormVo tuihuanForm);

    /**
     * 组合退换机提交
     * @param machineFormVo
     * @return
     */
    R<Integer> save(RefundMachineFormVo machineFormVo);


    /**
     * 良品自动退款办理
     * @param machineFormVo
     */
    void autoMobileHttpTuikuan(RefundMachineFormVo machineFormVo);

    /**
     * 重构自：oa999DAL\ShouhouTuihuan.cs#BeginTuihuan
     *
     * @param currUser
     * @param tuihuan
     * @param basketId
     * @return
     */
    Tuple2<Boolean, String> beginTuihuan(OaUserBO currUser, RefundMachineFormVo tuihuan, Integer basketId);

    /**
     * 提交售后退换（赠品退货） 扩展 带事务
     *
     * @param shouhouId 售后ID
     * @param inUser 操作人
     * @param tuihuanKind 退款类别 3、退款 4、换其它型号
     * @param areaId 门店
     * @param giftBasketList 赠品basketId
     * @return
     */
    String submitTuiEx(Integer shouhouId, String inUser, Integer tuihuanKind,
                       Integer areaId, List<TuiGiftBasket> giftBasketList);

    /**
     * 新增退换记录
     *
     * @param tuihuan 退换数据
     * @return
     */
    Integer insertTuihuan(ShouhouTuihuan tuihuan);

    /**
     * 撤销退款申请
     * @param tuihuanId
     * @param mark
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    R<Boolean> cancelRefund(Integer tuihuanId, String mark);

    /**
     * 退换机管理提交审核
     * @param tuiHuanCheckVo
     * @return
     */
    R<Integer> submitCheck(TuiHuanCheckVo tuiHuanCheckVo);

    /**
     * 发送通知消息给用户
     * @param check3R
     * @param tuiHuanCheckVo
     */
    void sendNoticeToUser(R<Integer> check3R, TuiHuanCheckVo tuiHuanCheckVo);

    R<Boolean> updatePiaoPrice(UpdatePiaoPriceFormVo formVo);

    /**
     * 插入小件详情信息
     * @param spId
     * @param basketId
     * @param ppid
     * @param count
     */
    void insertSmallProBill(Integer spId, Integer basketId, Integer ppid, Integer count);

    /**
     * 售后换机头转入
     *
     * @param currUser 当前登录用户
     * @param basketId 退换basketId
     * @param shouhouId 售后单号
     */
    @Transactional(rollbackFor = Exception.class)
    void mkcDellogsh1(OaUserBO currUser, Long basketId, Integer shouhouId);

    /**
     * 获取当前售后门店信息
     * @param shouhou
     * @return
     */
    AreaInfo getShouhouAreaInfo(Shouhou shouhou);

    /**
     * /获取购买价格 oa999DAL\ShouhouTuihuan.cs::GetTuihuanBuyPrice
     * @param orderId 订单号
     * @param shouhou 售后订单信息
     *
     * @param tuihuanKindEnum
     * @return BuyPriceSubInfoBo
     */
    BuyPriceSubInfoBo getBuyPriceAndSubInfo(Integer orderId, Shouhou shouhou, TuihuanKindEnum tuihuanKindEnum);

    /**
     * 通过售后ID获取售后信息
     * @param shouhouId
     * @return
     */
    Shouhou getShouhouById(Integer shouhouId);

    /**
     * 通过售后ID获取售后退还信息
     * @param shouhouId
     * @return
     */
    ShouhouTuiHuanPo getShouhouTuihuanByShouhouId(Integer shouhouId);

    /**
     * 通过订单id获取售后退还信息
     * @param subId
     * @return
     */
    ShouhouTuiHuanPo getShouhouTuihuanBySubId(Integer subId);

    List<TuiGiftBasketListVo> getLpAccessories(Shouhou shouhou, ShouhouTuiHuanPo tuihuan);

     Set<String> getHeedInfo(RefundMoneyDetailVo refundMoneyDetailVo);

    R<RecoverNoReasonReduceVo> getRecoverNoReasonReduce(Integer userId);

    /**
     * 保存折价支付的配件信息
     */
    R<Integer> saveZheJiaPay(ZheJiaPayFormVo form);
}
