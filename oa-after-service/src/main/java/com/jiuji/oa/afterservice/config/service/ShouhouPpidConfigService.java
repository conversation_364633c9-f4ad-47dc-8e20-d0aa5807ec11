package com.jiuji.oa.afterservice.config.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jiuji.oa.afterservice.config.po.ShouhouPpidConfig;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.oa.afterservice.config.vo.req.ShouhouPpidConfigQueryReq;
import com.jiuji.oa.afterservice.config.vo.req.ShouhouPpidOutPutAddReq;
import com.jiuji.oa.afterservice.config.vo.res.ShouhouPpidBindVo;
import com.jiuji.oa.afterservice.config.vo.res.ShouhouPpidOutPutConfig;
import com.jiuji.oa.afterservice.config.vo.res.XtenantInfo;
import com.jiuji.tc.common.vo.R;

import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-16
 */
public interface ShouhouPpidConfigService extends IService<ShouhouPpidConfig> {

    /**
     * 维修配件出库绑定列表
     *
     * @param req
     * @return
     */
    R<Page<ShouhouPpidOutPutConfig>> getShouhouPpidConfigPage(ShouhouPpidConfigQueryReq req);

    /**
     * 保存修改接口
     * @param req
     * @return
     */
    R<Boolean> saveOrUpdateBindInfo(ShouhouPpidOutPutAddReq req);

    /**
     * 获取绑定的配件信息
     * @param ppid
     * @param xtenant
     * @param areaId
     * @return
     */
    R<List<ShouhouPpidBindVo>> getBindPpidInfo(Integer ppid, Integer xtenant, Integer areaId);

    /**
     * 获取租户信息
     * @return
     */
    List<XtenantInfo> getXtenantInfo();
}
