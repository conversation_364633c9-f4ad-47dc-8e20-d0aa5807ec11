package com.jiuji.oa.afterservice.bigpro.service.impl;


import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.cloud.after.enums.ShouHouLogTypeEnum;
import com.jiuji.oa.afterservice.bigpro.bo.ShouhouLogNoticeBo;
import com.jiuji.oa.afterservice.bigpro.dao.ProductSnMapper;
import com.jiuji.oa.afterservice.bigpro.entity.ProductSn;
import com.jiuji.oa.afterservice.bigpro.enums.BindStateEnum;
import com.jiuji.oa.afterservice.bigpro.enums.OperationTypeEnum;
import com.jiuji.oa.afterservice.bigpro.enums.SnTypeEnum;
import com.jiuji.oa.afterservice.bigpro.po.Wxkcoutput;
import com.jiuji.oa.afterservice.bigpro.service.ProductSnService;
import com.jiuji.oa.afterservice.bigpro.service.ShouhouLogsService;
import com.jiuji.oa.afterservice.bigpro.service.SmsService;
import com.jiuji.oa.afterservice.bigpro.service.WxkcoutputService;
import com.jiuji.oa.afterservice.bigpro.vo.req.ProductSnQueryReq;
import com.jiuji.oa.afterservice.bigpro.vo.req.ProductSnUpdateBindReq;
import com.jiuji.oa.afterservice.common.bo.OaUserBO;
import com.jiuji.oa.afterservice.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.afterservice.common.exception.CustomizeException;
import com.jiuji.oa.afterservice.common.exception.RRExceptionHandler;
import com.jiuji.oa.orginfo.sysconfig.client.SysConfigClient;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.utils.constants.SysConfigConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;


@Slf4j
@Service
public class ProductSnServiceImpl extends ServiceImpl<ProductSnMapper, ProductSn> implements ProductSnService {

    @Resource
    private SysConfigClient sysConfigClient;
    @Resource
    private AbstractCurrentRequestComponent abstractCurrentRequestComponent;
    @Resource
    private ShouhouLogsService shouhouLogsService;
    @Resource
    private WxkcoutputService wxkcoutputService;
    @Resource
    private SmsService smsService;

    /**
     * 根据篮式ID和商品ID查询ProductSn列表
     *
     * @param queryReq 包含basketId和ppid的查询请求对象
     * @return ProductSn列表
     */
    @Override
    public ProductSn getProductSnByBasketIdAndPpid(ProductSnQueryReq queryReq) {
        ProductSn productSn = lambdaQuery()
                .eq(ProductSn::getBasketId, queryReq.getBasketId())
                .eq(ProductSn::getSnType, SnTypeEnum.WEI_XIU.getCode())
                .eq(ProductSn::getPpid, queryReq.getPpid())
                .and(wrapper -> wrapper.eq(ProductSn::getIsDel, false).or().isNull(ProductSn::getIsDel))
                .eq(ProductSn::getBindState, BindStateEnum.BOUND.getCode())
                .list().stream().findFirst().orElse(null);
        return productSn;
    }

    /**
     * 根据商品ID和SN设置篮式ID并解绑
     *
     * @param updateReq 更新请求对象
     * @return 是否成功
     */
    @Override
    public R<Boolean> updateBasketIdAndUnbind(ProductSnUpdateBindReq updateReq) {
        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();
        updateReq.setUserName(oaUserBO.getUserName());
        ProductSnQueryReq productSnQueryReq = new ProductSnQueryReq();
        productSnQueryReq.setPpid(updateReq.getPpid())
                .setBasketId(updateReq.getBasketId());
        ProductSn productSnOld = Optional.ofNullable(getProductSnByBasketIdAndPpid(productSnQueryReq)).orElse(new ProductSn());
        //统一调用C#的接口进行想管的修改处理 业务归口
        String host = Optional.ofNullable(sysConfigClient.getValueByCode(SysConfigConstant.MOA_URL)).map(R::getData)
                .filter(StringUtils::isNotEmpty).orElseThrow(() -> new CustomizeException("获取域名出错"));
        String evidenceUrl = host + "/ajaxApi/productSnBind";
        HttpResponse evidenceResult = HttpUtil.createPost(evidenceUrl)
                .header("authorization", oaUserBO.getToken())
                .body(JSONUtil.toJsonStr(updateReq))
                .execute();
        log.warn("绑定SN传入参数：{}，返回结果：{}，传入userBO:{}",updateReq,evidenceResult.body(),JSONUtil.toJsonStr(oaUserBO));
        if(evidenceResult.isOk()){
            try {
                Optional.ofNullable(wxkcoutputService.getById(updateReq.getBasketId())).ifPresent(wxkcoutput -> {
                    String name = wxkcoutput.getName();
                    StringBuilder builder = new StringBuilder();
                    String oldSn = productSnOld.getSn();
                    String comment =String.format("【%s ，PPID:%s】", name,wxkcoutput.getPpriceid());
                    Integer operationType = updateReq.getOperationType();
                    if(OperationTypeEnum.ADD.getCode().equals(operationType)){
                        builder.append(comment).append(OperationTypeEnum.ADD.getMessage()).append("sn:").append(updateReq.getSn());
                    } else if (OperationTypeEnum.CHANGE_BINDING.getCode().equals(operationType)){
                        builder.append(comment).append(String.format( "sn 由%s 修改为%s",oldSn,updateReq.getSn()));
                    } else if(OperationTypeEnum.UNBIND.getCode().equals(operationType)){
                        builder.append(comment).append(OperationTypeEnum.UNBIND.getMessage()).append("sn:").append(oldSn);
                    }
                    if(StrUtil.isNotEmpty(builder.toString())){
                        ShouhouLogNoticeBo shouhouLogNoticeBo = new ShouhouLogNoticeBo();
                        shouhouLogNoticeBo.setNeedNotice(Boolean.FALSE);
                        //进行维修单日志记录
                        shouhouLogsService.addShouhouLog(oaUserBO.getUserName(), wxkcoutput.getWxid(), ShouHouLogTypeEnum.CLXX.getCode(),
                                comment, shouhouLogNoticeBo, false, 0);
                    }
                });
            }catch (Exception e){
                RRExceptionHandler.logError("维修单记录sn修改记录日志异常",updateReq,e,smsService::sendOaMsgTo9JiMan);
            }

            return JSONUtil.toBean(JSONUtil.toJsonStr(evidenceResult.body()), R.class);
        } else {
            log.warn("绑定SN调用异常传入参数：{}",evidenceUrl);
            return R.error("绑定SN接口调用异常");
        }
    }
}
