package com.jiuji.oa.afterservice.bigpro.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.ch999.common.util.vo.Result;
import com.jiuji.cloud.after.enums.BaoXiuTypeEnum;
import com.jiuji.cloud.after.enums.ServiceEnum;
import com.jiuji.oa.afterservice.bigpro.bo.*;
import com.jiuji.oa.afterservice.bigpro.bo.productkc.OperateProductKcRes;
import com.jiuji.oa.afterservice.bigpro.bo.wxpj.*;
import com.jiuji.oa.afterservice.bigpro.dao.RepairMapper;
import com.jiuji.oa.afterservice.bigpro.dao.ShouHouPjMapper;
import com.jiuji.oa.afterservice.bigpro.dao.ShouhouMapper;
import com.jiuji.oa.afterservice.bigpro.enums.*;
import com.jiuji.oa.afterservice.bigpro.po.*;
import com.jiuji.oa.afterservice.bigpro.po.Wxkcoutput.TuiStatusEnum;
import com.jiuji.oa.afterservice.bigpro.service.*;
import com.jiuji.oa.afterservice.bigpro.vo.ShouhouPpidBindOutPut;
import com.jiuji.oa.afterservice.bigpro.vo.req.ApplyDelReq;
import com.jiuji.oa.afterservice.bigpro.vo.req.ProductSnQueryReq;
import com.jiuji.oa.afterservice.bigpro.vo.req.ProductSnUpdateBindReq;
import com.jiuji.oa.afterservice.bigpro.vo.res.DouYinCouponLogRes;
import com.jiuji.oa.afterservice.bigpro.vo.res.ShouhouCostPriceRes;
import com.jiuji.oa.afterservice.bigpro.vo.res.ShouhouWxpjRes;
import com.jiuji.oa.afterservice.cloud.service.WebCloud;
import com.jiuji.oa.afterservice.cloud.vo.ProRelateInfoService;
import com.jiuji.oa.afterservice.cloud.vo.ProductServiceOpeningBO;
import com.jiuji.oa.afterservice.cloud.vo.ProductServiceOpeningVO;
import com.jiuji.oa.afterservice.cloud.vo.web.RepairTuiFeeCheckVO;
import com.jiuji.oa.afterservice.common.bo.OaUserBO;
import com.jiuji.oa.afterservice.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.afterservice.common.config.rabbimq.RabbitMqConfig;
import com.jiuji.oa.afterservice.common.constant.RedisKeys;
import com.jiuji.oa.afterservice.common.constant.RequestAttrKeys;
import com.jiuji.oa.afterservice.common.constant.ShouhouConstants;
import com.jiuji.oa.afterservice.common.enums.*;
import com.jiuji.oa.afterservice.common.exception.CustomizeException;
import com.jiuji.oa.afterservice.common.exception.RRException;
import com.jiuji.oa.afterservice.common.exception.RRExceptionHandler;
import com.jiuji.oa.afterservice.common.source.InwcfUrlSource;
import com.jiuji.oa.afterservice.common.util.CommenUtil;
import com.jiuji.oa.afterservice.common.util.DateUtil;
import com.jiuji.oa.afterservice.common.util.SpringContextUtil;
import com.jiuji.oa.afterservice.other.bo.NewVoucherBo;
import com.jiuji.oa.afterservice.other.bo.PingzhengBO;
import com.jiuji.oa.afterservice.other.bo.PingzhengResultBO;
import com.jiuji.oa.afterservice.other.enums.AreaKindEnum;
import com.jiuji.oa.afterservice.other.service.VoucherService;
import com.jiuji.oa.afterservice.refund.service.way.ThirdOriginWayService;
import com.jiuji.oa.afterservice.refund.vo.res.way.ThirdOriginRefundVo;
import com.jiuji.oa.afterservice.stock.po.ProductKc;
import com.jiuji.oa.afterservice.stock.service.ProductKcService;
import com.jiuji.oa.afterservice.sub.po.Sub;
import com.jiuji.oa.afterservice.sub.service.SubService;
import com.jiuji.oa.afterservice.sys.bo.KemuFzhsItem;
import com.jiuji.oa.afterservice.sys.enums.EKemuEnum;
import com.jiuji.oa.afterservice.sys.service.AuthConfigService;
import com.jiuji.oa.afterservice.sys.service.KeMuService;
import com.jiuji.oa.finance.CaiwuApplyClient;
import com.jiuji.oa.orginfo.areainfo.client.AreaInfoClient;
import com.jiuji.oa.orginfo.areainfo.vo.AreaInfo;
import com.jiuji.oa.orginfo.sysconfig.client.SysConfigClient;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.common.vo.ResultCode;
import com.jiuji.tc.utils.common.CommonUtils;
import com.jiuji.tc.utils.common.DecideUtil;
import com.jiuji.tc.utils.constants.NumberConstant;
import com.jiuji.tc.utils.constants.SignConstant;
import com.jiuji.tc.utils.constants.SysConfigConstant;
import com.jiuji.tc.utils.enums.EnumUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.MessageFormat;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * @author: Li quan
 * @date: 2020/5/14
 */
@Service
@Slf4j
public class ShouHouPjServiceImpl implements ShouHouPjService {

    @Autowired
    private WxkcoutputService wxkcoutputService;
    @Resource
    private ShouHouPjMapper shouHouPjMapper;
    @Lazy
    @Autowired
    private ShouhouService shouhouService;
    @Resource
    private AbstractCurrentRequestComponent abstractCurrentRequestComponent;
    @Autowired
    private ShouhouHexiaoService shouhouHexiaoService;
    @Autowired
    private ProductKcService productKcService;
    @Autowired
    private ProductinfoService productinfoService;
    @Autowired
    private CardLogsService cardLogsService;
    @Autowired
    private ShouhouHuishouService shouhouHuishouService;
    @Resource
    private AreaInfoClient areaInfoClient;
    @Autowired
    private WeixinUserService weixinUserService;
    @Autowired
    private SmsService smsService;
    @Autowired
    private RepairMapper repairMapper;
    @Autowired
    private ShouhouYuyueService shouhouYuyueService;
    @Autowired
    private YuyueLogsService yuyueLogsService;
    @Autowired
    private NumberCardService numberCardService;
    @Autowired
    private AttachmentsService attachmentsService;
    @Autowired
    private ShouhouMsgService shouhouMsgService;
    @Resource
    private RedisTemplate redisTemplate;
    @Autowired
    private ShouhouExService shouhouExService;
    @Autowired
    private SubService subService;
    @Autowired
    private ShouhouApplyService shouhouApplyService;
    @Autowired
    private ShouhouServiceConfigService shouhouServiceConfigService;
    @Autowired
    private CommonService commonService;
    @Autowired
    private ShouhouConstants shouhouConstants;
    @Autowired
    private AuthConfigService authConfigService;
    @Autowired
    private KeMuService keMuService;
    @Autowired
    private VoucherService voucherService;
    @Autowired
    private WxoutKcCancelService kcCancelService;
    @Resource
    private SysConfigClient sysConfigClient;
    @Resource
    private ProductSnService snService;
    @Resource
    private InwcfUrlSource inwcfUrlSource;
    @Autowired
    private ShouhouWaisongHexiaoService waisongHexiaoService;
    @Resource
    @Lazy
    private ShouHouPjService shouHouPjService;
    @Resource
    private CaiwuApplyClient caiwuApplyClient;
    @Resource
    private WebCloud webCloud;
    @Resource
    private RepairAccessoriesService repairService;

    private ThreadLocal<List<Runnable>> shouhouLogThreadLocal = ThreadLocal.withInitial(LinkedList::new);

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<String> cancelPj(Integer shouhouId, List<String> rank) {
        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();

        //解除锁定、撤销配件
        //List<Wxkcoutput> wxkcoutputList = wxkcoutputService.list(new QueryWrapper<Wxkcoutput>().lambda().eq(Wxkcoutput::getWxid, shouhouId));
        List<Wxkcoutput> wxkcoutputList =CommenUtil.autoQueryHist(()->wxkcoutputService.list(new QueryWrapper<Wxkcoutput>().lambda().eq(Wxkcoutput::getWxid, shouhouId)),MTableInfoEnum.WXKCOUTPUT_WXID,shouhouId);
        if (CollectionUtils.isNotEmpty(wxkcoutputList)) {
            for (Wxkcoutput w : wxkcoutputList) {
                tui(w.getId(), 0, null);
            }
        }
        QujiBo qujiInfo = getQuji(shouhouId);
        if (qujiInfo == null) {
            return R.error("单号信息不存在");
        }

        Boolean flag = false;
        String resMsg = "";
        if (qujiInfo.getDyjid() != null) {
            resMsg = "请先收回代用机，再进行取机操作！";
            flag = true;
        } else if (qujiInfo.getFeiyong().compareTo(BigDecimal.ZERO) > 0 && qujiInfo.getShouyinglock() == 1) {
            resMsg = "请先收银，才可进行取机操作，若无收费请改费用为！";
            flag = true;
        } else if (qujiInfo.getStats() == 0 || qujiInfo.getStats() == 4) {
            resMsg = "必须是在已修好或修不好状态下，才可取机！";
            flag = true;
        } else if (qujiInfo.getBaoxiu() == 3) {
            resMsg = "待检测状态不可取机！";
            flag = true;
        } else if (StringUtils.isNotEmpty(qujiInfo.getPandianinuser()) && qujiInfo.getPandianinuser().equals("已核对")) {
            resMsg = "盘点人为“已核对”不可取机！";
            flag = true;
        } else if (qujiInfo.getUserid() != null && qujiInfo.getUserid().equals(76783L) && !rank.contains("0c5") && !qujiInfo.getIssoft()) {
            resMsg = "您没有该维修单的取机权限！权限：0c5";
            flag = true;
        }

        if (!flag) {
            Boolean isOk = checkQuji(shouhouId);
            if (isOk) {
                resMsg = "退换机流程还未完成，不可取机操作！";
                flag = true;
            }
            isOk = checkQujiToArea(shouhouId);
            if (isOk) {
                resMsg = "转地区操作还未完成，不可取机操作！";
                flag = true;
            }
        }
        if (!flag && qujiInfo.getWxkind() != 6) {
            Boolean isSoft = qujiInfo.getIssoft();
            Boolean isOk = hasGcLog(shouhouId, isSoft ? 2 : 1);
            if (!isOk) {
                resMsg = "工程师未添加备注，不可取机！";
                if (isSoft) {
                    resMsg = "未添加软件安装处理备注，不可取机！";
                }
                flag = true;
            }
        }
        if (flag) {
            return R.error(resMsg);
        } else {
            //取机
            R<String> dqR = dealQuji(shouhouId, oaUserBO.getUserName());
            if(!dqR.isSuccess()){
                //失败进行事务回滚
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            }
            return dqR;
        }
    }


    /**
     *
     * @param id
     * @param productId
     * @param tuiType 撤销配件方式: 1.仅退款 2 退货退款  other 退货
     * @see TuiStatusEnum
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<Integer> tui(Integer id, Integer productId, Integer tuiType) {
        List<WxKcBo> wxKcBoList = shouHouPjService.listWxkcOutput(Collections.singletonList(id), null);
        if(CollUtil.isEmpty(wxKcBoList)){
            return R.error("配件信息不存在");
        }
        wxKcBoList.stream().forEach(wxKcBo -> wxKcBo.setTuiStatus(tuiType));
        Map<Integer, R<Integer>> result = shouHouPjService.tui(wxKcBoList, false, false);
        Optional<R<Integer>> errOpt = result.entrySet().stream().map(Map.Entry::getValue)
                .filter(r -> !r.isSuccess()).findFirst();
        if(errOpt.isPresent()){
            return errOpt.get();
        }
        return result.get(id);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<Integer, R<Integer>> tui(List<WxKcBo> wxKcBoList, boolean isOnlyCheck, boolean isGroupTui) {
        shouhouLogThreadLocal.remove();
        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();
        if (CollUtil.isEmpty(wxKcBoList)) {
            return Collections.singletonMap(0,R.error("配件信息不存在"));
        }

        // 只能批量退同一维修单的配件
        List<Integer> wxIdList = wxKcBoList.stream().map(WxKcBo::getWxid).distinct().collect(Collectors.toList());
        if(wxIdList.size()>1){
            return Collections.singletonMap(0,R.error("只能批量退同一维修单的配件"));
        }
        Integer wxId = wxIdList.stream().findAny().get();
        Map<Integer, WxKcBo> wxKcBoMap = wxKcBoList.stream().collect(Collectors.toMap(WxKcBo::getId, Function.identity(), (v1,v2) -> v1));
        // 获取维修单下的所有配件信息
//        List<Wxkcoutput> allEffectWxkcoutput = wxkcoutputService.lambdaQuery().eq(Wxkcoutput::getWxid, wxId)
//                .ne(Wxkcoutput::getStats, Wxkcoutput.StatusEnum.CANCELED.getCode()).list();
        List<Wxkcoutput> allEffectWxkcoutput =CommenUtil.autoQueryHist(()->wxkcoutputService.lambdaQuery().eq(Wxkcoutput::getWxid, wxId)
                .ne(Wxkcoutput::getStats, Wxkcoutput.StatusEnum.CANCELED.getCode()).list(),MTableInfoEnum.WXKCOUTPUT_WXID,wxId);
        int wxkcSize = wxKcBoList.size();
        Map<Integer, R<Integer>> resultMap = wxKcBoList.stream().collect(Collectors.toMap(WxKcBo::getId,wxKcBo -> new R<>(wxKcBo.getId()),(v1,v2) -> v1));
        Predicate<WxKcBo> noErrorFilterFun = wxKcBo -> resultMap.get(wxKcBo.getId()) == null || resultMap.get(wxKcBo.getId()).isSuccess();
        ShouhouBo sh = shouhouService.getOne(wxId);

        List<RepairTuiFeeCheckVO.CheckItem> checkItem =  new ArrayList<>();
        if(XtenantEnum.isJiujiXtenant()){
            Integer userId = Convert.toInt(Optional.ofNullable(sh).orElse(new ShouhouBo()).getUserid());
            Result<RepairTuiFeeCheckVO> repairTuiFeeCheckVOResult = webCloud.repairTuiFeeCheck(userId, wxId);
            log.warn("维修配件校验主站优惠码传入参数：{}，返回结果：{}", wxId+","+userId, JSON.toJSONString(repairTuiFeeCheckVOResult));
            if(repairTuiFeeCheckVOResult.isSuccess()){
                RepairTuiFeeCheckVO data = repairTuiFeeCheckVOResult.getData();
                checkItem = data.getCheckItem();
            }
        }

        List<RepairTuiFeeCheckVO.CheckItem> finalCheckItem = checkItem;
        wxKcBoList.stream().filter(noErrorFilterFun)
                .forEach(wxKcBo -> {
                    Integer bStats = wxKcBo.getStats() == null ? 0 : wxKcBo.getStats();
                    if (bStats.equals(2) || bStats.equals(3)) {
                        resultMap.put(wxKcBo.getId(), R.error("已结算不可撤销"));
                    }
                    if(XtenantEnum.isJiujiXtenant() && CollectionUtils.isNotEmpty(finalCheckItem)){
                        finalCheckItem.stream()
                                .filter(item -> wxKcBo.getId().equals(item.getRepairId()) && Boolean.FALSE.equals(item.getCanTuiFee()))
                                .findFirst()
                                .ifPresent(wx-> resultMap.put(wxKcBo.getId(), R.error("优惠码已使用，不可撤销")));
                    }
                });
        Supplier<Boolean> isReturnFun = () -> {
            long errorCount = resultMap.entrySet().stream().filter(entry -> !entry.getValue().isSuccess()).count();
            return !isOnlyCheck && errorCount>0 || errorCount>= wxkcSize;
        };
        if (isReturnFun.get()){
            //全部异常就可以返回了
            return resultMap;
        }

        //如果存在生效的报销申请,不允许撤销
        List<WaisongHexiaoPo> waisongHexiaoList = waisongHexiaoService.lambdaQuery()
                .eq(WaisongHexiaoPo::getWxId, wxId)
                .in(WaisongHexiaoPo::getWxkId, wxKcBoList.stream().filter(noErrorFilterFun).map(WxKcBo::getId).collect(Collectors.toList()))
                .eq(WaisongHexiaoPo::getDel, false)
                .ne(WaisongHexiaoPo::getStatus, WaisongHexiaoPo.StatusEnum.DELETED.getCode())
                // 只查询 wxkId 和 id
                .select(WaisongHexiaoPo::getWxkId, WaisongHexiaoPo::getId)
                .list();
        for (WaisongHexiaoPo waisongHexiaoPo : waisongHexiaoList) {
            resultMap.put(waisongHexiaoPo.getWxkId() , R.error(StrUtil.format("存在进行中的报销单[{}]，不能进行删除", waisongHexiaoPo.getId())));
        }

        if (isReturnFun.get()){
            //全部异常就可以返回了
            return resultMap;
        }


        Predicate<WxKcBo> needCheckCaiWuFilterFun = wxKcBo -> ObjectUtil.defaultIfNull(wxKcBo.getPpriceid(), 0) == 0 && wxKcBo.getPrice().compareTo(BigDecimal.ZERO) > 0;
        //限制手动添加的维修费
        if (XtenantEnum.isJiujiXtenant() && wxKcBoList.stream().filter(noErrorFilterFun).anyMatch(needCheckCaiWuFilterFun)) {
            //如果存在关联的财务支出单 不允许撤销 不能影响正常流程
            R<Integer> relevancyCaiwuByShouhouId = caiwuApplyClient.getIsCaiwuByShouhouId(wxId, cn.hutool.core.date.DateUtil.format(sh.getModidate(), DatePattern.NORM_DATETIME_PATTERN));
            if (relevancyCaiwuByShouhouId.isSuccess() && CommenUtil.isNotNullZero(relevancyCaiwuByShouhouId.getData())) {
                wxKcBoList.stream().filter(noErrorFilterFun).filter(needCheckCaiWuFilterFun)
                        .forEach(wxKcBo -> resultMap.put(wxKcBo.getId(), R.error(StrUtil.format("当前维修单已关联财务支出单[{}]，不能撤销维修配件", relevancyCaiwuByShouhouId.getData()))));
            }
        }
        if (isReturnFun.get()){
            //全部异常就可以返回了
            return resultMap;
        }

        List<WxKcBo> pWxkcList = wxKcBoList.stream().filter(noErrorFilterFun)
                .filter(wxKcBo -> ObjectUtil.defaultIfNull(wxKcBo.getPpriceid(), 0) > 0).collect(Collectors.toList());
        List<Integer> ppriceIds = pWxkcList.stream().map(WxKcBo::getPpriceid).distinct().collect(Collectors.toList());
        //相关服务的集合
        List<ProductServiceOpeningVO> serviceOpeningConfigList = new LinkedList<>();
        //ppid和绑定服务map
        Map<Integer, List<ProRelateInfoService>> ppidBindServiceMap = new HashMap<>();
        if(XtenantEnum.isJiujiXtenant()){
            Long productId = sh.getProductId();
            addAllServices(productId, ppriceIds, serviceOpeningConfigList, ppidBindServiceMap);
        }

        Map<Integer, List<Integer>> bindServicesPpriceidListMap = CollUtil.newHashMap(resultMap.size());

        if(!ppriceIds.isEmpty()){
            if (XtenantEnum.isJiujiXtenant()) {
                pWxkcList.stream().filter(pWxkc -> CollUtil.isNotEmpty(ppidBindServiceMap.get(pWxkc.getPpriceid())))
                        .forEach(pWxkc -> bindServicesPpriceidListMap.put(pWxkc.getId(), ppidBindServiceMap.get(pWxkc.getPpriceid())
                                .stream().map(ProRelateInfoService::getPpriceid).distinct().collect(Collectors.toList())));
                listProductServiceOpening(bindServicesPpriceidListMap.entrySet().stream().map(entry -> entry.getValue())
                        .flatMap(Collection::stream).collect(Collectors.toList())).ifPresent(serviceOpeningConfigList::addAll);
            } else {
                Map<Integer, List<ShouhouServiceConfig>> shouhouServiceConfigsMap = shouhouServiceConfigService.getListShouhouServiceConfigCache()
                        .stream().collect(Collectors.groupingBy(ShouhouServiceConfig::getPpid));
//            if (CollectionUtils.isEmpty(serviceConfigList)) {
//                return R.error("获取售后服务出错");
//            }
                pWxkcList.stream().filter(pWxkc -> CollUtil.isNotEmpty(shouhouServiceConfigsMap.get(pWxkc.getPpriceid())))
                        .forEach(pWxkc -> bindServicesPpriceidListMap.put(pWxkc.getId(), shouhouServiceConfigsMap.get(pWxkc.getPpriceid())
                                .stream().map(ShouhouServiceConfig::getServicePpid).distinct().collect(Collectors.toList())));
            }


        }
        Map<Integer, WxKcBo> serviceWxkcMap = CollUtil.newHashMap(resultMap.size());
        if(CollUtil.isNotEmpty(serviceOpeningConfigList)){
            //九机服务商品
            pWxkcList.stream().filter(pWxkc -> serviceOpeningConfigList.stream()
                    .map(ProductServiceOpeningBO::getPpid).anyMatch(sppid -> Objects.equals(sppid, pWxkc.getPpriceid())))
                    .forEach(pWxkc -> serviceWxkcMap.put(pWxkc.getId(), pWxkc));
        }else if (!XtenantEnum.isJiujiXtenant() && !ppriceIds.isEmpty()) {
            //输出服务商品
            shouhouConstants.getShouhouServicesPpriceids().stream()
                    .filter(ppriceId -> ppriceIds.contains(ppriceId))
                    .forEach(ppriceId -> pWxkcList.stream().filter(pWxkc -> ObjectUtil.equals(pWxkc.getPpriceid(), ppriceId))
                            .forEach(pWxkc -> serviceWxkcMap.put(pWxkc.getId(), pWxkc)));

        }

        if(log.isDebugEnabled()){
            log.debug("获取到服务配置信息: {}", JSON.toJSONString(serviceOpeningConfigList), SerializerFeature.PrettyFormat);
            log.debug("服务ppid信息: {}",JSON.toJSONString(serviceWxkcMap, SerializerFeature.PrettyFormat));
            log.debug("绑定服务ppid信息: {}",JSON.toJSONString(bindServicesPpriceidListMap, SerializerFeature.PrettyFormat));
        }

        wxKcBoList.stream().filter(noErrorFilterFun)
                .forEach(wxKcBo -> {
                    if (!bindServicesPpriceidListMap.getOrDefault(wxKcBo.getId(), Collections.emptyList()).isEmpty()
                            && (Optional.ofNullable(SpringUtil.getBean(StringRedisTemplate.class).opsForValue()
                            .get(StrUtil.format(RedisKeys.NOT_CANCEL_BIND_SERVICE_WXKC_ID_KEY, wxKcBo.getId()))).map(Convert::toBool)
                            .filter(Boolean::booleanValue).isPresent() || (XtenantEnum.isJiujiXtenant() && Boolean.TRUE.equals(sh.getIsquji())))) {
                        List<Integer> bindServicePpids = bindServicesPpriceidListMap.get(wxKcBo.getId());
                        shouhouLogThreadLocal.get().add(() -> shouhouService.saveShouhouLog(wxKcBo.getWxid(),
                                StrUtil.format("只退配件【{}】不退绑定的售后服务 【{}】", wxKcBo.getName(), bindServicePpids),
                                oaUserBO.getUserName(), null, false));
                        //处理掉绑定对应服务
                        bindServicesPpriceidListMap.remove(wxKcBo.getId());
                    }
                });

        //解绑售后服务标识
        List<Integer> serviceTypes = DecideUtil.iif(XtenantEnum.isJiujiXtenant() && serviceOpeningConfigList.size() > 0,
                () -> serviceOpeningConfigList.stream().flatMap(soc -> Stream.concat(Stream.of(soc), soc.getChildren().stream()))
                        .map(ProductServiceOpeningVO::getType).distinct().collect(Collectors.toList()),
                () -> Arrays.asList(18, 19, 22));
        List<ShServiceDataInfoBo> servicesDt = shouHouPjMapper.getServiceDataList(wxId,serviceTypes);

        List<ServiceRecordBo> sDtList = this.listServiceRecordInfo(wxId, null, serviceTypes);
        //解绑的服务记录集合
        Set<ServiceRecordBo> unbindSdtList = CollUtil.newHashSet();
        for (Map.Entry<Integer, List<Integer>> entry : bindServicesPpriceidListMap.entrySet()) {
            if(!Boolean.TRUE.equals(sh.getIsquji())){
                break;
            }
            Wxkcoutput bindServiceWxkc = allEffectWxkcoutput.stream().filter(wxkc -> entry.getValue().contains(wxkc.getPpriceid()))
                    .findFirst().orElse(null);
            if(bindServiceWxkc == null){
                continue;
            }

            WxKcBo wxKcBo = wxKcBoMap.get(entry.getKey());
            if(wxkcSize >1 && wxKcBoMap.get(bindServiceWxkc.getId()) == null){
                // 没有选中绑定的服务
                resultMap.put(entry.getKey(), R.error(StrUtil.format("撤销配件[{}]需要同时撤销服务[{}]！", wxKcBo.getName(), bindServiceWxkc.getName())));
                continue;
            }

            shouhouLogThreadLocal.get().add(()->shouhouService.saveShouhouLog(wxId,
                    StrUtil.format("撤销配件【{}】绑定的售后服务 【{}】，PPID:{}",wxKcBo.getName(),bindServiceWxkc.getName(),bindServiceWxkc.getPpriceid()),
                    oaUserBO.getUserName(), null, true));
            ServiceRecordBo sDt = sDtList.stream().filter(sd -> ObjectUtil.equal(sd.getBasketId(), bindServiceWxkc.getId())).findFirst().orElse(null);
            if(sDt == null){
                resultMap.put(bindServiceWxkc.getId(), R.error(StrUtil.format("绑定售后服务[{}]获取不到对应的服务记录！", bindServiceWxkc.getName())));
                continue;
            }
            String msgPrev = "此配件绑定售后服务";
            String serviceName = bindServiceWxkc.getName();
            Integer ppid = bindServiceWxkc.getPpriceid();
            //九机服务折价金额
            BigDecimal reducePrice = BigDecimal.ZERO;
            if(sDt.getServicesTypeBindId() == null){
                reducePrice = servierRecordPrice(sDt, ppid, serviceOpeningConfigList.stream()
                        .filter(soc -> Objects.equals(soc.getPpid(), ppid)).map(ProductServiceOpeningBO::getDuration)
                        .findFirst().orElse(0));
            }
            unbindSdtList.add(sDt);
            sDtList.stream().filter(sdtt -> sDt.getId().equals(sdtt.getId()) || ObjectUtil.equals(sDt.getId(), sdtt.getServicesTypeBindId()))
                    .forEach(sdtt -> checkTuiService(resultMap, sdtt, msgPrev, serviceName, wxKcBo, isGroupTui));
            if(wxkcSize == 1 && !isReturnFun.get()){
                List<WxKcBo> kcDtList = CommenUtil.autoQueryHist(()->shouHouPjMapper.getWxkcOutputByIds(Collections.singletonList(bindServiceWxkc.getId())));
                if(kcDtList.isEmpty()){
                    // 没有选中绑定的服务
                    resultMap.put(entry.getKey(), R.error(StrUtil.format("撤销配件[{}]未查询到绑定的服务[{}]！", wxKcBo.getName(), bindServiceWxkc.getName())));
                }else{
                    kcDtList.get(0).setReducePrice(reducePrice);
                    wxKcBoList.addAll(kcDtList);
                }
            }
        }

        for (Map.Entry<Integer, WxKcBo> entry : serviceWxkcMap.entrySet()) {
            if(!Boolean.TRUE.equals(sh.getIsquji())){
                break;
            }

            WxKcBo wxKcBo = entry.getValue();
            shouhouLogThreadLocal.get().add(()->shouhouService.saveShouhouLog(wxId,
                    StrUtil.format("解绑售后服务 【{}】，PPID:{}",wxKcBo.getName(), wxKcBo.getPpriceid()),
                    oaUserBO.getUserName(), null, true));
            ServiceRecordBo sDt = sDtList.stream().filter(sd -> ObjectUtil.equals(sd.getBasketId(), wxKcBo.getId())).findFirst().orElse(null);
            if(sDt == null){
                resultMap.put(wxKcBo.getId(), R.error(StrUtil.format("售后服务[{}]获取不到对应的服务记录！", wxKcBo.getName())));
                continue;
            }
            String msgPrev = "此售后服务";
            String serviceName = wxKcBo.getName();
            Integer ppid = wxKcBo.getPpriceid();
            //九机服务折价金额
            BigDecimal reducePrice = BigDecimal.ZERO;
            if(sDt.getServicesTypeBindId() == null){
                reducePrice = servierRecordPrice(sDt, ppid, serviceOpeningConfigList.stream()
                        .filter(soc -> Objects.equals(soc.getPpid(), ppid)).map(ProductServiceOpeningBO::getDuration)
                        .findFirst().orElse(0));
            }
            wxKcBo.setReducePrice(reducePrice);
            unbindSdtList.add(sDt);
            sDtList.stream().filter(sdtt -> sDt.getId().equals(sdtt.getId()) || ObjectUtil.equals(sDt.getId(), sdtt.getServicesTypeBindId()))
                    .forEach(sdtt -> checkTuiService(resultMap, sdtt, msgPrev, serviceName, wxKcBo, isGroupTui));
        }

        if (isReturnFun.get()){
            //全部异常就可以返回了
            return resultMap;
        }

        Boolean pzFlag = Boolean.FALSE;
        Integer nowAreaId = wxKcBoList.get(0).getNowAreaId();
        AreaInfo areaInfo = Optional.ofNullable(areaInfoClient.getAreaInfoById(nowAreaId)).filter(R::isSuccess)
                .map(R::getData).orElseThrow(()->new CustomizeException("当前门店地址不能为空"));
        if(!isOnlyCheck && ObjectUtil.notEqual(nowAreaId, oaUserBO.getAreaId())){
            throw new CustomizeException(StrUtil.format("地区不符，请切换至{}再操作！", areaInfo.getArea()));
        }
        try {
            BigDecimal feiYong = BigDecimal.ZERO;
            BigDecimal hsPrice = BigDecimal.ZERO;
            BigDecimal inprice = BigDecimal.ZERO;
            List<Integer> hsId = new LinkedList<>();

            //这里逻辑调整一下，在撤销时先校验旧件价格是否高于维修费用，高于维修费不能撤销
            for (int i=0; i< wxKcBoList.size(); i++) {
                WxKcBo m = wxKcBoList.get(i);

                Integer hsidTmp = m.getHsId() == null ? 0 : m.getHsId();

                feiYong = feiYong.add(m.getPrice() == null ? BigDecimal.ZERO : m.getPrice()).add(m.getPriceGs() == null ? BigDecimal.ZERO : m.getPriceGs());
                if(!isGroupTui){
                    // 组合退这里不需要减折价
                    feiYong = feiYong.subtract(NumberUtil.null2Zero(m.getReducePrice()));
                }
                hsPrice = hsPrice.add(hsidTmp == 0 ? BigDecimal.ZERO : (m.getHsPrice() == null ? BigDecimal.ZERO : m.getHsPrice()));
                inprice = inprice.add(m.getInprice() == null ? BigDecimal.ZERO : m.getInprice());

                if (CommenUtil.isNotNullZero(hsidTmp)) {
                    hsId.add(hsidTmp);
                }
            }
            if (feiYong.subtract(hsPrice).compareTo(BigDecimal.ZERO) < 0) {
                throw new CustomizeException("旧件价高于维修费，不可撤销！");
            }

            if(isReturnFun.get() || isOnlyCheck){
                return resultMap;
            }

            // 后面为db操作部分
            List<Integer> ids = wxKcBoList.stream().map(WxKcBo::getId).collect(Collectors.toList());
            List<Integer> cids = wxKcBoList.stream().map(WxKcBo::getCid).collect(Collectors.toList());
            //批量刪除服务记录
            if(!unbindSdtList.isEmpty()){
                Set<Integer> unBindIds = unbindSdtList.stream().map(ServiceRecordBo::getId).collect(Collectors.toSet());
                int upSize = shouHouPjMapper.updateServiceRecordDelFlagById(unBindIds);
                if(upSize < unBindIds.size()){
                    throw new CustomizeException("解除服务出险记录异常!");
                }
            }
            // region 实际退的时候才解除出险记录
            Optional.ofNullable(!isOnlyCheck)
                    .filter(Boolean::booleanValue)
                    .map(isTui -> SpringUtil.getBean(StringRedisTemplate.class).opsForValue()
                    .get(StrUtil.format(RedisKeys.UNOUT_SERVICE_RECORD_SHOUHOU_ID_KEY, wxId)))
                    .map(Convert::toInt)
                    .ifPresent(serviceRecordId -> {
//                        List<Integer> wxkcServiceTypeIds = wxkcoutputService.lambdaQuery()
//                                .eq(Wxkcoutput::getWxid,wxId).eq(Wxkcoutput::getServiceType, sh.getServiceType())
//                                .ne(Wxkcoutput::getStats, 3).select(Wxkcoutput::getId).list().stream().map(Wxkcoutput::getId)
//                                .collect(Collectors.toList());
                        List<Integer> wxkcServiceTypeIds =CommenUtil.autoQueryHist(()->wxkcoutputService.lambdaQuery()
                                .eq(Wxkcoutput::getWxid,wxId).eq(Wxkcoutput::getServiceType, sh.getServiceType())
                                .ne(Wxkcoutput::getStats, 3).select(Wxkcoutput::getId).list().stream().map(Wxkcoutput::getId)
                                .collect(Collectors.toList()),MTableInfoEnum.WXKCOUTPUT_WXID,wxId);
                        if(wxkcServiceTypeIds.isEmpty() || !wxkcServiceTypeIds.stream()
                                .filter(stId -> wxKcBoList.stream().anyMatch(wxkcBo -> Objects.equals(stId,wxkcBo.getId()))).findFirst().isPresent()){
                            //没有出险的配件 or 出险的配件未撤销完 or 当前撤销配件费出险配件
                            return;
                        }
                        ServiceRecordService serviceRecordService = SpringUtil.getBean(ServiceRecordService.class);
                        if(serviceRecordId<=0){
                            serviceRecordId = serviceRecordService.lambdaQuery().eq(ServiceRecord::getServerShouhouId,wxId)
                                    .and(cnd -> cnd.eq(ServiceRecord::getIsdel,0).or().isNull(ServiceRecord::getIsdel))
                                    .select(ServiceRecord::getId).list().stream().map(ServiceRecord::getId).findFirst().orElse(0);
                        }
                        if(serviceRecordId<=0){
                            //还是查询不到记录id
                            return;
                        }
                        Integer finalServiceRecordId = serviceRecordId;
                        shouhouLogThreadLocal.get().add(()->shouhouService.saveShouhouLog(wxId,StrUtil.format("服务记录id[{}]恢复为未出险",
                                finalServiceRecordId), oaUserBO.getUserName(), null, false));
                        serviceRecordService.lambdaUpdate().eq(ServiceRecord::getId,serviceRecordId).gt(ServiceRecord::getFeiyong,0)
                                .set(ServiceRecord::getFeiyong,0).update();
                    });
            // endregion
            //任意一个为出险配件,撤销出险记录
            boolean anyIsServicePj = false;
            for (WxKcBo m : wxKcBoList) {
                Integer ppid = m.getPpriceid();

                if (CommenUtil.isNotNullZero(ppid)) {
                    //撤销配件方式: 1.仅退款 2 退货退款  other 退货
                    //非仅退款 才操作库存
                    if(!TuiStatusEnum.REFUND_ONLY.getCode().equals(m.getTuiStatus())){
                        Integer areaId = Optional.ofNullable(m.getNowAreaId()).filter(CommenUtil::isNotNullZero)
                                .orElse(m.getAreaid());
                        //没有备货锁定才还原库存+1
                        if ((m.getIslockkc() == null || !m.getIslockkc())) {
                            //配件撤销地区逻辑。配件返回给当前维修单所在地

                            // 库存操作 取机后的库存操作, 需要在旧件上转现才入库 new apiServices().product_kc
//                        SmallproNormalCodeMessageRes codeMessageRes = smallproForwardExService.stockOperations(ppid, 1, m.getInprice() == null ? BigDecimal.ZERO : m.getInprice(), areaId, oaUserBO.getUserName(), "", "维修撤销new:", null, 1, 1, m.getWxid(), false, false);
                            if (!(isGroupTui && Boolean.TRUE.equals(sh.getIsquji()))) {
                                R<String> productKcR = shouHouPjService.returnProductKc(oaUserBO, m, ppid, areaId);
                                if (!productKcR.isSuccess()) {
                                    throw new CustomizeException(StrUtil.format("配件[{}]归还库存发生[{}]异常", m.getName(), productKcR.getUserMsg()));
                                }
                            }else{
                                //添加换货记录
                                boolean isAddHuishouSuccess = shouhouHuishouService.save(new ShouhouHuishou().setInuser(oaUserBO.getUserName())
                                        .setInprice(NumberUtil.null2Zero(m.getInprice()).doubleValue()).setIshuanhuo(1).setIsfan(Boolean.FALSE)
                                        .setName(m.getName()).setPpid(m.getPpriceid()).setFromSource(ShouhouHuishou.FromSourceEnum.WX_PJ_REFUND.getCode())
                                        .setPrice(BigDecimal.ZERO).setDktype(ObjectUtil.equals(sh.getBaoxiu(), BaoxiuStatusEnum.Z.getCode()) ? 1 : 0)
                                        .setKcount(1).setIsdel(Boolean.FALSE).setArea(null).setAreaid(areaId)
                                        .setHsjjSaletype(CommenUtil.isNotNullZero(m.getPpriceid()) && Objects.equals(sh.getBaoxiu(), BaoxiuStatusEnum.GFBN.getCode()) ? 1 : 0)
                                        .setIssale(Boolean.FALSE).setSaleprice(null).setShouhouId(m.getWxid()).setWxkcid(m.getId())
                                        .setIndate(LocalDateTime.now()));
                                if(! isAddHuishouSuccess){
                                    throw new CustomizeException(StrUtil.format("配件[{}]添加换货记录失败", m.getName()));
                                }
                            }
                            //删除核销信息
                            shouhouHexiaoService.remove(new QueryWrapper<ShouhouHexiao>().lambda()
                                    .eq(ShouhouHexiao::getIshexiao, 1).eq(ShouhouHexiao::getKcLogsid, m.getId()));
                        } else {
                            //如果是库存锁定，解除库存锁定1个

                            productKcService.unlockKc(ppid, m.getAreaid());
                            //解除锁定状态
                            wxkcoutputService.update(new UpdateWrapper<Wxkcoutput>().lambda()
                                    .set(Wxkcoutput::getOutputDtime, null)
                                    .set(Wxkcoutput::getIslockkc, null).eq(Wxkcoutput::getId, m.getId()));
                        }
                    }
                }

                boolean isUseYouHuiMa = false;
                if (m.getPpriceid() != null && !shouhouConstants.getShouhouServicesPpriceids().contains(m.getPpriceid())) {
                    //使用过优惠码，则返回优惠码
                    List<Integer> cardLogIds = shouHouPjMapper.getCardIdfromCardLogsByWxId(wxId, null, null);
                    if (CollUtil.isNotEmpty(cardLogIds)){
                        isUseYouHuiMa = true;
                        shouHouPjService.tuiYouHuiMa(sh, m, cardLogIds);
                    }
                }
                // 未使用优惠码的单独进行归还费用
                if(!isUseYouHuiMa && Boolean.TRUE.equals(sh.getIsquji())){
                    boolean isUpdateYouhuiFeiyong = shouHouPjService.updateYouhuiFeiyong(sh, m);
                    if(!isUpdateYouhuiFeiyong){
                        throw new CustomizeException("更新优惠费用异常,编码:536!");
                    }
                }
                //出险配件
                if(ObjectUtil.defaultIfNull(m.getServiceType(),0) > 0){
                    anyIsServicePj = true;
                }
            }

            if (sh.getBaoxiu() != null && sh.getBaoxiu().equals(1)) {
                hsPrice = BigDecimal.ZERO;
            }

            Integer count = shouHouPjMapper.updateCostPriceById(inprice, feiYong, wxId);
            LambdaUpdateWrapper<Shouhou> shouhouUpdateLambda = new UpdateWrapper<Shouhou>().lambda();
            if (CommenUtil.isNotNullZero(count)){
                pzFlag = Boolean.TRUE;
                for (WxKcBo m : wxKcBoList) {
                    boolean updateStatus = wxkcoutputService.update(new LambdaUpdateWrapper<Wxkcoutput>()
                            // todo 非仅退款才修改撤销状态
                            .set(Wxkcoutput::getStats, 3)
                            .set(Wxkcoutput::getTuiStatus, m.getTuiStatus()).set(Wxkcoutput::getTuidtime, LocalDateTime.now())
                            .eq(Wxkcoutput::getId, m.getId()).eq(Wxkcoutput::getStats, m.getStats()));
                    if(!updateStatus){
                        throw new CustomizeException("更新配件状态异常,编码:669!");
                    }
                }
            }else {
                wxkcoutputService.remove(new LambdaQueryWrapper<Wxkcoutput>().in(Wxkcoutput::getId, ids));
                SpringUtil.getBean(DeleteDataRecordService.class).saveDelDataRecord("wxkcoutput", ids);
                if(anyIsServicePj){
                    //未取机,撤销配件,需要撤销出险标志
                    wxkcoutputService.cancelServiceType(wxId,sh.getServiceType());
                    shouhouUpdateLambda.set(Shouhou::getServiceType,null);
                    shouhouLogThreadLocal.get().add(()->shouhouService.saveShouhouLog(sh.getId(),
                            StrUtil.format("撤销出险配件,关联取消服务【{}】",Optional.ofNullable(EnumUtil.getEnumByCode(BaoXiuTypeEnum.class,sh.getServiceType()))
                            .map(BaoXiuTypeEnum::getMessage).orElseGet(()->String.valueOf(sh.getServiceType()))), oaUserBO.getUserName(), null, false));
                }
            }

            //维修等级修改，按芯片维修配件和硬盘升级维修配件依次判断改为哪种维修等级
            Integer repairLevel = sh.getRepairLevel() == null ? 0 : sh.getRepairLevel();
            if (repairLevel == 0) {
                repairLevel = 1;
            }
            if (!cids.contains(68)) {
                repairLevel = 4;
            }
            if (!cids.contains(410)) {
                repairLevel = 1;
            }
            shouhouService.update(shouhouUpdateLambda.set(Shouhou::getRepairLevel, repairLevel).eq(Shouhou::getId, wxId));

            //重新计算维修费
            shouHouPjService.updateShouhouFeiyong(wxId);

            for (WxKcBo wxKcBo : wxKcBoList) {
                if (Boolean.TRUE.equals(pzFlag) && !XtenantEnum.isJiujiXtenant()) {
                    Integer ztId = authConfigService.getZtIdByAuId(areaInfo.getAuthorizeId());
                    if (ztId != null) {
                        KemuFzhsItem k140518 = keMuService.getKemuFzhsInfoByEKemu(EKemuEnum.K140518.getCode(), ztId, nowAreaId);
                        KemuFzhsItem k140504 = keMuService.getKemuFzhsInfoByEKemu(EKemuEnum.K140504.getCode(), ztId, nowAreaId);
                        if (k140518 != null && k140504 != null) {
                            String summary = String.format("配件撤销，单号：%s|配件撤销，单号：%s", wxId, wxId);
                            String kemu = k140518.getCode() + "|" + k140504.getCode();
                            String fzhs = k140518.getFzhs() + "|" + k140504.getFzhs();
                            String jief = wxKcBo.getInprice() + "|-" + wxKcBo.getInprice();
                            String daif = "0|0";

                            PingzhengBO pz = new PingzhengBO();
                            pz.setKemu(kemu);
                            pz.setJief(jief);
                            pz.setDaif(daif);
                            pz.setFzhs(fzhs);
                            pz.setZhaiyao(summary);
                            pz.setZtid(ztId.toString());
                            pz.setPzdate(DateUtil.localDateTimeToString(LocalDateTime.now()));
                            pz.setIsaudit("1");
                            PingzhengResultBO result = voucherService.addPingZheng(pz);
                            if (result == null || !result.getFlag()) {
                                String msg = "";
                                if (result != null) {
                                    msg = result.getErrorMsg();
                                }

                                RRExceptionHandler.logError("撤销配件凭证", pz, new CustomizeException(msg), smsService::sendOaMsgTo9JiMan);
                                throw new CustomizeException("撤销配件凭证生成失败（" + msg + ")");
                            }
                            shouhouLogThreadLocal.get()
                                    .add(() -> shouhouService.saveShouhouLog(sh.getId(), StrUtil.format("维修配件ppid:{}撤销，凭证号:{}", wxKcBo.getPpriceid(), result.getPzId()),
                                            "系统", null, false));
                        }
                        WxoutKcCancel cancelInfo = new WxoutKcCancel();
                        cancelInfo.setAreaid(nowAreaId);
                        cancelInfo.setDtime(LocalDateTime.now());
                        cancelInfo.setInprice(wxKcBo.getInprice());
                        cancelInfo.setPpid(wxKcBo.getPpriceid());
                        cancelInfo.setWxid(wxId);
                        cancelInfo.setWxkcid(wxKcBo.getId());
                        cancelInfo.setPrice(wxKcBo.getTuiPrice());
                        cancelInfo.setInuser(oaUserBO.getUserName());
                        kcCancelService.save(cancelInfo);

                        NewVoucherBo voucher = new NewVoucherBo();
                        voucher.setAct("wxKcCancel");
                        voucher.setActName("维修单配件撤销");
                        voucher.setAccountSetId(ztId.toString());
                        NewVoucherBo.VoucherArgs argsO = new NewVoucherBo.VoucherArgs();
                        argsO.setId(cancelInfo.getId());
                        voucher.setArgsO(argsO);
                        voucher.setSubId(cancelInfo.toString());
                        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                        voucher.setVoucherTime(LocalDateTime.now().format(dtf));
                        voucher.setAreaId(areaInfo.getId());
                        voucherService.addNewVoucher(voucher);
                    }

                } else if (Boolean.TRUE.equals(pzFlag) && !TuiStatusEnum.REFUND_ONLY.getCode().equals(wxKcBo.getTuiStatus())) {
                    //九机取机后撤销保存记录
                    WxoutKcCancel cancelInfo = new WxoutKcCancel();
                    cancelInfo.setAreaid(nowAreaId);
                    cancelInfo.setDtime(LocalDateTime.now());
                    cancelInfo.setInprice(wxKcBo.getInprice());
                    cancelInfo.setPpid(wxKcBo.getPpriceid());
                    cancelInfo.setWxid(wxId);
                    cancelInfo.setWxkcid(wxKcBo.getId());
                    cancelInfo.setPrice(wxKcBo.getTuiPrice());
                    cancelInfo.setInuser(oaUserBO.getUserName());
                    kcCancelService.save(cancelInfo);
                }
            }

            //删除回收的配件记录 九机取机之后改为不删除回收记录
            if (CollectionUtils.isNotEmpty(hsId) && !(isGroupTui && Boolean.TRUE.equals(sh.getIsquji()))) {
                Boolean flag = shouhouHuishouService.remove(new QueryWrapper<ShouhouHuishou>().lambda().in(ShouhouHuishou::getId, hsId));
                if(!flag){
                    throw new CustomizeException("回收记录移除异常");
                }
            }
            for (WxKcBo wxKcBo : wxKcBoList) {
                if(ObjectUtil.defaultIfNull(wxKcBo.getHsId(),0)>0){
                    if (wxKcBo.getHsPrice() != null && wxKcBo.getHsPrice().compareTo(BigDecimal.ZERO) > 0
                            && Objects.equals(wxKcBo.getBaoxiu(), BaoxiuStatusEnum.GFBN.getCode())) {
                        // 智乐方做凭证
                        Integer ztId = authConfigService.getZtIdByAuId(areaInfo.getAuthorizeId());
                        Long hsjjPzid = wxKcBo.getHsjjPzid();
                        double huishouPrice = 0.00 - hsPrice.doubleValue();

                        if (CommenUtil.isNotNullZero(ztId)) {
                            KemuFzhsItem k140518 = keMuService.getKemuFzhsInfoByEKemu(EKemuEnum.K140518.getCode(), ztId, nowAreaId);
                            KemuFzhsItem k140504 = keMuService.getKemuFzhsInfoByEKemu(EKemuEnum.K140504.getCode(), ztId, nowAreaId);
                            if (k140518 != null && k140504 != null) {
                                String summary = String.format("维修单%s，旧件回收撤销，原凭证号%s，金额%s元", wxKcBo.getWxid(), hsjjPzid, huishouPrice);
                                summary = summary + "|" + summary;
                                String kemu = k140518.getCode() + "|" + k140504.getCode();
                                String fzhs = k140518.getFzhs() + "|" + k140504.getFzhs();
                                String jief = huishouPrice + "|0";
                                String daif = "0|" + huishouPrice;

                                PingzhengBO pz = new PingzhengBO();
                                pz.setKemu(kemu);
                                pz.setJief(jief);
                                pz.setDaif(daif);
                                pz.setFzhs(fzhs);
                                pz.setZhaiyao(summary);
                                pz.setZtid(ztId.toString());
                                pz.setPzdate(DateUtil.localDateTimeToString(LocalDateTime.now()));
                                pz.setIsaudit("1");
                                PingzhengResultBO result = voucherService.addPingZheng(pz);
                                if (result == null || !result.getFlag()) {
                                    String msg = "撤销佳海旧件回收凭证生成失败,维修单号(" + wxKcBo.getWxid() + ",hsid:" + hsId;
                                    if (result != null) {
                                        msg = msg + ",原因：" + result.getErrorMsg();
                                    }
                                    msg += ")";
                                    R<String> exceptionR = sysConfigClient.getValueByCode(SysConfigConstant.EXCEPTION_PUSH_USER_ID);
                                    if (exceptionR.getCode() == ResultCode.SUCCESS && exceptionR.getData() != null) {
                                        String url = inwcfUrlSource.getNoticeQywxMessage(exceptionR.getData(), oaUserBO.getUserIp(), "旧件返厂凭证生成异常推送", msg, "", "");
                                        HttpUtil.get(url);
                                    }
                                }
                            } else {
                                String errorMsg = "佳海旧件回收待返厂凭证生成失败,原因：科目140518，140504未配置,维修单号" + wxKcBo.getWxid();
                                R<String> exceptionR = sysConfigClient.getValueByCode(SysConfigConstant.EXCEPTION_PUSH_USER_ID);
                                if (exceptionR.getCode() == ResultCode.SUCCESS && exceptionR.getData() != null) {
                                    String url = inwcfUrlSource.getNoticeQywxMessage(exceptionR.getData(), oaUserBO.getUserIp(), "旧件返厂凭证生成异常推送", errorMsg, "", "");
                                    HttpUtil.get(url);
                                }
                            }

                            //旧件回收待返厂
                            NewVoucherBo voucher = new NewVoucherBo();
                            voucher.setAct("partRecoverCancel");
                            voucher.setActName("旧件回收撤销");
                            voucher.setAccountSetId(ztId.toString());
                            NewVoucherBo.VoucherArgs argsO = new NewVoucherBo.VoucherArgs();
                            argsO.setId(wxKcBo.getHsId());
                            voucher.setArgsO(argsO);
                            voucher.setSubId(wxKcBo.getHsId().toString());
                            DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                            voucher.setVoucherTime(LocalDateTime.now().format(dtf));
                            voucher.setAreaId(areaInfo.getId());
                            voucherService.addNewVoucher(voucher);

                        }
                    }
                    StringJoiner comment = new StringJoiner("");
                    if (hsPrice.compareTo(BigDecimal.ZERO) > 0) {
                        comment.add("配件被撤销，恢复抵扣维修费" + (hsPrice.intValue()) + "元");
                    } else {
                        comment.add(String.format("撤销维修配件【%s%s】回收ID【%s】", wxKcBo.getName(), Optional.ofNullable(wxKcBo.getPpriceid())
                                .filter(ppid -> ppid > 0).map(ppid -> String.format("，PPID:%s", ppid)).orElse(""), CollUtil.join(hsId, SignConstant.COMMA)));
                    }
                    //撤销配件方式: 1.仅退款 2 退货退款  other 退货
                    if (NumberConstant.ONE.equals(wxKcBo.getTuiStatus())) {
                        comment.add(",仅退款");
                    }
                    shouhouLogThreadLocal.get().add(() -> shouhouService.saveShouhouLog(wxKcBo.getWxid(), comment.toString(), oaUserBO.getUserName(), null, true));
                } else {
                    StringBuilder comment = new StringBuilder("撤销维修配件").append("【").append(wxKcBo.getName()).append("】");
                    Optional.ofNullable(wxKcBo.getPpriceid()).filter(ppid -> ppid > 0).ifPresent(ppid -> comment.append("，PPID:").append(ppid));
                    //撤销配件方式: 1.仅退款 2 退货退款  other 退货
                    if (NumberConstant.ONE.equals(wxKcBo.getTuiStatus())) {
                        comment.append(",仅退款");
                    }
                    shouhouLogThreadLocal.get().add(() -> shouhouService.saveShouhouLog(wxKcBo.getWxid(), comment.toString(), oaUserBO.getUserName(), null, true));
                }
                if(Boolean.TRUE.equals(pzFlag)){
                    addTuiPingZheng(sh,wxKcBo,areaInfo);
                }
            }
        } finally {
            //啥也不做,去掉原来的抓异常的写法
            ;
        }
        Integer shouYinLock = shouHouPjMapper.getShouYinLockById(wxId);
        shouYinLock = shouYinLock == null ? 0 : shouYinLock;

        if (shouYinLock != 1 && shouYinLock != 2) {

            Boolean upFlag = shouHouPjMapper.updateWxkcoutputPriceByWxId(wxId) > 0;
            if (upFlag) {
                //从新计算维修费
                shouHouPjService.updateShouhouFeiyong(wxId);
            }
        }

        Integer wxPjCount = shouHouPjMapper.getWxPjCountByWxId(wxId);
        if (wxPjCount == 0) {
            List<Dict> yhmDicts = shouHouPjMapper.getUpdatedNumberCardIdByWxId(wxId);
            if(CollUtil.isNotEmpty(yhmDicts)){
                List<Integer> yhmIds = yhmDicts.stream().map(dic -> dic.getInt("id")).collect(Collectors.toList());
                BigDecimal yhmAllTotal = yhmDicts.stream().map(dic -> dic.getBigDecimal("total")).filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);

                shouhouService.update(new UpdateWrapper<Shouhou>().lambda()
                        .set(Shouhou::getFeiyong, BigDecimal.ZERO).set(Shouhou::getCostprice, BigDecimal.ZERO)
                        .set(Shouhou::getYouhuima, null)
                        .set(Shouhou::getYouhuifeiyong,
                                NumberUtil.max(NumberUtil.null2Zero(sh.getYouhuifeiyong()).subtract(yhmAllTotal), BigDecimal.ZERO))
                        .eq(Shouhou::getId, wxId));

                Integer count = shouHouPjMapper.updateNumberCardInfoByWxId(wxId);
                if (CollectionUtils.isNotEmpty(yhmIds) && count > 0) {
                    cardLogsService.remove(new QueryWrapper<CardLogs>().lambda()
                            .in(CardLogs::getId, yhmIds));
                }
            }
        }

        Long xtenant = Long.valueOf(areaInfo.getXtenant());

        //解绑服务通知
        if (!unbindSdtList.isEmpty() && CollectionUtils.isNotEmpty(servicesDt)) {
            Integer areaId = servicesDt.get(0).getAreaid();
            Integer userId = servicesDt.get(0).getUserid();

            WeixinUser wxUser = weixinUserService.getWxxinUserByUserId(userId);
            if (wxUser != null) {
                String openId = wxUser.getOpenid();
                String subMobile = servicesDt.get(0).getMobile();
                if (StringUtils.isNotEmpty(openId) || StringUtils.isNotEmpty(subMobile)) {
                    //1 意外一年 2 意外二年 3 延保一年 6:延保2年 4:(2年) 5:碎屏保 7:进水保 9 碎屏保2年 8:屏背保
                    String title = "";
                    String keyword1 = "";
                    for (ShServiceDataInfoBo sdt : servicesDt) {
                        title = "亲，您的售后维修单：" + wxId + "，机型：" + sdt.getPro() + "，串号：" + sdt.getImei() + "，已取消" + sdt.getProductName() + "，解绑完成，服务取消";
                        String url = commonService.getUrlByXtenant(xtenant, ExtenAntUrlTypeEnum.MURL.getCode());
                        url = url + "/after-service/detail/" + wxId;
                        keyword1 = sdt.getProductName() + "取消";
                        if (StringUtils.isNotEmpty(openId)) {
                            shouhouMsgService.sendShouHouNotify(openId, url, title, "", keyword1, "已取消", LocalDateTime.now(), "已成功取消九机快修服务", 1, xtenant, xtenant.equals(0L) ? null : areaInfo.getCityId());
                        } else {
                            String shorturl = smsService.getShortUrl(0L, url, "");
                            smsService.sendSms(subMobile, title + shorturl, DateUtil.localDateTimeToString(LocalDateTime.now()), "系统", smsService.getSmsChannelByTenant(areaId, ESmsChannelTypeEnum.YZMTD));
                        }
                    }
                }
            }
        }
        //撤销sn绑定
        cancelBind(wxKcBoList, oaUserBO);
        //持久化售后单日志
        shouhouLogThreadLocal.get().forEach(Runnable::run);
        //清理内存中的日志,防止内存溢出,如果失败,在入口处remove
        shouhouLogThreadLocal.remove();
        //发送通知给亏损预警队列
        SpringUtil.getBean("oaRabbitTemplate", RabbitTemplate.class).convertAndSend(RabbitMqConfig.AFTER_SHOUHOU_QU_JI,
                RabbitMqConfig.SHOUHOU_QUJI_LOSSWARNING,Convert.toStr(wxId));
        return resultMap;
    }

    /**
     * 撤销sn绑定,大疆维修单方案绑定
     * @param wxKcBoList
     */
    private void cancelBind(List<WxKcBo> wxKcBoList,OaUserBO oaUserBO){
        //输出不进行该操作
        if(XtenantEnum.isSaasXtenant()){
            return;
        }
        OaUserBO userBO = Optional.ofNullable(oaUserBO).orElse(new OaUserBO());
        for (WxKcBo e : wxKcBoList) {
            //取消sn绑定
            ProductSnQueryReq productSnQueryReq = new ProductSnQueryReq().setBasketId(e.getId()).setPpid(e.getPpriceid());
            Optional.ofNullable(snService.getProductSnByBasketIdAndPpid(productSnQueryReq)).ifPresent(productSn -> {
                ProductSnUpdateBindReq req = new ProductSnUpdateBindReq();
                req.setSn(productSn.getSn())
                        .setPpid(productSn.getPpid())
                        .setBasketId(e.getId())
                        .setOperationType(OperationTypeEnum.UNBIND.getCode());
                R<Boolean> booleanR = snService.updateBasketIdAndUnbind(req);
                if(!booleanR.isSuccess()){
                    throw new CustomizeException(Optional.ofNullable(booleanR.getUserMsg()).orElse(booleanR.getMsg()));
                }
            });
            //取消大疆维修绑定
            RepairPlanService planService = SpringUtil.getBean(RepairPlanService.class);
            planService.cancelBind(e.getId(),CorrelationTypeEnum.WXKCOUTPUT.getCode(), userBO.getUserName());
        }

    }

    /**
     * 添加所有服务
     * @param productId
     * @param ppriceIds
     * @param serviceOpeningConfigList 服务配置
     * @param ppidBindServiceMap 绑定的服务配置
     */
    private void addAllServices(Long productId, List<Integer> ppriceIds, List<ProductServiceOpeningVO> serviceOpeningConfigList, Map<Integer, List<ProRelateInfoService>> ppidBindServiceMap) {
        WebCloud webCloud = SpringUtil.getBean(WebCloud.class);
        Optional.of(ppriceIds).filter(CollUtil::isNotEmpty)
                .ifPresent(ppids -> {
                    //通过ppid获取退的服务配置
                    listProductServiceOpening(ppids).ifPresent(serviceOpeningConfigList::addAll);
                    //通过配件的ppid获取绑定的服务配置
                    Optional.ofNullable(webCloud.listProductServicesV2(Convert.toInt(productId, 0),ppids, XtenantEnum.getXtenant()))
                            .filter(r -> {
                                if(r.getCode() != ResultCode.SUCCESS || r.getData() == null){
                                    throw new CustomizeException("通过配件获取可绑定服务配置异常,请稍后再试");
                                }
                                return true;
                            }).map(Result::getData)
                            .ifPresent(ppidBindServiceMap::putAll);
                });
    }

    private Optional<List<ProductServiceOpeningVO>> listProductServiceOpening(List<Integer> ppids) {
        WebCloud webCloud = SpringUtil.getBean(WebCloud.class);
        return Optional.ofNullable(webCloud.openingGetServices(ppids.stream().map(Convert::toStr).collect(Collectors.joining(StringPool.COMMA)),
                XtenantEnum.getXtenant()))
                .filter(r -> {
                    if (r.getCode() != ResultCode.SUCCESS || r.getData() == null) {
                        throw new CustomizeException("获取服务配置异常,请稍后再试");
                    }
                    return true;
                }).map(Result::getData).map(psos -> psos.stream()
                        .filter(pso -> Objects.nonNull(pso.getPpid())).collect(Collectors.toList()));
    }

    private void checkTuiService(Map<Integer, R<Integer>> resultMap, ServiceRecordBo sDt,
                                 String msgPrev, String serviceName, WxKcBo wxKcBo, boolean isGroupTui) {
        if (sDt.getFeiyong() != null && sDt.getFeiyong().compareTo(BigDecimal.ZERO) > 0) {
            resultMap.put(wxKcBo.getId(), R.error(StrUtil.format("{}[{}]已出险，不能撤销！", msgPrev, serviceName)));
            return;
        }
        if(sDt.getEndTime() != null && LocalDateTime.now().isAfter(CommonUtils.getEndOfDay(sDt.getEndTime()))){
            resultMap.put(wxKcBo.getId(), R.error(StrUtil.format("{}[{}]已过保，不能撤销！", msgPrev, serviceName)));
            return;
        }
        if (sDt.getEndTime() == null && sDt.getServiceType().equals(18) && LocalDateTime.now().isAfter(CommonUtils.getEndOfDay(sDt.getTradedate()).plusMonths(6))) {
            resultMap.put(wxKcBo.getId(), R.error(StrUtil.format("{}[{}]已过保，不能撤销！", msgPrev, serviceName)));
            return;
        }
        if (sDt.getEndTime() == null && sDt.getServiceType().equals(19) && LocalDateTime.now().isAfter(CommonUtils.getEndOfDay(sDt.getTradedate()).plusYears(2))) {
            resultMap.put(wxKcBo.getId(), R.error(StrUtil.format("{}[{}]已过保，不能撤销！", msgPrev, serviceName)));
            return;
        }
        List<Integer> shouhouServiceTypes = Arrays.stream(ServiceEnum.values()).filter(se -> Objects.equals(se.getCode(), sDt.getServiceType()))
                .map(ServiceEnum::getShouHouType).filter(Objects::nonNull).map(BaoXiuTypeEnum::getCode).distinct().collect(Collectors.toList());
        if(CollUtil.isNotEmpty(shouhouServiceTypes)){
            Optional<Shouhou> outingServiceOpt = CommenUtil.autoQueryHist(()->shouhouService.lambdaQuery().eq(Shouhou::getImei, sDt.getImei())
                    .in(Shouhou::getServiceType, shouhouServiceTypes)
                    .eq(Shouhou::getXianshi, 1).and(CommenUtil.isNullOrEq(Shouhou::getIsquji, false)).list().stream().findFirst());
//            Optional<Shouhou> outingServiceOpt = shouhouService.lambdaQuery().eq(Shouhou::getImei, sDt.getImei())
//                    .in(Shouhou::getServiceType, shouhouServiceTypes)
//                    .eq(Shouhou::getXianshi, 1).and(CommenUtil.isNullOrEq(Shouhou::getIsquji, false)).list().stream().findFirst();
            if(outingServiceOpt.isPresent()){
                resultMap.put(wxKcBo.getId(), R.error(StrUtil.format("{}[{}]存在出险中的服务，售后单号[{}]，不能撤销！", msgPrev, serviceName,
                        outingServiceOpt.map(Shouhou::getId).orElse(null))));
                return;
            }
        }

        //折价后的可退金额
        BigDecimal reducePrice = NumberUtil.null2Zero(wxKcBo.getReducePrice());
        if (isGroupTui && XtenantEnum.isSaasXtenant() && ObjectUtil.equal(sDt.getBasketId(), wxKcBo.getId())
                && NumberUtil.null2Zero(wxKcBo.getRefundedPrice()).compareTo(reducePrice)>0
                && !SpringUtil.getBean(StringRedisTemplate.class).hasKey(StrUtil.format(RedisKeys.SHOUHOU_NOT_DISCOUNT_WKCOUTPUT_KEY, wxKcBo.getId()))) {
            resultMap.put(wxKcBo.getId(), R.error(StrUtil.format("{}[{}]最多退:{}，不能撤销！", msgPrev, serviceName, reducePrice)));
            return;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateYouhuiFeiyong(ShouhouBo sh, WxKcBo m) {
        BigDecimal youhuifeiyong = Optional.ofNullable(sh.getYouhuifeiyong())
                //减去当前配件的分摊费用
                .map(yhfy -> yhfy.subtract(Optional.ofNullable(m.getYouhuifeiyong()).orElse(BigDecimal.ZERO)))
                .filter(yhfy -> yhfy.compareTo(BigDecimal.ZERO) > 0).orElse(BigDecimal.ZERO);
        boolean isUpdateYouhuiFeiyong = CommenUtil.autoWriteHist(()->shouhouService.lambdaUpdate().eq(Shouhou::getId, sh.getId())
                .eq(sh.getYouhuifeiyong() != null, Shouhou::getYouhuifeiyong, sh.getYouhuifeiyong())
                .set(Shouhou::getYouhuifeiyong, youhuifeiyong).update());
        //因为存在关联退,循环退的情况,需要更新优惠费用,才能确保第二次更新费用成功
        sh.setYouhuifeiyong(youhuifeiyong);
        return isUpdateYouhuiFeiyong;
    }

    private void addTuiPingZheng(ShouhouBo sh, WxKcBo wxKcBo,AreaInfo nowAreaInfo) {
        //撤销配件成本做账
        if(XtenantEnum.isJiujiXtenant() && !Optional.ofNullable(SpringUtil.getBean(StringRedisTemplate.class).opsForValue()
                .get(StrUtil.format(RedisKeys.NOTPZ_CANCEL_SHOUHOU_PJ_ID_KEY, wxKcBo.getId())))
                .map(Convert::toBool).filter(Boolean::booleanValue).isPresent()){
            addTuiPingZhengJiuji(sh,wxKcBo,nowAreaInfo);
        }

    }
    private void addTuiPingZhengJiuji(ShouhouBo sh, WxKcBo wxKcBo,AreaInfo nowAreaInfo) {

        Integer ztId = Optional.ofNullable(authConfigService.getJiujiZtIdByAreaId(wxKcBo.getNowAreaId()))
                .orElseGet(() -> {
                    throw new CustomizeException("获取不到账套id!");
                });
        PingzhengBO pz = new PingzhengBO().setKemu(StrUtil.format("140504|{}",DecideUtil.iif(Objects.equals(wxKcBo.getBaoxiu(),1),"640103","640114")))
                .setJief(StrUtil.indexedFormat("{0,number,#.##}|-{0,number,#.##}",wxKcBo.getInprice().setScale(2, RoundingMode.HALF_UP)))
                .setDaif("0|0").setFzhs(StrUtil.format("无|{}",nowAreaInfo.getArea()))
                .setZhaiyao(StrUtil.indexedFormat("{0}|{0}",StrUtil.format("维修配件撤销，单号：{}",wxKcBo.getWxid())))
                .setZtid(ztId.toString()).setPzdate(DateUtil.localDateTimeToString(LocalDateTime.now())).setIsaudit("1");
        PingzhengResultBO result = voucherService.addPingZheng(pz);
        if (result == null || !Boolean.TRUE.equals(result.getFlag())){
            String errorMsg = Optional.ofNullable(result).map(PingzhengResultBO::getErrorMsg).orElse("接口返回空");
            RRExceptionHandler.logError("撤销配件凭证",pz,new CustomizeException(errorMsg),smsService::sendOaMsgTo9JiMan);
            if(Objects.equals(AreaKindEnum.JOIN.getCode(), nowAreaInfo.getKind1())){
                //加盟店,老凭证,做凭证失败不阻断撤销配件的流程
                return;
            }
            throw new CustomizeException(StrUtil.format("撤销配件凭证生成失败({})", errorMsg));
        }
        //成功记录凭证
        shouhouLogThreadLocal.get().add(()->shouhouService.saveShouhouLog(sh.getId(), StrUtil.format("维修配件ppid:{}撤销，凭证号:{}",wxKcBo.getPpriceid(),result.getPzId()),
                "系统", null, false));
    }

    /**
     * 撤销配件归还库存
     * @param oaUserBO
     * @param m
     * @param ppid
     * @param areaId
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<String> returnProductKc(OaUserBO oaUserBO, WxKcBo m, Integer ppid, Integer areaId) {
        //存在商品的出库记录 in (shouhouId,wxkcId) and areaid = 1 and ppid = 1 and dtime>=配件增加时间
        if(shouHouPjMapper.existKcOut(ppid, m.getWxid(), m.getId())){
            OperateProductKcPara para = new OperateProductKcPara();
            para.setPpid(ppid);
            para.setCount(1);
            para.setInprice(m.getInprice() == null ? BigDecimal.ZERO : m.getInprice());
            para.setAreaId(areaId);
            para.setInuser(oaUserBO.getUserName());
            para.setInsource("");
            para.setComment("维修撤销new:");
            para.setBasketId(null);
            para.setCheck1(true);
            para.setCheck2(true);
            para.setShouhouId(Long.valueOf(m.getWxid()));
            para.setIsLp(false);
            para.setDiaoboFlag(false);

            R<OperateProductKcRes> productKcR = productKcService.operateProductKc(para);
            if (ResultCode.SUCCESS != productKcR.getCode()) {
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return R.error("操作库存失败【" + productKcR.getUserMsg() + "】");
            }
        }
        return R.success(null);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void tuiYouHuiMa(ShouhouBo sh, WxKcBo m, List<Integer> cardLogIds) {
        List<NumberCard> numberCards = new ArrayList<>();
        for (Integer cardLogId : cardLogIds) {
            if (CommenUtil.isNotNullZero(cardLogId)) {
                NumberCard dr = numberCardService.getById(cardLogId);
                numberCards.add(dr);
            }
        }
        Integer wxId = sh.getId();
        boolean isRefundCard = true;
        boolean isWeb = true;
        String comment = StrUtil.format("配件被撤销,返还优惠码{}",numberCards.stream().map(NumberCard::getCardID).collect(Collectors.toList()));
        // region 取机后退优惠码
        if (Boolean.TRUE.equals(sh.getIsquji())) {
            if (m.getYouhuifeiyong() == null) {
                m.setYouhuifeiyong(BigDecimal.ZERO);
                for (NumberCard dr : numberCards) {
                    //获取能够使用优惠码的配件
                    Set<Integer> wxkcIds = Optional.ofNullable(shouhouExService.listYouhuimaPj(dr, sh)).map(R::getData).orElseGet(Collections::emptySet);
                    //到最后为空就是没有限制配件,可以全部进行分摊
                    List<Wxkcoutput> wxkcoutputs = wxkcoutputService.listYouhuiMaAllocation(sh.getId(), wxkcIds,shouhouConstants.getShouhouServicesPpriceids());
                    //分摊计算
                    shouhouExService.youhuiMaAllocation(dr.getTotal(), wxkcoutputs);
                    //设置当前配件的分摊费用
                    wxkcoutputs.stream().filter(wxkc -> Objects.equals(wxkc.getId(), m.getId())).map(Wxkcoutput::getYouhuifeiyong)
                            .findFirst().ifPresent(yhfy -> m.setYouhuifeiyong(m.getYouhuifeiyong().add(yhfy)));
                    //批量更新分摊费用,并设置当前分摊优惠码的code,没有参与分摊的,且优惠费用为null设置为零
                    wxkcoutputService.updateYouHuiFeiYongBatch(sh.getId(), dr.getCardID(), wxkcoutputs);
                    //记录分摊日志
                    shouhouLogThreadLocal.get().add(()->shouhouService.saveShouhouLog(wxId, StrUtil.format("优惠码[{}]分摊优惠费用到配件:[{}]", dr.getCardID()
                                    , wxkcoutputs.stream().map(Wxkcoutput::getId).map(String::valueOf).collect(Collectors.joining(SignConstant.COMMA)))
                            , "系统", null, false));
                }
            }
            boolean isUpdateYouhuiFeiyong = shouHouPjService.updateYouhuiFeiyong(sh, m);
            if(!isUpdateYouhuiFeiyong){
                throw new CustomizeException("更新优惠费用异常,编码:942!");
            }
            // 优惠费用全部退完,就可以退优惠码了
            isRefundCard = ObjectUtil.defaultIfNull(sh.getYouhuifeiyong(),BigDecimal.ZERO).compareTo(BigDecimal.ZERO) <=0;
            String splitFeiyongMsg = MessageFormat.format("配件PPID:{0}被撤销,分摊优惠码费用: {1,number,#.##}元", m.getPpriceid(), m.getYouhuifeiyong());
            if (!isRefundCard) {
                comment = splitFeiyongMsg;
                isWeb = false;
            } else {
                //退优惠码需要增加一条日志
                shouhouLogThreadLocal.get().add(()->shouhouService.saveShouhouLog(wxId, splitFeiyongMsg, "系统", null, false));
            }
        }
        // endregion
        if (isRefundCard) {
            //更新优惠码的使用状态
            numberCardService.update(new LambdaUpdateWrapper<NumberCard>().setSql("use_count=use_count-1,State=0")
                    .in(NumberCard::getId, numberCards.stream().map(NumberCard::getId).collect(Collectors.toList())));
            // region 更新抖音优惠码的时效
            LocalDate todayDate = LocalDate.now();
            //抖音撤销卷,修改结束时间 优惠码的生效时间修改为2天（当天算第一天）
            Optional.ofNullable(shouhouService.getLastDouYinCouponLog(wxId, DouYinCouponLogRes.SubKindEnum.AFTERSALES.getCode()))
                    .filter(dycl -> DouYinCouponLogRes.CouponStatus.SUCCESS.getCode().equals(dycl.getStatus()))
                    .flatMap(dycl -> numberCards.stream().filter(cardInfo -> Objects.equals(dycl.getCardId(), cardInfo.getId()))
                            .filter(cardInfo -> ObjectUtil.defaultIfNull(cardInfo.getEndTime(),todayDate).isAfter(todayDate.plusDays(NumberConstant.ONE)))
                            .findFirst())
                    .ifPresent(cardInfo -> {
                        numberCardService.lambdaUpdate().eq(NumberCard::getId, cardInfo.getId())
                                .set(NumberCard::getEndTime, todayDate.plusDays(NumberConstant.ONE)).update();
                    });
            // endregion
            //删除优惠码的使用记录
            cardLogsService.remove(new QueryWrapper<CardLogs>().lambda()
                    .eq(CardLogs::getSubId, wxId).eq(CardLogs::getUseType, 1));
            //更新售后表优惠码值
            boolean updateYhfy = shouhouService.update(new UpdateWrapper<Shouhou>().lambda()
                    .eq(Shouhou::getId, wxId)
                    .eq(!Boolean.TRUE.equals(sh.getIsquji()) && sh.getYouhuifeiyong() != null, Shouhou::getYouhuifeiyong,sh.getYouhuifeiyong())
                    .eq(sh.getYouhuima() != null, Shouhou::getYouhuima, sh.getYouhuima())
                    //取机之前,扣减优惠码的费用
                    .set(!Boolean.TRUE.equals(sh.getIsquji()),Shouhou::getYouhuifeiyong, NumberUtil.max(BigDecimal.ZERO,
                            ObjectUtil.defaultIfNull(sh.getYouhuifeiyong(),BigDecimal.ZERO).subtract(
                                    numberCards.stream().map(NumberCard::getTotal).reduce(BigDecimal.ZERO,BigDecimal::add))))
                    .set(isRefundCard, Shouhou::getYouhuima, null)
            );
            if (!updateYhfy) {
                throw new CustomizeException("更新优惠码费用异常,编码:707!");
            }
        }
        final String tComment = comment;
        final boolean tIsWeb = isWeb;
        shouhouLogThreadLocal.get().add(()->shouhouService.saveShouhouLog(wxId, tComment, "系统", null, tIsWeb));


    }

    @Override
    public WxKcBo getWxkcOutput(Integer id) {
        return CommenUtil.autoQueryHist(()->shouHouPjMapper.listWxkcOutput(Collections.singletonList(id), null).stream().findFirst().orElse(null));
    }

    @Override
    public List<WxKcBo> listWxkcOutput(List<Integer> ids, Integer wxId) {
        return CommenUtil.autoQueryHist(()->shouHouPjMapper.listWxkcOutput(ids, wxId));
    }

    @Override
    public List<ServiceRecordBo> listServiceRecordInfo(Integer wxId, Integer wxKcId, List<Integer> serviceTypes) {
        return shouHouPjMapper.listServiceRecordInfo(wxId, wxKcId,serviceTypes);
    }

    @Override
    public BigDecimal servierRecordPrice(ServiceRecordBo sDt, Integer ppid, Integer duration) {
        if (Objects.isNull(sDt) || Objects.nonNull(sDt.getServicesTypeBindId())){
            return BigDecimal.ZERO;
        }
        boolean isHalfYear = duration>0 && duration <= 6;
        boolean isOneYear = duration>6 && duration <= 12;
        boolean isTwoYear = duration>12;
        BigDecimal price = sDt.getPrice();
        LocalDateTime buyTime = sDt.getTradedate();
        BigDecimal rePrice = price;
        LocalDateTime nowDate = SpringContextUtil.getRequest().map(req -> (LocalDateTime)req.getAttribute(RequestAttrKeys.NOW_DATE)).orElse(LocalDateTime.now());
        Long day = Duration.between(buyTime, nowDate).toDays();
        //15天内不折价
        if (day <= 15) {
            return rePrice;
        }
        List<Integer> halfYear = Arrays.asList(68145, 68144, 81682);
        List<Integer> oneYear = Arrays.asList(19882, 19881, 47365, 55428, 60884);
        List<Integer> twoYear = Arrays.asList(19885, 47359, 50009, 61029, 66133, 67776, 81683);

        List<Productinfo> productInfoList = productinfoService.list(new QueryWrapper<Productinfo>().lambda().select(Productinfo::getPpriceid1).eq(Productinfo::getPpriceid, ppid));
        if (CollectionUtils.isNotEmpty(productInfoList)) {
            Integer ppid1 = productInfoList.get(0).getPpriceid1();

            Double f = 0.00;
            if (isHalfYear || halfYear.contains(ppid1)) {
                f = 0.16;
            } else if (isOneYear || oneYear.contains(ppid1)) {
                f = 0.08;
            } else if (isTwoYear || twoYear.contains(ppid1)) {
                f = 0.04;
            }

            if (f != 0.00) {
                if (day > 0 && day <= 15) {
                    rePrice = price;
                }
                if (day > 16 && day <= 30) {
                    rePrice = price.multiply(BigDecimal.valueOf(0.8));
                } else if (day > 30 && day <= 60) {
                    rePrice = (price.multiply(BigDecimal.valueOf(0.8))).subtract(price.multiply(BigDecimal.valueOf(f).multiply(BigDecimal.valueOf(1))));
                } else if (day > 60 && day <= 90) {
                    rePrice = (price.multiply(BigDecimal.valueOf(0.8))).subtract(price.multiply(BigDecimal.valueOf(f).multiply(BigDecimal.valueOf(2))));
                } else if (day > 90 && day <= 120) {
                    rePrice = (price.multiply(BigDecimal.valueOf(0.8))).subtract(price.multiply(BigDecimal.valueOf(f).multiply(BigDecimal.valueOf(3))));
                } else if (day > 120 && day <= 150) {
                    rePrice = (price.multiply(BigDecimal.valueOf(0.8))).subtract(price.multiply(BigDecimal.valueOf(f).multiply(BigDecimal.valueOf(4))));
                } else if (day > 150 && day <= 180) {
                    rePrice = (price.multiply(BigDecimal.valueOf(0.8))).subtract(price.multiply(BigDecimal.valueOf(f).multiply(BigDecimal.valueOf(5))));
                } else if (day > 180 && day <= 210) {
                    rePrice = (price.multiply(BigDecimal.valueOf(0.8))).subtract(price.multiply(BigDecimal.valueOf(f).multiply(BigDecimal.valueOf(6))));
                } else if (day > 210 && day <= 240) {
                    rePrice = (price.multiply(BigDecimal.valueOf(0.8))).subtract(price.multiply(BigDecimal.valueOf(f).multiply(BigDecimal.valueOf(7))));
                } else if (day > 240 && day <= 270) {
                    rePrice = (price.multiply(BigDecimal.valueOf(0.8))).subtract(price.multiply(BigDecimal.valueOf(f).multiply(BigDecimal.valueOf(8))));
                } else if (day > 270 && day <= 300) {
                    rePrice = (price.multiply(BigDecimal.valueOf(0.8))).subtract(price.multiply(BigDecimal.valueOf(f).multiply(BigDecimal.valueOf(9))));
                } else if (day > 300 && day <= 330) {
                    rePrice = (price.multiply(BigDecimal.valueOf(0.8))).subtract(price.multiply(BigDecimal.valueOf(f).multiply(BigDecimal.valueOf(10))));
                } else if (day > 330 && day <= 360) {
                    rePrice = (price.multiply(BigDecimal.valueOf(0.8))).subtract(price.multiply(BigDecimal.valueOf(f).multiply(BigDecimal.valueOf(11))));
                } else if (day > 360 && day <= 390) {
                    rePrice = (price.multiply(BigDecimal.valueOf(0.8))).subtract(price.multiply(BigDecimal.valueOf(f).multiply(BigDecimal.valueOf(12))));
                } else if (day > 390 && day <= 420) {
                    rePrice = (price.multiply(BigDecimal.valueOf(0.8))).subtract(price.multiply(BigDecimal.valueOf(f).multiply(BigDecimal.valueOf(13))));
                } else if (day > 420 && day <= 450) {
                    rePrice = (price.multiply(BigDecimal.valueOf(0.8))).subtract(price.multiply(BigDecimal.valueOf(f).multiply(BigDecimal.valueOf(14))));
                } else if (day > 450 && day <= 480) {
                    rePrice = (price.multiply(BigDecimal.valueOf(0.8))).subtract(price.multiply(BigDecimal.valueOf(f).multiply(BigDecimal.valueOf(15))));
                } else if (day > 480 && day <= 510) {
                    rePrice = (price.multiply(BigDecimal.valueOf(0.8))).subtract(price.multiply(BigDecimal.valueOf(f).multiply(BigDecimal.valueOf(16))));
                } else if (day > 510 && day <= 540) {
                    rePrice = (price.multiply(BigDecimal.valueOf(0.8))).subtract(price.multiply(BigDecimal.valueOf(f).multiply(BigDecimal.valueOf(17))));
                } else if (day > 540 && day <= 570) {
                    rePrice = (price.multiply(BigDecimal.valueOf(0.8))).subtract(price.multiply(BigDecimal.valueOf(f).multiply(BigDecimal.valueOf(18))));
                } else if (day > 570 && day <= 600) {
                    rePrice = (price.multiply(BigDecimal.valueOf(0.8))).subtract(price.multiply(BigDecimal.valueOf(f).multiply(BigDecimal.valueOf(19))));
                } else if (day > 600) {
                    rePrice = BigDecimal.ZERO;
                }
            }

        }
        if (rePrice.intValue() < 0) {
            rePrice = BigDecimal.ZERO;
        }

        return rePrice;
    }

    @Override
    public Integer updateCostPriceById(BigDecimal inprice, BigDecimal feiyong, Integer wxId) {
        return shouHouPjMapper.updateCostPriceById(inprice, feiyong, wxId);
    }

    @Override
    public Boolean deleteWxkcoutputByIds(List<Integer> ids) {
        SpringUtil.getBean(DeleteDataRecordService.class).saveDelDataRecord("wxkcoutput", ids);
        return shouHouPjMapper.deleteWxkcoutputByIds(ids);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateShouhouFeiyong(Integer wxId) {
        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();
        if (oaUserBO.getXTenant().equals(0)) {
            shouHouPjService.updateShouhouFeiyongJiuji(wxId);
        } else {
            shouHouPjService.updateShouhouFeiyongSaas(wxId);
        }
    }

    /**
     * 更新九机的售后费用
     * @param wxId
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateShouhouFeiyongJiuji(Integer wxId) {
        //todo 这里数据有问题
        //维修费用
        BigDecimal wxFeiyong = BigDecimal.ZERO;
        //维修成本
        BigDecimal wxCostPrice = BigDecimal.ZERO;
        WxFeiYongBo cost = shouHouPjMapper.getWxFeiYongSum(wxId);
        if (cost != null) {
            if (cost.getFeiyong() != null) {
                wxFeiyong = cost.getFeiyong();
            }
            if (cost.getCostPrice() != null) {
                wxCostPrice = cost.getCostPrice();
            }
        }
        //查询优惠码使用情况
        BigDecimal youhuimaTotal = getShouhouYouhuimaTotal(wxId, wxFeiyong);
        if (youhuimaTotal.compareTo(BigDecimal.ZERO) > 0) {
            wxFeiyong = wxFeiyong.subtract(youhuimaTotal);
        }

        if (wxFeiyong.compareTo(BigDecimal.ZERO) < 0) {
            wxFeiyong = BigDecimal.ZERO;
        }
        shouhouService.update(new UpdateWrapper<Shouhou>().lambda().set(Shouhou::getFeiyong, wxFeiyong)
                .set(Shouhou::getCostprice, wxCostPrice).eq(Shouhou::getId, wxId));
    }

    @Override
    public BigDecimal getShouhouYouhuimaTotal(@NotNull Integer wxId,@NotNull BigDecimal repairFee) {
        //Shouhou shouhou = shouhouService.getById(wxId);
        Shouhou shouhou = CommenUtil.autoQueryHist(() ->shouhouService.getById(wxId), MTableInfoEnum.SHOUHOU, wxId);
        List<NumberCard> numberCards = repairMapper.queryYouHuiMaInfo(shouhou.getId()).stream()
                .filter(nc -> shouhouExService.listYouhuimaPj(nc, shouhou).isSuccess())
                .collect(Collectors.toList());
        BigDecimal youhuiFeiyong = numberCards.stream()
                .map(nr -> {
                    if (Boolean.TRUE.equals(shouhou.getIssoft()) && Objects.equals(nr.getCh999Id(), -39)) {
                        //软件单抵扣所有维修费用
                        return repairFee;
                    }
                    return nr.getTotal();
                })
                .filter(Objects::nonNull).reduce(BigDecimal::add).filter(yhm -> yhm.compareTo(BigDecimal.ZERO) > 0)
                //减去取机之后撤销配件的分摊费用
                .map(yhm -> DecideUtil.iif(Boolean.TRUE.equals(shouhou.getIsquji()),
                        //不会为空,已经做了处理
                        () -> yhm.subtract(wxkcoutputService.sumCancelPjYouHuiFeiyong(wxId)), () -> yhm))
                .orElse(BigDecimal.ZERO);
        //加上三方的优惠费用
        List<ThirdOriginRefundVo> thirdOriginRefundVos = SpringUtil.getBean(ThirdOriginWayService.class).listAll(wxId, TuihuanKindEnum.TWXF);
        BigDecimal totalThirdCouponPrice = thirdOriginRefundVos.stream()
                .peek(tor -> SpringContextUtil.addRequestKeyMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG,"[{}]收银id[{}]已退金额[{}],金额[{}]优惠费用[{}]",
                        tor.getReturnWayName(),tor.getShouyingId(),tor.getRefundedPrice(),tor.getRefundPrice(), tor.getCouponPrice()))
                .map(tor -> {
                    //已退金额,等比例扣减优惠费用
                    BigDecimal actualPayPrice = ObjectUtil.defaultIfNull(tor.getActualPayPrice(), BigDecimal.ZERO);
                    BigDecimal refundedPrice = ObjectUtil.defaultIfNull(tor.getRefundedPrice(), BigDecimal.ZERO);
                    BigDecimal couponPrice = ObjectUtil.defaultIfNull(tor.getCouponPrice(), BigDecimal.ZERO);
                    if(refundedPrice.compareTo(BigDecimal.ZERO) <=0){
                        return couponPrice;
                    }
                    return NumberUtil.max(couponPrice.multiply(BigDecimal.ONE.subtract(refundedPrice.divide(actualPayPrice, 4, RoundingMode.DOWN)))
                            .setScale(0,RoundingMode.DOWN),BigDecimal.ZERO);
                })
                .filter(ObjectUtil::isNotNull).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        youhuiFeiyong = youhuiFeiyong.add(totalThirdCouponPrice);
        if(youhuiFeiyong.compareTo(repairFee)>0){
            //优惠码费用大于维修费用,取维修费用
            return repairFee;
        }
        return youhuiFeiyong;
    }

    /**
     * 更新输出的售后费用
     * @param wxId
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateShouhouFeiyongSaas(Integer wxId) {

        //Shouhou shouhou = shouhouService.getById(wxId);
        Shouhou shouhou = CommenUtil.autoQueryHist(() ->shouhouService.getById(wxId), MTableInfoEnum.SHOUHOU, wxId);
        Integer wxAreaId = shouhou.getAreaid();

        R<AreaInfo> areaInfoR = areaInfoClient.getAreaInfoById(wxAreaId);
        if (areaInfoR.getCode() != ResultCode.SUCCESS || areaInfoR.getData() == null) {
            throw new CustomizeException(StrUtil.format("获取门店[{}]信息返回[{}]异常",wxAreaId,areaInfoR.getUserMsg()));
        }

        AreaInfo areaInfo = areaInfoR.getData();

        BigDecimal wxFeiyong = BigDecimal.ZERO;
        BigDecimal wxCostPrice = BigDecimal.ZERO;
        WxFeiYongBo cost = shouHouPjMapper.getWxFeiYongSum(wxId);
        if (areaInfo.getXtenant() / 1000 == 1) {
            cost = shouHouPjMapper.getWxFeiYongSumZlf(wxId);
        }
        if (cost != null) {
            if (cost.getFeiyong() != null) {
                wxFeiyong = cost.getFeiyong();
            }
            if (cost.getCostPrice() != null) {
                wxCostPrice = cost.getCostPrice();
            }
        }

        BigDecimal youhuimaTotal = getShouhouYouhuimaTotal(wxId, wxFeiyong);
        if (youhuimaTotal.compareTo(BigDecimal.ZERO) > 0) {
            wxFeiyong = wxFeiyong.subtract(youhuimaTotal);
        }

        if (wxFeiyong.compareTo(BigDecimal.ZERO) < 0) {
            wxFeiyong = BigDecimal.ZERO;
        }

        shouhouService.update(new UpdateWrapper<Shouhou>().lambda().set(Shouhou::getFeiyong, wxFeiyong)
                .set(Shouhou::getCostprice, wxCostPrice).eq(Shouhou::getId, wxId));
    }


    @Override
    public QujiBo getQuji(Integer id) {
        return shouHouPjMapper.getQuji(id);
    }

    @Override
    public Boolean checkQuji(Integer id) {
        return shouHouPjMapper.checkQuji(id) > 0;
    }

    @Override
    public Boolean checkQujiToArea(Integer id) {
        return shouHouPjMapper.checkQujiToArea(id) > 0;
    }

    @Override
    public Boolean hasGcLog(Integer shouhouId, Integer type) {
        List<ShouhouLogBo> logList = shouhouService.getShouhouLogs(shouhouId);
        if (CollectionUtils.isNotEmpty(logList)) {
            Optional<ShouhouLogBo> optional = logList.stream().filter(t -> Objects.equals(t.getType(),type)).findAny();
            if (optional.isPresent()) {
                return true;
            }
        }

        return false;
    }

    @Override
    public R<String> dealQuji(Integer shouhouId, String inuser) {
        //物流单信息校验 todo xxk

        //查询是否有在使用代用机
        Shouhou shouhouInfo = queryDaiyongjiUseInfo(shouhouId);
        if (shouhouInfo != null) {
            if (shouhouInfo.getDyjid() != null && shouhouInfo.getDyjid() > 0) {
                return R.error("请先收回代用机再操作");
            } else if (shouhouInfo.getYifum().compareTo(BigDecimal.ZERO) > 0) {
                return R.error("订单已经有付款记录，不可再删除操作");
            }
        }
        //组合退校验
        //是否存在审核中的退订信息。如果存在，则toast提示【请先审核或删除退订申请，再进行取机】，且取机失败。如果不存在，则正常取机完成。
        Integer isTui = SpringUtil.getBean(ShouhouMapper.class).canChangeImei(shouhouId);
        if (Boolean.FALSE.equals(CommenUtil.isNullOrZero(isTui))) {
            return R.error("存在退款退换记录，请取消后再删除订单");
        }
        Integer count = wxkcoutputService.count(new QueryWrapper<Wxkcoutput>().lambda()
                .eq(Wxkcoutput::getWxid, shouhouId).ne(Wxkcoutput::getStats, 3));
        if (count > 0) {
            return R.error("请先撤销已出库维修配件在操作");
        }
        //更新售后取机状态信息
        count = updateShouhouQujiStats(shouhouId);
        if (count == 0) {
            return R.error("删除失败");
        }
        //删除配件订购申请记录 xxk
        String sapLogMsg = StrUtil.format("<li>【删除操作】 原因：维修单删除<em>{}</em>[{}]</li>", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")),inuser);
        shouHouPjMapper.updateToDelShouhouApply(shouhouId,sapLogMsg);
        shouhouService.saveShouhouLog(shouhouId, "*·【删除操作】", inuser, null, true);
        Integer yuyueId = shouHouPjMapper.getShouhouYuyueId(shouhouId);
        if (CommenUtil.isNotNullZero(yuyueId)) {
            Boolean flag = shouhouYuyueService.update(new UpdateWrapper<ShouhouYuyue>().lambda().set(ShouhouYuyue::getIsdel, 1).eq(ShouhouYuyue::getId, yuyueId));
            if (flag) {
                yuyueLogsService.yuyueLogsAdd(yuyueId, "删除维修单", inuser, 0);
            }
            //删除关联的预约单的优惠码
            cancelYouhuima(yuyueId);

        }
        //如果使用过优惠码，则返回优惠码(针对维修单)
        shouHouPjMapper.updateNumberCardInfo(shouhouId);
        cardLogsService.remove(new QueryWrapper<CardLogs>().lambda()
                .eq(CardLogs::getSubId, shouhouId)
                .eq(CardLogs::getUseType, 1));

        //Shouhou shouhou = shouhouService.getById(shouhouId);
        Shouhou shouhou = CommenUtil.autoQueryHist(() ->shouhouService.getById(shouhouId), MTableInfoEnum.SHOUHOU, shouhouId);
        Integer yuyueid = shouhou.getYuyueid();
        if (CommenUtil.isNotNullZero(yuyueid)) {
            ShouhouYuyue shouhouYuyue = shouhouYuyueService.getById(yuyueid);
            List delStateList = new ArrayList<>();
            if(shouhouYuyueService.isUseNewYuYue()){
                delStateList.add(YuyueStatusEnum.YWQR.getCode());
            } else {
                delStateList.add(YuyueStatusEnum.YDD.getCode());
            }
            if(delStateList.contains(shouhouYuyue.getStats())) {
                R<Boolean> dyR = shouhouYuyueService.delYuyue(yuyueid, "删除维修单", "删除维修单", inuser, null);
                if (!dyR.isSuccess()) {
                    TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                    return R.error(dyR.getUserMsg());
                }
                //新版预约单不使用以下逻辑
                if(!shouhouYuyueService.isUseNewYuYue()){
                    shouhouYuyueService.update(new LambdaUpdateWrapper<ShouhouYuyue>()
                            .set(ShouhouYuyue::getStats, 5)
                            .set(ShouhouYuyue::getIsdel, 0)
                            .eq(ShouhouYuyue::getId, yuyueid));
                }

            }

        }
        return R.success("操作成功");
    }

    @Override
    public Shouhou queryDaiyongjiUseInfo(Integer shouhouId) {
        return shouHouPjMapper.queryDaiyongjiUseInfo(shouhouId);
    }

    @Override
    public Integer updateShouhouQujiStats(Integer shouhouId) {
        return shouHouPjMapper.updateShouhouQujiStats(shouhouId);
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void cancelYouhuima(Integer yuyueId) {
        YouHuiMaBo youHuiMaBo = shouHouPjMapper.getCancelYouhuimaIdInfo(yuyueId);
        if (youHuiMaBo != null) {
            cardLogsService.removeById(youHuiMaBo.getCid());
            //
            numberCardService.update(new UpdateWrapper<NumberCard>().lambda()
                    .set(NumberCard::getUseCount, 0).set(NumberCard::getState, 0).eq(NumberCard::getId, youHuiMaBo.getNid()));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<Boolean> editWxPj(Integer shouhouId, String act, String imei, Integer kcOutId, Integer
            productId,
                               Integer fmStats, Integer oldStats, Integer tuiType) {

        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();
        if (oaUserBO == null) {
            return R.unLogin();
        }
        String inuser = oaUserBO.getUserName();
        if (StringUtils.isNotEmpty(act)) {
            if (act.equals("webstats2")) {
                return webstats2(shouhouId, inuser);
            } else if (act.equals("webstats3")) {
                return webstats3(shouhouId, inuser, imei);
            } else if (act.equals("webstats4")) {
                return webstats4(shouhouId, inuser);
            } else if (act.equals("webstats5")) {
                return webstats5(shouhouId, inuser);
            } else if (act.equals("acept")) {
                if (!oaUserBO.getRank().contains("58")) {
                    return R.error("无权操作！权限：58");
                }
                if (!acept(kcOutId)) {
                    return R.error("审核操作失败");
                }
            } else if (act.equals("wxkcid")) {
                WxKcBo wxkc = getWxkcOutput(shouhouId);
                if (wxkc == null) {
                    return R.error("维修单号错误！");
                } else {
                    return R.success(wxkc.getWxid().toString());
                }
            } else if (act.equals("tui")) {

                //Wxkcoutput wxkc = wxkcoutputService.getById(kcOutId);
                Wxkcoutput wxkc = CommenUtil.autoQueryHist(() ->wxkcoutputService.getById(kcOutId), MTableInfoEnum.wxkcoutput,kcOutId);
                if (wxkc == null) {
                    return R.error("维修库存信息不存在");
                }
                if (!oaUserBO.getRank().contains("6e6")) {
                    return R.error("无权操作！权限：6e6");
                }
                R<Integer> tuiR = tui(kcOutId, productId,tuiType);
                if (tuiR.getCode() == ResultCode.SUCCESS) {
                    //关联服务退款
                    if(XtenantEnum.isJiujiXtenant()){
                        try {
                           // List<Wxkcoutput> wxkcoutputList = wxkcoutputService.lambdaQuery().eq(Wxkcoutput::getBindId, kcOutId).list();
                            List<Wxkcoutput> wxkcoutputList =CommenUtil.autoQueryHist(()->wxkcoutputService.lambdaQuery().eq(Wxkcoutput::getBindId, kcOutId).list(),MTableInfoEnum.WXKCOUTPUT_BIND_ID,kcOutId);
                            if(CollUtil.isNotEmpty(wxkcoutputList)){
                                wxkcoutputList.forEach(item->{
                                    R<Integer> tui = tui(item.getId(), NumberConstant.ZERO, NumberConstant.ZERO);
                                    if(tui.getCode() != ResultCode.SUCCESS){
                                        throw new CustomizeException(tui.getUserMsg());
                                    }
                                });
                            }
                        }catch (Exception e){
                            RRExceptionHandler.logError("服务关联撤销失败："+e.getMessage(),kcOutId,e,smsService::sendOaMsgTo9JiMan);
                        }
                    }
                    return R.success("操作成功");
                } else {
                    return R.error(tuiR.getUserMsg());
                }
            } else if (act.equals("del")) {
                // 九机：撤销维修配件，撤销配件订购申请
                if(XtenantEnum.isJiujiXtenant()) {
                    // 获取维修单下的所有配件信息
                    List<HexiaoBo> wxkcoutputList = wxkcoutputService.getHexiao(shouhouId);
                    if(CollUtil.isNotEmpty(wxkcoutputList)){
                        wxkcoutputList.forEach(item->{
                            R<Integer> tui = tui(item.getId(), NumberConstant.ZERO, NumberConstant.ZERO);
                            if(tui.getCode() != ResultCode.SUCCESS){
                                throw new CustomizeException("删除失败：" + tui.getUserMsg());
                            }
                        });
                    }
                    //删除配件订购申请
                    List<HexiaoBo> shouhouApplyList = shouhouApplyService.getShouhouApplyHexiaoBo(shouhouId);
                    if(CollUtil.isNotEmpty(shouhouApplyList)){
                        shouhouApplyList.forEach(item->{
                            ApplyDelReq applyDelReq = new ApplyDelReq();
                            applyDelReq.setId(item.getId());
                            applyDelReq.setComment("删除维修单：" + shouhouId + "关联删除");
                            R<String> applyDel = repairService.applyDel(applyDelReq);
                            if(applyDel.getCode() != ResultCode.SUCCESS){
                                throw new CustomizeException("删除失败：" + applyDel.getUserMsg());
                            }
                        });
                    }
                }
                R<String> delR = dealQuji(shouhouId, oaUserBO.getUserName());
                if (delR.getCode() != ResultCode.SUCCESS) {
                    TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                    return R.error(delR.getUserMsg());
                }
            }

            return R.success("操作成功");
        }

        ShouhouBo info = shouhouService.getOne(shouhouId);
        if (info == null) {
            return R.error("维修单不存在");
        }

        // todo 暂时注释
//        insertSearchHistory(oaUserBO.getUserId(), shouhouId + "|2", "oaSearch");

        //判断购买地区
        if ((info.getBuyareaid() == null || info.getBuyareaid() == 0) && info.getSubId() != null && info.getSubId() != 0) {
            //表示关联了订单，但是没有购买地区(有可能是退换机生成的维修单)
            //更改售后单购买地区
            shouhouExService.changeBuyAreaId(shouhouId, info.getSubId(), info.getIshuishou());
        }
        if (info.getEarnestMoneySubid() != null && info.getEarnestMoneySubid() > 0) {

            //获取订单基本信息
            Sub sub = subService.getOne(new LambdaQueryWrapper<Sub>().select(Sub::getSubId, Sub::getSubCheck, Sub::getYingfuM, Sub::getYifuM).eq(Sub::getSubId, info.getEarnestMoneySubid()));
            ShouhouWxpjRes.Subrow subrow = new ShouhouWxpjRes.Subrow();
            subrow.setSubCheck(sub.getSubCheck());
            subrow.setSubId(sub.getSubId());
            subrow.setYifuM(sub.getYifuM());
            subrow.setYingfuM(sub.getYingfuM());
        }

        return R.success("操作成功");
    }

    @Override
    public R<Boolean> webstats2(Integer id, String inuser) {
        List<Shouhou> shouhouList = CommenUtil.autoQueryHist(()->shouhouService.list(new QueryWrapper<Shouhou>().lambda()
                .select(Shouhou::getWebstats).select(Shouhou::getAreaid)
                .eq(Shouhou::getId, id).eq(Shouhou::getWebstats, 1)),MTableInfoEnum.SHOUHOU,id);
//        List<Shouhou> shouhouList = shouhouService.list(new QueryWrapper<Shouhou>().lambda()
//                .select(Shouhou::getWebstats).select(Shouhou::getAreaid)
//                .eq(Shouhou::getId, id).eq(Shouhou::getWebstats, 1));
        if (CollectionUtils.isNotEmpty(shouhouList)) {
            Shouhou shouhou = shouhouList.get(0);
            if (shouhou.getAreaid() == null) {
                return R.error("请先选择并保存接件地！");
            }
            Boolean flag = shouhouService.update(new UpdateWrapper<Shouhou>().lambda().set(Shouhou::getWebstats, 2).eq(Shouhou::getId, id));
            if (flag) {
                shouhouService.saveShouhouLog(id, "*·【预约审核】操作", inuser, null, true);
            }
        }
        return R.success("操作成功");
    }

    @Override
    @Transactional
    public R<Boolean> webstats3(Integer id, String inuser, String imei) {
        Shouhou shouhou = CommenUtil.autoQueryHist(() ->shouHouPjMapper.queryShouhouShoujianInfo(id), MTableInfoEnum.SHOUHOU, id);
        if (shouhou == null) {
            return R.error("错误的维修单号");
        }
        if (StringUtils.isEmpty(shouhou.getProblem())) {
            return R.error("故障描述为空，请填写故障描述");
        }
        if(Shouhou.WaiGuanStatusEnum.MO_SHUN.getCode().equals(shouhou.getWaiGuanStatus())
                && StrUtil.isBlank(shouhou.getWaiguan())){
            return R.error("请先填写外观描述");
        }
        if (CommenUtil.isNullOrZero(shouhou.getBaoxiu()) || shouhou.getBaoxiu().equals(2)) {
            Boolean onDoorFlag = false;
            if (shouhou.getYuyueid() != null && shouhou.getYuyueid() > 0) {
                Integer count = shouhouYuyueService.count(new QueryWrapper<ShouhouYuyue>().lambda().eq(ShouhouYuyue::getId, shouhou.getYuyueid()).eq(ShouhouYuyue::getStype, 6));
                onDoorFlag = count > 0;
            }
            if (!onDoorFlag) {
                List<Attachments> attachmentsList = attachmentsService.getAttachmentsByLinkId(id, AttachmentsEnum.SHOUHOU.getCode(),null);
                if (attachmentsList.size() < 2) {
                    return R.error("不在保、外修，待检测设备必须上传正反两面照片！");
                }
            }
        }

        if (shouhou.getAreaid() == null) {
            return R.error("请先选择接件地！");
        }
        Shouhou dt = CommenUtil.autoQueryHist(()->shouHouPjMapper.getShouhouIsticheng(imei));
        Integer wcount = 0;
        Integer isticheng = 1;
        if (dt != null) {
            wcount = 1; //getShouhouIsticheng 取top1
            Duration duration = Duration.between(LocalDateTime.now(), dt.getOfftime() == null ? LocalDateTime.now() : dt.getOfftime());
            isticheng = duration.toDays() < 30 ? 0 : 1;
        }
        Boolean flag = shouhouService.update(new UpdateWrapper<Shouhou>().lambda()
                .set(Shouhou::getStats, 0)
                .set(Shouhou::getWebstats, 3)
                .set(Shouhou::getWcount, wcount)
                .set(Shouhou::getIsticheng, isticheng)
                .set(Shouhou::getModidate, LocalDateTime.now())
                .set(Shouhou::getInuser, inuser)
                .set(Shouhou::getWeixiuzuid, 4)
                .eq(Shouhou::getId, id));
        if (flag) {
            //做库存处理
            try {
                R<Boolean> kcResR = this.wxpjNolockOutkc(id, inuser);
                if (kcResR == null || kcResR.getCode() != ResultCode.SUCCESS) {
                    TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                    return R.error(kcResR == null ? "库存处理出现异常！" : kcResR.getUserMsg());
                }
                shouhouService.saveShouhouLog(id, "*·【设备已收到，稍后工程师进行检测。】", inuser, null, false);
            } catch (Exception e) {
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();

                return R.error(ExceptionUtils.getRootCause(e).getMessage());
            }
        }
        R<AreaInfo> areaInfoR = areaInfoClient.getAreaInfoById(shouhou.getAreaid());
        if (areaInfoR.getCode() != ResultCode.SUCCESS || areaInfoR.getData() == null) {
            return R.error("查询区域信息出错");
        }
        if (areaInfoR.getData().getIsSend()) {
            String area = "";
            R<AreaInfo> areaInfoRA = areaInfoClient.getAreaInfoByArea(shouhou.getAreaid().toString());
            if (areaInfoRA.getCode() == ResultCode.SUCCESS && areaInfoRA.getData() != null) {
                area = areaInfoRA.getData().getAreaName();
            }

            Integer areaId = shouhou.getAreaid() == null ? 0 : shouhou.getAreaid();
            String areaId1 = StringUtils.isEmpty(shouhou.getArea()) ? "0" : shouhou.getArea();


            R<AreaInfo> curAreaH1R = areaInfoClient.getCurAreaH1(shouhou.getAreaid());
            R<AreaInfo> curAreaDCR = areaInfoClient.getCurAreaDC(shouhou.getAreaid());

            Boolean isDc = Boolean.FALSE;
            if (curAreaDCR.getCode() == ResultCode.SUCCESS && curAreaH1R.getCode() == ResultCode.SUCCESS) {
                if (curAreaH1R.getData().getId().equals(areaId) || curAreaDCR.getData().getId().equals(areaId)) {
                    isDc = Boolean.TRUE;
                }
            }
            String areaName = isDc ? areaInfoR.getData().getPrintName() + "售后服务中心" : area;
            String msg = "接件成功通知\n启奏陛下！我们已经收到您的爱机啦，我们将立即安排检测维修！\n接件时间：" + DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm").format(LocalDateTime.now()) + "\n接件地点：" + areaName + "\n想了解更多，点击“<a href=\"" + areaInfoR.getData().getMUrl() + "/user/mysevrdetail.aspx?id=" + id + "\">查看详情</a>”\n有任何问题在微信中找客服mm！";

            if (shouhou.getUserid() != null) {
                shouhouMsgService.sendWeixin(shouhou.getUserid().toString(), "", msg, 2);//主体已处理
            }

        }
        R<Boolean> success = R.success("操作成功");
        //收件之后把预约单状态修改为完成
        ShouhouYuyueService yuyueService = SpringUtil.getBean(ShouhouYuyueService.class);
        if(yuyueService.isUseNewYuYue()){
            Integer yuyueid = shouhou.getYuyueid();
            if(ObjectUtil.isNotNull(yuyueid)){
                Optional.ofNullable(yuyueService.getById(yuyueid)).ifPresent(item->{
                    List<Integer> list = Arrays.asList(YuyueStatusEnum.YSM.getCode(), YuyueStatusEnum.YDD.getCode(), YuyueStatusEnum.YWQR.getCode(), YuyueStatusEnum.KFQR.getCode());
                    //判断是否可以修改预约单状态
                    if(list.contains(item.getStats())){
                        //调用预约单已完成接口
                        R<Boolean> booleanR = yuyueService.yuyueconfirmSmDdEnter(yuyueid, NumberConstant.ONE, Boolean.FALSE);
                        if(booleanR.isSuccess()){
                            Map<String, Object> map = Optional.ofNullable(booleanR.getExData()).orElse(new HashMap<>());
                            success.setExData(map);
                            shouhouService.saveShouhouLog(id, "维修单收件完成对应的预约单", inuser, null, false);
                        } else {
                            throw new CustomizeException(booleanR.getUserMsg());
                        }
                    }
                });
            }
        }
        return success;
    }

    @Override
    public R<Boolean> webstats4(Integer id, String inuser) {
        Shouhou shouhou = shouhouService.getOne(new QueryWrapper<Shouhou>().lambda()
                .select(Shouhou::getWebstats)
                .select(Shouhou::getIsquji)
                .eq(Shouhou::getWebstats, 3)
                .eq(Shouhou::getId, id));
        if (shouhou != null) {
            if (shouhou.getIsquji()) {
                Boolean flag = shouhou.update(new UpdateWrapper<Shouhou>().lambda().set(Shouhou::getWebstats, 4).eq(Shouhou::getWebstats, 3).eq(Shouhou::getId, id));
                if (flag) {
                    shouhouService.saveShouhouLog(id, "*·【发货操作】", inuser, null, true);
                }
            } else {
                return R.error("取机操作后才可发货！");
            }
        }
        return R.success("操作成功");
    }

    @Override
    public R<Boolean> webstats5(Integer id, String inuser) {
        Shouhou shouhou = shouhouService.getOne(new QueryWrapper<Shouhou>().lambda()
                .select(Shouhou::getWebstats)
                .select(Shouhou::getIsquji)
                .eq(Shouhou::getWebstats, 4)
                .eq(Shouhou::getId, id));
        if (shouhou != null) {
            if (shouhou.getIsquji()) {
                Boolean flag = shouhou.update(new UpdateWrapper<Shouhou>().lambda().set(Shouhou::getWebstats, 5).eq(Shouhou::getWebstats, 4).eq(Shouhou::getId, id));
                if (flag) {
                    shouhouService.saveShouhouLog(id, "*·【客户收货确认】", inuser, null, true);
                }
            } else {
                return R.error("取机操作后才可确认！");
            }
        }
        return R.success("操作成功");
    }

    @Override
    public Boolean acept(Integer id) {
        return shouHouPjMapper.acept(id) > 0;
    }

    @Override
    @Transactional
    public R<Boolean> wxpjNolockOutkc(Integer shouhouId, String user) {
//        List<Wxkcoutput> kcList = wxkcoutputService.list(new LambdaQueryWrapper<Wxkcoutput>()
//                .eq(Wxkcoutput::getWxid, shouhouId).and(bo -> bo.isNotNull(Wxkcoutput::getPpriceid).ne(Wxkcoutput::getPpriceid, 0)));
        List<Wxkcoutput> kcList =CommenUtil.autoQueryHist(()->wxkcoutputService.list(new LambdaQueryWrapper<Wxkcoutput>()
                .eq(Wxkcoutput::getWxid, shouhouId).and(bo -> bo.isNotNull(Wxkcoutput::getPpriceid).ne(Wxkcoutput::getPpriceid, 0))),MTableInfoEnum.WXKCOUTPUT_WXID,shouhouId);
        if (CollectionUtils.isNotEmpty(kcList)) {
            kcList = kcList.stream().filter(e -> e.getPpriceid() != null && e.getPpriceid() > 0).collect(Collectors.toList());
            kcList.stream().forEach(kc -> {

                if (kc.getIslockkc() != null && kc.getIslockkc()) {

                    //如果是库存锁定，解除库存锁定1个
                    Boolean unLockWxKc = wxkcoutputService.update(new LambdaUpdateWrapper<Wxkcoutput>()
                            .set(Wxkcoutput::getIslockkc, null).set(Wxkcoutput::getOutputDtime, LocalDateTime.now())
                            .eq(Wxkcoutput::getId, kc.getId()).eq(Wxkcoutput::getIslockkc, Boolean.TRUE));
                    if(!Boolean.TRUE.equals(unLockWxKc)){
                        throw new CustomizeException(StrUtil.format("ppid[{}]已出库存,请勿重复出库",kc.getPpriceid()));
                    }
                    //出库 new apiServices().product_kc
//                    SmallproNormalCodeMessageRes codeMessageRes = smallproForwardExService.stockOperations(kc.getPpriceid(), -1, kc.getInprice(), kc.getAreaid(), user, "", "维修出库,售后单:" + shouhouId, null, 0, 0, kc.getId(), false, false);

                    OperateProductKcPara para = new OperateProductKcPara();
                    para.setPpid(kc.getPpriceid());
                    para.setCount(-1);
                    para.setInprice(kc.getInprice());
                    para.setAreaId(kc.getAreaid());
                    para.setInuser(user);
                    para.setInsource("");
                    para.setComment("维修出库,售后单:" + shouhouId);
                    para.setBasketId(null);
                    para.setCheck1(false);
                    para.setCheck2(false);
                    para.setShouhouId(Long.valueOf(kc.getId()));
                    para.setIsLp(false);
                    para.setDiaoboFlag(false);
                    productKcService.unlockKc(kc.getPpriceid(), kc.getAreaid());
                    R<OperateProductKcRes> productKcR = productKcService.operateProductKc(para);
                    if (ResultCode.SUCCESS != productKcR.getCode()) {
                        TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                        throw new CustomizeException(String.format("操作库存失败【%s】,ppid:%s", productKcR.getUserMsg(),kc.getPpriceid()));
                    }

                    BigDecimal inprice = productKcR.getData().getInprice();

                    wxkcoutputService.update(new LambdaUpdateWrapper<Wxkcoutput>().set(Wxkcoutput::getInprice, inprice).eq(Wxkcoutput::getId, kc.getId()));
                    shouhouExService.updateCostPriceById(shouhouId);
                }

            });
        }

        return R.success("操作成功");
    }

    @Override
    public Boolean insertSearchHistory(Integer ch999Id, String searchKey, String kind) {
        if (ch999Id == 0) {
            return true;
        }
        try {

            if (StringUtils.isEmpty(kind)) {
                kind = "Moa_Search";
            }
            String newId = kind + ch999Id;

            redisTemplate.opsForZSet().add(newId, searchKey, DateUtil.getTicks());
        } catch (Exception e) {
            return false;
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<Boolean> gouMaiPj(Integer wxId, String title, String content, String quDao, String
            link, Integer
                                       ppid,
                               Boolean autoDiaobo) {
        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();
        if (oaUserBO == null) {
            return R.unLogin();
        }

        ShouhouApply apply = new ShouhouApply();
        apply.setWxid(wxId);
        apply.setTitle(title);
        apply.setContent(content);
        apply.setLink(link);
        apply.setInuser(oaUserBO.getUserName());
        apply.setAreaid(oaUserBO.getAreaId());
        apply.setQudao(quDao);
        apply.setAddTime(LocalDateTime.now());
        apply.setCaigouid(0);
        apply.setKindstats(1);
        apply.setPpid(ppid);

        shouhouApplyService.save(apply);
        Integer applyId = apply.getId();

        //Shouhou shouhou = shouhouService.getById(wxId);
        Shouhou shouhou = CommenUtil.autoQueryHist(() ->shouhouService.getById(wxId), MTableInfoEnum.SHOUHOU, wxId);
        if (shouhou != null) {
            ShouhouMsgPushMessageBo message = new ShouhouMsgPushMessageBo();
            message.setMsgId(6);
            message.setShouhouId(shouhou.getId());
            message.setAreaId(shouhou.getAreaid());
            message.setUserId(shouhou.getUserid().intValue());
            message.setOptUser(oaUserBO.getUserName());
            Map<String, String> tmpData = new HashMap<>();
            tmpData.put("wxpeijian", title);
            tmpData.put("wxliyou", content);

            if (CommenUtil.isNotNullZero(ppid)) {
                List<Productinfo> productinfoList = productinfoService.list(new LambdaQueryWrapper<Productinfo>().select(Productinfo::getMemberprice)
                        .eq(Productinfo::getPpriceid, ppid));
                if (CollectionUtils.isNotEmpty(productinfoList)) {
                    tmpData.put("wxpeijianprice", productinfoList.get(0).getMemberprice().toString());
                }
            }
            shouhouService.pushMessage(message, true);
        }
        //九机自营店面提交订购，根据物流路线，同城找地区，找不到则再判断dc是否有库存，然后自动生成调拨自动出库
        if (autoDiaobo && oaUserBO.getAreaKind1().equals(1) && CommenUtil.isNotNullZero(ppid)) {
            Integer wlAreaid = 0;

            //根据物流路线，同城路线获取地区
            //todo sameCityMkcid 同城订单自动调货

        }

        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<Boolean> bindPpidOutPutKc(ShouhouPpidBindOutPut req) {
        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();
        //绑定配件出库逻辑
        Integer shouhouId = req.getShouhouId();
        //Shouhou sh = shouhouService.getById(shouhouId);
        Shouhou sh = CommenUtil.autoQueryHist(() ->shouhouService.getById(shouhouId), MTableInfoEnum.SHOUHOU, shouhouId);
        Integer areaId = sh.getAreaid();
        List<ShouhouPpidBindOutPut.OutPutItem> items = req.getItems();
        if (CollectionUtils.isEmpty(items)) {
            return R.error("绑定配件项不能为空");
        }
        List<Integer> ppidList = req.getItems().stream().map(e -> e.getPpid()).collect(Collectors.toList());
        //查询门店库存信息
        List<ProductKc> productKcList = productKcService.list(new LambdaQueryWrapper<ProductKc>().in(ProductKc::getPpriceid, ppidList).eq(ProductKc::getAreaid, areaId));

        Map<Integer, Productinfo> productMapByPpids = productinfoService.getProductMapByPpids(ppidList);

        if (CollectionUtils.isEmpty(productKcList) || productKcList.size() != items.size()) {
            return R.error("门店配件信息不存在");
        }
        List<WxFeeBo> wxkcList = productKcList.stream().map(p -> {
            WxFeeBo fee = new WxFeeBo();
            fee.setAreaId(areaId);
            fee.setInprice(p.getInprice());
            fee.setPrice1(p.getInprice1());
            fee.setProductName(productMapByPpids.get(p.getPpriceid()) != null ? productMapByPpids.get(p.getPpriceid()).getProductName() : "");
            fee.setShouhouId(shouhouId);
            fee.setUser(oaUserBO.getUserName());
            return fee;
        }).collect(Collectors.toList());

        //添加维修费用
        wxkcList.stream().forEach(w -> {
            R<ShouhouCostPriceRes> shouhouCostPriceResR = shouhouService.addCostPrice(w, Boolean.FALSE, Boolean.FALSE);
            if (shouhouCostPriceResR.getCode() != ResultCode.SUCCESS) {
                throw new RRException(shouhouCostPriceResR.getUserMsg());
            }
        });


        return R.success("操作成功");
    }

}
