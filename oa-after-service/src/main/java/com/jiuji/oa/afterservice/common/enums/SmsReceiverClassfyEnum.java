package com.jiuji.oa.afterservice.common.enums;

import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 消息类型 替换为公共的枚举
 * @see com.jiuji.tc.utils.enums.sms.SmsReceiverClassfyEnum
 * @author: gengjiaping
 * @date: 2020/3/18
 */
@Getter
@AllArgsConstructor
@Deprecated
public enum SmsReceiverClassfyEnum implements CodeMessageEnumInterface {
    SMQJFZR("15","上门取件负责人"),
    SMKX("16","上面快修负责人"),
    YYDD_QR_SEND("79","小件预约到店预约单确认推送"),

    MOBILE_YYDD_QR_SEND("80","大件预约到店预约单确认推送"),

    SMQJ_QR_SEND("81","上门取件预约单确认推送"),

    YJSX_QR_SEND("82","邮寄送修预约单确认推送"),
    AFTER_SALES_REPORT_ALL_DEPART("91","售后报表接收（全区）"),
    DOU_DIAN_PRODUCT_SYSNC("92","抖音商城商品信息同步"),

    ;
    /**
     * 类别
     */
    private String code;
    /**
     * 名称
     */
    private String message;
}
