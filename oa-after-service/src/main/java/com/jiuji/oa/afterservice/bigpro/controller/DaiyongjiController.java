package com.jiuji.oa.afterservice.bigpro.controller;

import cn.hutool.core.lang.Dict;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jiuji.oa.afterservice.bigpro.machinehero.vo.req.ForwardMqttReq;
import com.jiuji.oa.afterservice.bigpro.service.DaiyongjiService;
import com.jiuji.oa.afterservice.bigpro.service.WeixinUserService;
import com.jiuji.oa.afterservice.bigpro.vo.req.DaiYongJiListReq;
import com.jiuji.oa.afterservice.bigpro.vo.req.dyj.CancelDaiYongjiReq;
import com.jiuji.oa.afterservice.bigpro.vo.res.DaiYongJiInfoRes;
import com.jiuji.oa.afterservice.bigpro.vo.res.DaiYongJiListRes;
import com.jiuji.oa.afterservice.common.aspect.RepeatSubmitCheck;
import com.jiuji.oa.afterservice.common.bo.OaUserBO;
import com.jiuji.oa.afterservice.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.tc.common.vo.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.math.BigDecimal;

/**
 * 备用机
 *
 * <AUTHOR> quan
 * @date 2020-05-09 16:44:33
 */
@Api(tags = "售后：备用机相关")
@RestController
@RequestMapping("bigpro/daiyongji")
public class DaiyongjiController {

    private final static String MQTT_TOPIC = "daiyongji-saveSignature-";

    @Autowired
    private DaiyongjiService daiyongjiService;
    @Autowired
    private WeixinUserService weixinUserService;
    @Resource
    private AbstractCurrentRequestComponent abstractCurrentRequestComponent;


    @GetMapping("/forwardMqtt")
    @ApiOperation(value = "代用机签署协议校验")
    public R<String> forwardMqtt(@RequestParam("dyjId") String dyjId){
        ForwardMqttReq req = new ForwardMqttReq();
        req.setCodeStr(dyjId.toString());
        req.setTopic(MQTT_TOPIC + dyjId);
        return R.success(daiyongjiService.forwardMqtt(req));
    }

    @ApiOperation(value = "查询备用机列表")
    @PostMapping("/getDaiYongJiPage")
    public R<Page<DaiYongJiListRes>> getDaiYongJiPage(@RequestBody DaiYongJiListReq req){
        return daiyongjiService.getDaiYongJiPage(req);
    }

    @ApiOperation(value = "备用机申请提交")
    @PostMapping("/applyDaiyongji")
    public R<Boolean> applyDaiyongji(Integer wxid, Integer dyjId, BigDecimal yajin){
        return daiyongjiService.applyDaiyongji(wxid,dyjId,yajin);
    }
    @ApiOperation(value = "获取备用机详情")
    @PostMapping("/getDaiYongJiById")
    public R<DaiYongJiInfoRes> getDaiYongJiBy(Integer dyjId, Integer wxId){
        return daiyongjiService.getDaiYongJiBy(dyjId,wxId);
    }
    @ApiOperation(value = "归还代用机、扣除押金")
    @PostMapping("/cancelDaiyongji")
    public R<Boolean> cancelDaiyongji(Integer wxid, Integer dyjid,@RequestParam(required = false,defaultValue = "false")Boolean isGuihuan,@RequestParam(required = false,defaultValue = "0") Integer cancelType){
        return daiyongjiService.cancelDaiyongji(wxid,dyjid,isGuihuan, cancelType == 1);
    }
    @ApiOperation(value = "归还代用机、扣除押金")
    @PostMapping("/cancelDaiyongji/v2")
    @RepeatSubmitCheck()
    public R<Dict> cancelDaiyongjiV2(@RequestBody @Valid CancelDaiYongjiReq cancelDaiYongjiReq){
        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();
        return daiyongjiService.cancelDaiyongjiV2(cancelDaiYongjiReq,oaUserBO);
    }

    @GetMapping("/checkDyjSign")
    @ApiOperation(value = "代用机签署协议校验",notes = "")
    public R<String> checkDyjSign(@RequestParam("wxid") Integer wxid,@RequestParam("dyjId") Integer dyjId
            ,@RequestParam(value = "yajin",required = false) BigDecimal yajin){
        return daiyongjiService.checkDyjSign(wxid,dyjId,yajin);
    }

    @GetMapping("/removeSignatureByDataId")
    @ApiOperation(value = "移除代用机协议",notes = "移除用户在网站签署的代用机协议")
    public R<String> removeSignatureByDataId(Integer wxid){
        return daiyongjiService.removeSignatureByDataId(wxid);
    }
}
