package com.jiuji.oa.afterservice.common.config.db;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.jiuji.cloud.org.service.AreaInfoCloud;
import com.jiuji.oa.afterservice.common.bo.OaUserBO;
import com.jiuji.oa.afterservice.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.afterservice.common.config.xtenant.MultitenancyInterceptor;
import com.jiuji.oa.afterservice.common.constant.DataSourceConstants;
import com.jiuji.oa.afterservice.common.constant.RequestCacheKeys;
import com.jiuji.oa.afterservice.common.enums.XtenantEnum;
import com.jiuji.oa.afterservice.common.util.SpringContextUtil;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.foundation.db.config.DynamicDataSource;
import lombok.extern.slf4j.Slf4j;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Optional;

@Slf4j
public class MyDynamicDataSource extends DynamicDataSource {
    @Override
    protected Object determineCurrentLookupKey() {
        String lookupKey = (String)super.determineCurrentLookupKey();

        switch (ObjectUtil.defaultIfNull(lookupKey, StringPool.EMPTY)){
            case DataSourceConstants.DEFAULT:
                //默认数据源为ch999oanew
            case DataSourceConstants.CH999_OA_NEW:
            case DataSourceConstants.CH999_OA_NEW_REPORT:
                if (isTaxModel()){
                    log.info("由{}切换数据源到{}", lookupKey, DataSourceConstants.CH999_OA_NEW2);
                    lookupKey = DataSourceConstants.CH999_OA_NEW2;
                }
                break;
            case DataSourceConstants.OFFICE:
                if (isTaxModel()){
                    log.info("由{}切换数据源到{}", lookupKey, DataSourceConstants.OFFICE2);
                    lookupKey = DataSourceConstants.OFFICE2;
                }
                break;
            default:
                break;
        }

        return lookupKey;
    }

    /**
     * 是否是税务模块
     * @return
     */
    public static boolean isTaxModel() {
        if (SpringUtil.getApplicationContext() == null) {
            return false;
        }
        Optional<HttpServletRequest> reqOpt = SpringContextUtil.getRequest();
        if(reqOpt.map(req -> req.getHeader(MultitenancyInterceptor.PLAT_FORM))
                .filter(platform -> StrUtil.startWithIgnoreCase(platform, "pda/")).isPresent()){
            //pad设备调用
            return false;
        }
        OaUserBO oaUser = SpringUtil.getBean(AbstractCurrentRequestComponent.class).getCurrentStaffId();
        if (XtenantEnum.isJiujiXtenant() && oaUser != null && oaUser.getAreaId() != null) {
            R<List<Integer>> taxCompanysR = SpringContextUtil.reqCache(() -> SpringUtil.getBean(AreaInfoCloud.class).listTaxCompanyIdAreaId(oaUser.getAreaId()),
                    RequestCacheKeys.USER_TAX_MODEL, oaUser.getUserId(), oaUser.getAreaId());
            if (taxCompanysR.isSuccess() && CollUtil.isNotEmpty(taxCompanysR.getData())) {
                return true;
            }
        }
        return false;
    }
}
