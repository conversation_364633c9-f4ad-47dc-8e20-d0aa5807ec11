package com.jiuji.oa.afterservice.delayQueuePush.appointmentFormStrategy.impl;

import cn.hutool.json.JSONUtil;
import com.jiuji.oa.afterservice.common.enums.SmsReceiverClassfyEnum;
import com.jiuji.oa.afterservice.delayQueuePush.appointmentFormStrategy.AppointmentFormPushStrategy;
import com.jiuji.oa.afterservice.delayQueuePush.vo.AppointmentFormPush;
import com.jiuji.tc.utils.constants.NumberConstant;
import com.meitu.platform.lmstfy.client.LmstfyClient;
import com.meitu.platform.lmstfy.exception.LmstfyException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;


/**
 * 处理方式为预约到店且订单类型为小件预约单
 * <AUTHOR>
 */
@Slf4j
@Service(value = "SmallPiecesQueueStrategy")
public class SmallPiecesQueueStrategy extends CommonStrategy implements AppointmentFormPushStrategy {

    @Value("${lmstfy.mult.first-lmstfy-client.smallPiecesQueue}")
    private String smallPiecesQueue;
    @Value("${lmstfy.mult.first-lmstfy-client.smallPiecesAccumulationQueue}")
    private String smallPiecesAccumulationQueue;
    @Resource(name = "firstLmstfyClient")
    private LmstfyClient firstLmstfyClient;


    /**
     * 超时未进行业务确认的预约单，超30分钟未业务确认，给预约单门店对应的店长/主管/第—负责人（同时推送）进行超时信息推送，没有确认每隔30分钟进行一次推送5次封顶;
     * 推送方式：oa “售后通知”
     * 推送内容：你负责的区域存在超时未确认的 预约到店小件预约单，请及时跟进确认。订单号 XXXX，XXXX，XXX
     * @param appointmentFormPush
     */
    @Override
    public void pushDataDelayDetection(AppointmentFormPush appointmentFormPush)  {
        try {
            //计算延迟队列时间和统计推送次数
            Integer calculationDelaySecond = this.calculationDelaySecond(appointmentFormPush);
            String publish = firstLmstfyClient.publish(smallPiecesQueue, JSONUtil.toJsonStr(appointmentFormPush).getBytes(), 0, (short) 1, calculationDelaySecond);
            log.warn("预约到店且订单类型为小件预约单延迟队列推送成功，队列名称{}，推送参数{}，返回结果{}",smallPiecesQueue,JSONUtil.toJsonStr(appointmentFormPush),publish);
        } catch (LmstfyException e){
            appointmentFormPush.setPushNumber(appointmentFormPush.getPushNumber()-NumberConstant.ONE);
            log.error("预约到店且订单类型为小件预约单延迟队列推送异常，队列名称{}，推送参数{}",smallPiecesQueue,JSONUtil.toJsonStr(appointmentFormPush),e);
        }
    }

    @Override
    public void pushData(AppointmentFormPush appointmentFormPush) {
        //判断当前是否为0点到9点30
        Boolean accumulationTime = super.isAccumulationTime();
        if(accumulationTime){
            //在晚上0点到9点30的订单把订单推送到积压队列
            try {
                //计算延迟队列时间和统计推送次数
                Integer calculationDelaySecond = super.calculationDelaySecond(appointmentFormPush);
                String publish = firstLmstfyClient.publish(smallPiecesAccumulationQueue, JSONUtil.toJsonStr(appointmentFormPush).getBytes(), 0, (short) 1, calculationDelaySecond);
                log.warn("预约到店且订单类型为小件预约单延迟队列（堆积）推送成功，队列名称{}，推送参数{}，返回结果{}",smallPiecesAccumulationQueue,JSONUtil.toJsonStr(appointmentFormPush),publish);
            } catch (LmstfyException e){
                appointmentFormPush.setPushNumber(appointmentFormPush.getPushNumber()- NumberConstant.ONE);
                log.error("预约到店且订单类型为小件预约单延迟队列（堆积）推送异常，队列名称{}，推送参数{}",smallPiecesAccumulationQueue,JSONUtil.toJsonStr(appointmentFormPush),e);
            }
        } else {
            //直接推送OA信息
            pushDirectMsg(appointmentFormPush, SmsReceiverClassfyEnum.YYDD_QR_SEND);
            //消息推送之后进行消息延迟监控
            pushDataDelayDetection(appointmentFormPush);
        }

    }

}
