package com.jiuji.oa.afterservice.bigpro.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.afterservice.apollo.ShouhouTestInfoConfig;
import com.jiuji.oa.afterservice.bigpro.enums.ShouhouTestAttrTypeEnum;
import com.jiuji.oa.afterservice.bigpro.enums.ShouhouTestTypeEnum;
import com.jiuji.oa.afterservice.bigpro.machinehero.enums.MachineHeroReportHandleTypeEnum;
import com.jiuji.oa.afterservice.bigpro.mapstruct.ShouhouTestMapStruct;
import com.jiuji.oa.afterservice.bigpro.po.ShouhouTestResult;
import com.jiuji.oa.afterservice.bigpro.po.ShouhouTestResultInfo;
import com.jiuji.oa.afterservice.bigpro.service.ShouhouTestResultInfoService;
import com.jiuji.oa.afterservice.bigpro.dao.ShouhouTestResultInfoMapper;
import com.jiuji.oa.afterservice.bigpro.service.ShouhouTestResultService;
import com.jiuji.oa.afterservice.bigpro.vo.req.ShouhouTestResultAddReq;
import com.jiuji.oa.afterservice.bigpro.vo.req.ShouhouTestResultInfoReq;
import com.jiuji.oa.afterservice.bigpro.vo.res.ShouhouTestResultAddRes;
import com.jiuji.oa.afterservice.bigpro.vo.res.ShouhouTestResultInfoRes;
import com.jiuji.oa.afterservice.bigpro.vo.res.ShouhouTestResultRes;
import com.jiuji.oa.afterservice.common.bo.OaUserBO;
import com.jiuji.oa.afterservice.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.afterservice.common.exception.CustomizeException;
import com.jiuji.oa.afterservice.common.util.CommenUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 *
 */
@Service
public class ShouhouTestResultInfoServiceImpl extends ServiceImpl<ShouhouTestResultInfoMapper, ShouhouTestResultInfo>
implements ShouhouTestResultInfoService{
    @Resource
    private ShouhouTestMapStruct shouhouTestMapStruct;
    @Resource
    private ShouhouTestResultService shouhouTestResultService;
    @Resource
    private AbstractCurrentRequestComponent abstractCurrentRequestComponent;
    @Resource
    private ShouhouTestInfoConfig shouhouTestInfoConfig;

    @Override
    public Page<ShouhouTestResultInfoRes> getTestInfoList(ShouhouTestResultInfoReq req) {
        Page<ShouhouTestResultInfoRes> page = new Page<>();
        page.setSize(req.getSize());
        page.setCurrent(req.getCurrent());
        return CommenUtil.autoQueryHist(()->this.baseMapper.getTestInfoPageByShouhouId(page, req));
    }

    /**
     * 查询售后测试记录
     *
     * @param shouhouId
     * @return
     */
    @Override
    public List<ShouhouTestResultInfoRes> getTestInfoListByShouhouId(Integer shouhouId) {
        List<ShouhouTestResultInfoRes> list = CommenUtil.autoQueryHist(()->this.baseMapper.getTestInfoListByShouhouId(shouhouId));
        if(CollectionUtils.isNotEmpty(list)){
            list.forEach(item->{
                item.setHandleTypeValue(MachineHeroReportHandleTypeEnum.getShowMessageByCode(item.getHandleType()));
            });
        }
        return list;
    }

    /**
     * 保存测试记录
     *
     * @param req
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ShouhouTestResultAddRes saveTestResult(ShouhouTestResultAddReq req) {
        OaUserBO currentStaffId = abstractCurrentRequestComponent.getCurrentStaffId();
        ShouhouTestResultInfo shouhouTestResultInfo = shouhouTestMapStruct.toShouhouTestResultInfo(req);
        shouhouTestResultInfo.setInuser(currentStaffId.getUserName());
        if (Objects.equals(ShouhouTestTypeEnum.BEFORE_REPAIRING.getCode(), req.getTestType())) {
            shouhouTestResultInfo.setTestResult(true);
        } else if (Objects.equals(ShouhouTestTypeEnum.AFTER_REPAIRING.getCode(), req.getTestType())) {
            String remark = req.getTestResultInfoList().stream()
                    .filter(v -> Objects.equals(ShouhouTestAttrTypeEnum.radio.getCode(), v.getTestAttrType()))
                    .filter(v -> Objects.equals(shouhouTestInfoConfig.getAttrAbnormalValue(), v.getTestValue()))
                    .map(v -> v.getTestAttrName() + StrUtil.COLON + v.getAttrItemLable()).collect(Collectors.joining(StrUtil.COMMA));
            shouhouTestResultInfo.setTestResult(StringUtils.isBlank(remark));
            shouhouTestResultInfo.setRemark(remark);
        }
        boolean flag = this.save(shouhouTestResultInfo);
        if (flag) {
            List<ShouhouTestResult> shouhouTestResultList = req.getTestResultInfoList().stream()
                    .map(v -> {
                        ShouhouTestResult shouhouTest = shouhouTestMapStruct.toShouhouTestResult(v);
                        shouhouTest.setShouhouTestInfoId(shouhouTestResultInfo.getId());
                        return shouhouTest;
                    })
                    .collect(Collectors.toList());
            flag = shouhouTestResultService.saveShouhouTestResultBatch(shouhouTestResultList);
        }
        ShouhouTestResultAddRes res = new ShouhouTestResultAddRes();
        if (flag) {
            res.setId(shouhouTestResultInfo.getId());
        } else {
            throw new CustomizeException("操作失败");
        }
        return res;
    }

    /**
     * 查询售后测试详情
     *
     * @param shouhouTestInfoId
     * @return
     */
    @Override
    public ShouhouTestResultRes getResultByTestId(Integer shouhouTestInfoId) {
        return this.baseMapper.getTestResultInfoResById(shouhouTestInfoId);
    }

    /**
     * 查询最新的一条测试记录
     *
     * @param shouhouId
     * @return
     */
    @Override
    public ShouhouTestResultInfo getLastResultByTypeAndShouhouId(Integer type,Integer shouhouId) {
        return this.baseMapper.getLastResultByTypeAndShouhouId(type, shouhouId);
    }

    /**
     * 售后单和测试类型查询最新的测试记录
     *
     * @param shouhouId
     * @return
     */
    @Override
    public ShouhouTestResultInfo getLastResultByShouhouId(Integer shouhouId) {
        return this.baseMapper.getLastResultByShouhouId(shouhouId);
    }
}




