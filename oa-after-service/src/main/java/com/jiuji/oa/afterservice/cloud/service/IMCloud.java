package com.jiuji.oa.afterservice.cloud.service;

import com.ch999.common.util.vo.Result;
import com.jiuji.oa.afterservice.cloud.fallbackfactory.IMCloudFallbackFactory;
import com.jiuji.oa.afterservice.smallpro.bo.filmCard.FilmAccessoriesV2Data;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@FeignClient(value = "WEB-EXTENDS", fallbackFactory = IMCloudFallbackFactory.class)
public interface IMCloud {

    /**
     * 服务完成通知 模板类型为27
     *
     * @param openId   微信用户OpenId
     * @param url      消息链接地址
     * @param first    主题
     * @param keyword1
     * @param keyword2
     * @param keyword3
     * @param remark
     * @return
     */
    @GetMapping("/extends/api/weixin/sendServicesCompleteMsg")
    Result<String> sendServicesCompleteMsg(
            @RequestParam(value = "openId") String openId,
            @RequestParam(value = "url") String url,
            @RequestParam(value = "first") String first,
            @RequestParam(value = "keyword1") String keyword1,
            @RequestParam(value = "keyword2") String keyword2,
            @RequestParam(value = "keyword3") String keyword3,
            @RequestParam(value = "remark") String remark,
            @RequestHeader(value = "City") Integer cityId,
            @RequestHeader(value = "xtenant") Long xtenant);


    /**
     * 服务完成通知 模板类型为27
     *
     * @param openId   微信用户OpenId
     * @param url      消息链接地址
     * @param first    主题
     * @param keyword1
     * @param keyword2
     * @param keyword3
     * @param remark
     * @return
     */
    @GetMapping("/extends/api/weixin/sendServicesCompleteMsg/v2")
    public Result<String> sendServicesCompleteMsgV2(@RequestParam(value = "openId") String openId,
                                                    @RequestParam(value = "url", required = false) String url,
                                                    @RequestParam(value = "first", required = false) String serviceNo,
                                                    @RequestParam(value = "completeTime", required = false) String completeTime);

    /**
     * 售后服务处理进度提醒 模板类型为3或24
     *
     * @param openId        微信用户OpenId
     * @param url           消息链接地址
     * @param first         主题
     * @param HandleType    服务类型
     * @param Status        处理状态
     * @param RowCreateDate 提交时间
     * @param LogType       当前进度
     * @param remark        备注
     * @return
     */
    @GetMapping("/extends/api/weixin/sendAfterServiceProgressMsg")
    Result<String> sendAfterServiceProgressMsg(@RequestParam(value = "openId") String openId,
                                               @RequestParam(value = "url", required = false) String url,
                                               @RequestParam(value = "first", required = false) String first,
                                               @RequestParam(value = "HandleType", required = false) String HandleType,
                                               @RequestParam(value = "Status", required = false) String Status,
                                               @RequestParam(value = "RowCreateDate", required = false) String RowCreateDate,
                                               @RequestParam(value = "LogType", required = false) String LogType,
                                               @RequestParam(value = "remark", required = false) String remark,
                                               @RequestHeader(value = "City") Integer cityId,
                                               @RequestHeader(value = "xtenant") Long xtenant
    );

    /**
     * 服务确认提醒
     *
     * @param openId   微信用户OpenId
     * @param url      消息链接地址
     * @param first    主题
     * @param keyword1
     * @param keyword2
     * @param remark
     * @return
     */
    @GetMapping("/extends/api/weixin/sendServiceConfirmMsg")
    Result<String> sendServiceConfirmMsg(@RequestParam(value = "openId") String openId,
                                         @RequestParam(value = "url", required = false) String url,
                                         @RequestParam(value = "first", required = false) String first,
                                         @RequestParam(value = "keyword1", required = false) String keyword1,
                                         @RequestParam(value = "keyword2", required = false) String keyword2,
                                         @RequestParam(value = "remark", required = false) String remark
    );

    /**
     * 积分提醒
     *
     * @param openId       微信用户OpenId
     * @param url          模板跳转
     * @param first        亲爱的X，您有Y积分即将于2021年1月31日到期清零，请尽快前往会员俱乐部使用积分
     * @param account      取用户的九机昵称，若昵称为空，取电话号码中间四位打*
     * @param time         积分清零时间-2021年1月31日23:59:59
     * @param type         清除2019年获得且未使用积分
     * @param creditChange 即将过期
     * @param number       取需要清除的积分数量
     * @param creditName   可用积分
     * @param amount       取用户账号当前积分数量-需要清除的积分数量
     * @param remark       会员积分超值换购，点击前往>
     * @return
     */
    @GetMapping("/extends/api/weixin/sendPointsReminderMsg")
    Result<String> sendPointsReminderMsg(@RequestParam(value = "openId") String openId,
                                         @RequestParam(value = "url", required = false) String url,
                                         @RequestParam(value = "first", required = false) String first,
                                         @RequestParam(value = "account", required = false) String account,
                                         @RequestParam(value = "time", required = false) String time,
                                         @RequestParam(value = "type", required = false) String type,
                                         @RequestParam(value = "creditChange", required = false) String creditChange,
                                         @RequestParam(value = "number", required = false) String number,
                                         @RequestParam(value = "creditName", required = false) String creditName,
                                         @RequestParam(value = "amount", required = false) String amount,
                                         @RequestParam(value = "remark", required = false) String remark
    );


    @GetMapping("/extends/api/afterService/renewConfig/page/v1")
    Result<List<FilmAccessoriesV2Data>> getFilmAccessoriesV2(@RequestParam(value = "ppid") Integer ppid,@RequestParam(value = "type") Integer type);


}
