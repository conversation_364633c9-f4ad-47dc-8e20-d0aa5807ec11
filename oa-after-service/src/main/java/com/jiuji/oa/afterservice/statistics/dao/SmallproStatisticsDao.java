package com.jiuji.oa.afterservice.statistics.dao;

import com.jiuji.oa.afterservice.common.constant.DataSourceConstants;
import com.jiuji.oa.afterservice.statistics.bo.smallpro.LossReportedAndCashAmountBO;
import com.jiuji.oa.afterservice.statistics.bo.smallpro.PieceQuantityBO;
import com.jiuji.oa.afterservice.statistics.bo.smallpro.SmallSalesBO;
import com.jiuji.oa.afterservice.statistics.bo.smallpro.SmallproSstaticsWithFilterBO;
import com.jiuji.oa.afterservice.statistics.vo.req.SmallproStatisticsFilterReq;
import com.jiuji.tc.foundation.db.annotation.DS;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Auther: qiweiqing
 * @Date: 2020/05/22/15:21
 * @Description:
 */
@Mapper
public interface SmallproStatisticsDao {
    @DS(DataSourceConstants.CH999_OA_NEW_REPORT)
    List<PieceQuantityBO> getPieceQuantity(@Param("req") SmallproStatisticsFilterReq req, @Param("kind") Integer code,@Param("status") Integer i);
    @DS(DataSourceConstants.CH999_OA_NEW_REPORT)
    List<LossReportedAndCashAmountBO> getLossReportedAndCashAmount(@Param("req") SmallproStatisticsFilterReq req);
    @DS(DataSourceConstants.CH999_OA_NEW_REPORT)
    List<SmallSalesBO> getSmallSales(@Param("req") SmallproStatisticsFilterReq req);
    @DS(DataSourceConstants.CH999_OA_NEW_REPORT)
    List<SmallproSstaticsWithFilterBO> getAllDataByCustomer(@Param("req") SmallproStatisticsFilterReq req);
    @DS(DataSourceConstants.CH999_OA_NEW_REPORT)
    List<SmallproSstaticsWithFilterBO> getAllDataByArea(@Param("req") SmallproStatisticsFilterReq req);
    @DS(DataSourceConstants.CH999_OA_NEW_REPORT)
    List<PieceQuantityBO> getPieceQuantityByArea(@Param("req") SmallproStatisticsFilterReq req, @Param("kind") Integer code,@Param("status") Integer i);
    @DS(DataSourceConstants.CH999_OA_NEW_REPORT)
    List<LossReportedAndCashAmountBO> getLossReportedAndCashAmountByArea(@Param("req") SmallproStatisticsFilterReq req);
    @DS(DataSourceConstants.CH999_OA_NEW_REPORT)
    List<SmallSalesBO> getSmallSalesByArea(@Param("req") SmallproStatisticsFilterReq req);
    @DS(DataSourceConstants.CH999_OA_NEW_REPORT)
    int getCurUserDefaultKind1(@Param("userId") Integer userId);
}
