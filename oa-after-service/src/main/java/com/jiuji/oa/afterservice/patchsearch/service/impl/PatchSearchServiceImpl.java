package com.jiuji.oa.afterservice.patchsearch.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.google.common.collect.Lists;
import com.jiuji.cloud.oaapi.service.UserSubCloud;
import com.jiuji.cloud.oaapi.vo.response.FilmServerItem;
import com.jiuji.cloud.oaapi.vo.response.FilmUseLog;
import com.jiuji.cloud.oaapi.vo.response.SubFilmList;
import com.jiuji.oa.afterservice.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.afterservice.patchsearch.dao.PatchSearchDao;
import com.jiuji.oa.afterservice.patchsearch.enums.SmallSubStateEnum;
import com.jiuji.oa.afterservice.patchsearch.enums.SubStateEnum;
import com.jiuji.oa.afterservice.patchsearch.service.PatchSearchService;
import com.jiuji.oa.afterservice.patchsearch.vo.req.PatchSearchReq;
import com.jiuji.oa.afterservice.patchsearch.vo.req.PatchSearchReqV2;
import com.jiuji.oa.afterservice.patchsearch.vo.res.PatchSearchRes;
import com.jiuji.oa.afterservice.patchsearch.vo.res.PatchSearchResV2;
import com.jiuji.oa.orginfo.sysconfig.client.SysConfigClient;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.common.vo.ResultCode;
import com.jiuji.tc.foundation.db.annotation.DS;
import com.jiuji.tc.utils.constants.SysConfigConstant;
import com.jiuji.tc.utils.enums.EnumUtil;
import io.swagger.models.auth.In;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Auther: qiweiqing
 * @Date: 2020/02/06
 * @Description:
 */
@Service
@DS("ch999oanew")
public class PatchSearchServiceImpl implements PatchSearchService {

    private static final String SUB_LINK = "%s/addOrder/editOrder?SubID=%s";
    private static final String SMALL_SUB_LINK = "%s/staticpc/#/small-refund/%s";
    private static final String M_SUB_LINK = "%s/order/editOrder?SubID=%s";
    private static final String M_SMALL_SUB_LINK = "%s/new/#/small-refund/%s";
    @Autowired
    private PatchSearchDao patchSearchDao;
    @Autowired
    private SysConfigClient sysConfigClient;
    @Resource
    private UserSubCloud userSubCloud;
    @Resource
    private AbstractCurrentRequestComponent currentRequestComponent;

    @Override
    public List<PatchSearchRes> listPageById(PatchSearchReq personChangesReq) {
        List<PatchSearchRes> records = patchSearchDao.getBasketPage(personChangesReq);
        Optional<String> oaUrlOpt = Optional.ofNullable(sysConfigClient.getValueByCode(SysConfigConstant.OA_URL)).map(R::getData);
        if (CollectionUtils.isNotEmpty(records)) {
            for (PatchSearchRes record : records) {
                if ("销售订单".equals(record.getType())) {
                    String messageByCode = EnumUtil.getMessageByCode(SubStateEnum.class, record.getSubCheck());
                    record.setSubCheckName(messageByCode);
                    oaUrlOpt.ifPresent(oaUrl->record.setLink(String.format(SUB_LINK,oaUrl,record.getSubId())));

                } else if ("售后小件订单".equals(record.getType())) {
                    String messageByCode = EnumUtil.getMessageByCode(SmallSubStateEnum.class, record.getSubCheck());
                    record.setSubCheckName(messageByCode);
                    oaUrlOpt.ifPresent(oaUrl->record.setLink(String.format(SMALL_SUB_LINK,oaUrl,record.getSubId())));
                }
            }
        }
        return records;
    }

    @Override
    public R<PatchSearchResV2> getPatchById(PatchSearchReqV2 req) {
        PatchSearchResV2 patchSearchResV2 = new PatchSearchResV2();
        // 查询订单信息
        R<SubFilmList> subFilmListR = userSubCloud.getSubFilmInfo(req.getBasketId(), req.getType(), req.getTransferCode(), null);
        if(!subFilmListR.isSuccess()){
            return R.error(subFilmListR.getUserMsg());
        }
        PatchSearchResV2.SubInfo subInfo = new PatchSearchResV2.SubInfo();
        SubFilmList subFilmList = subFilmListR.getData();
        if(null == subFilmList){
            return R.error("壳/膜信息为空");
        }
        subInfo.setSubId(subFilmList.getSubId());
        subInfo.setProductName(subFilmList.getProductName());
        subInfo.setPriceId(subFilmList.getPriceId());
        subInfo.setProductColor(subFilmList.getProductColor());
        subInfo.setOriginProductName(subFilmList.getOriginProductName());
        List<FilmServerItem> filmServerItemList = subFilmList.getServerItemList();
        if(CollUtil.isNotEmpty(filmServerItemList)){
            // 取数排序优先级: 服务类型>开始时间 > 结束时间
            FilmServerItem filmServerItem = null;
            for(FilmServerItem serverItem: filmServerItemList){
                if(null == filmServerItem){
                    if(req.getServiceType().equals(serverItem.getServiceType())
                            && req.getStartDate().equals(serverItem.getStartDate())
                            && req.getExpireDate().equals(serverItem.getExpireDate())){
                        filmServerItem = serverItem;
                    }
                }else {
                    continue;
                }
            }
            if(null == filmServerItem){
                return R.error("壳/膜明细为空");
            }
            subInfo.setFilmServerItem(filmServerItem);
        }
        patchSearchResV2.setSubInfo(subInfo);

        List<PatchSearchRes> allPatchSearchResList = Lists.newArrayList();

        // 查询壳（年包）
        if(req.getType().equals(new Integer(2))){

            List<Integer> subIdList = subInfo.getFilmServerItem().getLogs().stream().filter(e-> null != e.getChangeSubId())
                    .map(FilmUseLog::getChangeSubId).collect(Collectors.toList());

            List<Integer> smallProIdList = subInfo.getFilmServerItem().getLogs().stream().filter(e-> null != e.getSmallProId())
                    .map(FilmUseLog::getSmallProId).collect(Collectors.toList());

            if(CollUtil.isNotEmpty(subIdList)){
                List<PatchSearchRes> subPatchList = patchSearchDao.getBasketSub(subIdList);
                if(CollUtil.isNotEmpty(subPatchList)){
                    allPatchSearchResList.addAll(subPatchList);
                }
            }
            if(CollUtil.isNotEmpty(smallProIdList)){
                List<PatchSearchRes> smallPatchList = patchSearchDao.getBasketSmall(smallProIdList);
                if(CollUtil.isNotEmpty(smallPatchList)){
                    allPatchSearchResList.addAll(smallPatchList);
                }
            }
        }

        HttpServletRequest request = Optional.ofNullable((ServletRequestAttributes) RequestContextHolder.getRequestAttributes())
                .map(ServletRequestAttributes::getRequest).orElse(null);
        String platform = Optional.ofNullable(request.getHeader("Platform")).orElse("");
        boolean isMoa = false;
        if (platform.contains("MOA") || platform.contains("iOS") || platform.contains("Android")){
            isMoa = true;
        }
        // 查询膜（年包+赠送）
        if(req.getType().equals(new Integer(1))){
            // 查询小件膜年包
            List<Integer> smallProIdList = Lists.newArrayList();
            if(CollUtil.isNotEmpty(subInfo.getFilmServerItem().getLogs())){
                // 日志type:1 年包赠送日志 2 赠送年包使用日志 0 年包使用
                smallProIdList = subInfo.getFilmServerItem().getLogs().stream().filter(e-> e.getType() != null && e.getType() == 0 && null != e.getSmallProId())
                        .map(FilmUseLog::getSmallProId).collect(Collectors.toList());
                // 赠送年包的使用
                if(StringUtils.isNotBlank(req.getTransferCode())){
                    smallProIdList = subInfo.getFilmServerItem().getLogs().stream().filter(e-> e.getType() != null && e.getType() == 2 && null != e.getSmallProId() && StringUtils.isNotBlank(e.getTransferCode()))
                            .map(FilmUseLog::getSmallProId).collect(Collectors.toList());
                }
            }

            // smallProIdList 有值才去查询数据
            if(CollUtil.isNotEmpty(smallProIdList)){
                List<PatchSearchRes> patchSearchResList = patchSearchDao.getBasketSmall(smallProIdList);
                if(CollUtil.isNotEmpty(patchSearchResList)){
                    allPatchSearchResList.addAll(patchSearchResList);
                }
            }

            // 查询赠送
            List<String> transferCodeList = Lists.newArrayList();
            if(CollUtil.isNotEmpty(subInfo.getFilmServerItem().getLogs())){
                // 日志type:1 年包赠送日志 2 赠送年包使用日志 0 年包使用
                transferCodeList = subInfo.getFilmServerItem().getLogs().stream().filter(e-> e.getType() != null && e.getType() == 1 && StringUtils.isNotBlank(e.getTransferCode()))
                        .map(FilmUseLog::getTransferCode).collect(Collectors.toList());
            }
            List<PatchSearchResV2.PatchGive> giveList = Lists.newArrayList();
            if(CollUtil.isNotEmpty(transferCodeList)){
                giveList = patchSearchDao.listGive(req.getBasketId(), transferCodeList);
                if(CollUtil.isNotEmpty(giveList)){
                    giveList.forEach(e -> {
                        // 状态 0 待领取 1 已领取【待领取 将领取时间置空】
                        if (ArrayUtils.contains(new Integer[]{0}, e.getStatus())) {
                            e.setReceiverTime(null);
                        }
                    });
                }
            }
            //赋值
            patchSearchResV2.setPatchGiveList(giveList);
        }

        if(CollUtil.isNotEmpty(allPatchSearchResList)){

            Optional<String> oaUrlOpt = Optional.ofNullable(sysConfigClient.getValueByCode(SysConfigConstant.OA_URL)).map(R::getData);
            Optional<String> moaOpt = Optional.ofNullable(sysConfigClient.getValueByCode(SysConfigConstant.MOA_URL)).map(R::getData);

            for (PatchSearchRes record : allPatchSearchResList) {

                if ("销售订单".equals(record.getType())) {
                    String messageByCode = EnumUtil.getMessageByCode(SubStateEnum.class, record.getSubCheck());
                    record.setSubCheckName(messageByCode);

                    if(isMoa){
                        moaOpt.ifPresent(oaUrl->record.setLink(String.format(M_SUB_LINK, oaUrl, record.getSubId())));
                    }else {
                        oaUrlOpt.ifPresent(oaUrl->record.setLink(String.format(SUB_LINK, oaUrl, record.getSubId())));
                    }

                } else if ("售后小件订单".equals(record.getType())) {
                    String messageByCode = EnumUtil.getMessageByCode(SmallSubStateEnum.class, record.getSubCheck());
                    record.setSubCheckName(messageByCode);

                    if(isMoa){
                        moaOpt.ifPresent(oaUrl->record.setLink(String.format(M_SMALL_SUB_LINK,oaUrl,record.getSubId())));
                    }else {
                        oaUrlOpt.ifPresent(oaUrl->record.setLink(String.format(SMALL_SUB_LINK,oaUrl,record.getSubId())));
                    }
                }
            }

        }

        //赋值
        patchSearchResV2.setPatchList(allPatchSearchResList);

        return R.success(patchSearchResV2);
    }
}
