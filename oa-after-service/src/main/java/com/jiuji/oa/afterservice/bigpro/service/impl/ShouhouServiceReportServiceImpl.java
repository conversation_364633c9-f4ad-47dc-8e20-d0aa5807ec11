package com.jiuji.oa.afterservice.bigpro.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.afterservice.bigpro.bo.UserClassConfig;
import com.jiuji.oa.afterservice.bigpro.dao.ShouhouServiceReportMapper;
import com.jiuji.oa.afterservice.bigpro.enums.AttachmentsEnum;
import com.jiuji.oa.afterservice.bigpro.po.Attachments;
import com.jiuji.oa.afterservice.bigpro.po.Shouhou;
import com.jiuji.oa.afterservice.bigpro.po.ShouhouServiceReport;
import com.jiuji.oa.afterservice.bigpro.service.AttachmentsService;
import com.jiuji.oa.afterservice.bigpro.service.ShouhouService;
import com.jiuji.oa.afterservice.bigpro.service.ShouhouServiceReportService;
import com.jiuji.oa.afterservice.bigpro.vo.req.PrintServiceReportReq;
import com.jiuji.oa.afterservice.bigpro.vo.req.ShouhouServiceReportReq;
import com.jiuji.oa.afterservice.bigpro.vo.res.PrintShouhouServiceReportRes;
import com.jiuji.oa.afterservice.bigpro.vo.res.ShouhouServiceReportRes;
import com.jiuji.oa.afterservice.common.bo.OaUserBO;
import com.jiuji.oa.afterservice.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.afterservice.common.constant.DataSourceConstants;
import com.jiuji.oa.afterservice.common.enums.MTableInfoEnum;
import com.jiuji.oa.afterservice.common.enums.UserClassEnum;
import com.jiuji.oa.afterservice.common.exception.RRException;
import com.jiuji.oa.afterservice.common.source.OaWcfUrlSource;
import com.jiuji.oa.afterservice.common.util.CommenUtil;
import com.jiuji.oa.afterservice.smallpro.service.SmallproService;
import com.jiuji.oa.afterservice.sub.po.Sub;
import com.jiuji.oa.afterservice.sub.service.SubService;
import com.jiuji.oa.orginfo.areainfo.client.AreaInfoClient;
import com.jiuji.oa.orginfo.areainfo.vo.AreaInfo;
import com.jiuji.oa.orginfo.member.client.MemberClient;
import com.jiuji.oa.orginfo.member.res.MemberBasicRes;
import com.jiuji.oa.orginfo.sysconfig.client.SysConfigClient;
import com.jiuji.oa.orginfo.userinfo.client.UserInfoClient;
import com.jiuji.oa.orginfo.userinfo.vo.Ch999UserVo;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.common.vo.ResultCode;
import com.jiuji.tc.utils.common.DecideUtil;
import com.jiuji.tc.utils.constants.NumberConstant;
import com.jiuji.tc.utils.constants.SysConfigConstant;
import com.jiuji.tc.utils.enums.EnumUtil;
import com.jiuji.tc.utils.transaction.MultipleTransaction;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date ${Date} 10:20
 * @Description
 */
@Service
public class ShouhouServiceReportServiceImpl extends ServiceImpl<ShouhouServiceReportMapper, ShouhouServiceReport> implements ShouhouServiceReportService {

    @Resource
    private ShouhouServiceReportMapper shouhouServiceReportMapper;
    @Resource
    private ShouhouService shouhouService;
    @Resource
    private MemberClient memberClient;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private AreaInfoClient areaInfoClient;
    @Resource
    private AbstractCurrentRequestComponent abstractCurrentRequestComponent;
    @Resource
    private SysConfigClient sysConfigClient;
    @Resource
    private UserInfoClient userInfoClient;
    @Resource
    private SubService subService;
    @Resource
    private SmallproService smallproService;
    @Resource
    private AttachmentsService attachmentsService;
    @Resource
    private OaWcfUrlSource oaWcfUrlSource;

    @Override
    public R<Boolean> saveReport(ShouhouServiceReportReq req) {
        if (CommenUtil.isNullOrZero(req.getShouhouId())) {
            return R.error("售后单id不能为空！");
        }
        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();
        if (oaUserBO == null) {
            return R.error("登陆信息异常！");
        }
        //Shouhou shouhou = shouhouService.getById(req.getShouhouId());
        Shouhou shouhou = CommenUtil.autoQueryHist(() ->shouhouService.getById(req.getShouhouId()), MTableInfoEnum.SHOUHOU,req.getShouhouId());
        if (Objects.isNull(shouhou)) {
            return R.error("售后单不存在");
        }
        //用户等级
        Integer userClass = null;
        //用户等级名称
        String userClassName = null;
        //购买人姓名
        String purchasingUserName = null;
        //购买人手机号
        String purchasingUserMobile = null;
        //地区名称
        String areaName = null;
        //区域电话
        String areaMobile = null;
        //xtenant
        Integer xtenant = null;
        //当前订单类型
        Integer purchaseType = 0;
        //会员等级
        if (shouhou.getUserid() != null) {
            R<MemberBasicRes> memberBasicResR = memberClient.getMemberBasicInfo(shouhou.getUserid().intValue());
            if (ResultCode.SUCCESS == memberBasicResR.getCode() && memberBasicResR.getData() != null) {
                purchasingUserName = memberBasicResR.getData().getUserName();
                purchasingUserMobile = memberBasicResR.getData().getMobile();
                userClass = memberBasicResR.getData().getUserClass();
                //获取新的会员名称
                xtenant = memberBasicResR.getData().getXtenant();
                UserClassConfig newUserClassName = UserClassEnum.getNewUserClassName(userClass, xtenant, stringRedisTemplate, memberBasicResR.getData().getAreaId(), areaInfoClient);
                //兜底处理
                if (ObjectUtil.isEmpty(newUserClassName)) {
                    userClassName = EnumUtil.getMessageByCode(UserClassEnum.class, memberBasicResR.getData().getUserClass());
                } else {
                    userClassName = newUserClassName.getUserClassName();
                }
            }
        }
        //当前门店信息
        Integer areaId = null;
        if (CommenUtil.isNotNullZero(shouhou.getAreaid())) {
            areaId = shouhou.getAreaid();
        }
        if (CommenUtil.isNotNullZero(shouhou.getToareaid())) {
            areaId = shouhou.getToareaid();
        }
        if (CommenUtil.isNotNullZero(areaId)){
            R<AreaInfo> areaInfoById = areaInfoClient.getAreaInfoById(areaId);
            if (areaInfoById.isSuccess()) {
                areaName = areaInfoById.getData().getAreaName();
                areaMobile = areaInfoById.getData().getCompanyTel2();
            }
        }
        //当前订单类型 通过ishuishou来查询sub表的状态
        purchaseType = getInteger(shouhou, purchaseType);
        //构建参数
        ShouhouServiceReport shouhouServiceReport = new ShouhouServiceReport();
        shouhouServiceReport.setAcceptanceTime(shouhou.getModidate())//接件时间
                .setShouhouId(req.getShouhouId())//维修单号
                .setAfterUserName(shouhou.getTruename())//送修人姓名
                .setAfterUserMobile(shouhou.getMobile())//送修人电话
                .setUserClass(userClass)//会员等级
                .setUserClassName(userClassName)//会员等级
                .setImei(shouhou.getImei())//设备imei
                .setDeviceName(Optional.ofNullable(shouhou.getName()).orElse("") + Optional.ofNullable(shouhou.getProductColor()).orElse(""))//设备名称
                .setAppearanceDescription(shouhou.getWaiguan())//外观描述
                .setToConfigure(shouhou.getPeizhi())//配置
                .setSubId(shouhou.getSubId())//订单ID
                .setPurchaseType(purchaseType)//购买类型
                .setPurchasingDate(shouhou.getTradedate())//购买日期
                .setPurchasingUserName(purchasingUserName)//购买人姓名
                .setPurchasingUserMobile(purchasingUserMobile)//购买人电话
                .setFaultDescription(req.getFaultDescription())//故障描述
                .setFaultAnalysis(req.getFaultAnalysis())//检测分析
                .setFaultResult(req.getFaultResult())//故障结果
                .setAreaId(areaId)//区域id
                .setAreaName(areaName)//区域名称
                .setAreaMobile(areaMobile)//区域电话
                .setCheckUser(oaUserBO.getUserName())//添加人
                .setCheckTime(LocalDateTime.now())//添加时间
                .setReminder("此报告仅作为九机售后服务判断依据，不具备任何法律效益，若您对此份报告存在异议，您可前往官方售后服务网点或第三方检测机构进行进一步检测，我们将全力配合！")//温馨提示
                .setCreateTime(LocalDateTime.now())//创建时间
                .setUpdateTime(LocalDateTime.now())//更新时间
                .setCreateUser(oaUserBO.getUserName())//添加人
                .setIsDel(Boolean.FALSE)//是否删除
                .setXtenant(xtenant);//xtenant
        //添加时写日志
        new MultipleTransaction().execute(DataSourceConstants.DEFAULT, () -> {
            shouhouServiceReportMapper.add(shouhouServiceReport);
            shouhouService.saveShouhouLog(req.getShouhouId(), StrUtil.format("生成设备产品服务报告单，编号：{}", shouhouServiceReport.getId()), oaUserBO.getUserName(), null, true);
        }).commit();
        return R.success("操作成功！");
    }

    /**
     * 查询订单类型
     *
     * @param shouhou shouhou
     * @param purchaseType purchaseType
     * @return
     */
    private Integer getInteger(Shouhou shouhou, Integer purchaseType) {
        if (CommenUtil.isNotNullZero(shouhou.getSubId())) {
            if (Objects.equals(shouhou.getIshuishou(), 1)) {
                purchaseType = ShouhouServiceReport.PurchaseTypeEnum.LP_ORDER.getCode();
            } else {
                Sub sub = subService.getByIdSqlServer(shouhou.getSubId());
                if (ObjectUtil.isNotNull(sub) && CommenUtil.isNotNullZero(sub.getSubId())) {
                    purchaseType = ShouhouServiceReport.PurchaseTypeEnum.NEW_ORDER.getCode();
                    //查询是否是优品
                    List<Integer> basketTypeById = smallproService.getBasketTypeById(Collections.singletonList(shouhou.getBasketId()));
                    if (CollUtil.isNotEmpty(basketTypeById)) {
                        purchaseType = ShouhouServiceReport.PurchaseTypeEnum.YP_ORDER.getCode();
                    }
                }
            }
        }
        return purchaseType;
    }

    /**
     * 查询设备产品服务报告单
     *
     * @param imei      imei
     * @param shouhouId shouhouId
     * @param subId     subId
     * @return
     */
    @Override
    public R<ShouhouServiceReportRes> getShouhouServiceReportList(String imei, Integer shouhouId, Integer subId) {
        if (StrUtil.isEmpty(imei) && CommenUtil.isNullOrZero(shouhouId) && CommenUtil.isNullOrZero(subId)) {
            return R.error("参数不能为空！");
        }
        Shouhou shouhou = null;
        //如果有售后id 则根据售后id取值
        if (CommenUtil.isNotNullZero(shouhouId)) {
            //查询售后订单
            shouhou = CommenUtil.autoQueryHist(() ->shouhouService.getById(shouhouId), MTableInfoEnum.SHOUHOU,shouhouId);
            //shouhou = shouhouService.getById(shouhouId);
        }
        ShouhouServiceReportRes shouhouServiceReportRes = new ShouhouServiceReportRes();
        //枚举类
        shouhouServiceReportRes.setPurchaseTypeEnum(ShouhouServiceReport.PurchaseTypeEnum.toEnumVOList());
        shouhouServiceReportRes.setFaultResultEnum(ShouhouServiceReport.FaultResultEnum.toEnumVOList());
        if (shouhou != null) {
            //当前订单类型
            Integer purchaseType = 0;
            purchaseType = getInteger(shouhou, purchaseType);
            shouhouServiceReportRes.setImei(shouhou.getImei())
                    .setDeviceName(shouhou.getName() + Optional.ofNullable(shouhou.getProductColor()).orElse(""))
                    .setSubId(DecideUtil.iif(CommenUtil.isNullOrZero(shouhou.getSubId()), null, shouhou.getSubId()))//订单ID
                    .setPurchaseType(purchaseType)
                    .setPurchaseTypeName(EnumUtil.getMessageByCode(ShouhouServiceReport.PurchaseTypeEnum.class, purchaseType));
            //兜底处理
            if (StrUtil.isEmpty(imei)) {
                imei = shouhou.getImei();
            }
            if (CommenUtil.isNullOrZero(subId)) {
                subId = shouhou.getSubId();
            }
        }
        if (StrUtil.isEmpty(imei) && CommenUtil.isNullOrZero(subId)){
            return R.success(shouhouServiceReportRes);
        }
        //查询质保信息
        List<ShouhouServiceReportRes.ServiceReportData> list = shouhouServiceReportMapper.getShouhouServiceReportList(imei, subId);
        if (CollUtil.isEmpty(list)) {
            return R.success(shouhouServiceReportRes);
        }
        //售后维修单为空 但imei和订单id不为空时 根据订单信息和imei查询(兜底处理一下)
        if (shouhou == null && (StrUtil.isNotEmpty(imei) && CommenUtil.isNotNullZero(subId))) {
            PrintShouhouServiceReportRes shouhouServiceReport = baseMapper.getShouhouServiceReportById(list.get(0).getId());
            if (shouhouServiceReport != null) {
                shouhouServiceReportRes.setImei(imei)
                        .setDeviceName(shouhouServiceReport.getDeviceName())
                        .setSubId(DecideUtil.iif(CommenUtil.isNullOrZero(subId),null,subId))//订单ID
                        .setPurchaseType(shouhouServiceReport.getPurchaseType())
                        .setPurchaseTypeName(EnumUtil.getMessageByCode(ShouhouServiceReport.PurchaseTypeEnum.class, shouhouServiceReport.getPurchaseType()));
            }
        }
        //跳转连接
        String host = Optional.of(sysConfigClient.getValueByCode(SysConfigConstant.OA_URL))
                .filter(r -> ResultCode.SUCCESS == r.getCode())
                .map(R::getData)
                .orElseThrow(() -> new RRException("获取域名出错"));
        R<List<AreaInfo>> listR = areaInfoClient.listAll();
        if (!listR.isSuccess()) {
            return R.error("网络异常请重试！");
        }
        list.forEach(li -> {
            li.setShouhouUrl(host + "/staticpc/#/after-service/order/edit/" + li.getShouhouId());
            if (CommenUtil.isNotNullZero(li.getAreaId()) && listR.isSuccess()) {
               li.setArea(listR.getData().stream().filter(areaInfo -> Objects.equals(li.getAreaId(),areaInfo.getId())).map(AreaInfo::getArea).findFirst().orElse(null));
            }
            li.setFaultResultName(EnumUtil.getMessageByCode(ShouhouServiceReport.FaultResultEnum.class, li.getFaultResult()));
        });
        //倒序
        shouhouServiceReportRes.setServiceReportData(list.stream().sorted(Comparator.comparing(ShouhouServiceReportRes.ServiceReportData::getId).reversed()).collect(Collectors.toList()));
        R<ShouhouServiceReportRes> r = R.success(shouhouServiceReportRes);
        Map<String, Object> exData = new HashMap<>(NumberConstant.ONE);
        exData.put("size", DecideUtil.iif(CollUtil.isNotEmpty(r.getData().getServiceReportData()), r.getData().getServiceReportData().size(), 0));
        r.setExData(exData);
        return r;
    }

    @Override
    public R<Boolean> printShouhouServiceReport(PrintServiceReportReq req) {
        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();
        if (CommenUtil.isNullOrZero(req.getId()) || StrUtil.isEmpty(req.getFid()) || StrUtil.isEmpty(req.getClientNo())){
            return R.error("参数错误！");
        }
        PrintShouhouServiceReportRes shouhouServiceReport = shouhouServiceReportMapper.getShouhouServiceReportById(req.getId());
        if (shouhouServiceReport == null){
            return R.error("编号错误，请重试！");
        }
        //type
        int type = NumberConstant.FIFTY + NumberConstant.SIX;
        //文件地址为空时 查询库 否则存储
        if (StrUtil.isEmpty(req.getFilePath())){
            //查询存在库里的文件
            List<Attachments> dbAttachments = attachmentsService.getAttachmentsByFid(req.getId(), StrUtil.subBefore(req.getFid(), ".", true), AttachmentsEnum.AFTER_SALES_QUALITY_INSPECTION_REPORT.getCode(), null);
            if (CollUtil.isEmpty(dbAttachments)) {
                return R.error("数据不存在,请重试！");
            }
            req.setFilePath(dbAttachments.get(0).getFilepath());
        }else {
            //存储
            Attachments attachments = new Attachments();
            attachments.setFid(StrUtil.subBefore(req.getFid(),".",true));
            attachments.setFilename(Optional.ofNullable(req.getFileName()).orElse(StrUtil.format("售后质检报告打印{}{}", DateUtil.format(LocalDateTime.now(), DatePattern.NORM_DATETIME_MINUTE_PATTERN), req.getId())));
            attachments.setLinkedID(req.getId());
            attachments.setFilepath(req.getFilePath());
//            attachments.setFileSize(Double.valueOf(Optional.ofNullable(req.getFileSize()).orElse(0)));
            attachments.setDtime(LocalDateTime.now());
            //后缀名 切割字符串
            attachments.setExtension("." + StrUtil.subAfter(req.getFid(),".",true));
            attachments.setUserid(oaUserBO.getUserId());
            //开事务 存储
            new MultipleTransaction().execute(DataSourceConstants.DEFAULT,()->{
                update(new LambdaUpdateWrapper<ShouhouServiceReport>().set(ShouhouServiceReport::getFid, attachments.getFid()).eq(ShouhouServiceReport::getId, req.getId()));
                attachmentsService.save(attachments);
            }).commit();
        }
        //调用打印接口
        String url = oaWcfUrlSource.getBasicUrl() + "/kcApi/GeneralPrint";
        String param = "?clientNo=" + req.getClientNo() + "&source=" + oaUserBO.getArea() + "&userId=" + oaUserBO.getUserId() + "&printId=" + req.getFilePath() + "&type=" + AttachmentsEnum.AFTER_SALES_QUALITY_INSPECTION_REPORT.getCode();
        String json = HttpUtil.get(url + param);
        JSONObject jsonObject = JSON.parseObject(json);
        if (jsonObject == null || Integer.parseInt(jsonObject.get("code").toString()) != 0) {
            return R.error("打印失败！");
        } else {
            //添加时写日志
            shouhouService.saveShouhouLog(shouhouServiceReport.getShouhouId(), StrUtil.format("打印设备产品服务报告单，编号：{}", shouhouServiceReport.getId()), oaUserBO.getUserName(), null, true);
            return R.success("打印成功！");
        }
    }

    @Override
    public R<PrintShouhouServiceReportRes> getShouhouServiceReportById(Integer id) {
        if (CommenUtil.isNullOrZero(id)) {
            return R.error("参数不能为空！");
        }
        PrintShouhouServiceReportRes shouhouServiceReport = CommenUtil.autoQueryHist(()->shouhouServiceReportMapper.getShouhouServiceReportById(id), MTableInfoEnum.SHOUHOU_SERVICE_REPORT, id);

        if (shouhouServiceReport == null) {
            return R.error("查询不到数据！");
        }
        R<Ch999UserVo> ch999UserByUserName = userInfoClient.getCh999UserByUserName(shouhouServiceReport.getCheckUser());
        if (ch999UserByUserName.isSuccess()) {
            shouhouServiceReport.setCheckUserId(ch999UserByUserName.getData().getCh999Id());
        }
        R<AreaInfo> areaInfoById = areaInfoClient.getAreaInfoById(shouhouServiceReport.getAreaId());
        if (areaInfoById.isSuccess()) {
            shouhouServiceReport.setArea(areaInfoById.getData().getArea());
        }
        shouhouServiceReport.setFaultResultName(EnumUtil.getMessageByCode(ShouhouServiceReport.FaultResultEnum.class, shouhouServiceReport.getFaultResult()));
        shouhouServiceReport.setPurchaseTypeName(EnumUtil.getMessageByCode(ShouhouServiceReport.PurchaseTypeEnum.class, shouhouServiceReport.getPurchaseType()));
        return R.success(shouhouServiceReport);
    }

}



