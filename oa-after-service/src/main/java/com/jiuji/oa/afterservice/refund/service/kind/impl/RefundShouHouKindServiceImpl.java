package com.jiuji.oa.afterservice.refund.service.kind.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.extra.spring.SpringUtil;
import com.jiuji.oa.afterservice.bigpro.dao.ShouhouRefundMapper;
import com.jiuji.oa.afterservice.bigpro.po.Shouhou;
import com.jiuji.oa.afterservice.bigpro.po.refund.ShouhouTuiHuanPo;
import com.jiuji.oa.afterservice.bigpro.service.ShouhouService;
import com.jiuji.oa.afterservice.bigpro.vo.res.DouYinCouponLogRes;
import com.jiuji.oa.afterservice.common.bo.OaUserBO;
import com.jiuji.oa.afterservice.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.afterservice.common.constant.RequestAttrKeys;
import com.jiuji.oa.afterservice.common.enums.BusinessTypeEnum;
import com.jiuji.oa.afterservice.common.enums.MTableInfoEnum;
import com.jiuji.oa.afterservice.common.enums.TuihuanKindEnum;
import com.jiuji.oa.afterservice.common.exception.CustomizeException;
import com.jiuji.oa.afterservice.common.util.CommenUtil;
import com.jiuji.oa.afterservice.common.util.SpringContextUtil;
import com.jiuji.oa.afterservice.refund.bo.RefundSubInfoBo;
import com.jiuji.oa.afterservice.refund.dao.ShouhouRefundMoneyMapper;
import com.jiuji.oa.afterservice.refund.service.kind.RefundShouHouKindService;
import com.jiuji.oa.afterservice.refund.service.way.WechatAlipaySecondsRefundService;
import com.jiuji.oa.afterservice.refund.vo.req.GroupTuihuanFormVo;
import com.jiuji.oa.afterservice.refund.vo.res.TuiHuanCheckVo;
import com.jiuji.oa.afterservice.refund.vo.res.way.ThirdOriginRefundVo;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.utils.common.DecideUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2022/12/5 19:32
 */
@Service
@Slf4j
public class RefundShouHouKindServiceImpl extends ParentTuiHuanKindServiceImpl implements RefundShouHouKindService {

    @Resource
    private ShouhouService shouhouService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(GroupTuihuanFormVo tuihuanForm) {

    }

    @Override
    public List<TuihuanKindEnum> myKind() {
        return Arrays.asList(TuihuanKindEnum.TWXF, TuihuanKindEnum.TDJ_WXF);
    }

    @Override
    protected RefundSubInfoBo getSuInfoWithMaxRefundPrice(Integer subId, TuihuanKindEnum tuihuanKindEnum) {
        switch (tuihuanKindEnum){
            case TWXF:
                //Shouhou shouhou = shouhouService.getById(subId);
                Shouhou shouhou = CommenUtil.autoQueryHist(() ->shouhouService.getById(subId), MTableInfoEnum.SHOUHOU,subId);
                ShouhouTuiHuanPo shouhouTuiHuanPo = SpringContextUtil.getRequest().map(req -> (ShouhouTuiHuanPo)req.getAttribute(RequestAttrKeys.SHOUHOU_TUIHUAN_INFO))
                        .orElseGet(()-> SpringUtil.getBean(ShouhouRefundMapper.class).getLastNotCompleteRefund(subId, tuihuanKindEnum.getCode()));
                if(shouhouTuiHuanPo != null){
                    RefundSubInfoBo rfsi = new RefundSubInfoBo();
                    rfsi.setInPrice(shouhouTuiHuanPo.getInprice());
                    rfsi.setOrderId(shouhou.getId());
                    rfsi.setTotalPrice(shouhouTuiHuanPo.getBuypriceM());
                    rfsi.setAreaId(shouhou.getBuyareaid());

                    rfsi.setBusinessType(DecideUtil.iif(Objects.equals(0,shouhou.getIshuishou()), BusinessTypeEnum.NEW_ORDER.getCode(),BusinessTypeEnum.LP_ORDER.getCode()));
                    rfsi.setShouhouAreaId(shouhou.getToareaid());
                    rfsi.setUserId(Convert.toInt(shouhou.getUserid()));
                    rfsi.setYifuM(shouhouTuiHuanPo.getBuypriceM());
                    rfsi.setYingfuM(shouhouTuiHuanPo.getBuypriceM());
                    rfsi.setSubCheck(1);
                    //最大退款直接从请求对象中获取
                    rfsi.setMaxRefundPrice(SpringContextUtil.getRequest().map(req -> req.getAttribute(RequestAttrKeys.SHOUHOU_MAX_REFUND_PRICE))
                            .map(smrp -> (BigDecimal)smrp).orElseThrow(()-> new CustomizeException("折价后的退款金额不能为空")));
                    return rfsi;
                }
                break;
            default:
                break;
        }

        //查询购买价,如果查询不到订单信息,应该返回为空
        ShouhouRefundMoneyMapper shouhouRefundMoneyMapper = SpringUtil.getBean(ShouhouRefundMoneyMapper.class);
        RefundSubInfoBo rfsi = CommenUtil.autoQueryHist(()->shouhouRefundMoneyMapper.getShouhouMaxRefundPrice(subId,tuihuanKindEnum.getCode()), MTableInfoEnum.SHOUHOU, subId) ;

        if (Objects.isNull(rfsi)) {
            return null;
        }
        if(TuihuanKindEnum.TWXF == tuihuanKindEnum){
            //折价前的总金额
            rfsi.setTotalPrice(SpringContextUtil.getRequest().map(req -> req.getAttribute(RequestAttrKeys.SHOUHOU_TOTAL_PRICE))
                    .map(smrp -> (BigDecimal)smrp).orElseThrow(()-> new CustomizeException("折价前的退款金额不能为空")));
            //最大退款直接从请求对象中获取
            rfsi.setMaxRefundPrice(SpringContextUtil.getRequest().map(req -> req.getAttribute(RequestAttrKeys.SHOUHOU_MAX_REFUND_PRICE))
                    .map(smrp -> (BigDecimal)smrp).orElseThrow(()-> new CustomizeException("折价后的退款金额不能为空")));
        }
        return rfsi;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<Integer> checkSubmitCheckAndSet(TuiHuanCheckVo tuiHuanCheckVo) {
        ShouhouTuiHuanPo tuiHuanPo = tuiHuanCheckVo.getTuiHuanPo();
        ShouhouService shouhouService = SpringUtil.getBean(ShouhouService.class);
        Optional<OaUserBO> oaUserOpt = Optional.ofNullable(SpringUtil.getBean(AbstractCurrentRequestComponent.class).getCurrentStaffId());
        switch (tuiHuanCheckVo.getProcessStatusEnum()){
            case CHECK3:
                R<Boolean> srlCheck = SpringUtil.getBean(WechatAlipaySecondsRefundService.class).secondsRefundLimit(tuiHuanCheckVo);
                if(!srlCheck.isSuccess()){
                    //校验不同过记录日志
                    shouhouService.saveShouhouLog(tuiHuanPo.getShouhouId(),srlCheck.getUserMsg(), oaUserOpt.map(OaUserBO::getUserName)
                            .orElse("系统"), null, false);
                    return R.error(srlCheck.getUserMsg());
                }
                break;
            default:
                break;
        }
        return R.success(null);
    }

    @Override
    public void forEachThirdRefund(ThirdOriginRefundVo tor, TuihuanKindEnum tuihuanKindEnum) {
        super.forEachThirdRefund(tor, tuihuanKindEnum);
        DouYinCouponLogRes.SubKindEnum subKindsEnum = null;
        boolean isLimitCanRefundPrice = true;
        switch (tuihuanKindEnum){
            case TDJ_WXF:
                isLimitCanRefundPrice = false;
                subKindsEnum = DouYinCouponLogRes.SubKindEnum.AFTERSALES;
                break;
            case TWXF:
                subKindsEnum = DouYinCouponLogRes.SubKindEnum.AFTERSALES;
                break;
            default:
                break;
        }
        setDouYinNotRefund(tor, subKindsEnum, isLimitCanRefundPrice);
    }

    @Override
    public boolean isEnable(Integer orderId, Integer kind, boolean isEnableParam) {
        boolean isEnable = isEnableParam;
        if(isEnable && TuihuanKindEnum.TWXF.getCode().equals(kind)){
            //退维修费兼容老的接口, 如果已收>应收费用, 走老的退维修费
            isEnable = isEnable && SpringUtil.getBean(ShouhouService.class).lambdaQuery().eq(Shouhou::getId, orderId)
                    .eq(Shouhou::getIsquji, Boolean.TRUE).apply("yifum > feiyong").count()<=0;
        }
        return isEnable && super.isEnable(orderId, kind, isEnable);
    }
}
