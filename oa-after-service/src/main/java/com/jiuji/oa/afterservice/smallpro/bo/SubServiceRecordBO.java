package com.jiuji.oa.afterservice.smallpro.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * description: <小件保修状态Res>
 * translation: <Smallpro order warranty status res>
 *
 * <AUTHOR>
 * @date 2019/11/27
 * @since 1.0.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class SubServiceRecordBO implements Serializable {
    private static final long serialVersionUID = -7681602346228921083L;
    /**
     * 交易时间
     */
    @ApiModelProperty(value = "交易时间")
    private String tradeDate;
    /**
     * 保修服务结束时间
     */
    @ApiModelProperty(value = "保修服务结束时间")
    private String endDate;
    /**
     * 保修服务类别[1只换不修|2意外换新|3延保|4年包服务]
     */
    @ApiModelProperty(value = "保修服务类别[1只换不修|2意外换新|3延保|4年包服务]")
    private Integer warrantyType;
    /**
     * 保修服务类别是否可用[0可用|1不可用]
     */
    @ApiModelProperty(value = "保修服务类别是否可用[0可用|1不可用]")
    private Integer warrantyStatus;
    /**
     * 保修服务价格
     */
    @ApiModelProperty(value = "保修服务价格")
    private Double warrantyPrice;
    /**
     * 保修服务年限
     */
    @ApiModelProperty(value = "保修服务年限")
    private Integer warrantyYear;
    /**
     * 是否为三九服务,old:isSv
     */
    @ApiModelProperty(value = "是否为三九服务,old:isSv")
    private Integer isServiceRecord;
    /**
     * 三九服务是否已被使用,old:isUsed
     */
    @ApiModelProperty(value = "三九服务是否已被使用,old:isUsed")
    private Integer isServiceRecordUsed;
    /**
     * 是否为年包卡服务
     */
    @ApiModelProperty(value = "是否为年包卡服务")
    private Integer isFilmCard;
}
