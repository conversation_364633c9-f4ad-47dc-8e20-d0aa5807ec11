package com.jiuji.oa.afterservice.refund.vo.req.machine;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.jiuji.oa.afterservice.bigpro.po.Shouhou;
import com.jiuji.oa.afterservice.common.enums.TuihuanKindEnum;
import com.jiuji.oa.afterservice.refund.vo.res.TuiGiftBasketListVo;
import com.jiuji.oa.orginfo.areainfo.vo.AreaInfo;
import com.jiuji.tc.utils.fastjson.TrimDeSerializeCodec;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.DecimalMin;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.LinkedList;
import java.util.List;

/**
 * 退换机管理详情对象
 * <AUTHOR>
 * @since 2022/11/16 17:22
 */
@Data
@Accessors(chain = true)
@ApiModel("退换机详情提交vo")
public class ZheJiaMachineFormVo {
    @ApiModelProperty("退换机申请id")
    private Integer tuihuanId;
    @ApiModelProperty("售后id")
    private Integer shouhouId;
    @ApiModelProperty("故障类型")
    private String faultType;
    @ApiModelProperty("审核类型")
    private String checkType;
    @ApiModelProperty("退换类型")
    private Integer tuihuanKind;
    @ApiModelProperty("审核类别")
    private Integer cType;
    @ApiModelProperty("购机时间")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime tradeDate;
    @ApiModelProperty("购机天数")
    private Integer tradeDay;
    @ApiModelProperty("设备类型 1、新机 2、优品 3、良品")
    private Integer tradeType;
    @ApiModelProperty("总金额")
    @JSONField(format = "0.00@DOWN")
    private BigDecimal totalPrice;
    @ApiModelProperty("退款金额")
    @JSONField(format = "0.00@DOWN")
    private BigDecimal tuikuanM;
    @ApiModelProperty("九机币")
    @JSONField(format = "0.00@DOWN")
    private BigDecimal coinM;
    @ApiModelProperty("赠品金额")
    @JSONField(format = "0.00@DOWN")
    private BigDecimal giftPrice;
    @ApiModelProperty("白条金额")
    @JSONField(format = "0.00@DOWN")
    private BigDecimal baitiaoPrice;
    @ApiModelProperty("酷白条金额")
    @JSONField(format = "0.00@DOWN")
    private BigDecimal kuBaitiaoPrice;
    @ApiModelProperty("微信红包金额")
    @JSONField(format = "0.00@DOWN")
    private BigDecimal wxHongBao;
    /**
     * 单号(交易单号)
     */
    @ApiModelProperty(value = "单号")
    private Integer subId;
    @ApiModelProperty("大件商品id")
    private Integer basketId;
    @ApiModelProperty("所换机型单号")
    @JSONField(deserializeUsing = TrimDeSerializeCodec.class)
    private Integer newSubId;
    @ApiModelProperty("所换机型mkcId")
    @JSONField(deserializeUsing = TrimDeSerializeCodec.class)
    private Integer newMkcId;
    @ApiModelProperty("售后门店id")
    private Integer areaId;
    @ApiModelProperty("售后租户编码")
    private Integer xtenant;

    @ApiModelProperty("附件折价金额")
    @JSONField(format = "0.00@DOWN")
    @DecimalMin(value = "0.00", message = "附件折价金额不能小于0")
    private BigDecimal peizhiPrice;
    @ApiModelProperty("计算类型 1 计算发票金额")
    private Integer calculateType;
    @ApiModelProperty("发票折价金额")
    @JSONField(format = "0.00@UP")
    @DecimalMin(value = "0.00", message = "发票折价金额不能小于0")
    private BigDecimal piaoPrice;
    /**
     * 发票类别 1、普票(纸质发票) 2、专票 3、普票(电子发票)
     */
    private Integer piaoType;
    /**
     * 发票收回 1 已收回 2 未收回
     * @see com.jiuji.oa.afterservice.refund.enums.PiaoInfoEnum
     */
    @ApiModelProperty("发票收回")
    private Integer piaoInfo;
    /**
     * 良品附件
     */
    @ApiModelProperty("良品附件")
    private List<TuiGiftBasketListVo> goodProductAccessoriesList;
    @ApiModelProperty("会员编号")
    private Integer userId;


    /**内部流转字段 start*/
    /**
     * 售后信息
     */

    @ApiModelProperty(value = "退款分类",hidden = true)
    @JSONField(serialize = false,deserialize = false)
    private TuihuanKindEnum tuihuanKindEnum;
    @ApiModelProperty(value = "售后信息",hidden = true)
    @JSONField(serialize = false,deserialize = false)
    private Shouhou shouhou;
    /**
     * 售后门店信息
     */
    @ApiModelProperty(value = "售后门店信息",hidden = true)
    @JSONField(serialize = false,deserialize = false)
    private AreaInfo shouhouAreaInfo;
    /**
     * 组合退提示信息
     */
    @ApiModelProperty(value = "组合退提示信息",hidden = true)
    @JSONField(serialize = false,deserialize = false)
    private List<String> nodes = new LinkedList<>();
    /**内部流转字段 end*/
}
