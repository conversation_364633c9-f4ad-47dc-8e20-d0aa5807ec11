package com.jiuji.oa.afterservice.common.config.feign;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.http.Header;
import com.jiuji.oa.afterservice.common.bo.OaUserBO;
import com.jiuji.oa.afterservice.common.config.component.ProdAbstractCurrentRequestComponent;
import com.jiuji.oa.afterservice.common.config.xtenant.MultitenancyInterceptor;
import com.jiuji.oa.afterservice.common.enums.XtenantEnum;
import com.jiuji.oa.afterservice.common.util.SpringContextUtil;
import com.jiuji.tc.utils.common.TraceIdUtil;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.util.Optional;

/**
 * feight请求增加请求头
 * <AUTHOR>
 * @since 2022/12/21 10:19
 */
@Component
public class MyFeignRequestInterceptor implements RequestInterceptor {
    @Override
    public void apply(RequestTemplate template) {
        Optional<HttpServletRequest> requestOpt = SpringContextUtil.getRequest();
        //设置授权
        requestOpt
                .map(req -> req.getHeader(Header.AUTHORIZATION.getValue()))
                .filter(auth -> !template.headers().containsKey(Header.AUTHORIZATION.getValue()))
                .ifPresent(auth -> template.header(Header.AUTHORIZATION.getValue(),auth));
        //从当前登录用户获取授权
        if (!template.headers().containsKey(Header.AUTHORIZATION.getValue())){
            OaUserBO oaUser = SpringUtil.getBean(ProdAbstractCurrentRequestComponent.class).getCurrentStaffId();
            if(oaUser != null && StrUtil.isNotBlank(oaUser.getToken())){
                template.header(Header.AUTHORIZATION.getValue(), oaUser.getToken());
            }
        }
        //设置xtenant
        if (!template.headers().containsKey(MultitenancyInterceptor.TENANT_HEADER_NAME)){
            template.header(MultitenancyInterceptor.TENANT_HEADER_NAME, Convert.toStr(XtenantEnum.getXtenant()));
        }
        requestOpt
                .map(req -> req.getHeader(MultitenancyInterceptor.PLAT_FORM))
                .filter(StrUtil::isNotBlank)
                .filter(isPad -> !template.headers().containsKey(MultitenancyInterceptor.PLAT_FORM))
                .ifPresent(isPad -> template.header(MultitenancyInterceptor.PLAT_FORM,isPad));
        //设置上下文日志
        if (!template.headers().containsKey(TraceIdUtil.TRACE_ID_KEY)){
            template.header(TraceIdUtil.TRACE_ID_KEY,TraceIdUtil.getTraceId());
        }

    }
}
