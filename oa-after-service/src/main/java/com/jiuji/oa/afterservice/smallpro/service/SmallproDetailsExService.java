package com.jiuji.oa.afterservice.smallpro.service;

import com.alicp.jetcache.anno.Cached;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jiuji.cloud.after.vo.res.ChangeInfoRes;
import com.jiuji.oa.afterservice.bigpro.vo.req.ReturnAnnualPackageReq;
import com.jiuji.oa.afterservice.cloud.vo.AfterServiceTimeCfg;
import com.jiuji.oa.afterservice.common.bo.OaUserBO;
import com.jiuji.oa.afterservice.common.enums.TuihuanKindEnum;
import com.jiuji.oa.afterservice.refund.bo.RefundSubInfoBo;
import com.jiuji.oa.afterservice.refund.bo.smallpro.SmallproSubBuyInfoBo;
import com.jiuji.oa.afterservice.smallpro.bo.SmallproOrderInfoBO;
import com.jiuji.oa.afterservice.smallpro.bo.SmallproProductInfoBO;
import com.jiuji.oa.afterservice.smallpro.bo.SmallproReturnFactoryInfoBO;
import com.jiuji.oa.afterservice.smallpro.bo.filmCard.FilmAccessoriesV2Item;
import com.jiuji.oa.afterservice.smallpro.bo.smallproInfo.SmallProReturnTypeBO;
import com.jiuji.oa.afterservice.smallpro.po.Smallpro;
import com.jiuji.oa.afterservice.smallpro.po.SmallproBill;
import com.jiuji.oa.afterservice.smallpro.vo.req.LossDetailsReq;
import com.jiuji.oa.afterservice.smallpro.vo.req.ReceivableSmallProReq;
import com.jiuji.oa.afterservice.smallpro.vo.req.SelectResistFilmReq;
import com.jiuji.oa.afterservice.smallpro.vo.req.SmallproInfoUpdateReq;
import com.jiuji.oa.afterservice.smallpro.vo.res.*;
import com.jiuji.tc.common.vo.R;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;

/**
 * description: <小件接件详情页扩展服务类>
 * translation: <Expanded service class for small piece detail page>
 *
 * <AUTHOR>
 * @date 2020/4/10
 * @since 1.0.0
 */
public interface SmallproDetailsExService {


    /**
     * 查询换货信息
     * @param smallProId
     * @return
     */
    List<ChangeInfoRes> selelctChangeInfoList(Integer smallProId);
    /**
     * 是否为一个月内的订单
     * @param subId
     * @return
     */
    Boolean isRecentMonthSub(Long subId);
    /**
     * 是否可以生成现货单
     * @param smallpro
     * @return
     */
    Boolean createIsGenerateCashOrder(Smallpro smallpro);

     List<Integer> selectAboutBasketId(List<Integer> basketIdList, Map<Integer, Integer> oldBasketIdMap);

    // region 逻辑方法

    // region 获取小件详情

    /**
     * description: <获取小件详情>
     * translation: <Get smallpro details>
     *
     * @param smallproId 小件Id
     * @param oaUserBO   当前操作用户
     * @return com.jiuji.oa.afterservice.smallpro.vo.res.SmallproInformationRes
     * <AUTHOR>
     * @date 10:51 2019/11/14
     * @since 1.0.0
     **/
    SmallproInformationRes getSmallproInfo(Integer smallproId, OaUserBO oaUserBO);

    // endregion

    // region 获取可接件商品

    /**
     * 最新版本根据网站配置
     * @param ppid
     * @return
     */
    boolean isYearCardProduct(Integer ppid);


    Integer getProductServiceType(Integer ppid);

    /**
     * 最新版本根据网站配置 （判断是服务商品 并且排除年包服务）
     * @param ppid
     * @return
     */
    boolean isServiceProduct(Integer ppid);

    boolean isServiceProduct(Integer smallproBillBasketId, Integer ppid);

    /**
     * description: <获取可接件商品>
     * translation: <Get Connectable Items>
     *
     * @param type    接件类型  1 subId 2 ppid 3 barCode
     * @param keyList 接件Key列表
     * @param areaId  当前用户所在地区
     * @return com.jiuji.oa.afterservice.smallpro.vo.res.SmallproReceivableRes
     * <AUTHOR>
     * @date 13:59 2019/12/2
     * @since 1.0.0
     **/
    SmallproReceivableRes getReceivableSmallpro(Integer type, List<String> keyList, Integer areaId, String imei, ReceivableSmallProReq req);

    /**
     * 获取售后配置的天数
     * @param afterTimeCfgMap
     * @param ppid
     * @param cfgFun
     * @return
     */
    Integer getAfterTimeCfgDays(Map<String, AfterServiceTimeCfg> afterTimeCfgMap, Integer ppid,
                                Function<AfterServiceTimeCfg, Integer> cfgFun);

    /**
     * 获取售后时间配置, 只开启线程级别的缓存
     * @param ppids
     * @return
     */
    Map<String, AfterServiceTimeCfg> getAfterTimeCfgMap(List<Integer> ppids);

    /**
     * 查询可接件信息
     * @param subId
     * @return
     */
    R<SmallproReceivableRes> getReceivableSmallProV2(Integer subId,String imei,Integer selectBasketId);

    // endregion

    // region 更新小件接件信息

    /**
     * description: <更新小件接件信息>
     * translation: <Update smallpro pickup information>
     *
     * @param smallproId            小件Id
     * @param oaUserBO              操作人信息
     * @param smallproInfoUpdateReq 小件更新数据
     * @return com.jiuji.oa.afterservice.smallpro.vo.res.SmallproInfoUpdateRes
     * <AUTHOR>
     * @date 17:28 2019/11/25
     * @since 1.0.0
     **/
    SmallproInfoUpdateRes updateSmallproInfo(Integer smallproId, OaUserBO oaUserBO,
                                             SmallproInfoUpdateReq smallproInfoUpdateReq);

    // endregion

    // region

    /**
     * description: <校验置换商品>
     * translation: <Verify replacement products>
     *
     * @param ppriceId    置换商品ppid
     * @param price       价格
     * @param serviceType 服务类别
     * @return com.jiuji.oa.afterservice.smallpro.bo.SmallproProductInfoBO
     * <AUTHOR>
     * @date 21:32 2020/4/14
     * @since 1.0.0
     **/
    SmallproProductInfoBO checkChangeProduct(Integer ppriceId, BigDecimal price, Integer serviceType);

    // endregion

    // region 获取最后一次置换的商品PPID getLastChangePpid

    /**
     * description: <获取最后一次置换的商品PPID>
     * translation: <Get the PPID of the last replacement product>
     *
     * @param subId          原订单Id
     * @param basketId       原条目Id
     * @param ppriceId       原商品Ppid
     * @param changePpriceId 已填写的置换商品Ppid
     * @return com.jiuji.oa.afterservice.smallpro.vo.res.SmallproLastChangePpidRes
     * <AUTHOR>
     * @date 14:33 2020/4/17
     * @since 1.0.0
     **/
    SmallproLastChangePpidRes getLastChangePpid(Integer subId, Integer basketId, Integer ppriceId,
                                                Integer changePpriceId);

    // endregion

    // region 删除小件附件 deleteSmallproFile


    /**
     * description: <删除小件单附件>
     * translation: <Delete small form attachment>
     *
     * @param smallproId 小件单Id
     * @param fileId     附件Id
     * @return com.jiuji.oa.afterservice.smallpro.vo.res.SmallproNormalCodeMessageRes
     * <AUTHOR>
     * @date 21:56 2020/4/22
     * @since 1.0.0
     **/
    SmallproNormalCodeMessageRes deleteSmallproFile(Integer smallproId, Integer fileId);


    // endregion

    /**
     * 获取小件接件商品相关信息
     * @param smallproBillList
     * @param subId
     * @param overDays
     * @return
     */
    List<SmallproOrderInfoBO> getSmallproOrderListInfo(
            List<SmallproBill> smallproBillList, Integer subId, Integer overDays);

    /**
     * 九机三方收银处理
     * @param subId
     * @param smallproOrderInfos
     */
    void jiujiThirdShouyingReturnPrice(Integer subId, List<SmallproOrderInfoBO> smallproOrderInfos);

    /**
     * 获取退款方式
     * @param subId
     * @param type
     * @param areaId
     * @return
     */
    List<SmallProReturnTypeBO> getSmallProReturnWaysSaas(Integer subId, Integer type, Integer areaId);

    /**
     * 获取退款类型
     * @param businessType
     * @return
     */
    Integer getReturnTypeByBusinessType(Integer businessType);

    /**
     * 获取壳膜分类下所有上架的壳商品
     * @return
     */
    @Cached(name = "com.jiuji.oa.afterservice.smallpro.service.SmallproDetailsExService.getSafeShellByPpid", expire = 1, timeUnit = TimeUnit.MINUTES)
    List<FilmAccessoriesV2Item> getSafeShellByPpid(Integer basketId ,Integer areaId);

    /**
     * 获取小件接件单返厂商品信息
     * @return
     */
    R<Page<SmallproReturnFactoryInfoBO>> getReturnFactoryInfoBOPage(Integer smallproId, Integer returnFactoryId, OaUserBO oaUserBO, Integer size, Integer current);

    /**
     *
     * @param smallproId
     * @param tuihuanKind
     * @return
     */
    RefundSubInfoBo getSmallproMaxRefundPrice(Integer smallproId, TuihuanKindEnum tuihuanKind);

    SmallproSubBuyInfoBo getSmallproSubBuyInfo(Integer smallproId, TuihuanKindEnum tuihuanKind);

    /**
     *
     * @param req
     */
    void returnAnnualPackage(ReturnAnnualPackageReq req );


    /**
     * 保护膜接件列表
     * @param req
     * @return
     */
    Page<SelectResistFilmRes> selectResistFilm( SelectResistFilmReq req );


    /**
     * 保护膜接件列表导出
     * @param req
     * @param response
     */
    void exportResistFilm(SelectResistFilmReq req, HttpServletResponse response);

    /**
     * 损耗详情查询
     * @param req
     * @return
     */
    LossDetailsRes selectLossDetails(LossDetailsReq req);

    /**
     * 判断小件单是否服务商品
     * @param smallproId
     * @return
     */
    boolean getSmallproIsServiceProduct(Integer smallproId);
}
