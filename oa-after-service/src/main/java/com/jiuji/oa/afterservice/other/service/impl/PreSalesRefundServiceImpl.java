package com.jiuji.oa.afterservice.other.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.MD5;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.jiuji.oa.afterservice.batchreturn.enums.BatchReturnWayEnum;
import com.jiuji.oa.afterservice.bigpro.bo.WeixinUserInfoBo;
import com.jiuji.oa.afterservice.bigpro.po.RecoverMarketinfo;
import com.jiuji.oa.afterservice.bigpro.po.Shouhou;
import com.jiuji.oa.afterservice.bigpro.po.WeixinUser;
import com.jiuji.oa.afterservice.bigpro.service.RecoverMarketinfoService;
import com.jiuji.oa.afterservice.bigpro.service.ShouhouService;
import com.jiuji.oa.afterservice.bigpro.service.WeixinUserService;
import com.jiuji.oa.afterservice.bigpro.wrapper.PreSaleRefundWrapper;
import com.jiuji.oa.afterservice.common.bo.OaUserBO;
import com.jiuji.oa.afterservice.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.afterservice.common.constant.PayConstants;
import com.jiuji.oa.afterservice.common.constant.RedisKeys;
import com.jiuji.oa.afterservice.common.constant.UrlConstant;
import com.jiuji.oa.afterservice.common.enums.BusinessTypeEnum;
import com.jiuji.oa.afterservice.common.enums.MTableInfoEnum;
import com.jiuji.oa.afterservice.common.enums.TuihuanKindEnum;
import com.jiuji.oa.afterservice.common.exception.CustomizeException;
import com.jiuji.oa.afterservice.common.util.CommenUtil;
import com.jiuji.oa.afterservice.other.bo.*;
import com.jiuji.oa.afterservice.other.dao.ReturnsDetailMapper;
import com.jiuji.oa.afterservice.other.dao.ShouhouTuihuanMapper;
import com.jiuji.oa.afterservice.other.enums.AreaKindEnum;
import com.jiuji.oa.afterservice.other.enums.PreSalesRefundVerifyTypeEnum;
import com.jiuji.oa.afterservice.other.po.Areainfo;
import com.jiuji.oa.afterservice.other.po.NetPayRefundInfo;
import com.jiuji.oa.afterservice.other.po.ShouhouTuihuan;
import com.jiuji.oa.afterservice.other.service.*;
import com.jiuji.oa.afterservice.other.vo.req.PreSalesRefundReq;
import com.jiuji.oa.afterservice.other.vo.res.PreSaleRefundTimeLinesRes;
import com.jiuji.oa.afterservice.other.vo.res.PreSalesRefundRes;
import com.jiuji.oa.afterservice.smallpro.bo.smallproExchange.SmallproPayInfoBO;
import com.jiuji.oa.afterservice.smallpro.bo.smallproInfo.SmallProReturnTypeBO;
import com.jiuji.oa.afterservice.smallpro.service.NetpayRecordService;
import com.jiuji.oa.afterservice.smallpro.service.SmallproDetailsExService;
import com.jiuji.oa.afterservice.smallpro.service.SmallproRefundExService;
import com.jiuji.oa.afterservice.smallpro.vo.res.SmallproGetWxUserInfoRes;
import com.jiuji.oa.afterservice.sub.enums.CheckStatusEnum;
import com.jiuji.oa.afterservice.sub.po.Sub;
import com.jiuji.oa.afterservice.sub.po.SubReturnCheckBefore;
import com.jiuji.oa.afterservice.sub.service.SubReturnCheckBeforeService;
import com.jiuji.oa.afterservice.sub.service.SubService;
import com.jiuji.oa.loginfo.order.service.SubLogsCloud;
import com.jiuji.oa.loginfo.order.vo.res.SubLogRes;
import com.jiuji.oa.orginfo.sysconfig.client.SysConfigClient;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.utils.constants.NumberConstant;
import com.jiuji.tc.utils.constants.SubCheckStatusEnum;
import com.jiuji.tc.utils.constants.SysConfigConstant;
import com.jiuji.tc.utils.enums.EnumUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.MessageFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;
import java.util.concurrent.ForkJoinPool;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;


/**
 * <AUTHOR> quan
 * @description: 售前退订接口类
 * @date 2021/7/6 15:04
 */
@Service
@RequiredArgsConstructor
public class PreSalesRefundServiceImpl implements PreSalesRefundService {

    private final ReturnsDetailMapper returnsDetailMapper;
    private final SubService subService;
    private final SmallproDetailsExService smallproDetailsExService;
    private final SmallproRefundExService refundExService;
    private final AbstractCurrentRequestComponent currentRequestComponent;
    private final RedisTemplate redisTemplate;
    private final PayConfigService payConfigService;
    private final ShouhouTuihuanService shouhouTuihuanService;
    private final NetPayRefundInfoService netPayRefundInfoService;
    private final NetpayRecordService netpayRecordService;
    private final AreainfoService areainfoService;
    private final SubReturnCheckBeforeService preSalesAuthCheckService;
    private final SubLogsCloud subLogsCloud;
    private final ShouhouTuihuanMapper shouhouTuihuanMapper;
    private final WeixinUserService wechatService;
    private final RecoverMarketinfoService marketInfoService;
    private final RecoverMarketinfoService recoverMarketinfoService;
    private final ShouhouService shouhouService;
    private final WeixinUserService weixinUserService;
    private final SysConfigClient sysConfigClient;

    private static final String WECHAT_NAME = "微信";
    private static final String XING_YE_YING_HANG_QR = "兴业银行扫码";

    @Override
    public R<PreSalesRefundRes> getRefundInfoBySubId(Integer subId, Integer businessType) {
        OaUserBO currentStaff = currentRequestComponent.getCurrentStaffId();

        CompletableFuture<SubInfoBo> subInfoFuture = CompletableFuture.supplyAsync(() -> buildSubInfo(subId, businessType));
        CompletableFuture<List<ShouHouTuiHuanBo>> refundFuture = CompletableFuture
                .supplyAsync(() -> returnsDetailMapper.getShouhouTuiHuanListBySubId(subId, getRefundKindByBusinessType(businessType)), ForkJoinPool.commonPool());
        CompletableFuture<BigDecimal> btFuture = CompletableFuture
                .supplyAsync(() -> returnsDetailMapper.getKuBaiTiaoPriceBySubId(subId, getRefundKindByBusinessType(businessType), payConfigService.getKuBaiTiaoNameList()), ForkJoinPool.commonPool());
        CompletableFuture<SubOnlinePayItemBo> onlinePayFuture = CompletableFuture
                .supplyAsync(() -> returnsDetailMapper.getSubOnlinePayInfo(subId, businessType), ForkJoinPool.commonPool());
        CompletableFuture<List<SmallProReturnTypeBO>> returnWayFuture = CompletableFuture.supplyAsync(() ->
                smallproDetailsExService.getSmallProReturnWaysSaas(subId, smallproDetailsExService.getReturnTypeByBusinessType(businessType), currentStaff.getAreaId()));
        CompletableFuture.allOf(subInfoFuture, refundFuture, btFuture, onlinePayFuture, returnWayFuture).join();

        SubInfoBo sub = subInfoFuture.join();
        PreSalesRefundRes result = new PreSalesRefundRes();
        result.setIsLock(Optional.ofNullable(sub.getIsLock()).orElse(NumberConstant.ZERO));
        List<ShouHouTuiHuanBo> refundList = Optional.ofNullable(refundFuture.join()).orElseGet(Collections::emptyList);

        //历史记录
        result.setRefundHistory(refundList.stream().filter(t -> t.getCheck3() != null && t.getCheck3()).collect(Collectors.toList()));
        SubOnlinePayItemBo onlinePayPrice = Optional.ofNullable(onlinePayFuture.join()).orElse(null);
        if (onlinePayPrice == null) {
            return R.success("暂无可退订信息", result);
        }
        buildRefundKind(result, businessType);
        //退订验证信息
        ShouHouTuiHuanBo processingRefundInfo = refundList.stream().filter(t -> !CommenUtil.isCheckTrue(t.getCheck3())).findFirst().orElse(null);
        //构建维信息信息
        buildWechatInfo(processingRefundInfo, result, sub, currentStaff);
        //是否在处理中
        if (processingRefundInfo != null) {
            result.setStatus(NumberConstant.TWO);
            result.setShouHouTuiHuan(processingRefundInfo);
            return R.success("获取成功", result);
        }
        result.setPrice(onlinePayPrice.getYiFuM());
        result.setRefundPrice(onlinePayPrice.getYiFuM());
        result.setKuBaiTiaoPrice(Optional.ofNullable(btFuture.join()).orElse(BigDecimal.ZERO));
        List<SmallProReturnTypeBO> returnWay = returnWayFuture.join();
        List<String> payWayList = returnWay.stream().filter(SmallProReturnTypeBO::getIsHavePayInfo).map(SmallProReturnTypeBO::getReturnName).collect(Collectors.toList());
        List<SmallproPayInfoBO> payInfoList = this.getPayInfo(subId, businessType, payWayList);
        returnWay.stream().map(r -> {
            List<SmallproPayInfoBO> originPathList = payInfoList.stream().filter(p -> Objects.equals(r.getReturnName().replace("返回", ""), p.getPayWay()))
                    .collect(Collectors.toList());
            r.setIsOriginPath(Boolean.FALSE);
            if (CollectionUtils.isNotEmpty(originPathList)) {
                r.setNetPayRecordList(originPathList);
                r.setIsOriginPath(Boolean.TRUE);
            }
            return r;
        }).collect(Collectors.toList());
        result.setNetPayRecordList(payInfoList);
        result.setReturnWay(returnWay);
        return R.success("操作成功", result);
    }

    private void buildWechatInfo(ShouHouTuiHuanBo processingRefundInfo, PreSalesRefundRes result, SubInfoBo sub, OaUserBO currentStaff) {
        AtomicReference<SmallproGetWxUserInfoRes> wechatInfo = new AtomicReference<>(new SmallproGetWxUserInfoRes());
        Optional.ofNullable(processingRefundInfo).filter(Objects::nonNull)
                .ifPresent(k -> {
                    Integer refundId = processingRefundInfo.getId();
                    AlipayAuthBO alipayAuthInfo = refundExService.getAlipayAuthInfo(String.valueOf(processingRefundInfo.getId()), String.valueOf(NumberConstant.ONE));
                    wechatInfo.set(wechatService.getWechatUserInfo(sub.getUserId(), refundId, sub.getAreaId()));
                    result.setAlipayAuthInfo(alipayAuthInfo);
                });
        //微信绑定信息校验
        Optional.ofNullable(processingRefundInfo == null).filter(Boolean::booleanValue)
                .ifPresent(k -> {
                    String wxBindUrl = weixinUserService.getWxBindUrl(sub.getUserId(), sub.getAreaId(), "", NumberConstant.TWO);
                    wechatInfo.get().setIsBind(Boolean.TRUE);
                    Optional.ofNullable(wxBindUrl).filter(StringUtils::isNotEmpty)
                            .ifPresent(kk ->
                                    wechatInfo.get().setIsBind(Boolean.FALSE)
                                            .setBindUrl(wxBindUrl)
                                            .setWxUserInfo(null));
                });
        result.setWechatInfo(wechatInfo.get());
        //校验用户有没有绑定九机网微信信息
        if (Objects.isNull(processingRefundInfo)) {
            String host = Optional.ofNullable(sysConfigClient.getValueByCodeAndXtenant(SysConfigConstant.M_URL, currentStaff.getXTenant())).map(R::getData).orElse("");
            String url = MessageFormat.format(UrlConstant.WECHAT_BIND_URL, host, sub.getUserId());
            String userJson = HttpUtil.get(url);
            if (StringUtils.isNotEmpty(userJson)) {
                WeixinUserInfoBo weixinUserInfoBo = Optional.ofNullable(JSON.parseObject(userJson, new TypeReference<R<WeixinUserInfoBo>>() {
                })).map(R::getData).filter(Objects::nonNull).orElse(null);
                Optional.of(weixinUserInfoBo != null && StringUtils.isNotEmpty(weixinUserInfoBo.getOpenid())).filter(Boolean::booleanValue)
                        .ifPresent(k -> {
                            wechatInfo.get().setIsBind(Boolean.TRUE);
                            wechatInfo.get().setWxUserInfo(PreSaleRefundWrapper.buildWxUserInfoBO(weixinUserInfoBo));
                            result.setWechatInfo(wechatInfo.get());
                        });
            }
        }
    }

    private void buildRefundKind(PreSalesRefundRes result, Integer businessType) {
        BusinessTypeEnum businessTypeEnum = Optional.ofNullable(EnumUtil.getEnumByCode(BusinessTypeEnum.class, businessType))
                .orElseThrow(() -> new CustomizeException("订单类型不存在!"));
        switch (businessTypeEnum) {
            case NEW_ORDER:
                result.setRefundKind(TuihuanKindEnum.TDJ.getCode());
                break;
            case LP_ORDER:
                result.setRefundKind(TuihuanKindEnum.TDJ_LP.getCode());
                break;
            case AFTER_ORDER:
                result.setRefundKind(TuihuanKindEnum.TWXF.getCode());
                break;
            default:
                break;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<Boolean> submitRefund(PreSalesRefundReq req) {
        OaUserBO currentStaff = currentRequestComponent.getCurrentStaffId();
        //校验退款信息
        checkRefundInfo(req, currentStaff);
        autoRefundKuBaiTiaoPrice(req);
        ShouhouTuihuan th = PreSaleRefundWrapper.buildAfterRefundEntity(req, currentStaff);
        shouhouTuihuanService.save(th);
        Optional.of(CollectionUtils.isNotEmpty(req.getRefundItems())
                && req.getRefundItems().stream().anyMatch(t -> t.getPrice() != null && t.getPrice().compareTo(BigDecimal.ZERO) > 0)).filter(Boolean::booleanValue)
                .ifPresent(k -> {
                    BigDecimal itemPrice = req.getRefundItems().stream().filter(t -> Objects.nonNull(t.getRefundPrice())).map(PreSalesRefundReq.PreSalesRefundItem::getRefundPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
                    Optional.ofNullable(itemPrice.compareTo(req.getRefundPrice()) > NumberConstant.ZERO).filter(Boolean::booleanValue)
                            .ifPresent(kk -> {
                                throw new CustomizeException("退款金额错误，请核对");
                            });
                    req.getRefundItems().forEach(m -> {
                        NetPayRefundInfo refundInfo = new NetPayRefundInfo()
                                .setNetRecordId(m.getNetPayRecordId())
                                .setPrice(m.getRefundPrice())
                                .setReturnid(th.getId())
                                .setDtime(LocalDateTime.now())
                                .setInuser(currentStaff.getUserName());
                        netPayRefundInfoService.save(refundInfo);
                        Integer count = netpayRecordService.updateRefundPrice(m.getRefundPrice(), m.getNetPayRecordId());
                        Optional.of(CommenUtil.isNullOrZero(count)).filter(Boolean::booleanValue)
                                .ifPresent(b -> {
                                    throw new CustomizeException("退款金额错误！");
                                });
                    });
                });
        return R.success("操作成功！");
    }

    @Override
    public List<SmallproPayInfoBO> getPayInfo(Integer subId, Integer businessType, List<String> payWayList) {
        List<String> tuiWayList = payWayList
                .stream()
                .map(t -> t.replace("返回", ""))
                .collect(Collectors.toList());
        if (tuiWayList.stream().anyMatch(t -> Objects.equals(t, WECHAT_NAME))) {
            tuiWayList.add("微信");
            tuiWayList.add("微信APP");
        }
        if (tuiWayList.stream().anyMatch(t -> Objects.equals(t, XING_YE_YING_HANG_QR))) {
            tuiWayList.add("扫码枪");
            tuiWayList.add(XING_YE_YING_HANG_QR);
        }
        return returnsDetailMapper.getPayInfo(StrUtil.toString(subId), businessType, tuiWayList);
    }

    @Override
    public List<PreSaleRefundTimeLinesRes> getPreSaleRefundTimeLines() {
        List<PreSaleRefundTimeLinesRes> result = new LinkedList<>();
        result.add(new PreSaleRefundTimeLinesRes().setRefundWay("现金、余额")
                .setHandleTimeLines(Collections.singletonList("审核完毕15分钟内"))
                .setAccountTimeLines(Arrays.asList("根据客户支付方式不同：", "1.余额支付：1-3个工作日", "2.银行卡快捷支付：根据发卡行到账时效，一般1-7个工作日")));
        result.add(new PreSaleRefundTimeLinesRes().setRefundWay("在线支付（微信、支付宝、通联直联）")
                .setHandleTimeLines(Collections.singletonList("审核完毕15分钟内"))
                .setAccountTimeLines(Collections.singletonList("3-7个工作日")));
        result.add(new PreSaleRefundTimeLinesRes().setRefundWay("建行分期")
                .setHandleTimeLines(Arrays.asList("1. 提交时间：9:00-18:00，3小时内审核办理", "2. 提交时间：18:00-次日9:00,次日11:00前审核办理"))
                .setAccountTimeLines(Arrays.asList("1.本金：到账时间3-5个工作日", "2.手续费：请客户在确认本金到账且分期结清后，致电建行信用卡中心客服申请退还手续费")));
        result.add(new PreSaleRefundTimeLinesRes().setRefundWay("银行转账")
                .setHandleTimeLines(Arrays.asList("提交时间：9:00-18:00，3小时内审核办理，即时到账", "2.提交时间：18:00-次日9:00,次日11:00前审核办理，即时到账"))
        );
        return result;
    }

    /**
     * 校验提交参数信息
     *
     * @param req
     * @param currentStaff
     */
    private void checkRefundInfo(PreSalesRefundReq req, OaUserBO currentStaff) {
        //校验订单状态
        SubInfoBo subInfo = buildSubInfo(req);

        //退换类型校验
        CompletableFuture<Void> checkRefundKindFuture = CompletableFuture.runAsync(() -> this.checkRefundKind(req), ForkJoinPool.commonPool());
        //微信秒退提交验证
        CompletableFuture<Void> checkWechatRefundFuture = CompletableFuture.runAsync(() -> this.checkWechatRefund(req, Math.toIntExact(subInfo.getUserId()), currentStaff), ForkJoinPool.commonPool());
        //校验是否有进行中的订单
        CompletableFuture<Void> refundInfoFuture = CompletableFuture.runAsync(() -> this.checkExistsProcessingRefundInfo(subInfo, req), ForkJoinPool.commonPool());
        //校验退款金额
        CompletableFuture<Void> checkRefundPriceFuture = CompletableFuture.runAsync(() -> checkRefundPrice(req), ForkJoinPool.commonPool());
        //校验验证码
        CompletableFuture<Void> checkVerifyCodeFuture = CompletableFuture.runAsync(() -> checkVerifyCode(req, subInfo), ForkJoinPool.commonPool());
        //校验加盟店不支持余额退款
        CompletableFuture<Void> checkJoinedStoreFuture = CompletableFuture.runAsync(() -> checkJoinedStore(req, currentStaff), ForkJoinPool.commonPool());
        // 支付方式、退款方式校验
        //1、只能以【支付宝(pay1)返回】退订！ 2、网上支付订单只能原路径退订
        CompletableFuture<Void> checkPayWayAndRefundWayFuture = CompletableFuture.runAsync(() -> checkPayWayAndRefundWay(req), ForkJoinPool.commonPool());
        //校验原路径退款以及交易号信息
        CompletableFuture<Void> checkOnlinePayFuture = CompletableFuture.runAsync(() -> checkOnlinePay(req), ForkJoinPool.commonPool());
        //校验操作地区
        CompletableFuture<Void> checkOperateAreaFuture = CompletableFuture.runAsync(() -> checkOperateArea(subInfo, currentStaff), ForkJoinPool.commonPool());

        try {
            //等待所有任务执行结束，每个任务执行异常会被CompletableFuture捕获转换成CompletionException，这里需要扔出自定义业务异常，需要try-catch转换处理
            CompletableFuture.allOf(
                    checkRefundKindFuture,
                    checkWechatRefundFuture,
                    refundInfoFuture,
                    checkRefundPriceFuture,
                    checkVerifyCodeFuture,
                    checkJoinedStoreFuture,
                    checkPayWayAndRefundWayFuture,
                    checkOnlinePayFuture,
                    checkOperateAreaFuture).join();
        } catch (CompletionException e) {
            if (e.getCause() instanceof CustomizeException) {
                throw new CustomizeException(((CustomizeException) e.getCause()).getMsg());
            } else {
                throw e;
            }
        }
    }

    private void lpRefundCheck(PreSalesRefundReq req) {
        //退订【二手良品】
        RecoverMarketinfo recoverMarketinfo = recoverMarketinfoService.getById(req.getSubId());
        if (recoverMarketinfo != null && Objects.equals(recoverMarketinfo.getSubCheck(), SubCheckStatusEnum.GOING.getCode())) {
            req.setDiscountPrice(BigDecimal.ZERO);
        }
    }

    private void subRefundCheck(PreSalesRefundReq req) {
        Sub sub = Optional
                .ofNullable(subService.getOne(new LambdaQueryWrapper<Sub>()
                        .eq(Sub::getSubId, req.getSubId())
                        .notIn(Sub::getSubCheck,
                                Arrays.asList(SubCheckStatusEnum.DELETED.getCode(),
                                        SubCheckStatusEnum.FINISHED.getCode(),
                                        SubCheckStatusEnum.RETURN.getCode(),
                                        SubCheckStatusEnum.REFUND.getCode()))))
                .orElseThrow(() -> new CustomizeException("当前订单状态不可操作"));

        Optional.of(req.getRefundPrice().compareTo(Optional.ofNullable(sub.getYifuM()).orElse(BigDecimal.ZERO)) > 0).filter(Boolean::booleanValue).ifPresent(b -> {
            throw new CustomizeException(MessageFormat.format("退款金额不能大于全款{0,number,#.##}！", sub.getYifuM()));
        });
        boolean returnAll = req.getRefundPrice().compareTo(Optional.ofNullable(sub.getYifuM()).orElse(BigDecimal.ZERO)) == 0;
        Integer subId = req.getSubId();
        Optional.of(returnAll && CommenUtil.isNotNullZero(returnsDetailMapper.getBeiHuoMkcCount(subId))).filter(Boolean::booleanValue).ifPresent(b -> {
            throw new CustomizeException("存在备货记录，不能退订，请核对！");
        });
        Optional.of(returnAll && CommenUtil.isNotNullZero(returnsDetailMapper.getBeiHuoBasketId(subId))).filter(Boolean::booleanValue).ifPresent(b -> {
            throw new CustomizeException("在备货锁定项，不能退订，请核对！");
        });
        Optional.of(returnAll && CommenUtil.isNotNullZero(returnsDetailMapper.getOutPutKcBasketId(subId))).filter(Boolean::booleanValue).ifPresent(b -> {
            throw new CustomizeException("存在已出库商品，不能退订，请核对！");
        });

        // todo 洛克订单 后续补充完善

        //支付宝优惠码的必须原路径退款
        List<Integer> aliPayPpidList = Optional.ofNullable(String.valueOf(redisTemplate.opsForValue().get(RedisKeys.ALIPAY_COUPON_PPID)))
                .map(CommenUtil::toIntList)
                .orElseThrow(() -> new CustomizeException("获取支付宝优惠券信息失败"));
        Optional.of(Objects.equals(req.getRefundWay(), "支付宝(pay1)返回")
                && CommenUtil.isCheckTrue(returnsDetailMapper.checkAlipayCoupon(req.getSubId(), aliPayPpidList)))
                .filter(Boolean::booleanValue)
                .ifPresent(b -> {
                    throw new CustomizeException("此订单只能以【支付宝(pay1)返回】退订！");
                });
        //校验网络订单支付
        List<BigDecimal> subPay03List = returnsDetailMapper.getSubPay03(req.getSubId());
        Optional.of(CollectionUtils.isNotEmpty(subPay03List)
                && Objects.equals(req.getRefundWay(), "现金")
                && subPay03List.stream().allMatch(s -> s.compareTo(BigDecimal.ZERO) > 0))
                .filter(Boolean::booleanValue)
                .ifPresent(b -> {
                    throw new CustomizeException("网上支付订单请原路径退订！");
                });
    }

    /**
     * 校验退订方式
     *
     * @param req
     */
    private void checkRefundKind(PreSalesRefundReq req) {
        BusinessTypeEnum businessTypeEnum = Optional
                .ofNullable(EnumUtil.getEnumByCode(BusinessTypeEnum.class, req.getBusinessType()))
                .orElseThrow(() -> new CustomizeException("订单类型有误"));
        switch (businessTypeEnum) {
            case NEW_ORDER:
                subRefundCheck(req);
                break;
            case LP_ORDER:
                lpRefundCheck(req);
                break;
            case AFTER_ORDER:
                Shouhou shouhou = Optional.ofNullable(CommenUtil.autoQueryHist(() ->shouhouService.getById(req.getShouhouId()), MTableInfoEnum.SHOUHOU,req.getShouhouId())).orElseThrow(() -> new CustomizeException("售后单号有误"));

                if (shouhou != null) {
                    req.setDiscountPrice(BigDecimal.ZERO);
                }
                break;
            default:
                throw new CustomizeException("暂不支持该退订方式");
        }
    }

    private boolean checkExistsProcessingRefundInfo(SubInfoBo sub, PreSalesRefundReq req) {
        //校验是否有进行中的退款信息
        List<ShouHouTuiHuanBo> processingRecords = returnsDetailMapper.getShouhouTuiHuanListBySubId(sub.getSubId(), req.getRefundKind());
        Optional.of(CollectionUtils.isNotEmpty(processingRecords) &&
                processingRecords
                        .stream()
                        .anyMatch(t -> (t.getCheck3() == null || !t.getCheck3()) && Objects.equals(req.getRefundKind(), t.getRefundKind())))
                .filter(Boolean::booleanValue)
                .ifPresent(b -> {
                    throw new CustomizeException("存在进行中的退订信息，请勿重复提交");
                });
        //校验售后单
        LambdaQueryWrapper<ShouhouTuihuan> queryWrapper = new LambdaQueryWrapper<ShouhouTuihuan>()
                .eq(ShouhouTuihuan::getShouhouId, req.getShouhouId())
                .eq(ShouhouTuihuan::getTuihuanKind, req.getRefundKind())
                .isNull(ShouhouTuihuan::getCheck3);
        Optional.of(
                CommenUtil.isNotNullZero(req.getShouhouId()) && Objects.equals(req.getRefundKind(), TuihuanKindEnum.TDJ_WXF.getCode())
                        && shouhouTuihuanService.count(queryWrapper) > NumberConstant.ZERO)
                .filter(Boolean::booleanValue)
                .ifPresent(b -> {
                    throw new CustomizeException("已经存在退款记录!");
                });
        return true;
    }

    private static void checkRefundPrice(PreSalesRefundReq req) {
        Optional.of(!Objects.equals(req.getRefundWay(), BatchReturnWayEnum.BALANCE.getMessage()) && req.getRefundItems()
                .stream()
                .anyMatch(b -> Optional.ofNullable(b.getRefundPrice()).orElse(BigDecimal.ZERO)
                        .compareTo(Optional.ofNullable(b.getPrice()).orElse(BigDecimal.ZERO)) > 0))
                .filter(Boolean::booleanValue)
                .ifPresent(b -> {
                    throw new CustomizeException("退款金额错误，请核对后重新提交");
                });
    }

    private static void checkJoinedStore(PreSalesRefundReq req, OaUserBO currentStaff) {
        Integer areaKind1 = Optional.ofNullable(currentStaff.getAreaKind1())
                .orElseThrow(() -> new CustomizeException("获取门店类型失败"));
        Optional.of(!Objects.equals(areaKind1, NumberConstant.ONE) && Objects.equals(req.getRefundWay(), "余额"))
                .filter(Boolean::booleanValue)
                .ifPresent(b -> {
                    throw new CustomizeException("加盟店不支持余额退款");
                });
    }

    private void checkVerifyCode(PreSalesRefundReq req, SubInfoBo sub) {
        Optional.of(StringUtils.isEmpty(req.getVerifyCode())
                && !Objects.equals(req.getVerifyType(), PreSalesRefundVerifyTypeEnum.AUTH_VERIFY.getCode())
        ).filter(Boolean::booleanValue)
                .ifPresent(b -> {
                    throw new CustomizeException("验证码为空！");
                });
        PreSalesRefundVerifyTypeEnum verifyTypeEnum = EnumUtil.getEnumByCode(PreSalesRefundVerifyTypeEnum.class, req.getVerifyType());
        switch (verifyTypeEnum) {
            case SMS_VERIFY:
                checkSmsCode(req);
                break;
            case PAY_PWD_VERIFY:
                checkPayPwd(Math.toIntExact(sub.getUserId()), req.getVerifyCode());
                break;
            case AUTH_VERIFY:
                authCheck(sub.getSubId());
                break;
            default:
                throw new CustomizeException("验证类型不存在");
        }
    }

    private void checkSmsCode(PreSalesRefundReq req) {
        String redisKey = MessageFormat.format("{0,number,#}_returnMoney_{1,number,#}", req.getSubId(), req.getRefundKind());
        Optional.of(!CommenUtil.isCheckTrue(redisTemplate.hasKey(redisKey)))
                .filter(Boolean::booleanValue)
                .ifPresent(b -> {
                    throw new CustomizeException("请先发送验证码");
                });
        AtomicReference<String> code = new AtomicReference<>(String.valueOf(redisTemplate.opsForValue().get(redisKey)));
        Optional.ofNullable(code.get()).filter(StringUtils::isNotEmpty)
                .ifPresent(k ->
                        code.set(code.get().replaceAll("\"", "")));
        if (StringUtils.isNotEmpty(req.getVerifyCode()) && !Objects.equals(req.getVerifyCode(), code.get())) {
            throw new CustomizeException("验证码错误！");
        }
    }

    private void checkPayPwd(Integer userId, String payPwd) {
        MemberPayPwdInfo memberPayPwdInfo = Optional.ofNullable(returnsDetailMapper.getMemberPayPwdInfo(userId)).orElseThrow(() -> new CustomizeException("会员信息不存在"));
        Optional.of(StringUtils.isEmpty(memberPayPwdInfo.getPayPwd())).filter(Boolean::booleanValue)
                .ifPresent(b -> {
                    throw new CustomizeException("用户未设置支付密码！");
                });
        StringBuilder sb = new StringBuilder();
        sb.append(memberPayPwdInfo.getSaltPay())
                .append(payPwd)
                .append("9ji.com");
        String pwdStr = sb.toString();
        String md5Str = MD5.create().digestHex(pwdStr).toUpperCase(Locale.getDefault());
        Optional.of(!Objects.equals(md5Str, memberPayPwdInfo.getPayPwd().toUpperCase(Locale.getDefault())))
                .filter(Boolean::booleanValue)
                .ifPresent(b -> {
                    throw new CustomizeException("支付密码验证失败！");
                });
    }

    private void authCheck(Integer subId) {
        Integer count = preSalesAuthCheckService.count(new LambdaQueryWrapper<SubReturnCheckBefore>()
                .eq(SubReturnCheckBefore::getSubId, subId)
                .eq(SubReturnCheckBefore::getSubType, BusinessTypeEnum.NEW_ORDER.getCode())
                .eq(SubReturnCheckBefore::getStatus, CheckStatusEnum.PASS.getCode()));
        Optional.of(CommenUtil.isNullOrZero(count)).filter(Boolean::booleanValue)
                .ifPresent(b -> {
                    throw new CustomizeException("验证码授权验证审核校验不通过");
                });
    }

    private void checkPayWayAndRefundWay(PreSalesRefundReq req) {

        //自提点余额退订校验 新机/良品退订
        Optional.of(Objects.equals(req.getRefundWay(), "自提点余额")
                && Arrays.asList(TuihuanKindEnum.TDJ.getCode(), TuihuanKindEnum.TPJ.getCode(), TuihuanKindEnum.TDJ_LP.getCode()).contains(req.getRefundKind())
                && !CommenUtil.isCheckTrue(returnsDetailMapper.checkZtAccount(req.getSubId())))
                .filter(Boolean::booleanValue)
                .ifPresent(b -> {
                    throw new CustomizeException("自提点账户查找失败!");
                });
    }

    private void checkOnlinePay(PreSalesRefundReq req) {
        //CheckOnlinePay 目的 原路径退款必须确定交易号
        boolean isPayOnline = checkOnlinePayByRefundWay(req.getRefundWay());
        Optional.of(isPayOnline && CollectionUtils.isEmpty(req.getRefundItems()))
                .filter(Boolean::booleanValue)
                .ifPresent(b -> {
                    throw new CustomizeException("原路径退款请先确定并选择交易号！");
                });
    }

    private PreSalesRefundReq autoRefundKuBaiTiaoPrice(PreSalesRefundReq req) {
        //库分期金额自动提交退订
        if (req.getKuBaiTiaoPrice() == null || req.getKuBaiTiaoPrice().compareTo(BigDecimal.ZERO) == 0) {
            return req;
        }
        List<KuBaiTiaoPriceInfoBo> kuBaiTiaoPriceList = returnsDetailMapper.getKuBaiTiaoPriceInfo(req.getSubId(), payConfigService.getKuBaiTiaoNameList());
        Optional.of(CollectionUtils.isEmpty(kuBaiTiaoPriceList))
                .filter(Boolean::booleanValue)
                .ifPresent(b -> {
                    throw new CustomizeException("未找到库分期支付记录！");
                });
        double tmpPrice = req.getKuBaiTiaoPrice().doubleValue();
        double realKuBaiTiaoPrice = 0.0;
        for (KuBaiTiaoPriceInfoBo k : kuBaiTiaoPriceList) {
            double leftPrice = Optional.ofNullable(k.getLeftPrice()).orElse(0.0);
            if (tmpPrice > leftPrice) {
                realKuBaiTiaoPrice += leftPrice;
                req.getRefundItems().add(new PreSalesRefundReq.PreSalesRefundItem().setNetPayRecordId(k.getId()).setPrice(BigDecimal.valueOf(leftPrice)));
                tmpPrice = tmpPrice - leftPrice;
            } else {
                realKuBaiTiaoPrice += realKuBaiTiaoPrice;
                req.getRefundItems().add(new PreSalesRefundReq.PreSalesRefundItem().setNetPayRecordId(k.getId()).setPrice(BigDecimal.valueOf(tmpPrice)));
                tmpPrice = 0;
            }
        }
        req.setKuBaiTiaoPrice(BigDecimal.valueOf(realKuBaiTiaoPrice));
        return req;
    }

    private void checkOperateArea(SubInfoBo sub, OaUserBO currentStaff) {
        Areainfo areaInfo = areainfoService.getById(sub.getAreaId());
        Optional.of(!Objects.equals(currentStaff.getAreaId(), sub.getAreaId()))
                .filter(Boolean::booleanValue)
                .ifPresent(b -> {
                    throw new CustomizeException(MessageFormat.format("地区不符，请切换至{0}再操作", areaInfo.getArea()));
                });
    }

    private void checkWechatRefund(PreSalesRefundReq req, Integer userId, OaUserBO currentStaff) {
        if (!Arrays.asList(WECHAT_REFUND_WAY, ALIPAY_REFUND_WAY).contains(req.getRefundWay())) {
            return;
        }
        //修改记录校验
        List<SubLogRes> subLogs = Optional.ofNullable(subLogsCloud.getSubLogsListJSON(req.getSubId())).map(R::getData).orElseGet(Collections::emptyList);
        Optional.of(subLogs
                .stream()
                .anyMatch(l -> StringUtils.isNotEmpty(l.getComment()) && l.getComment().contains("手机由")))
                .filter(Boolean::booleanValue)
                .ifPresent(b -> {
                    throw new CustomizeException("该订单不能使用此退款方式，原因：存在手机修改记录！");
                });
        //加盟校验，加盟店非网上支付的不能提交
        Optional.of(
                !Objects.equals(currentStaff.getAreaKind1(), AreaKindEnum.SELF.getCode())
                        && !Arrays.asList(TuihuanKindEnum.TDJ.getCode(), TuihuanKindEnum.TDJ_LP.getCode(), TuihuanKindEnum.TDJ_WXF.getCode()).contains(req.getRefundKind()))
                .filter(Boolean::booleanValue)
                .ifPresent(b -> {
                    throw new CustomizeException(MessageFormat.format("暂不支持{0}！", req.getRefundWay()));
                });
        //网络订单退订校验
        String type = "";
        if (Objects.equals(req.getRefundKind(), TuihuanKindEnum.TDJ_LP.getCode())) {
            type = "2";
        }
        BigDecimal subPay03 = Optional.ofNullable(shouhouTuihuanMapper.getSubPaySum(req.getSubId(), Arrays.asList("订金" + type, "交易" + type))).orElse(BigDecimal.ZERO);
        Optional.of(
                BigDecimal.ZERO.compareTo(subPay03) >= NumberConstant.ZERO)
                .filter(Boolean::booleanValue)
                .ifPresent(b -> {
                    throw new CustomizeException("该订单不能使用此退款方式，原因：非网上支付订单！");
                });
        BigDecimal wechatRefundPrice = Optional.ofNullable(shouhouTuihuanMapper.getWechatTuikuanM(req.getSubId())).orElse(BigDecimal.ZERO);
        Optional.of(
                wechatRefundPrice.add(req.getRefundPrice()).compareTo(subPay03) > NumberConstant.ZERO)
                .filter(Boolean::booleanValue)
                .ifPresent(b -> {
                    throw new CustomizeException("退款金额大于网上支付金额！");
                });
        //微信绑定验证
        if (Objects.equals(req.getRefundKind(), TuihuanKindEnum.TDJ.getCode())
                && Objects.equals(req.getRefundWay(), "微信秒退")) {
            WeixinUser wechatInfo = Optional.ofNullable(wechatService.getWxxinUserByUserId(userId)).orElseThrow(() -> new CustomizeException("获取用户微信信息失败"));
            Optional.of(StringUtils.isEmpty(wechatInfo.getOpenid())).filter(Boolean::booleanValue)
                    .ifPresent(b -> {
                        throw new CustomizeException("该客户还未绑定微信！");
                    });
        }
    }

    private boolean checkOnlinePayByRefundWay(String refundWay) {
        String onlinePayName = payConfigService.getNickNameByPay(refundWay);
        return PayConstants.checkOnlinePay(refundWay) || StringUtils.isNotEmpty(onlinePayName);
    }

    private SubInfoBo buildSubInfo(PreSalesRefundReq req) {
        SubInfoBo subInfo = new SubInfoBo().setSubId(req.getSubId());
        boolean lpFlag = Objects.equals(req.getRefundKind(), TuihuanKindEnum.TDJ_LP.getCode())
                || Objects.equals(req.getBusinessType(), BusinessTypeEnum.LP_ORDER.getCode());
        if (lpFlag) {
            RecoverMarketinfo recoverSub = Optional.ofNullable(recoverMarketinfoService.getById(req.getSubId())).orElseThrow(() -> new CustomizeException("良品订单信息不存在"));
            subInfo.setBusinessType(BusinessTypeEnum.LP_ORDER.getCode())
                    .setSubCheck(recoverSub.getSubCheck())
                    .setUserId(Math.toIntExact(recoverSub.getUserid()))
                    .setAreaId(recoverSub.getAreaid());
        } else {
            Sub sub = Optional.ofNullable(subService.getById(req.getSubId())).orElseThrow(() -> new CustomizeException("订单信息不存在"));
            subInfo.setBusinessType(BusinessTypeEnum.NEW_ORDER.getCode())
                    .setSubCheck(sub.getSubCheck())
                    .setUserId(Math.toIntExact(sub.getUserId()))
                    .setAreaId(sub.getAreaId());
        }
        return subInfo;
    }

    private SubInfoBo buildSubInfo(Integer subId, Integer businessType) {
        SubInfoBo subInfo = new SubInfoBo()
                .setSubId(subId);
        BusinessTypeEnum businessTypeEnum = Optional
                .ofNullable(EnumUtil.getEnumByCode(BusinessTypeEnum.class, businessType))
                .orElseThrow(() -> new CustomizeException("订单类型有误"));
        switch (businessTypeEnum) {
            //良品
            case LP_ORDER:
                buildLpInfo(subId, subInfo);
                break;
            case NEW_ORDER:
                buildSubInfo(subId, subInfo);
                break;
            default:
                throw new CustomizeException("暂不支持其他订单类型查询");
        }
        return subInfo;
    }

    private void buildSubInfo(Integer subId, SubInfoBo subInfo) {
        Sub sub = Optional
                .ofNullable(subService.getById(subId))
                .orElseThrow(() -> new CustomizeException("订单信息不存在"));
        subInfo.setBusinessType(BusinessTypeEnum.NEW_ORDER.getCode())
                .setSubCheck(sub.getSubCheck())
                .setUserId(Math.toIntExact(sub.getUserId()))
                .setAreaId(sub.getAreaId())
                .setIsLock(sub.getIsLock());
    }

    private void buildLpInfo(Integer subId, SubInfoBo subInfo) {
        RecoverMarketinfo lpSub = Optional
                .ofNullable(marketInfoService.getById(subId))
                .orElseThrow(() -> new CustomizeException("良品订单信息不存在"));
        subInfo.setBusinessType(BusinessTypeEnum.LP_ORDER.getCode())
                .setSubCheck(lpSub.getSubCheck())
                .setUserId(Math.toIntExact(lpSub.getUserid()))
                .setAreaId(lpSub.getAreaid())
                .setIsLock(lpSub.getIslock());
    }

    private static Integer getRefundKindByBusinessType(Integer businessType) {
        BusinessTypeEnum businessTypeEnum = Optional
                .ofNullable(EnumUtil.getEnumByCode(BusinessTypeEnum.class, businessType))
                .orElseThrow(() -> new CustomizeException("订单类型有误"));
        Integer refundKind;
        switch (businessTypeEnum) {
            case NEW_ORDER:
                refundKind = TuihuanKindEnum.TDJ.getCode();
                break;
            case LP_ORDER:
                refundKind = TuihuanKindEnum.TDJ_LP.getCode();
                break;
            case AFTER_ORDER:
                refundKind = TuihuanKindEnum.TDJ_WXF.getCode();
                break;
            default:
                throw new CustomizeException("暂不支持其他订单类型查询");
        }
        return refundKind;
    }
}
