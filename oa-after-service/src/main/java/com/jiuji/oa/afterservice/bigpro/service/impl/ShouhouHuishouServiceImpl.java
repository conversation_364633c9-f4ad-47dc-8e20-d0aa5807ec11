package com.jiuji.oa.afterservice.bigpro.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.servlet.ServletUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ch999.common.util.utils.HttpClientUtils;
import com.jiuji.cloud.after.vo.res.ReceiveSendResVO;
import com.jiuji.oa.afterservice.bigpro.bo.ShouhouBo;
import com.jiuji.oa.afterservice.bigpro.bo.ShouhouHuishouListBo;
import com.jiuji.oa.afterservice.bigpro.bo.ShouhouHuishouSumDataBo;
import com.jiuji.oa.afterservice.bigpro.dao.ShouhouHuishouMapper;
import com.jiuji.oa.afterservice.bigpro.enums.OaMesTypeEnum;
import com.jiuji.oa.afterservice.bigpro.po.*;
import com.jiuji.oa.afterservice.bigpro.service.*;
import com.jiuji.oa.afterservice.bigpro.statistics.dao.ShouhouBatchToAreaMapper;
import com.jiuji.oa.afterservice.bigpro.statistics.po.WuliuLogs;
import com.jiuji.oa.afterservice.bigpro.statistics.service.WuliuLogsService;
import com.jiuji.oa.afterservice.bigpro.vo.req.ShouhouHuiShouReq;
import com.jiuji.oa.afterservice.bigpro.vo.req.ShouhouHuishouListReq;
import com.jiuji.oa.afterservice.bigpro.vo.res.ShouHouHuishouListRes;
import com.jiuji.oa.afterservice.bigpro.vo.res.ShouhouPjInfoRes;
import com.jiuji.oa.afterservice.bigpro.vo.res.WeiXiuPjRes;
import com.jiuji.oa.afterservice.cloud.service.IMCloud;
import com.jiuji.oa.afterservice.common.bo.OaUserBO;
import com.jiuji.oa.afterservice.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.afterservice.common.config.xtenant.MultitenancyInterceptor;
import com.jiuji.oa.afterservice.common.constant.ShouhouConstants;
import com.jiuji.oa.afterservice.common.enums.MTableInfoEnum;
import com.jiuji.oa.afterservice.common.enums.RankEnum;
import com.jiuji.oa.afterservice.common.enums.XtenantEnum;
import com.jiuji.oa.afterservice.common.exception.CustomizeException;
import com.jiuji.oa.afterservice.common.source.InwcfUrlSource;
import com.jiuji.oa.afterservice.common.util.CommenUtil;
import com.jiuji.oa.afterservice.common.util.DateUtil;
import com.jiuji.oa.afterservice.common.util.SpringContextUtil;
import com.jiuji.oa.afterservice.other.bo.*;
import com.jiuji.oa.afterservice.other.enums.ProductLabelEnum;
import com.jiuji.oa.afterservice.other.po.Areainfo;
import com.jiuji.oa.afterservice.other.service.AreainfoService;
import com.jiuji.oa.afterservice.other.service.ReceivePersonConfigService;
import com.jiuji.oa.afterservice.other.service.VoucherService;
import com.jiuji.oa.afterservice.refund.service.RefundShouhouService;
import com.jiuji.oa.afterservice.smallpro.bo.smallproInfo.SmallproInfoInuserInfoBO;
import com.jiuji.oa.afterservice.smallpro.dao.SmallproMapper;
import com.jiuji.oa.afterservice.smallpro.po.ShouhouProRemarkLogs;
import com.jiuji.oa.afterservice.smallpro.recover.bo.ShouHouZhiHuanShenHeBo;
import com.jiuji.oa.afterservice.smallpro.recover.bo.SmallProRecoverInfo;
import com.jiuji.oa.afterservice.smallpro.recover.enums.PressScreenStatusEnum;
import com.jiuji.oa.afterservice.smallpro.recover.enums.ReceiveTypeEnum;
import com.jiuji.oa.afterservice.smallpro.recover.enums.ReceiveWTypeEnum;
import com.jiuji.oa.afterservice.smallpro.recover.enums.ToAreaStateEnum;
import com.jiuji.oa.afterservice.smallpro.recover.vo.req.*;
import com.jiuji.oa.afterservice.smallpro.recover.vo.res.*;
import com.jiuji.oa.afterservice.sys.bo.KemuFzhsItem;
import com.jiuji.oa.afterservice.sys.enums.EKemuEnum;
import com.jiuji.oa.afterservice.sys.service.AuthConfigService;
import com.jiuji.oa.afterservice.sys.service.KeMuService;
import com.jiuji.oa.afterservice.sys.service.SysConfigService;
import com.jiuji.oa.oacore.common.req.PageReq;
import com.jiuji.oa.oacore.common.res.PageRes;
import com.jiuji.oa.oacore.oaorder.WuliuCloud;
import com.jiuji.oa.oacore.oaorder.req.SubWLModelReq;
import com.jiuji.oa.orginfo.areainfo.client.AreaInfoClient;
import com.jiuji.oa.orginfo.areainfo.vo.AreaInfo;
import com.jiuji.oa.orginfo.sysconfig.client.SysConfigClient;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.common.vo.ResultCode;
import com.jiuji.tc.utils.common.CommonUtils;
import com.jiuji.tc.utils.constants.NumberConstant;
import com.jiuji.tc.utils.constants.SysConfigConstant;
import com.jiuji.tc.utils.enums.EnumUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.Cookie;
import java.math.BigDecimal;
import java.text.MessageFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-15
 */
@Slf4j
@Service
public class ShouhouHuishouServiceImpl extends ServiceImpl<ShouhouHuishouMapper, ShouhouHuishou> implements ShouhouHuishouService {

    @Lazy
    @Autowired
    private ShouhouService shouhouService;
    @Resource
    private AbstractCurrentRequestComponent currentRequestComponent;
    @Autowired
    private ProductinfoService productinfoService;
    @Resource
    private AreaInfoClient areaInfoClient;
    @Autowired
    private WxkcoutputService wxkcoutputService;
    @Autowired
    private AuthConfigService authConfigService;
    @Autowired
    private KeMuService keMuService;
    @Autowired
    private VoucherService voucherService;
    @Resource
    private InwcfUrlSource inwcfUrlSource;
    @Autowired
    private SysConfigService sysConfigService;
    @Autowired
    private TApplyinfoService tApplyinfoService;
    @Autowired
    private SmsService smsService;
    @Resource
    private AreainfoService areainfoService;
    @Resource
    private WeixinUserService weixinUserService;
    @Resource
    private SysConfigClient sysConfigClient;
    @Resource
    private IMCloud imCloud;
    @Resource
    private WuliuCloud wuliuCloud;
    @Resource
    private ReceivePersonConfigService receivePersonConfigService;
    @Resource
    private SmallproMapper smallproMapper;



    private static final String OA_AFTER_URL = "{0}/staticpc/#/after-service/order/edit/{1,number,#}";

    private static final String WECHAT_MSG = "尊敬的客户，您于{0}送修的设备（维修单号：{1,number,#}），因设备故障于{2}进行换货处理（维修单号：{3,number,#}）。如有疑问请拨打客服电话：400-008-3939。";

    @Override
    public Boolean saveShouhouHuishou(ShouhouHuishou shouhouHuishou) {

        return this.save(shouhouHuishou);
    }

    @Override
    public PageRes<ShouhouHuishou> pageHuishouToWx(PageReq req) {
        Integer count = baseMapper.countHuishouToWx();
        if (count <= 0) {
            return new PageRes<>();
        }
        Integer startRows = req.getStartRows();
        if (startRows > count) {
            return new PageRes<>();
        }

        List<ShouhouHuishou> list = baseMapper.listHuishouToWx(startRows, req.getSize());
        PageRes<ShouhouHuishou> res = new PageRes<>(req.getCurrent(), req.getSize());
        res.setTotal(count);
        res.setRecords(list);

        return res;
    }

    @Override
    public PageRes<DiaoboToWxBO> pageDiaoboToWx(PageReq req) {
        Integer count = baseMapper.countDiaoboToWx();
        if (count <= 0) {
            return new PageRes<>();
        }
        Integer startRows = req.getStartRows();
        if (startRows > count) {
            return new PageRes<>();
        }

        List<DiaoboToWxBO> list = baseMapper.listDiaoboToWx(startRows, req.getSize());
        PageRes<DiaoboToWxBO> res = new PageRes<>(req.getCurrent(), req.getSize());
        res.setTotal(count);
        res.setRecords(list);
        return res;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<Boolean> addHuiShou(ShouhouHuiShouReq req) {

        OaUserBO oaUserBO = currentRequestComponent.getCurrentStaffId();
        Shouhou shouhou = CommenUtil.autoQueryHist(()->Optional.ofNullable(shouhouService.lambdaQuery()
                .eq(Shouhou::getId, req.getShouhouId())
                .one()).orElseThrow(() -> new CustomizeException("当前售后单有误~")), MTableInfoEnum.SHOUHOU,req.getShouhouId());
//        Shouhou shouhou = Optional.ofNullable(shouhouService.lambdaQuery()
//                .eq(Shouhou::getId, req.getShouhouId())
//                .one()).orElseThrow(() -> new CustomizeException("当前售后单有误~"));
        Shouhou recentlyAfterInfo = null;
        //校验换货参数
        if (XtenantEnum.isJiujiXtenant() && CommenUtil.isNotNullZero(req.getIshuanhuo())) {
            try {
                Assert.notBlank(req.getRecentlyAfterId(),"上一次维修单号不能为空");
                Assert.isTrue(NumberUtil.isInteger(req.getRecentlyAfterId()),"单号:{}不正确",req.getRecentlyAfterId());
                Integer recentlyAfterId = Convert.toInt(req.getRecentlyAfterId());
                recentlyAfterInfo = CommenUtil.autoQueryHist(()->Optional.ofNullable(shouhouService.lambdaQuery()
                        .eq(Shouhou::getId, recentlyAfterId)
                        .one()).orElseThrow(() -> new CustomizeException(StrUtil.format("填入售后单:{}不存在，请核对~",recentlyAfterId))),MTableInfoEnum.SHOUHOU,recentlyAfterId);
//                recentlyAfterInfo = Optional.ofNullable(shouhouService.lambdaQuery()
//                        .eq(Shouhou::getId, recentlyAfterId)
//                        .one()).orElseThrow(() -> new CustomizeException(StrUtil.format("填入售后单:{}不存在，请核对~",recentlyAfterId)));
                afterExchangeValidate(req, shouhou, recentlyAfterInfo);
            } catch (IllegalArgumentException e) {
               throw new CustomizeException(e.getMessage());
            }
            //换货操作时把维修配件价格设置为零
            if (XtenantEnum.isJiujiXtenant()){
                String s = shouhouService.editPriceByIshuanhuo(req);
                if (StrUtil.isNotEmpty(s)) {
                    return R.error(s);
                }
            }
        }
        //回收、换货操作时，需维修单当前所在地区与操作地区一致才可以操作
        ShouhouBo sh = shouhouService.getOne(req.getShouhouId());
        R<AreaInfo> areaInfoR = areaInfoClient.getAreaInfoById(sh.getNowarea());
        AreaInfo areaInfo = areaInfoR.getData();
        if (!oaUserBO.getAreaId().equals(sh.getNowarea())) {
            return R.error("维修单所在地区与当前所在地不一致，请切换至：" + areaInfo.getArea() + "，再进行操作！");
        }

        req.setArea(oaUserBO.getArea());
        req.setAreaid(sh.getNowarea());
        req.setInuser(oaUserBO.getUserName());
        Integer baoxiu = sh.getBaoxiu();
        Integer hsjjSaletype = CommenUtil.isNotNullZero(req.getPpid()) && Objects.equals(baoxiu, 5) ? 1 : 0;

        if (req.getWxkcId() != null && req.getWxkcId() > 0) {
            List<ShouhouHuishou> huishouList = baseMapper.selectList(new LambdaQueryWrapper<ShouhouHuishou>().eq(ShouhouHuishou::getWxkcid, req.getWxkcId()));
            if (CollectionUtils.isNotEmpty(huishouList)) {
                return R.error();
            }
        }

        //判断是否在保，在保则不抵扣维修费
        ShouhouHuishou shouhouHuishou = new ShouhouHuishou();
        BeanUtils.copyProperties(req, shouhouHuishou);
        if (shouhou != null) {
            shouhouHuishou.setDktype(shouhou.getBaoxiu() == 1 ? 1 : 0);
        }
        shouhouHuishou.setWxkcid(req.getWxkcId());
        shouhouHuishou.setKcount(1);
        shouhouHuishou.setIndate(LocalDateTime.now());
        shouhouHuishou.setIsdel(false);
        shouhouHuishou.setArea(null);
        shouhouHuishou.setIssale(false);
        shouhouHuishou.setHsjjSaletype(hsjjSaletype);
        Integer count = baseMapper.insert(shouhouHuishou);
        //换货成功需要记录进程
        if (XtenantEnum.isJiujiXtenant() && CommenUtil.isNotNullZero(req.getIshuanhuo())){
            pushWechatMsgAndRecordAfterLogs(shouhou, recentlyAfterInfo);
        }
        Boolean isZhiLeFang = Arrays.asList(1000, 1001, 1002).contains(oaUserBO.getXTenant()) ? Boolean.TRUE : Boolean.FALSE;

        //智乐方旧件返厂功能
        if (isZhiLeFang) {
            //todo 联调时注意rabbit消息消费情况
            this.hsjjFc(sh.getNowarea(), shouhouHuishou.getId(), areaInfo.getAuthorizeId(), req);
        }

        if (!isZhiLeFang && count > 0 && CommenUtil.isNullOrZero(req.getIshuanhuo()) && !Boolean.TRUE.equals(req.getIsfan())) {

            if (req.getPrice() != null && req.getPrice().compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal feiYong = sh.getFeiyong().subtract(req.getPrice());
                if (feiYong.compareTo(BigDecimal.ZERO) < 0) {
                    feiYong = BigDecimal.ZERO;
                }
                shouhouService.update(new LambdaUpdateWrapper<Shouhou>().set(Shouhou::getFeiyong, feiYong).eq(Shouhou::getId, req.getShouhouId()));
            }

            String comment = StrUtil.format("旧件回收【{}，PPID:{}】" , req.getName(),req.getPpid());
            if (req.getPrice() != null && req.getPrice().compareTo(BigDecimal.ZERO) > 0) {
                Double price = req.getPrice().setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
                comment = "旧件回收，抵扣维修费" + (price.toString().replace(".00", "")) + "元";
            }
            shouhouService.saveShouhouLog(req.getShouhouId(), comment, req.getInuser(), null, false);
        }

        if (count > 0 && CommenUtil.isNotNullZero(req.getIshuanhuo())) {
            String comment = "配件换货【" + req.getName() + "，PPID:" + req.getPpid() + "】";
            shouhouService.saveShouhouLog(req.getShouhouId(), comment, req.getInuser(), null, true);
        }
        //主板、屏幕总成、摄像头这三类配件返还给客户自动推送跟进可视进程给到客户
        Boolean isfan = req.getIsfan() == null ? false : req.getIsfan();
        if (count > 0 && isfan && CommenUtil.isNotNullZero(req.getPpid())) {
            List<Productinfo> productinfoList = productinfoService.list(new LambdaQueryWrapper<Productinfo>().eq(Productinfo::getPpriceid, req.getPpid()).select(Productinfo::getCid));
            if (CollectionUtils.isNotEmpty(productinfoList)) {
                Integer cid = productinfoList.get(0).getCid();
                areaInfoR = areaInfoClient.getAreaInfoById(req.getAreaid());
                if (areaInfoR.getCode() == ResultCode.SUCCESS && areaInfoR.getData() != null) {
                    String comment = "尊敬的用户，维修旧件默认由" + areaInfoR.getData().getPrintName() + "回收进行环保处理，应您要求返还给您，请确认查收。";
                    shouhouService.saveShouhouLog(req.getShouhouId(), comment, req.getInuser(), null, true);
                }
            }
        }
        //todo
        return count > 0 ? R.success(true) : R.error();
    }

    @Override
    public ShouHouHuishouListRes getHuishouList(ShouhouHuishouListReq param) {
        ShouHouHuishouListRes result = new ShouHouHuishouListRes();
        PageRes<ShouhouHuishouListBo> pageData = new PageRes<ShouhouHuishouListBo>(param.getCurrent(), param.getSize());
        OaUserBO oaUserBO = currentRequestComponent.getCurrentStaffId();
        Integer total = baseMapper.getgetHuishouListCount(param, oaUserBO.getAreaId());
        if (total == 0) {
            return result;
        }
        if (param.getCurrent() == null || param.getCurrent() == 0) {
            param.setCurrent(1);
            param.setSize(15);
        }
        Integer startRow = (param.getCurrent() - 1) * param.getSize();
        List<ShouhouHuishouListBo> list = baseMapper.getHuishouList(param, oaUserBO.getAreaId(), startRow, param.getSize());
        if (CollectionUtils.isNotEmpty(list)) {
            list = list.stream().map(e -> {
                ShouhouHuishouListBo data = new ShouhouHuishouListBo();
                BeanUtils.copyProperties(e, data);
                if (StringUtils.isNotBlank(data.getPLabel())) {
                    data.setPLabel(e.getPjkinds().equals("wx") ? EnumUtil.getMessageByCode(ProductLabelEnum.class, Integer.valueOf(e.getPLabel()))
                            : EnumUtil.getMessageByCode(ProductLabelEnum.class, Integer.valueOf(e.getPLabel())) == null ? "" : EnumUtil.getMessageByCode(ProductLabelEnum.class, Integer.valueOf(e.getPLabel()))
                    );
                }
                data.setFancheck(e.getFancheck() == null ? false : e.getFancheck());
                data.setIstoarea(e.getIstoarea() == null ? false : e.getIstoarea());
                data.setIshuan(e.getIshuan() == null ? false : e.getIshuan());
                data.setBindtoolprice(e.getBindtoolprice() == null ? 0 : e.getBindtoolprice());
                if (data.getIshuan()) {
                    data.setConfirm(e.getConfirm() == null ? false : e.getConfirm());
                    data.setComplete(e.getComplete() == null ? false : e.getComplete());
                }
                return data;
            }).collect(Collectors.toList());
        }
        pageData.setTotal(total);
        pageData.setRecords(list);
        pageData.setSize(param.getSize());
        ShouhouHuishouSumDataBo sumDataBo = this.getHuiSouSumData(param, oaUserBO.getAreaId());
        result.setSumData(sumDataBo);
        result.setPageData(pageData);
        return result;

    }

    @Override
    public ShouhouHuishouSumDataBo getHuiSouSumData(ShouhouHuishouListReq param, Integer loginArea) {
        return baseMapper.getHuiSouSumData(param, loginArea);
    }


    @Override
    public List<ShouhouHuishou> getHuishouListBy(Integer shouhouId) {
        List<ShouhouHuishou> huishouList = super.list(new QueryWrapper<ShouhouHuishou>().lambda()
                .eq(ShouhouHuishou::getShouhouId, shouhouId)
                .and(s -> s.eq(ShouhouHuishou::getIsdel, Boolean.FALSE).or().isNull(ShouhouHuishou::getIsdel)));
        if (CollectionUtils.isNotEmpty(huishouList)) {
            return huishouList;
        } else {
            return Collections.emptyList();
        }
    }

    @Override
    public Map<Integer, List<ShouhouHuishou>> getHuishouMapByshouhouIds(List<Integer> shouhouId) {
        List<ShouhouHuishou> huishouList =  CommonUtils.bigDataInQuery(NumberConstant.PROGRAMMER_NUMBER, shouhouId, ids ->
            super.list(new QueryWrapper<ShouhouHuishou>().lambda()
                    .in(ShouhouHuishou::getShouhouId, ids)
                    .and(s -> s.eq(ShouhouHuishou::getIsdel, Boolean.FALSE).or().isNull(ShouhouHuishou::getIsdel))
                    .and(s -> s.eq(ShouhouHuishou::getIshuanhuo, Boolean.TRUE)))
        );
        //根据售后id进行分组
        return huishouList.stream().collect(Collectors.groupingBy(ShouhouHuishou::getShouhouId));
    }

    @Override
    public ShouhouPjInfoRes getShouhouPjInfoById(Integer shouhouId) {
        ShouhouPjInfoRes res = new ShouhouPjInfoRes();

        //回收配件
        List<ShouhouHuishou> huishouList = this.getHuishouListBy(shouhouId);
        if (CollectionUtils.isNotEmpty(huishouList)) {
            Set<Integer> areaIds = new HashSet<>(huishouList.size());
            BigDecimal hspjTotal = huishouList.stream()
                    .peek(hs -> {
                        if(hs.getAreaid() != null){
                            areaIds.add(hs.getAreaid());
                        }
                        ShouhouHuishou.HuiShouStatusEnum huiShouStatusEnum = RefundShouhouService.getHuiShouStatusEnum(hs.getIsfan(), hs.getIssale(), hs.getIssalecheck(),
                                hs.getHsjjSaletype(), hs.getComplete(), hs.getConfirm(), hs.getFromSource(), hs.getFancheck());
                        if(huiShouStatusEnum != null){
                            hs.setStatus(huiShouStatusEnum.getCode());
                            hs.setStatusText(huiShouStatusEnum.getMessage());
                        }
                    })
                    .filter(e -> e.getPrice() != null)
                    .map(ShouhouHuishou::getPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
            //设置门店编码
            Map<Integer, AreaInfo> areaInfoMap = Optional.ofNullable(areaIds).filter(CollUtil::isNotEmpty)
                    .map(aIds -> CommonUtils.getResultData(areaInfoClient.listAreaInfo(CollUtil.newArrayList(areaIds)),
                            errMsg -> {
                                throw new CustomizeException("获取门店信息异常");
                            }).stream().collect(Collectors.toMap(AreaInfo::getId, Function.identity(), (v1, v2) -> v1)))
                    .orElse(Collections.emptyMap());
            huishouList.forEach(hs -> hs.setArea(Optional.ofNullable(areaInfoMap.get(hs.getAreaid())).map(AreaInfo::getArea).orElse("")));
            //设置商品图片
            Set<Integer> ppidSet = huishouList.stream().map(ShouhouHuishou::getPpid).filter(Objects::nonNull).collect(Collectors.toSet());
            if (CollectionUtils.isNotEmpty(ppidSet)) {
                Map<Integer, Productinfo> productMap = productinfoService.getProductMapByPpids(new ArrayList<>(ppidSet));
                String imgUrl = sysConfigClient.getValueByCodeAndXtenant(SysConfigConstant.IMG_URL, XtenantEnum.getXtenant()).getData();
                huishouList.forEach(hs -> hs.setBpic(Optional.ofNullable(productMap.get(hs.getPpid()))
                        .map(v -> {
                            String picUrl = v.getBpic();
                            if (StringUtils.isEmpty(picUrl)) {
                                return null;
                            }
                            picUrl = StringUtils.stripEnd(picUrl, "/");
                            String pa = "^\\d{1,9}/.*";
                            Pattern pattern = Pattern.compile(pa);
                            if (pattern.matcher(picUrl).matches()) {
                                picUrl = imgUrl + "/newstatic/" + picUrl;
                            } else {
                                picUrl = imgUrl + "/pic/product/440x440/" + picUrl;
                            }
                            return picUrl;
                        }).orElse("")));
            }
            res.setHspjTotal(hspjTotal);
            res.setHspj(huishouList);
        }
        //维修配件
        R<List<WeiXiuPjRes>> wxPjR = wxkcoutputService.getWeixiuPJ(shouhouId);
        if (wxPjR.getCode() == ResultCode.SUCCESS) {
            res.setWxpj(wxPjR.getData());
        }
        return res;
    }

    @Override
    public R<String> huiShouCheck(Integer id) {

        OaUserBO oaUserBO = currentRequestComponent.getCurrentStaffId();
        if (oaUserBO == null) {
            return R.unLogin();
        }
        if (!oaUserBO.getRank().contains(RankEnum.RECOVER_CHECK.getCode())) {
            return R.error("没有旧件返还审核权限");
        }
        Boolean flag = super.update(new LambdaUpdateWrapper<ShouhouHuishou>().set(ShouhouHuishou::getFancheck, 1)
                .set(ShouhouHuishou::getCheckUser, oaUserBO.getUserName())
                .set(ShouhouHuishou::getCheckDate, LocalDateTime.now())
                .eq(ShouhouHuishou::getId, id)
                .and(bo -> bo.eq(ShouhouHuishou::getIsdel, Boolean.FALSE).or().isNull(ShouhouHuishou::getIsdel))
                .and(bo -> bo.eq(ShouhouHuishou::getFancheck, Boolean.FALSE).or().isNull(ShouhouHuishou::getFancheck)));
        if (!flag) {
            return R.error("审核失败");
        } else {
            return R.success("审核成功");
        }

    }

    @Override
    public R<String> huishouPjDel(Integer id) {

        Boolean flag = super.remove(new LambdaQueryWrapper<ShouhouHuishou>()
                .and(bo -> bo.eq(ShouhouHuishou::getIsdel, Boolean.FALSE).or().isNull(ShouhouHuishou::getIsdel))
                .eq(ShouhouHuishou::getIssale, Boolean.FALSE)
                .eq(ShouhouHuishou::getIsfan, Boolean.TRUE)
                .eq(ShouhouHuishou::getId, id));
        return flag ? R.success("撤销成功") : R.error("撤销失败");
    }

    @Override
    public R<SmallProRecoverListQueryRes> getHuishouList(SmallProRecoverListQueryReq req) {
        SmallProRecoverListQueryRes result = new SmallProRecoverListQueryRes();
        if (CommenUtil.isNullOrZero(req.getCurrent()) || CommenUtil.isNullOrZero(req.getSize())) {
            req.setCurrent(1);
            req.setSize(30);
        }

        Page<SmallProRecoverListQueryPageItem> page = new Page<SmallProRecoverListQueryPageItem>(req.getCurrent(), req.getSize());
        OaUserBO oaUserBO = currentRequestComponent.getCurrentStaffId();
        page.setDesc("hs.id");

        page = baseMapper.getHuiShouListPage(page, req, oaUserBO.getAreaId());
        List<SmallProRecoverListQueryPageItem> records = page.getRecords();
        if (CollectionUtils.isNotEmpty(records)) {
            records.stream().map(e -> {
                SmallProRecoverListQueryPageItem data = new SmallProRecoverListQueryPageItem();
                BeanUtils.copyProperties(e, data);
                if (StringUtils.isNotBlank(data.getPLabel())) {
                    data.setPLabel(Objects.equals(e.getPjKinds(), "wx")
                            ? EnumUtil.getMessageByCode(ProductLabelEnum.class, Integer.valueOf(e.getPLabel()))
                            : EnumUtil.getMessageByCode(ProductLabelEnum.class, Integer.valueOf(e.getPLabel())) == null ? "" : EnumUtil.getMessageByCode(ProductLabelEnum.class, Integer.valueOf(e.getPLabel()))
                    );
                }
                data.setPjKinds(StringUtils.isNotEmpty(e.getCidFamily())
                        && Arrays.asList(e.getCidFamily().split(",")).contains("23")
                        ? "wx" : "pj");

                return data;
            }).collect(Collectors.toList());
        }
        page.setRecords(records);
        SmallProRecoverSumData sumDataBo = this.getHuiSouSumDataV2(req, oaUserBO.getAreaId());
        result.setSumData(sumDataBo);
        result.setPage(page);
        return R.success(result);
    }

    @Override
    public R<Boolean> exchangeBatchConfirm(List<Integer> shouhouIds) {

        OaUserBO oaUserBO = currentRequestComponent.getCurrentStaffId();
        if (CollectionUtils.isEmpty(shouhouIds)) {
            return R.error("id为空");
        }

        super.update(new LambdaUpdateWrapper<ShouhouHuishou>()
                .set(ShouhouHuishou::getConfirm, Boolean.TRUE)
                .set(ShouhouHuishou::getCheckUser, oaUserBO.getUserName())
                .set(ShouhouHuishou::getCheckDate, LocalDateTime.now())
                .eq(ShouhouHuishou::getIshuan, Boolean.TRUE)
                .and(bo -> bo.eq(ShouhouHuishou::getConfirm, Boolean.FALSE).or().isNull(ShouhouHuishou::getConfirm))
                .in(ShouhouHuishou::getShouhouId, shouhouIds));

        return R.success("操作成功");
    }

    @Override
    public R<Boolean> recoverTransferToExchangeBatch(List<Integer> shouhouIds) {
        if (CollectionUtils.isEmpty(shouhouIds)) {
            return R.error("id为空");
        }

        super.update(new LambdaUpdateWrapper<ShouhouHuishou>()
                .set(ShouhouHuishou::getIshuan, Boolean.TRUE)
                .and(bo -> bo.eq(ShouhouHuishou::getIshuanhuo, 0).or().isNull(ShouhouHuishou::getIshuanhuo))
                .in(ShouhouHuishou::getShouhouId, shouhouIds));

        return R.success("操作成功");
    }

    @Override
    public R<Boolean> transferToRecover(Integer recoverId) {
        ShouhouHuishou recoverInfo = super.getById(recoverId);
        if (recoverInfo == null) {
            return R.error("数据无效，操作失败");
        }
        super.update(new LambdaUpdateWrapper<ShouhouHuishou>()
                .set(ShouhouHuishou::getIsfan, Boolean.FALSE)
                .set(ShouhouHuishou::getIshuan, Boolean.FALSE)
                .eq(ShouhouHuishou::getId, recoverId));

        return R.success("操作成功");
    }

    @Override
    public R<Boolean> updateRecoverKind(RecoverSetKindReq req) {
        super.update(new LambdaUpdateWrapper<ShouhouHuishou>()
                .set(ShouhouHuishou::getKind, req.getKind())
                .eq(ShouhouHuishou::getId, req.getId()));

        return R.success("操作成功");
    }

    @Override
    public R<Boolean> updatePreSaleChannel(RecoverSetPreSaleChannelReq req) {
        super.update(new LambdaUpdateWrapper<ShouhouHuishou>()
                .set(ShouhouHuishou::getPreSaleChannel, req.getChannel())
                .eq(ShouhouHuishou::getId, req.getId()));

        return R.success("操作成功");
    }

    @Override
    public R<Boolean> recoverSaleBatchConfirm(RecoverSaleBatchConfirmReq req) {

        OaUserBO oaUserBO = currentRequestComponent.getCurrentStaffId();
        if (!CommenUtil.isJiuJiXtenant(oaUserBO.getXTenant())) {
            return this.recoverSaleBatchConfirmSaas(req);
        }
        if (!oaUserBO.getRank().contains(RankEnum.RECOVER_SALE.getCode())) {
            return R.error("您没有操作权限");
        }
        TApplyinfo applyInfo = tApplyinfoService.getApplyInfoById(req.getPqId());
        if (applyInfo == null) {
            return R.error("批签验证失败");
        }
        List<Integer> ids = req.getSaleItemList().stream().map(item -> item.getId()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(ids)) {
            return R.error("旧件编号错误");
        }
        LambdaQueryWrapper<ShouhouHuishou> queryWrapper = new LambdaQueryWrapper<ShouhouHuishou>().ne(ShouhouHuishou::getCompanyId, null)
                .ne(ShouhouHuishou::getCompanyId, 0)
                .eq(ShouhouHuishou::getIssalecheck, Boolean.TRUE)
                .and(bo -> bo.eq(ShouhouHuishou::getIssale, Boolean.FALSE).or().isNull(ShouhouHuishou::getIssale))
                .in(ShouhouHuishou::getId, ids);
        List<ShouhouHuishou> list = list(queryWrapper);
        if (CollectionUtils.isEmpty(list)) {
            return R.error("请确认所选旧件是否处于待出售状态");
        }
        super.update(new LambdaUpdateWrapper<ShouhouHuishou>()
                .set(ShouhouHuishou::getIssale, Boolean.TRUE)
                .set(ShouhouHuishou::getSaledate, LocalDateTime.now())
                .set(ShouhouHuishou::getSalePiqianid, req.getPqId())
                .ne(ShouhouHuishou::getCompanyId, null)
                .ne(ShouhouHuishou::getCompanyId, 0)
                .eq(ShouhouHuishou::getIssalecheck, Boolean.TRUE)
                .and(bo -> bo.eq(ShouhouHuishou::getIssale, Boolean.FALSE).or().isNull(ShouhouHuishou::getIssale))
                .in(ShouhouHuishou::getId, ids));

        Map<Integer, List<ShouhouHuishou>> collect = list.stream().collect(Collectors.groupingBy(item -> item.getAreaid()));
        List<Integer> areaIds = collect.entrySet().stream().filter(t -> CommenUtil.isNotNullZero(t.getKey())).map(t -> t.getKey()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(areaIds)) {
            return R.success("操作成功");
        }
        R<List<AreaInfo>> areaInfoR = areaInfoClient.listAreaInfo(areaIds);
        if (areaInfoR.getCode() != ResultCode.SUCCESS || CollectionUtils.isEmpty(areaInfoR.getData())) {
            return R.error("获取门店信息失败");
        }
        List<AreaInfo> areaInfoList = areaInfoR.getData();

        collect.entrySet().forEach(item -> {
            AreaInfo areaInfo = areaInfoList.stream().filter(a -> Objects.equals(item.getKey(), a.getId())).findFirst().orElse(null);

            List<ShouhouHuishou> saleItems = item.getValue();
            List<String> idList = saleItems.stream().map(ShouhouHuishou::getId).map(String::valueOf).collect(Collectors.toList());
            BigDecimal total = saleItems.stream().map(ShouhouHuishou::getPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
            if (areaInfo != null && total != null && total.compareTo(BigDecimal.ZERO) > 0) {
                String area = Objects.equals(areaInfo.getKind1(), 1) ? areaInfo.getArea() : "h1";
                String daif = "-" + total + "|" + total;
                String jief = "0|0";
                String fzhs = "h1" + "|" + area;
                String kemu = "600121|600121";
                String zhaiYao = MessageFormat.format("{0}售后旧件出售，编码：" + String.join(",", idList) + "，批签：" + req.getPqId(),
                        DateUtil.format_m_d.format(LocalDateTime.now()));
                PingzhengResultBO voucherResult =
                        voucherService.addPingZheng(voucherService.buildPingzheng(zhaiYao, kemu,
                                jief, daif, fzhs));
                if (voucherResult.getFlag() != null
                        && voucherResult.getFlag()) {
                    Integer pzId = voucherResult.getPzId();
                    super.update(new LambdaUpdateWrapper<ShouhouHuishou>()
                            .set(ShouhouHuishou::getPzid, pzId)
                            .in(ShouhouHuishou::getId, idList.stream().map(Integer::valueOf).collect(Collectors.toList())));
                } else {
                    //oa消息推送
                    smsService.sendOaMsg("售后旧件出售凭证生成失败,金额：" + total + "；原因：" + voucherResult.getErrorMsg(), "", "13685", OaMesTypeEnum.YCTZ);
                }
            }
        });

        return R.success("操作成功");
    }

    @Override
    public R<Boolean> recoverSqueezeBatch(RecoverSqueezeBatchReq req) {
        OaUserBO oaUserBO = currentRequestComponent.getCurrentStaffId();
        if (CollectionUtils.isEmpty(req.getIds())) {
            return R.error("请先选择");
        }
        Integer yaPingAreaId = CommenUtil.isJiuJiXtenant(oaUserBO.getXTenant()) ? 360 : 0;

        List<Integer> ids = req.getIds();
        Boolean flag = Boolean.FALSE;
        if (Objects.equals(req.getKind(), PressScreenStatusEnum.FINISHED.getCode())) {
            //压屏完成
            flag = super.lambdaUpdate()
                    .set(ShouhouHuishou::getYaping, PressScreenStatusEnum.FINISHED.getCode())
                    .in(ShouhouHuishou::getId, ids)
                    .eq(ShouhouHuishou::getYaping, PressScreenStatusEnum.WAITING.getCode())
                    .eq(ShouhouHuishou::getToareaid, yaPingAreaId)
                    .ge(ShouhouHuishou::getBindtoolcount, 0)
                    .update();
        } else if (Objects.equals(req.getKind(), PressScreenStatusEnum.WAIT_SCRAP.getCode())) {
            //压屏待报损
            flag = super.lambdaUpdate()
                    .set(ShouhouHuishou::getYaping, PressScreenStatusEnum.WAIT_SCRAP.getCode())
                    .in(ShouhouHuishou::getId, ids)
                    .in(ShouhouHuishou::getYaping, Arrays.asList(PressScreenStatusEnum.WAITING.getCode(), PressScreenStatusEnum.FINISHED.getCode()))
                    .eq(ShouhouHuishou::getToareaid, yaPingAreaId)
                    .update();
        } else if (Objects.equals(req.getKind(), PressScreenStatusEnum.SCRAPPED.getCode())) {
            //压屏已报损
            if (CommenUtil.isNullOrZero(req.getPqId())) {
                return R.error("请备注报废批签号！");
            }
            TApplyinfo applyInfo = tApplyinfoService.getApplyInfoById(req.getPqId());
            if (applyInfo == null) {
                return R.error("报废批签号不存在！");
            }
            List<ShouhouHuishou> list = super.lambdaQuery().in(ShouhouHuishou::getId, ids)
                    .eq(ShouhouHuishou::getYaping, 5)
                    .eq(ShouhouHuishou::getToareaid, yaPingAreaId).list();

            flag = super.lambdaUpdate()
                    .set(ShouhouHuishou::getYaping, 6)
                    .set(ShouhouHuishou::getBaofeitime, LocalDateTime.now())
                    .in(ShouhouHuishou::getId, ids)
                    .eq(ShouhouHuishou::getYaping, 5)
                    .eq(ShouhouHuishou::getToareaid, yaPingAreaId)
                    .update();
            if (flag && CollectionUtils.isNotEmpty(list)) {
                String zhaiYao = MessageFormat.format("{0}h3压屏失败，报废", DateUtil.format_m_d.format(LocalDateTime.now()));
                zhaiYao += zhaiYao + "|" + zhaiYao;
                String fzhs = "h1" + "|无";
                String kemu = "640116|140516";
                Double total = list.stream().map(ShouhouHuishou::getInprice).reduce(0d, Double::sum);
                String daif = total + "|0";
                String jief = "0" + total;
                PingzhengResultBO voucherResult =
                        voucherService.addPingZheng(voucherService.buildPingzheng(zhaiYao, kemu,
                                jief, daif, fzhs));
                if (voucherResult.getFlag() != null
                        && voucherResult.getFlag()) {
                    Integer pzId = voucherResult.getPzId();
                    super.update(new LambdaUpdateWrapper<ShouhouHuishou>()
                            .set(ShouhouHuishou::getBaofeipq, req.getPqId())
                            .set(ShouhouHuishou::getBaofeipz, pzId)
                            .in(ShouhouHuishou::getId, list.stream().map(ShouhouHuishou::getId).collect(Collectors.toList())));
                } else {
                    smsService.sendOaMsg("h3压屏报废,凭证生成失败，错误原因：" + voucherResult.getErrorMsg(), "", "13685", OaMesTypeEnum.YCTZ);
                }
            }

        } else if (Objects.equals(req.getKind(), PressScreenStatusEnum.TRANS_WAITING.getCode())) {
            //【压好】或【待报损】的转【待压屏】
            flag = super.lambdaUpdate()
                    .set(ShouhouHuishou::getYaping, PressScreenStatusEnum.WAITING.getCode())
                    .in(ShouhouHuishou::getId, ids)
                    .in(ShouhouHuishou::getYaping, Arrays.asList(PressScreenStatusEnum.FINISHED.getCode(), PressScreenStatusEnum.WAIT_SCRAP.getCode()))
                    .eq(ShouhouHuishou::getToareaid, yaPingAreaId)
                    .update();
        }
        return flag ? R.success("操作成功") : R.error("更新失败");
    }

    @Override
    public R<RecoverProBindToolRes> getRecoverProBindToolList(Integer hsId) {
        RecoverProBindToolRes res = new RecoverProBindToolRes();
        Integer count = super.lambdaQuery().eq(ShouhouHuishou::getId, hsId)
                .eq(ShouhouHuishou::getYaping, 1)
                .count();
        res.setIsDaiYaPing(CommenUtil.isNotNullZero(count));
        res.setBindToolList(baseMapper.getYaPingToolList(hsId));
        return R.success(res);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<Boolean> recoverBatchSale(RecoverBatchSaleReq req) {

        OaUserBO oaUserBO = currentRequestComponent.getCurrentStaffId();
        List<SmallProRecoverInfo> waitRecoverSaleInfoList = baseMapper.getWaitRecoverSaleInfoList(req.getIds());
        if (CollectionUtils.isEmpty(waitRecoverSaleInfoList)) {
            return R.error("暂无可销售旧件信息");
        }
        waitRecoverSaleInfoList.forEach(item -> {
            super.lambdaUpdate().set(ShouhouHuishou::getIssale, Boolean.FALSE)
                    .set(ShouhouHuishou::getIssalecheck, Boolean.TRUE)
                    .set(ShouhouHuishou::getCompanyId, req.getChannelId())
                    .set(ShouhouHuishou::getSaleprice, req.getPrice())
                    .set(ShouhouHuishou::getSaleuser, oaUserBO.getUserName())
                    .eq(ShouhouHuishou::getId, item.getId()).update();

            if (item.getHuanHuiPrice() != null
                    && item.getHuanHuiPrice().compareTo(req.getPrice()) > 0) {
                ShouHouZhiHuanShenHeBo data = new ShouHouZhiHuanShenHeBo();
                data.setHsId(item.getId())
                        .setDTime(LocalDateTime.now())
                        .setStatus(1);
                baseMapper.insertShouHouZhiHuanShenHe(data);
            }
        });
        return R.success("操作成功");
    }

    private boolean afterExchangeValidate(ShouhouHuiShouReq req, Shouhou currentAfterInfo, Shouhou recentlyAfterInfo) {
        if (CommenUtil.isNullOrZero(req.getIshuanhuo())) {
            return true;
        }
        //校验用户信息是否一致
//        Optional.of(!Objects.equals(currentAfterInfo.getUserid(), recentlyAfterInfo.getUserid())).filter(Boolean::booleanValue)
//                .ifPresent(k -> {
//                    throw new CustomizeException("输入售后单与当前售后单用户信息不一致~");
//                });
        //校验两个售后单号是否相同
        Optional.of(Convert.toStr(req.getShouhouId()).equals(req.getRecentlyAfterId())).filter(Boolean::booleanValue)
                .ifPresent(k -> {
                    throw new CustomizeException("来源单号不能与当前售后单一致~");
                });
        OaUserBO currentStaff = currentRequestComponent.getCurrentStaffId();
        if (!currentStaff.getRank().contains(ShouhouConstants.AFTER_EXCHANGE_SPECIAL_DEAL_PERMISSION)) {
            Optional.of(Optional.ofNullable(req.getIsSpecialCondition()).orElse(Boolean.FALSE)).filter(Boolean::booleanValue)
                    .ifPresent(k -> {
                        throw new CustomizeException(MessageFormat.format("您无权处理，权值{0}",ShouhouConstants.AFTER_EXCHANGE_SPECIAL_DEAL_PERMISSION));
                    });
        } else {
            if (Optional.ofNullable(req.getIsSpecialCondition()).orElse(Boolean.FALSE)) {
                return true;
            }
        }
        //校验串号
        String errorTips = "输入的维修单号与当前换货维修单，存在串号不相符或原维修单没有当前配件出库记录的情况，请核实原订单串号或出库配件。";
        Optional.of(!Objects.equals(currentAfterInfo.getImei(), recentlyAfterInfo.getImei())).filter(Boolean::booleanValue)
                .ifPresent(k -> {
                    throw new CustomizeException(errorTips);
                });
        //校验换货PPID
        Optional.of(CommenUtil.autoQueryHist(()->wxkcoutputService.lambdaQuery()
                .eq(Wxkcoutput::getWxid, req.getRecentlyAfterId())
                .eq(Wxkcoutput::getPpriceid, req.getPpid())
                .count(),MTableInfoEnum.WXKCOUTPUT_WXID,Convert.toInt(req.getRecentlyAfterId())))
                .filter(CommenUtil::isNotNullZero)
                .orElseThrow(() -> new CustomizeException(errorTips));
        return true;
    }

    private void pushWechatMsgAndRecordAfterLogs(Shouhou currentAfterInfo, Shouhou recentlyAfterInfo) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy年M月d日");
        //新维修单进程
        String host = Optional.of(sysConfigClient.getValueByCode(SysConfigConstant.OA_URL))
                .filter(t -> t.getCode() == ResultCode.SUCCESS)
                .map(R::getData)
                .orElseThrow(()-> new CustomizeException("获取域名出错"));
        MessageFormat.format(OA_AFTER_URL,host,recentlyAfterInfo.getId());
        String currentAfterProgress = "换货来源维修单号：<a style='color: #3caaff' href=\"" + MessageFormat.format(OA_AFTER_URL,host,recentlyAfterInfo.getId()) + "\">" + recentlyAfterInfo.getId();
        shouhouService.saveShouhouLog(currentAfterInfo.getId(),currentAfterProgress, "系统",null,Boolean.TRUE);
        //消息推送暂时去掉
//        String wechatMag = MessageFormat.format(WECHAT_MSG,
//                recentlyAfterInfo.getModidate().format(formatter),
//                recentlyAfterInfo.getId(),
//                currentAfterInfo.getModidate().format(formatter),
//                currentAfterInfo.getId());
//        //推送消息 有微信推微信，无微信推短信
//        AreaInfo areaInfo = Optional.ofNullable(areaInfoClient.getAreaInfoById(recentlyAfterInfo.getAreaid())).map(R::getData)
//                .filter(Objects::nonNull)
//                .orElseThrow(() -> new CustomizeException("门店信息获取失败"));
//        if (areaInfo != null && areaInfo.getIsSend()) {
//            WeixinUser weixinUser = weixinUserService.getWxxinUserByUserId(recentlyAfterInfo.getUserid().intValue());
//            String mHost = Optional.of(sysConfigClient.getValueByCode(SysConfigConstant.M_URL)).map(R::getData)
//                    .filter(StringUtils::isNotBlank)
//                    .orElseThrow(() -> new CustomizeException("获取m端域名出错"));
//            if (weixinUser != null && StringUtils.isNotEmpty(weixinUser.getOpenid())) {
//                wechatMag += "，点击查看！";
//                imCloud.sendAfterServiceProgressMsg(weixinUser.getOpenid(), mHost + "/after-service/detail/" + currentAfterInfo.getId(),"",
//                        "售后维修" , "处理中", DateUtil.localDateTimeToString(LocalDateTime.now()), "售后换货", wechatMag, null, 0L);
//            } else {
//                if (StringUtils.isBlank(currentAfterInfo.getMobile())) {
//                    return;
//                }
//                R<Boolean> response = smsService.sendSms(currentAfterInfo.getMobile(), wechatMag, DateUtil.localDateTimeToString(LocalDateTime.now()), "系统",
//                        smsService.getSmsChannelByTenant(Optional.ofNullable(areaInfo.getId()).orElse(NumberConstant.ZERO), ESmsChannelTypeEnum.YXTD));
//                if (response.getCode() == ResultCode.RETURN_ERROR) {
//                    // todo
//                }
//            }
//        }
    }

    /**
     * 九机和输出代码差异大，兼容不方便，单独提供一个方法
     *
     * @param req
     * @return
     */
    private R<Boolean> recoverSaleBatchConfirmSaas(RecoverSaleBatchConfirmReq req) {

        OaUserBO oaUserBO = currentRequestComponent.getCurrentStaffId();
        List<Integer> ids = req.getSaleItemList().stream().map(item -> item.getId()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(ids)) {
            return R.error("旧件编号错误");
        }

        List<SmallProRecoverInfo> recoverSaleInfoList = baseMapper.getRecoverSaleInfoList(ids);

        super.update(new LambdaUpdateWrapper<ShouhouHuishou>()
                .set(ShouhouHuishou::getIssale, Boolean.TRUE)
                .set(ShouhouHuishou::getSaledate, LocalDateTime.now())
                .in(ShouhouHuishou::getId, recoverSaleInfoList.stream().map(item -> item.getId()).collect(Collectors.toList())));

        List<SmallProRecoverInfo> recoverInfoList = baseMapper.getRecoverInfoList(ids);
        if (CollectionUtils.isEmpty(recoverInfoList)) {
            return R.success("操作成功");
        }
        List<Integer> areaIds = recoverInfoList.stream().map(SmallProRecoverInfo::getNowAreaId).collect(Collectors.toList());
        R<List<AreaInfo>> areaInfoR = areaInfoClient.listAreaInfo(areaIds);
        if (areaInfoR.getCode() != ResultCode.SUCCESS || CollectionUtils.isEmpty(areaInfoR.getData())) {
            return R.error("获取门店信息失败");
        }
        List<AreaInfo> areaInfoList = areaInfoR.getData();
        String daif = "";
        String jief = "";
        String fzhs = "";
        String kemu = "";
        String zhaiYao = "";
        for (SmallProRecoverInfo item : recoverInfoList) {

            Integer channelId = item.getCompanyId() == null ? 0 : item.getCompanyId();
            BigDecimal price = item.getPrice() == null ? BigDecimal.ZERO : item.getPrice();
            BigDecimal salePrice = item.getSalePrice() == null ? BigDecimal.ZERO : item.getSalePrice();
            BigDecimal differentPrice = salePrice.subtract(price);
            AreaInfo areaInfo = areaInfoList.stream().filter(a -> Objects.equals(item.getNowAreaId(), a.getId())).findFirst().orElse(null);
            String area = areaInfo.getArea();

            kemu += "220203|605105|140518|";
            zhaiYao += MessageFormat.format("维修单{0}，旧件返厂，出售价{1}元，旧件价格{2}元|维修单{0}，旧件返厂，出售价{1}元，旧件价格{2}元|维修单{0}，旧件返厂，出售价{1}元，旧件价格{2}元|",
                    item.getShouhouId(), salePrice, price);
            jief += salePrice + "|0|0|";
            daif += "0|" + differentPrice + "|" + price + "|";
            fzhs += channelId + "|" + area + "|无|";

        }

        Integer ztId = authConfigService.getZtIdByAuId(oaUserBO.getAuthorizeId());
        zhaiYao = StringUtils.stripEnd(zhaiYao, "|");
        kemu = StringUtils.stripEnd(kemu, "|");
        jief = StringUtils.stripEnd(jief, "|");
        daif = StringUtils.stripEnd(daif, "|");
        fzhs = StringUtils.stripEnd(fzhs, "|");

        PingzhengResultBO voucherResult =
                voucherService.addPingZheng(voucherService.buildPingzheng(zhaiYao, kemu,
                        jief, daif, fzhs));
        if (voucherResult.getFlag() != null
                && voucherResult.getFlag()) {
            Integer pzId = voucherResult.getPzId();
            super.update(new LambdaUpdateWrapper<ShouhouHuishou>()
                    .set(ShouhouHuishou::getIssale, Boolean.TRUE)
                    .set(ShouhouHuishou::getSaledate, LocalDateTime.now())
                    .set(ShouhouHuishou::getHsjjfcPzid, pzId)
                    .in(ShouhouHuishou::getId, ids));

            //旧件返厂凭证
            Collection<ShouhouHuishou> collection = super.listByIds(ids);
            collection.forEach(item -> {
                shouhouService.saveShouhouLog(item.getShouhouId(), MessageFormat.format("旧件返厂凭证{0}", pzId), oaUserBO.getUserName());
                NewVoucherBo voucher = new NewVoucherBo();
                voucher.setAct("partRecoverReturned");
                voucher.setActName("旧件返厂");
                voucher.setAccountSetId(ztId.toString());
                NewVoucherBo.VoucherArgs argsO = new NewVoucherBo.VoucherArgs();
                argsO.setId(item.getId());
                voucher.setArgsO(argsO);
                voucher.setSubId(item.getId().toString());
                DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                voucher.setVoucherTime(LocalDateTime.now().format(dtf));
                voucher.setAreaId(oaUserBO.getAreaId());
                voucherService.addNewVoucher(voucher);
            });

        } else {
            //oa消息推送
            smsService.sendOaMsg("旧件返厂凭证生成失败,原因：" + voucherResult.getErrorMsg(), "", sysConfigService.getDevopsExceptionUserId(oaUserBO.getXTenant()), OaMesTypeEnum.YCTZ);
        }

        return R.success("操作成功");
    }


    public SmallProRecoverSumData getHuiSouSumDataV2(SmallProRecoverListQueryReq param, Integer loginArea) {
        return baseMapper.getHuiSouSumDataV2(param, loginArea);
    }


    /**
     * 回收旧件返厂功能模块代码
     *
     * @param areaId
     * @return
     */
    private Boolean hsjjFc(Integer areaId, Integer hsId, Integer authId, ShouhouHuiShouReq req) {
        OaUserBO oaUserBO = currentRequestComponent.getCurrentStaffId();

        Integer ztId = authConfigService.getZtIdByAuId(authId);
        if (CommenUtil.isNullOrZero(ztId)) {
            return Boolean.FALSE;
        }
        KemuFzhsItem k140518 = keMuService.getKemuFzhsInfoByEKemu(EKemuEnum.K140518.getCode(), ztId, areaId);
        KemuFzhsItem k140504 = keMuService.getKemuFzhsInfoByEKemu(EKemuEnum.K140504.getCode(), ztId, areaId);
        if (k140518 != null && k140504 != null) {
            String summary = String.format("维修单%s，旧件回收待返厂，金额%s元", req.getShouhouId(), req.getPrice());
            summary = summary + "|" + summary;
            String kemu = k140518.getCode() + "|" + k140504.getCode();
            String jief = req.getPrice() + "|0";
            String daif = "0|" + req.getPrice();
            String fzhs = k140518.getFzhs() + "|" + k140504.getFzhs();

            PingzhengBO pz = new PingzhengBO();
            pz.setKemu(kemu);
            pz.setJief(jief);
            pz.setDaif(daif);
            pz.setFzhs(fzhs);
            pz.setZhaiyao(summary);
            pz.setZtid(ztId.toString());
            pz.setPzdate(DateUtil.localDateTimeToString(LocalDateTime.now()));
            PingzhengResultBO result = voucherService.addPingZheng(pz);
            if (result == null || !result.getFlag()) {
                String msg = "";
                if (result != null) {
                    msg = result.getErrorMsg();
                }
                String errMsg = "佳海旧件回收待返厂凭证生成失败,维修单号(" + req.getShouhouId() + "),kcid:" + req.getWxkcId() + ",原因：" + msg;
                String userIds = sysConfigService.getDevopsExceptionUserId(oaUserBO.getXTenant());
                if (StringUtils.isNotEmpty(userIds)) {
                    String url = inwcfUrlSource.getNoticeQywxMessage(userIds, oaUserBO.getUserIp(), "旧件返厂凭证生成异常推送", errMsg, "", "");
                    HttpUtil.get(url);
                }
            } else {
                super.update(new LambdaUpdateWrapper<ShouhouHuishou>().set(ShouhouHuishou::getHsjjPzid, result.getPzId()).eq(ShouhouHuishou::getId, hsId));
                shouhouService.saveShouhouLog(req.getShouhouId(), "旧件回收待返厂凭证" + result.getPzId(), oaUserBO.getUserName(), null, false);

            }
        } else {
            String errMsg = "佳海旧件回收待返厂凭证生成失败,原因：科目140518，140504未配置,维修单号(" + req.getShouhouId() + ")";
            String userIds = sysConfigService.getDevopsExceptionUserId(oaUserBO.getXTenant());
            if (StringUtils.isNotEmpty(userIds)) {
                String url = inwcfUrlSource.getNoticeQywxMessage(userIds, oaUserBO.getUserIp(), "旧件返厂凭证生成异常推送", errMsg, "", "");
                HttpUtil.get(url);
            }
        }

        //旧件回收待返厂
        NewVoucherBo voucher = new NewVoucherBo();
        voucher.setAct("partRecoverToBeReturned");
        voucher.setActName("旧件回收待返厂");
        voucher.setAccountSetId(ztId.toString());
        NewVoucherBo.VoucherArgs argsO = new NewVoucherBo.VoucherArgs();
        argsO.setId(hsId);
        voucher.setSubId(hsId.toString());
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        voucher.setVoucherTime(LocalDateTime.now().format(dtf));
        voucher.setAreaId(areaId);
        voucherService.addNewVoucher(voucher);
        return Boolean.TRUE;
    }

    /**
     * 扫码收发货操作
     *
     * @param wType
     * @param receiveType
     * @param areaId
     * @param qrCode
     * @return
     */
    @Override
    public R<ReceiveSendRes> saveReceiveSendByQrCode(Integer wType, Integer receiveType, Integer areaId, String qrCode) {
        if (CommenUtil.isNullOrZero(receiveType) || CommenUtil.isNullOrZero(areaId)) {
            return R.error("参数错误，必要参数不能为空！");
        }
        if (StrUtil.isBlank(qrCode) || !NumberUtil.isNumber(qrCode)) {
            //解析成旧件编号和维修单号
            List<String> parts = extractNumberGroups(qrCode);
            if (CollUtil.isNotEmpty(parts) && parts.size() == 2) {
                qrCode = parts.get(0); // 旧件编号
                String repairOrderNumber = parts.get(1); // 维修单号
            } else {
                return R.error("扫码解析异常，请重试！");
            }
        }

        if (Objects.equals(receiveType, ReceiveTypeEnum.SHIPMENT.getCode()) && CommenUtil.isNullOrZero(wType)) {
            return R.error("发货时必须选择发货类型！");
        }

        ReceiveSendRes receiveSendRes = initReceiveSendRes(wType, receiveType, areaId);

        List<ReceiveSendRes.DataInfo> list = baseMapper.listReceiveSend(null, null, null, Collections.singletonList(Integer.valueOf(qrCode)), null);
        if (CollUtil.isEmpty(list)) {
            list = baseMapper.listReceiveSend(null, null, null, null, Integer.valueOf(qrCode));
        }
        if (CollUtil.isEmpty(list)) {
            return R.error(StrUtil.format("旧件编号（{}）不正确哦！请检查后再试一次", qrCode));
        }
        for (ReceiveSendRes.DataInfo dataInfo : list) {
            if ((Objects.equals(dataInfo.getIsHuanHuo(), 1) && Objects.equals(ReceiveWTypeEnum.RECYCLE_OLD_PART.getCode(), wType))
                    || (!Objects.equals(dataInfo.getIsHuanHuo(), 1) && Objects.equals(ReceiveWTypeEnum.REPAIR_AND_EXCHANGE.getCode(), wType))) {
                return R.error(StrUtil.format("只能扫描{}类型", EnumUtil.getMessageByCode(ReceiveWTypeEnum.class, wType)));
            }

            if (Objects.equals(receiveType,ReceiveTypeEnum.SHIPMENT.getCode())
                    && !Objects.equals(dataInfo.getToAreaState(),ToAreaStateEnum.COMMITTED.getCode())){
                return R.error(StrUtil.format("该旧件当前转地区状态为【{}】，不能进行转地区操作", EnumUtil.getMessageByCode(ToAreaStateEnum.class, dataInfo.getToAreaState())));
            }

            if (Objects.equals(receiveType, ReceiveTypeEnum.SHIPMENT.getCode())
                    && !Objects.equals(dataInfo.getFromAreaId(), areaId)) {
                Areainfo areainfo = areainfoService.getById(dataInfo.getFromAreaId());
                return R.error(StrUtil.format("地区错误，该旧件所属地区为：{}", areainfo.getArea()));
            }

            if (Objects.equals(receiveType,ReceiveTypeEnum.RECEIPT.getCode())
                    && !Objects.equals(dataInfo.getToAreaState(),ToAreaStateEnum.DELIVERED.getCode())){
                return R.error(StrUtil.format("该旧件当前转地区状态为【{}】，不能进行转地区操作", EnumUtil.getMessageByCode(ToAreaStateEnum.class, dataInfo.getToAreaState())));
            }

            if (Objects.equals(receiveType, ReceiveTypeEnum.RECEIPT.getCode())
                    && !Objects.equals(dataInfo.getToAreaId(), areaId)) {
                Areainfo areainfo = areainfoService.getById(dataInfo.getToAreaId());
                return R.error(StrUtil.format("地区错误，该旧件转入地区为：{}", areainfo.getArea()));
            }
            dataInfo.setWType(Objects.equals(dataInfo.getIsHuanHuo(), 1) ? ReceiveWTypeEnum.REPAIR_AND_EXCHANGE.getCode() : ReceiveWTypeEnum.RECYCLE_OLD_PART.getCode());
            dataInfo.setWTypeName(Objects.equals(dataInfo.getIsHuanHuo(), 1) ? ReceiveWTypeEnum.REPAIR_AND_EXCHANGE.getName() : ReceiveWTypeEnum.RECYCLE_OLD_PART.getName());
        }

        list.forEach(l -> {
            l.setToAreaStateName(EnumUtil.getMessageByCode(ToAreaStateEnum.class, l.getToAreaState()));
        });
        if (Objects.equals(receiveType, ReceiveTypeEnum.SHIPMENT.getCode())) {
            String toArea = list.stream().map(ReceiveSendRes.DataInfo::getToArea).distinct().collect(Collectors.joining(","));
            if (StrUtil.isNotBlank(toArea)) {
                receiveSendRes.setToAreaDescribe(StrUtil.format("{} 发往 {}", areainfoService.getById(areaId).getArea(), toArea));
            }
        }
        receiveSendRes.setList(list);
        return R.success(receiveSendRes);
    }

    private static LinkedList<String> extractNumberGroups(String input) {
        LinkedList<String> numberGroups = new LinkedList<>();

        // 正则表达式匹配数字组
        Pattern pattern = Pattern.compile("\\d+");
        Matcher matcher = pattern.matcher(input);

        // 查找所有符合条件的数字组
        while (matcher.find()) {
            numberGroups.add(matcher.group());
        }

        return numberGroups;
    }


    /**
     * 查询收发货列表
     *
     * @param wType
     * @param receiveType
     * @param areaId
     * @return
     */
    @Override
    public R<ReceiveSendRes> listReceiveSend(Integer wType, Integer receiveType, Integer areaId, String wuliuId) {
        // 校验参数
        if (CommenUtil.isNullOrZero(receiveType) || CommenUtil.isNullOrZero(areaId)) {
            return R.error("参数错误，必要参数不能为空！");
        }
        if (Objects.equals(receiveType, ReceiveTypeEnum.SHIPMENT.getCode()) && CommenUtil.isNullOrZero(wType)) {
            return R.error("发货时必须选择发货类型！");
        }
        if (!NumberUtil.isNumber(wuliuId) && StrUtil.isNotBlank(wuliuId)){
            return R.error("物流单号不正确，请检查！");
        }

        ReceiveSendRes receiveSendRes = initReceiveSendRes(wType, receiveType, areaId);
        Integer wuliuIdInt = null;
        if (NumberUtil.isNumber(wuliuId) && StrUtil.isNotBlank(wuliuId)) {
            wuliuIdInt = Integer.valueOf(wuliuId);
        }
        // 查询待发货列表
        List<ReceiveSendRes.DataInfo> list = baseMapper.listReceiveSend(wType, receiveType, areaId, null, wuliuIdInt);
        list.forEach(l -> {
            l.setToAreaStateName(EnumUtil.getMessageByCode(ToAreaStateEnum.class, l.getToAreaState()));
            l.setWType(Objects.equals(l.getIsHuanHuo(), 1) ? ReceiveWTypeEnum.REPAIR_AND_EXCHANGE.getCode() : ReceiveWTypeEnum.RECYCLE_OLD_PART.getCode());
            l.setWTypeName(Objects.equals(l.getIsHuanHuo(), 1) ? ReceiveWTypeEnum.REPAIR_AND_EXCHANGE.getName() : ReceiveWTypeEnum.RECYCLE_OLD_PART.getName());
        });
        if (Objects.equals(receiveType, ReceiveTypeEnum.SHIPMENT.getCode())) {
            String toArea = list.stream().map(ReceiveSendRes.DataInfo::getToArea).distinct().collect(Collectors.joining(","));
            if (StrUtil.isNotBlank(toArea)) {
                receiveSendRes.setToAreaDescribe(StrUtil.format("{} 发往 {}", areainfoService.getById(areaId).getArea(), toArea));
            }
        }
        receiveSendRes.setList(list);
        return R.success(receiveSendRes);
    }

    private ReceiveSendRes initReceiveSendRes(Integer wType, Integer receiveType, Integer areaId) {
        ReceiveSendRes receiveSendRes = new ReceiveSendRes();
        receiveSendRes.setReceiveType(receiveType);
        receiveSendRes.setWType(wType);
        if (Objects.equals(receiveType, ReceiveTypeEnum.SHIPMENT.getCode())) {
            receiveSendRes.setTitle(StrUtil.format("{}{}",
                    EnumUtil.getMessageByCode(ReceiveWTypeEnum.class, wType),
                    EnumUtil.getMessageByCode(ReceiveTypeEnum.class, receiveType)));
        } else {
            receiveSendRes.setTitle(StrUtil.format("维修旧件收货({})", areainfoService.getById(areaId).getArea()));
        }
        receiveSendRes.setAreaId(areaId);
        return receiveSendRes;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<Boolean> saveReceiveSend(ReceiveSendRes req) {
        OaUserBO oaUserBO = new OaUserBO();
        oaUserBO = currentRequestComponent.getCurrentStaffId();
        if (Objects.isNull(oaUserBO)) {
            if (req.getUserId() == null || req.getUserName() == null){
                return R.error("登陆信息失效，请重新登陆！");
            }
            oaUserBO.setUserName(req.getUserName());
            oaUserBO.setUserId(req.getUserId());
            oaUserBO.setAreaId(req.getAreaId());
            oaUserBO.setArea(areainfoService.getById(req.getAreaId()).getArea());
        }

        // 提取请求中的ID
        List<Integer> ids = req.getList().stream()
                .map(ReceiveSendRes.DataInfo::getId)
                .collect(Collectors.toList());

        // 查询收发货数据
        List<ReceiveSendRes.DataInfo> list = baseMapper.listReceiveSend(null, null, null, ids, null);
        if (CollUtil.isEmpty(list)) {
            return R.error("数据查询为空，请刷新后重试！");
        }

        // 验证请求的ID是否与数据库中的ID一致
        List<Integer> retrievedIds = list.stream()
                .map(ReceiveSendRes.DataInfo::getId)
                .collect(Collectors.toList());
        Collection<Integer> disjunction = CollUtil.disjunction(ids, retrievedIds);
        if (CollUtil.isNotEmpty(disjunction)) {
            return R.error(StrUtil.format("编号数据异常{}，请检查！", disjunction));
        }

        List<Integer> collect = list.stream().map(ReceiveSendRes.DataInfo::getIsHuanHuo).distinct().collect(Collectors.toList());
        //校验数据
        for (ReceiveSendRes.DataInfo dataInfo : list) {
            if ((Objects.equals(dataInfo.getIsHuanHuo(), 1) && Objects.equals(ReceiveWTypeEnum.RECYCLE_OLD_PART.getCode(), req.getWType()))
                    || (!Objects.equals(dataInfo.getIsHuanHuo(), 1) && Objects.equals(ReceiveWTypeEnum.REPAIR_AND_EXCHANGE.getCode(), req.getWType()))) {
                return R.error(StrUtil.format("只能扫描{}类型", EnumUtil.getMessageByCode(ReceiveWTypeEnum.class, req.getWType())));
            }

            if (Objects.equals(req.getReceiveType(),ReceiveTypeEnum.SHIPMENT.getCode())
                    && !Objects.equals(dataInfo.getToAreaState(),ToAreaStateEnum.COMMITTED.getCode())){
                return R.error(StrUtil.format("该旧件当前转地区状态为【{}】，不能进行转地区操作", EnumUtil.getMessageByCode(ToAreaStateEnum.class, dataInfo.getToAreaState())));
            }

            if (Objects.equals(req.getReceiveType(), ReceiveTypeEnum.SHIPMENT.getCode())
                    && !Objects.equals(dataInfo.getFromAreaId(), req.getAreaId())) {
                Areainfo areainfo = areainfoService.getById(dataInfo.getFromAreaId());
                return R.error(StrUtil.format("地区错误，该旧件所属地区为：{}", areainfo.getArea()));
            }

            if (Objects.equals(req.getReceiveType(), ReceiveTypeEnum.RECEIPT.getCode())) {
                if (collect.size() > 1) {
                    return R.error("不能同时对“回收旧件”和“换货旧件”进行收货操作");
                }
                if (CommenUtil.isNullOrZero(req.getWType())) {
                    req.setWType(Objects.equals(collect.get(0), 1) ? ReceiveWTypeEnum.REPAIR_AND_EXCHANGE.getCode() : ReceiveWTypeEnum.RECYCLE_OLD_PART.getCode());
                }
            }

            if (Objects.equals(req.getReceiveType(), ReceiveTypeEnum.RECEIPT.getCode())
                    && !Objects.equals(dataInfo.getToAreaState(), ToAreaStateEnum.DELIVERED.getCode())) {
                return R.error(StrUtil.format("旧件编号{}当前转地区状态为【{}】，不能进行转地区操作", dataInfo.getId(), EnumUtil.getMessageByCode(ToAreaStateEnum.class, dataInfo.getToAreaState())));
            }

            if (Objects.equals(req.getReceiveType(), ReceiveTypeEnum.RECEIPT.getCode())
                    && !Objects.equals(dataInfo.getToAreaId(), req.getAreaId())) {
                Areainfo areainfo = areainfoService.getById(dataInfo.getToAreaId());
                return R.error(StrUtil.format("编号{}地区错误，该旧件所属地区为：{}", dataInfo.getId(), areainfo.getArea()));
            }
            dataInfo.setWType(Objects.equals(dataInfo.getIsHuanHuo(), 1) ? ReceiveWTypeEnum.REPAIR_AND_EXCHANGE.getCode() : ReceiveWTypeEnum.RECYCLE_OLD_PART.getCode());
            dataInfo.setWTypeName(Objects.equals(dataInfo.getIsHuanHuo(), 1) ? ReceiveWTypeEnum.REPAIR_AND_EXCHANGE.getName() : ReceiveWTypeEnum.RECYCLE_OLD_PART.getName());
        }

        String comment = null;

        // 根据接收类型处理发货或收货逻辑
        if (Objects.equals(req.getReceiveType(), ReceiveTypeEnum.SHIPMENT.getCode())) {
            comment = handleShipment(req, list, oaUserBO);
        } else if (Objects.equals(req.getReceiveType(), ReceiveTypeEnum.RECEIPT.getCode())) {
            comment = handleReceipt(req, list, oaUserBO);
        }

        // 添加备注日志
        addRemarkLogs(req.getList(), comment, oaUserBO);

        return R.success("操作成功");
    }



    /**
     * 处理发货逻辑
     *
     * @param req 请求参数
     * @param list 从数据库查询的列表
     * @param oaUser 当前用户信息
     * @return 返回的日志内容
     */
    private String handleShipment(ReceiveSendRes req, List<ReceiveSendRes.DataInfo> list, OaUserBO oaUserBO) {
        req.setList(list);
        // 以收货地区分类
        Map<Integer, List<ReceiveSendRes.DataInfo>> dataInfoMaps = list.stream()
                .collect(Collectors.groupingBy(ReceiveSendRes.DataInfo::getToAreaId));
        List<Integer> wuliuIds = new ArrayList();
        for (Map.Entry<Integer, List<ReceiveSendRes.DataInfo>> entry : dataInfoMaps.entrySet()) {
            Integer toAreaId = entry.getKey();
            List<ReceiveSendRes.DataInfo> value = entry.getValue();
            Integer areaId = req.getAreaId();

            // 获取收件人信息
            LogisticsRecipientBO receiver = receivePersonConfigService.getReceiverUserInfo(req.getWType(), areaId, toAreaId);
            if (Objects.isNull(receiver)) {
                throw new CustomizeException("该地区未配置收件人信息，请联系管理员配置");
            }

            // 创建物流单
            SubWLModelReq subWLModelReq = buildReceiveSendWuliuReqForToArea(oaUserBO, req, receiver, toAreaId);
            R<Integer> wuliuResult = wuliuCloud.saveWuLiu(subWLModelReq, areaId, oaUserBO.getUserName());
            if (!wuliuResult.isSuccess()) {
                return wuliuResult.getUserMsg();
            }

            // 处理成功的物流单
            Integer wuliuId = wuliuResult.getData();
            wuliuIds.add(wuliuId);
            List<Integer> ids = value.stream().map(ReceiveSendRes.DataInfo::getId).collect(Collectors.toList());
            int i = baseMapper.saveSend(ids, ToAreaStateEnum.DELIVERED.getCode(), 1, req.getReceiveType(),
                    wuliuId, receiver.getReceiveUserName(), areaId);
            if (!Objects.equals(ids.size(), i)) {
                log.warn(StrUtil.format("发货异常，请求参数：{}，收件人信息：{}", JSON.toJSONString(req), JSON.toJSONString(receiver)));
                throw new CustomizeException("发货异常！");
            }

            //如果有打印
            if (StrUtil.isNotBlank(req.getClientNo())) {
                doPrint(oaUserBO, String.valueOf(33), String.valueOf(wuliuId), req.getClientNo(), req.getPrintCount());
            }
        }
        String comment = "物流单号生成成功，物流单号：" +
                wuliuIds.stream()
                        .distinct()
                        .map(wuliuId -> String.format("<a href='/addOrder/wuliu?wuliuid=%s'>%s</a>", wuliuId, wuliuId))
                        .collect(Collectors.joining(", "));
        return comment;
    }


    /**
     * 处理收货逻辑
     *
     * @param req 请求参数
     * @param list 从数据库查询的列表
     * @param oaUser 当前用户信息
     * @return 返回的日志内容
     */
    private String handleReceipt(ReceiveSendRes req, List<ReceiveSendRes.DataInfo> list, OaUserBO oaUserBO) {
        req.setList(list);
        // 以收货地区分类
        Map<Integer, List<ReceiveSendRes.DataInfo>> dataInfoMaps = list.stream()
                .collect(Collectors.groupingBy(ReceiveSendRes.DataInfo::getFromAreaId));

        for (Map.Entry<Integer, List<ReceiveSendRes.DataInfo>> entry : dataInfoMaps.entrySet()) {
            Integer fromAreaId = entry.getKey();
            List<ReceiveSendRes.DataInfo> value = entry.getValue();
            Integer areaId = req.getAreaId();

            // 获取收件人信息
            LogisticsRecipientBO receiver = receivePersonConfigService.getReceiverUserInfo(req.getWType(), fromAreaId, areaId);
            if (Objects.isNull(receiver)) {
                throw new CustomizeException(StrUtil.format("{}该地区未配置收件人信息，请联系管理员配置", fromAreaId));
            }

            List<Integer> ids = value.stream().map(ReceiveSendRes.DataInfo::getId).collect(Collectors.toList());
            int i = baseMapper.saveReceive(ids, ToAreaStateEnum.RECEIVED.getCode(), 0, req.getReceiveType(), req.getAreaId(), receiver.getReceiveUserName());
            if (!Objects.equals(i, ids.size())) {
                log.warn(StrUtil.format("收货失败，请求参数：{}，收件人信息：{}", JSON.toJSONString(req), JSON.toJSONString(receiver)));
                throw new CustomizeException("收货失败！");
            }
        }

        // 查询当前物流单是否都已被签收 都签收了话更改物流状态并且记日志
        checkWuliuSignStatus(list, oaUserBO);
        return StrUtil.format("收货成功,旧件编号：{}", list.stream().map(ReceiveSendRes.DataInfo::getId).distinct().map(String::valueOf).collect(Collectors.joining(", ")));
    }

    /**
     * 检查物流单签收状态并处理
     *
     * @param list 数据列表
     * @param oaUserBO 当前用户
     */
    private void checkWuliuSignStatus(List<ReceiveSendRes.DataInfo> list, OaUserBO oaUserBO) {
        List<Integer> wuliuIds = list.stream()
                .map(ReceiveSendRes.DataInfo::getWuliuId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(wuliuIds)) {
            return;
        }

        List<Map> huishouByWuliuIdsMap = baseMapper.getHuishouByWuliuIds(wuliuIds);
        for (Map map : huishouByWuliuIdsMap) {
            Integer wuliuId = (Integer) map.get("wuliuid");
            Integer quantity = (Integer) map.get("quantity");
            String hsids = (String) map.get("ids");

            //更新物流日志
            if (Objects.equals(quantity, 0)) {
                Integer count = SpringUtil.getBean(ShouhouBatchToAreaMapper.class).updateWuliuStatsById(Long.valueOf(wuliuId));
                if (CommenUtil.isNotNullZero(count)) {
                    WuliuLogs wuliuLogs = new WuliuLogs();
                    wuliuLogs.setWuliuid(Long.valueOf(wuliuId));
                    wuliuLogs.setInuser(oaUserBO.getUserName());
                    wuliuLogs.setMsg("旧件转地区处理完成，单号：" + hsids + "，物流单自动完成");
                    wuliuLogs.setDtime(LocalDateTime.now());
                    SpringUtil.getBean(WuliuLogsService.class).save(wuliuLogs);
                }
            }
        }
    }


    /**
     * 添加备忘日志
     *
     * @param dataInfoList 数据列表
     * @param comment 日志内容
     * @param oaUserBO 当前用户信息
     */
    private void addRemarkLogs(List<ReceiveSendRes.DataInfo> dataInfoList, String comment, OaUserBO oaUserBO) {
        List<ShouhouProRemarkLogs> shouhouProRemarkLogsList = dataInfoList.stream()
                .map(dataInfo -> {
                    ShouhouProRemarkLogs log = new ShouhouProRemarkLogs();
                    log.setHuishouId(dataInfo.getId());
                    log.setInuser(oaUserBO.getUserName());
                    log.setContent(comment);
                    return log;
                })
                .collect(Collectors.toList());

        baseMapper.addRemarkLogs(shouhouProRemarkLogsList);
    }

    /**
     * 构建物流请求对象
     */
    private SubWLModelReq buildReceiveSendWuliuReqForToArea(OaUserBO oaUserBO, ReceiveSendRes receiveSend,
                                                            LogisticsRecipientBO receiver, Integer toAreaId) {
        // 获取收件和发件的地区信息
        Areainfo receiveInfo = areainfoService.getById(toAreaId);
        Areainfo sendInfo = areainfoService.getById(receiveSend.getAreaId());

        // 获取收发货数量和ID列表
        int count = receiveSend.getList().size();
        String ids = receiveSend.getList().stream().map(ReceiveSendRes.DataInfo::getId)
                .map(String::valueOf).collect(Collectors.joining(","));

        // 准备请求对象
        SubWLModelReq req = new SubWLModelReq();
        if (count > 0) {
            // 生成快递内容
            req.setComment(buildWuliuContent(count, receiveInfo.getAreaName(), receiveInfo.getArea(), ids));
            req.setAreaId(sendInfo.getId());
            req.setActionName("售后回收旧件调拨");
            req.setReceiveAddress(getEffectiveReceiveAddress(receiveInfo));
            if (Objects.equals(ReceiveWTypeEnum.REPAIR_AND_EXCHANGE.getCode(), receiveSend.getWType())) {
                req.setWCateId(27);
            } else if (Objects.equals(ReceiveWTypeEnum.RECYCLE_OLD_PART.getCode(), receiveSend.getWType())) {
                req.setWCateId(28);
            }
            // 填充用户信息
            fillUserDetails(req, oaUserBO, receiver, sendInfo, receiveInfo, receiveSend);
        }
        return req;
    }

    /**
     * 生成物流内容
     *
     * @param count        物品数量
     * @param areaName     区域名称
     * @param area         区域
     * @return 物流的内容字符串
     */
    private String buildWuliuContent(int count, String areaName, String area, String ids) {
        String toAreaName = areaName + "(" + area + ")";
        String toAreaFlag = toAreaName + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmm"));
        StringBuilder contentBuilder = new StringBuilder("回收旧件调拨，共计")
                .append(count).append("个,转地区标识码：")
                .append(toAreaFlag).append(",单号：").append(ids);
        return contentBuilder.length() > 190 ? contentBuilder.substring(0, 190) : contentBuilder.toString();
    }

    /**
     * 获取有效的收件地址
     *
     * @param receiveInfo 收件地区信息
     * @return 收件地址
     */
    private String getEffectiveReceiveAddress(Areainfo receiveInfo) {
        AreaBelongsDcHqD1AreaId areaBelongs = areainfoService.getAreaBelongsDcHqD1AreaId(receiveInfo.getId());
        if (areaBelongs != null && (Objects.equals(areaBelongs.getH1AreaId(), receiveInfo.getId()) ||
                Objects.equals(areaBelongs.getDcAreaId(), receiveInfo.getId()))) {
            Areainfo area = areainfoService.getById(areaBelongs.getH1AreaId());
            return area.getCompanyAddress();
        }
        // 如果没有找到替代地址，返回默认地址
        return receiveInfo.getCompanyAddress();
    }

    /**
     * 填充用户与地址信息
     *
     * @param req         请求对象
     * @param oaUserBO    当前用户信息
     * @param receiver    收件人信息
     * @param sendInfo    发件地区信息
     * @param receiveInfo 收件地区信息
     */
    private void fillUserDetails(SubWLModelReq req, OaUserBO oaUserBO, LogisticsRecipientBO receiver,
                                 Areainfo sendInfo, Areainfo receiveInfo, ReceiveSendRes receiveSend) {
        String sendUserMobile = getUserMobile(oaUserBO.getUserName(), receiver);
        String receiverUserMobile = getUserMobile(receiver.getReceiveUserName(), receiver);

        req.setSendName(oaUserBO.getUserName());
        req.setSendMobile(sendUserMobile);
        req.setSendAddress(sendInfo.getCompanyAddress());
        req.setSAreaId(sendInfo.getId());
        req.setSendCityId(sendInfo.getCityid());

        req.setReceiveName(receiver.getReceiveUserName());
        req.setReceiveMobile(receiverUserMobile);

        // 设置收件地区信息
        req.setRAreaId(receiveInfo.getId());
        req.setReceiveCityId(receiveInfo.getCityid());
        req.setConsignee(receiveInfo.getAreaName());
        req.setStatus(1);
        req.setDispatchedPerson("");
        req.setLogisticType(1);
        req.setDTime(LocalDateTime.now());
        req.setSpid(sendInfo.getPid());
        req.setSzid(sendInfo.getZid());
        req.setSdid(sendInfo.getDid());
        req.setRpid(receiveInfo.getPid());
        req.setRzid(receiveInfo.getZid());
        req.setRdid(receiveInfo.getDid());
        req.setInUser(oaUserBO.getUserName());
        req.setPrice(BigDecimal.ZERO);
        req.setCostPrice(BigDecimal.ZERO);
        req.setCom("");
        req.setLogs(new ArrayList<>());
    }

    /**
     * 获取用户的手机号码
     *
     * @param userName 用户名
     * @param receiver 收件人信息
     * @return 用户手机号码或空字符串
     */
    private String getUserMobile(String userName, LogisticsRecipientBO receiver) {
        List<SmallproInfoInuserInfoBO> userInfoList = smallproMapper.getSmallproInUserInfo(Collections.singletonList(userName));
        for (SmallproInfoInuserInfoBO bo : userInfoList) {
            if (userName.equals(bo.getInUserName())) {
                return bo.getMobile();
            }
        }
        // 默认返回空字符串
        return "";
    }

    /**
     * 打印通知
     * @param currUser
     * @param type
     * @param printID
     * @param clientNo
     * @param printCount
     * @return
     */
    private R doPrint(OaUserBO currUser, String type, String printID, String clientNo, Integer printCount) {
        if (CommenUtil.isNullOrZero(printCount)) {
            printCount = 1;
        }
        String url = ("https://moa.9ji.com/oa/Print/doPrint?printid=" + StrUtil.format("0|{}", printID)
                + "&type=" + type + "&clientNo=" + clientNo + "&printcount=" + printCount);
        try {
            Map<String, String> headMap = new HashMap<>();
            headMap.put("Authorization", currentRequestComponent.getCurrentStaffId().getToken());
            headMap.put("is_request_from_pad", Convert.toStr(Convert.toInt(isPda())));
            String result = HttpClientUtils.get(url, headMap);
            log.info("打印数据" + url + "___" + result);
        } catch (Exception e) {
            log.error("打印机通知失败，参数：{}", StringUtils.join(url, StringPool.COMMA));
            return R.error("打印失败！");
        }
        return R.success("打印成功");
    }

    /**
     * 是否为pda
     * @return
     */
    public static boolean isPda() {
        return SpringContextUtil.getRequest()
                .map(req -> {
                    boolean isPda = Convert.toBool(req.getHeader("is_request_from_pad"), false);
                    if (isPda) {
                        return true;
                    }
                    Cookie isPdaCookie = ServletUtil.getCookie(req, "isPDA");
                    if (isPdaCookie == null) {
                        return false;
                    }
                    return Convert.toBool(isPdaCookie.getValue(), false);
                }).orElse(false);
    }


    @Override
    public R<ReceiveSendResVO> listReceiveSendCount(Integer areaId) {
        ReceiveSendResVO receiveSendResVO = new ReceiveSendResVO();

        //当前后台门店维修旧件（包括回收旧件和换货旧件）转地区状态有【已提交】状态的数据，展示红色小点
        List<ReceiveSendRes.DataInfo> list1 = baseMapper.listReceiveSend(ReceiveWTypeEnum.RECYCLE_OLD_PART.getCode(), ReceiveTypeEnum.SHIPMENT.getCode(), areaId, null, null);
        List<ReceiveSendRes.DataInfo> list2 = baseMapper.listReceiveSend(ReceiveWTypeEnum.REPAIR_AND_EXCHANGE.getCode(), ReceiveTypeEnum.SHIPMENT.getCode(), areaId, null, null);
        receiveSendResVO.setRepairedParts(list1.size() + list2.size());

        //当前后台门店维修旧件（包括回收旧件和换货旧件）转地区状态有待接收的【已发出】状态的数据，展示红色小点
        List<ReceiveSendRes.DataInfo> list3 = baseMapper.listReceiveSend(ReceiveWTypeEnum.RECYCLE_OLD_PART.getCode(), ReceiveTypeEnum.RECEIPT.getCode(), areaId, null, null);
        List<ReceiveSendRes.DataInfo> list4 = baseMapper.listReceiveSend(ReceiveWTypeEnum.REPAIR_AND_EXCHANGE.getCode(), ReceiveTypeEnum.RECEIPT.getCode(), areaId, null, null);
        receiveSendResVO.setRepairReceivingParts(list3.size() + list4.size());

        return R.success(receiveSendResVO);
    }
}
