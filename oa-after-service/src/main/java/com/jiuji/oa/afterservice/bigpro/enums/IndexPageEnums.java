package com.jiuji.oa.afterservice.bigpro.enums;

import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @<NAME_EMAIL>
 * @date 2020/7/23 15:54
 **/
public class IndexPageEnums {

    @Getter
    @AllArgsConstructor
    public enum ShouKind1 implements CodeMessageEnumInterface {

        MOBILE("mobile", "电话号码",true),
        IMEI("imei", "串号",true),
        ID("id", "维修单号",true),
        NAME("name", "机型",true),
        INUSER("inuser", "接件人",true),
        WEIXIUREN("weixiuren", "维修人",true),
        USERID("userid", "客户编号",true),
        MKC_ID("mkc_id", "mkc_id",true),
        TESTUSER("testuser", "测试人",true),
        COMMENT("comment", "订单备注",true),
        PRODUCTID("productid", "productID",true),
        MINPRICE("minprice", "机器金额(大于n元)",true),

        ORDERID("orderid", "机器编号",true),
        PANDIANINUSER("pandianinuser", "盘点人",true),
        SYDAY("syday", "送修时使用天数(小于n天)",true),
        DAY("day", "维修周期(大于n天)",true),
        MINDAY("minday", "维修周期(小于等于n天)",true),
        MINHOUR("minhour", "维修周期(小于等于n小时)",true),
        MINMINUTE("minminute", "维修周期(小于等于n分钟)",true),
        FEIYONG("feiyong", "维修费(大于n元)",true),
        COSTPRICE("costprice", "维修成本(大于n元)",true),
        WXLIRUN("wxlirun", "维修利润(大于n元)",true),
        WXLIRUNF("wxlirunf", "维修利润(小于n元)",true),
        WXCOUNT("wxcount", "维修次数(大于n次)",true),
        QUJIZHOUQI("qujizhouqi", "取机通知周期（大于n天）",true),
        WXKINDSHOUYIN("wxkindShouyin", "快修未收银",false),
        GJUSER("gjuser", "跟进人",true)
        ;
        private String code;
        private String message;
        private Boolean show;
    }

    @Getter
    @AllArgsConstructor
    public enum Issoft implements CodeMessageEnumInterface {
        SOFT("1", "软件"),
        HARD("0", "硬件");
        private String code;
        private String message;
    }

    @Getter
    @AllArgsConstructor
    public enum ShouKind2 implements CodeMessageEnumInterface {
        CLZ("clz", "处理中"),
        YXH("yxh", "已修好"),
        XBH("xbh", "修不好");
        //HSZ("hsz", "回收站");
        private String code;
        private String message;
    }

    @Getter
    @AllArgsConstructor
    public enum Baoxiu implements CodeMessageEnumInterface {
        NO(0, "不在"),
        YES(1, "在"),
        EX(4, "检测换机"),
        WAIT_CHECK(3, "待确认"),
        OUT(2, "外修");
        private Integer code;
        private String message;
    }

    @Getter
    @AllArgsConstructor
    public enum Isquji implements CodeMessageEnumInterface {
        YES(1, "已取机"),
        NO(0, "未取机");
        private Integer code;
        private String message;
    }

    /**
     * 外送类型
     * <AUTHOR>
     * @date 2021/7/28
     **/
    @Getter
    @AllArgsConstructor
    public enum WaiSong implements CodeMessageEnumInterface {
        YES(1, "外送"),
        NO(2, "自修");
        private Integer code;
        private String message;
    }

    @Getter
    @AllArgsConstructor
    public enum Tuihuankind implements CodeMessageEnumInterface {
        NO(1, "换机头"),
        YES(3, "退款"),
        OTHER(4, "换其它型号");
        private Integer code;
        private String message;
    }

    @Getter
    @AllArgsConstructor
    public enum IsToArea implements CodeMessageEnumInterface {
        NO("0", "否"),
        IN("1", "转入"),
        OUT("2", "转出"),
        Location("3", "所在地");
        private String code;
        private String message;
    }

    @Getter
    @AllArgsConstructor
    public enum RepairLevel implements CodeMessageEnumInterface {
        RepairLevel1(1, "更配维修"),
        RepairLevel2(2, "芯片维修"),
        RepairLevel3(3, "总成置换"),
        RepairLevel4(4, "硬盘升级"),
        RepairLevel10(10, "裸机"),
        RepairLevel11(11, "套餐");
        private Integer code;
        private String message;
    }

    @Getter
    @AllArgsConstructor
    public enum ShouhouServicesSale implements CodeMessageEnumInterface {
        RepairLevel1("1", "售后电池险"),
        RepairLevel2("2", "售后碎屏险"),
        RepairLevel3("3", "售后后盖险")
        ;
        private String code;
        private String message;
    }

    /**
     * 服务方式
     */
    @Getter
    @AllArgsConstructor
    public enum WebType implements CodeMessageEnumInterface {
        WebType4("4", "上门快修"),
        WebType1("1", "到店快修");
        private String code;
        private String message;
    }

    @Getter
    @AllArgsConstructor
    public enum DateKind implements CodeMessageEnumInterface {
        ServiceType1("modidate", "送修时间"),
        ServiceType2("tradedate", "购机时间"),
        ServiceType4("result_dtime", "跟进时间"),
        ServiceType5("modidtime", "修好时间"),
        ServiceType6("offtime", "取机时间"),
        ServiceType7("weixiu_startdtime", "维修组分配时间"),
        ServiceType8("weixiudtime", "维修组完成时间"),
        ServiceType9("testtime", "测试时间");
        private String code;
        private String message;
    }

    @Getter
    @AllArgsConstructor
    public enum YouPinMachine implements CodeMessageEnumInterface {
        YOU_PIN(1, "优品机"),
        NOT_YOU_PIN(2, "非优品机");
        private Integer code;
        private String message;
    }

    /**
     * 日期类型
     * <AUTHOR>
     * @date 2021/7/28
     **/
    @Getter
    @AllArgsConstructor
    public enum DateKindV2 implements CodeMessageEnumInterface {
        ServiceType1("modidate", "送修时间"),
        ServiceType5("modidtime", "修好/修不好时间"),
        ServiceType6("offtime", "取机时间");
        private String code;
        private String message;
    }

    /**
     * 分类名称
     * <AUTHOR>
     * @date 2021/7/28
     **/
    @Getter
    @AllArgsConstructor
    public enum KindName implements CodeMessageEnumInterface {
        WxKindName1(1, "修"),
        WxKindName2(2, "换"),
        WxKindName4(3, "多"),
        WxKindName5(4, "送"),
        WxKindName6(5, "快");
        private Integer code;
        private String message;
    }

    @Getter
    @AllArgsConstructor
    public enum WxKind implements CodeMessageEnumInterface {
        WxKind1(1, "维修"),
        WxKind2(2, "退换"),
        WxKind3(3, "多维换机"),
        WxKind4(4, "外送维修"),
        WxKind5(5, "显示总成置换"),
        WxKind6(6, "上门快修");
        private Integer code;
        private String message;
    }

}

