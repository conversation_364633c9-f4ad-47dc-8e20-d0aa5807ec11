package com.jiuji.oa.afterservice.stock.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.afterservice.common.bo.OaUserBO;
import com.jiuji.oa.afterservice.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.afterservice.common.util.PageUtil;
import com.jiuji.oa.afterservice.stock.dao.StockLocationKcMapper;
import com.jiuji.oa.afterservice.stock.dao.StockLocationMapper;
import com.jiuji.oa.afterservice.stock.po.StockLocation;
import com.jiuji.oa.afterservice.stock.service.StockLocationService;
import com.jiuji.oa.afterservice.stock.vo.CurrentLocationVO;
import com.jiuji.oa.afterservice.stock.vo.PickUpProductVO;
import com.jiuji.oa.afterservice.stock.vo.StockLocationKcVO;
import com.jiuji.oa.afterservice.stock.vo.req.StockLocationCheckItem;
import com.jiuji.oa.afterservice.stock.vo.req.StockLocationCheckQuery;
import com.jiuji.oa.afterservice.stock.vo.req.StockLocationHttpReq;
import com.jiuji.oa.afterservice.stock.vo.req.StockLocationOutInReq;
import com.jiuji.tc.common.vo.R;
import com.sun.istack.Nullable;
import io.lettuce.core.RedisException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-09-15
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class StockLocationServiceImpl extends ServiceImpl<StockLocationMapper, StockLocation> implements StockLocationService {
    private final StringRedisTemplate redisTemplate;
    private final static String CACHE_KEY = "OA:JAVA:AFTER:STOCK_LOCATIONS";
    private final static String LOCK_KEY = "OA:JAVA:AFTER:LOCK_STOCK_LOCATIONS";
//    private final static String CACHE_KEY_START_STOCK_LOCATION_CHECK_PREFIX = "OA:JAVA:AFTER:StartStockLocationCheck:";
    private final RedissonClient redisson;
    private final StockLocationKcMapper stockLocationKcMapper;
    private final AbstractCurrentRequestComponent abstractCurrentRequestComponent;

    @Override
    public List<StockLocation> listAllStockLocation() {
        if (Boolean.TRUE.equals(redisTemplate.hasKey(CACHE_KEY)))
            return JSONObject.parseArray(redisTemplate.opsForValue().get(CACHE_KEY), StockLocation.class);
        RLock fairLock = redisson.getFairLock(LOCK_KEY);
        List<StockLocation> results = this.baseMapper.getAllLocations();
        try {
            boolean flag = fairLock.tryLock(3, 10, TimeUnit.SECONDS);
            if (flag) redisTemplate.opsForValue().set(CACHE_KEY,
                    JSONObject.toJSONString(results),
                    1440L, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("获取所有库位信息异常:" + e.getMessage());
        } finally {
            try {
                //解锁
                if (fairLock.isHeldByCurrentThread()) {
                    fairLock.unlock();
                }
            } catch (RedisException e) {
                log.error("listAllStockLocation->fairLock.unlock error", e);
            }
        }
        return results;
    }

    @Override
    public @Nullable
    StockLocation getStockLocationByCode(String locationCode, int areaId) {
        return this.listAllStockLocation().stream().filter(stockLocation ->
                Objects.equals(stockLocation.getCode(), locationCode)
                        && Objects.equals(stockLocation.getAreaid(), areaId)
        ).findFirst().orElse(null);
    }

    @Override
    public List<StockLocation> getStockLocationByArea(int areaId) {
        return this.listAllStockLocation().stream().filter(stockLocation -> Objects.equals(stockLocation.getAreaid(), areaId)).collect(Collectors.toList());
    }

    /**
     * 库位开始盘点
     *
     * @param locationCode
     * @param areaId
     */
    @Override
    public R startStockLocationCheck(String locationCode, int areaId) {
        StockLocation stockLocation = this.getStockLocationByCode(locationCode, areaId);
        if (stockLocation == null)
            return R.error("无效库位号：" + locationCode);
        // 原缓存KEY   "startStockLocationCheckCacheKeys_"+stockLocation.code+"_"+areaid;
        // 业务逻辑改变，cachekey改为由用户控制
//        String cacheKey = CACHE_KEY_START_STOCK_LOCATION_CHECK_PREFIX + stockLocation.getCode() + "_" + areaId;
//        if (this.redisTemplate.hasKey(cacheKey))
//            return R.error("库位号：" + stockLocation.getCode() + " 已在盘点期间，请先生成盘点记录！");
        this.baseMapper.stockLocationCheck(stockLocation.getCode(), stockLocation.getId(), areaId);
//        this.redisTemplate.opsForValue().set(cacheKey, DateUtil.df.format(LocalDateTime.now()));
        return R.success(null);
    }

    /**
     * 库位盘点生成盘点记录
     *
     * @param locationCode locationCode
     * @param areaId       areaId
     * @param userName     userName
     * @param panWay       1 追加 2 覆盖 3 删除当天 4 独立盘点，全部新增
     * @return R
     */
    @Override
    @Transactional
    public R createStockLocationRecord(String locationCode, int areaId, String userName, int panWay) {
        StockLocation stockLocation = getStockLocationByCode(locationCode, areaId);
        if (Objects.equals(null, stockLocation))
            return R.error("无效库位号：" + locationCode);
        // 原缓存KEY   "startStockLocationCheckCacheKeys_"+stockLocation.code+"_"+areaid;
        // 改变，放到调用里判断
//        String cacheKey = CACHE_KEY_START_STOCK_LOCATION_CHECK_PREFIX + stockLocation.getCode() + "_" + areaId;
//        if (!redisTemplate.hasKey(cacheKey))
//            return R.error("库位号：" + stockLocation.getCode() + " 不在盘点期间，请先点击开始盘点！");

        int subId;
        boolean isChange = false;
        StockLocationCheckQuery query = StockLocationCheckQuery.builder()
                .areaId(areaId)
                .kinds("pj")
                .type("3")
                .inUser(userName)
                .comment("库位号盘点，库位号：" + stockLocation.getCode())
                .panKind("0")
                .locationCode(stockLocation.getCode())
                .locationId(stockLocation.getId())
                .build();
        Integer subId2 = this.baseMapper.getKCPanSubId(query);
        if (Objects.equals(null, subId2)) {
            subId2 = this.baseMapper.insertKCPanSub(query);
            subId = subId2;
        } else {
            subId = subId2;
            this.baseMapper.updateKCPanSub(query, subId);
        }
        if (panWay == 1) {//追加
            subId2 = this.baseMapper.zhuiJiaUpdate(query, subId);
            if (subId2 != null && subId2 > 0)
                isChange = true;
            //增加之前没有的
            subId2 = this.baseMapper.zhuiJiaInsert(query, subId);
            if (subId2 != null && subId2 > 0)
                isChange = true;
        } else if (panWay == 2) {//覆盖
            subId2 = this.baseMapper.fuGaiUpdate(query, subId);
            if (subId2 != null && subId2 > 0)
                isChange = true;
            //增加之前没有的
            subId2 = this.baseMapper.fuGaiInsert(query, subId);
            if (subId2 != null && subId2 > 0)
                isChange = true;
        } else if (panWay == 3) {//删除当天
            subId2 = this.baseMapper.dayDelete(subId);
            if (subId2 != null && subId2 > 0)
                isChange = true;
            subId2 = this.baseMapper.dayInsert(query, subId);
            if (subId2 != null && subId2 > 0)
                isChange = true;
        } else if (panWay == 4) {//独立盘点，全部新增
            subId2 = this.baseMapper.allInsert(query, subId);
            if (subId2 != null && subId2 > 0)
                isChange = true;
        }

        if (isChange) {
            if (isChange && this.redisTemplate.hasKey("ManpanSetAll"))
                this.redisTemplate.delete("ManpanSetAll");
        } else
            return R.error("盘点记录生成失败");
        return R.success(null);
    }

    /**
     * 库位盘点
     *
     * @param locationCode locationCode
     * @param areaId       areaId
     * @param userName     userName
     * @param items        StockLocationCheckItem
     * @return R
     * @see .net oa999.oa999DAL.stockLocationService.cs#stockLocationCheck
     */
    @Override
    public R stockLocationCheck(String locationCode, int areaId, String userName, List<StockLocationCheckItem> items) {
        StockLocation stockLocation = getStockLocationByCode(locationCode, areaId);
        if (stockLocation == null)
            return R.error("无效库位号：" + locationCode);
        // 原缓存KEY   "startStockLocationCheckCacheKeys_"+stockLocation.code+"_"+areaid;
        // 改变，放到调用方法内判断
//        String cacheKey = CACHE_KEY_START_STOCK_LOCATION_CHECK_PREFIX + stockLocation.getCode() + "_" + areaId;
//        if (!redisTemplate.hasKey(cacheKey))
//            return R.error("库位号：" + stockLocation.getCode() + " 不要在盘点期间，请先点击开始盘点！");
        if (Objects.equals(null, items) || items.isEmpty())
            return R.error("没有盘点数据！");
        for (StockLocationCheckItem stockLocationCheckItem : items) {
            if (stockLocationCheckItem.getPanCount() != null && stockLocationCheckItem.getPanCount() >= 0) {
                this.stockLocationKcMapper.stockLocationCheck(
                        stockLocationCheckItem.getPpid(),
                        stockLocationCheckItem.getPanCount(),
                        locationCode, userName, areaId, stockLocation.getId()
                );
            }
        }
        return R.success(null);
    }

    /**
     * 库位库存列表
     *
     * @return
     * @see .net:oa999DAL.stockLocationServices.cs#getStockLocationKcList
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<Page<StockLocationKcVO>> getStockLocationKcList(StockLocationHttpReq req) {
        if (Objects.equals(req.getActionName(), "startCheckStockLocation")) {
            if (Objects.equals(null, req.getKey()))
                return R.error("库位号不能为空！");
            else if (Objects.equals(req.getKinds(), "code"))
                return R.error("查询条件必须为库位号！");
            R r = startStockLocationCheck(req.getKey(), req.getAreaId());
            if (r.getCode() != 0)
                return R.error(r.getUserMsg());
            else
                return R.success("库位：" + req.getKey() + " 开始盘点成功！");
        } else if (Objects.equals(req.getActionName(), "dealCheckStockLocation")) {
            if (Objects.equals(null, req.getKey()))
                return R.error("库位号不能为空！");
            else if (Objects.equals(req.getKinds(), "code"))
                return R.error("查询条件必须为库位号！");
            R r = createStockLocationRecord(req.getKey(), req.getAreaId(), req.getUserName(), req.getStockCheckWay());
            if (r.getCode() != 0)
                return R.error(r.getUserMsg());
            else
                return R.success("库位：" + req.getKey() + " 生成盘点记录成功！");
        }
        int total = this.baseMapper.countStockLocation(req);
        Page<StockLocationKcVO> page = PageUtil.getPages(req, total);
        if (total > 0) {
            List<StockLocationKcVO> records = this.baseMapper.listStockLocation(req);
            page.setRecords(records);
        }
        return R.success(page);
    }

    /***
     * @see .net:oa999DAL.stockLocationServices.cs#moveStockLocation
     * @param fromLocationCode 被转移库位
     * @param toLocationCode 转移至库位
     * @param areaId 门店编码
     * @param ppid ppid
     * @param counts counts
     * @param userName userName
     * @param cmd cmd
     * @return R
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R moveStockLocation(String fromLocationCode, String toLocationCode, int areaId, int ppid, int counts, String userName, Object cmd) {


        StockLocation fromLocationObj = getStockLocationByCode(fromLocationCode, areaId);
        if (Objects.equals(null, fromLocationObj))
            return R.error("无效库位号：" + fromLocationCode);

        StockLocation toLocationObj = getStockLocationByCode(toLocationCode, areaId);
        if (Objects.equals(null, toLocationObj))
            return R.error("无效库位号：" + toLocationCode);

        String comment = "库位移库，从" + fromLocationObj.getCode() + "->" + toLocationObj.getCode();
        StockLocationOutInReq fromReq = StockLocationOutInReq.builder()
                .locationCode(fromLocationCode).areaId(areaId).ppid(ppid)
                .counts(-counts)
                .inUser(userName).comment(comment)
                .basketId(null)
                .shouHouId(null)
                .locationId(fromLocationObj.getId())
//                .cmd(cmd)
                .build();
        R from = this.stockLocationOutIn(fromReq);
        if (from.getCode() != 0)
            return R.error(from.getUserMsg());

        StockLocationOutInReq toReq = StockLocationOutInReq.builder()
                .locationCode(toLocationCode).areaId(areaId).ppid(ppid)
                .counts(counts)
                .inUser(userName).comment(comment)
                .basketId(null)
                .shouHouId(null)
                .locationId(toLocationObj.getId())
//                .cmd(cmd)
                .build();
        R to = this.stockLocationOutIn(toReq);
        if (to.getCode() != 0)
            return R.error(to.getUserMsg());

        else return R.success("移库成功");
    }

    /**
     * 库位出入库管理
     *
     * @param req StockLocationOutInReq
     * @return R
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R stockLocationOutIn(StockLocationOutInReq req) {
        StockLocation stockLocation = getStockLocationByCode(req.getLocationCode(), req.getAreaId());
        if (Objects.equals(null, stockLocation))
            return R.error("无效库位号：" + req.getLocationCode());
        req.setLocationId(stockLocation.getId());
        List<CurrentLocationVO> curs = this.baseMapper.listCurLocation(req);
        //当前库位库存
        int curStockCount = 0;
        int stockLocationKcId = 0;
        if (CollectionUtils.isNotEmpty(curs)) {
            curStockCount = curs.get(0).getLCount();
            stockLocationKcId = curs.get(0).getId();
        }
        // C# 代码是<= 0，根据实际情况等于0也说明库存是够的，应当允许移库
        if (curStockCount + req.getCounts() < 0)
            return R.error("库位库存不足，请核对！");
        Integer lastCount = 0;
        if (stockLocationKcId > 0)
            this.baseMapper.updateStockLCounts(req.getCounts(), stockLocationKcId);
        else
            lastCount = this.baseMapper.insertStockLCounts(req);
        req.setLastCount(lastCount);
        if (this.baseMapper.insertLogs(req) <= 0)
            return R.error("库位日志记录失败！");
        return R.success(null);
    }

    /**
     * 拿货队列
     *
     * @param area     area
     * @param isMobile 0  1  拿 -1 预订配件未拿 -2  预订手机未拿
     * @return List<PickUpProductVO>
     */
    @Override
    public List<PickUpProductVO> naHuoList(String area, Integer isMobile) {
        if (Objects.equals(null, isMobile))
            isMobile = 1;
        OaUserBO cur = this.abstractCurrentRequestComponent.getCurrentStaffId();
        List<PickUpProductVO> rtn = this.baseMapper.naHuoList(cur.getAreaId(), isMobile);
        rtn.forEach(PickUpProductVO::dealData);
        return rtn;
    }

    @Override
    public Boolean isAreaHasStockLocktion(Integer areaId) {
        return listAllStockLocation().stream().filter((location) -> location.getAreaid().intValue() == areaId).findFirst().orElse(null) != null;
    }
}
