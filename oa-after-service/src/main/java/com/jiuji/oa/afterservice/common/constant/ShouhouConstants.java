package com.jiuji.oa.afterservice.common.constant;


import com.jiuji.oa.afterservice.common.util.DateUtil;
import lombok.Getter;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * @Description:
 * @author: <PERSON>
 * @date: 2020/5/15 9:22
 */

@Getter
@Component
public class ShouhouConstants {
    /**
     * 售后服务对应ppid
     */
    private final List<Integer> shouhouServicesPpriceids = Arrays.asList(81682, 81683,110939);

    private final List<Integer> shouhouServiceTypeList = Arrays.asList(9, 10,11);

    private final List<Integer> redPacketPpriceid = Arrays.asList(42527, 42665);

    private final List<Integer> ServicesPpriceids = Arrays.asList(19882, 19885, 19881, 47359, 47365, 50009, 55428, 60884, 61029, 66133, 67776, 68145, 68144, 78371, 78370, 78364, 78940, 85422);

    private final List<Integer> OtherServicesPpriceids = Arrays.asList(19886, 20603, 36398, 55139, 20528, 20599, 20602, 60379, 55138);

    /**口罩商品*/
    private final List<Integer> maskPPriceids = Arrays.asList(87788, 88051, 87998, 87997, 87443, 87458, 88147, 88185, 87874, 88255, 88255, 88289, 88361, 89317, 89304, 88361, 89305, 89170, 91973, 90971, 92030);

    /**安装服务ppid 软件维修类*/
    private final List<Integer> installServicesPpriceid = Arrays.asList(55429, 55430, 55431, 52769, 52770, 57197, 57201, 57198, 57202, 57204, 57205, 57203, 57206, 57207, 57199, 57200, 71287);

    /**
     * 测试通过
     */
    private final Integer ShouhouProcessStatsCSTG = 3;

    /**
     * 无需测试
     */
    private final Integer ShouhouProcessStatsWUCS = 4;


    /**
     * 维修单可用优惠码分类
     */
    private final LocalDateTime weixiuCodeUpdateTime = DateUtil.stringToLocalDateTime("2020-02-17 16:20:00");

    private final List<Integer> weixiuCodeLimitIds = Arrays.asList(24, 25, 26, 27, 28, 29, 30, 31, 40, 53, 54, 68, 69, 70, 159, 311, 393, 410, 463, 476, 481, 482, 522,603,708,728);

    private final List<Integer> otherPPid = Arrays.asList(16386, 16388, 16387, 10729, 6984, 6999, 18424, 16644, 16642, 16629, 16649, 16654, 16646, 16653, 16648, 16633, 16657, 16651, 10754, 16655, 18009, 16921, 16690, 16660, 16659, 16658, 16652, 16650, 16647, 12018, 11683, 11682, 11681, 11680, 11679, 11677, 16920, 18877, 19259, 19260, 19261, 19262, 19263, 19287, 19288, 19289, 19290, 19291, 19292, 19293, 19294, 19295, 19296, 19297, 19298, 19299, 19300, 19301, 19248, 19888, 19887, 19882, 19885, 19881, 26690, 26642, 26689, 32342, 31642, 26487, 49726, 49732, 49733, 49734, 49834, 51806, 51807, 51755, 53127, 53125, 59830, 71287, 70862, 70318, 70317, 51807, 51806, 49834, 49734, 49733, 49732, 49726, 72515, 72526, 73338, 75077, 74136, 72515, 72526, 74137, 74138, 74141, 74095, 74139, 74140, 74724, 77236, 77235, 77234, 53127, 81458, 89564, 89565);
    private final List<Integer> otherCid = Arrays.asList(7, 33, 50, 34, 35, 52, 58, 62, 164, 221, 297, 165, 370, 457, 454, 481, 463, 407, 506, 522, 543, 544, 545, 546, 295,728);
    /**
     * 需要弹框的配件分类
     * 除此以外的分类配件不用弹框选择全部按照现在的撤销流程操作即可
     */
    public static final List<Integer> CANCEL_REFUND_PRICE_CIDS = Collections.unmodifiableList(Arrays.asList(393,68,70,476,27,54,482,28,30,29,159,603));

    public static final String AFTER_EXCHANGE_SPECIAL_DEAL_PERMISSION = "tscl";
    public static final  String ZHENG_CHANG_TUI_HUAN = "正常退换";
    public static final  String TE_SHU_TUI_HUAN = "特殊退换";
    public static final  String HAS_FAULT = "有故障";
    public static final  String NO_FAULT = "无故障";

//    public List<Integer> getOtherPPid() {
//        List<Integer> otherPpidList = new LinkedList<>();
//        otherPpidList.addAll(otherPPid);
//        otherPpidList.addAll(ServicesPpriceids);
//        otherPpidList.addAll(shouhouServicesPpriceids);
//        otherPpidList.addAll(OtherServicesPpriceids);
//        return otherPpidList;
//    }

}
