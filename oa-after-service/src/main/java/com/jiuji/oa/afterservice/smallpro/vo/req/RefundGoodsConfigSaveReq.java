package com.jiuji.oa.afterservice.smallpro.vo.req;

import com.jiuji.oa.afterservice.smallpro.enums.SmallProServiceTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/5/23 11:50
 * @Description 小件换货配置查询参数
 */
@Data
@Accessors(chain = true)
@ApiModel("小件换货配置保存")
public class RefundGoodsConfigSaveReq implements Serializable {

    @ApiModelProperty(value = "id")
    private Integer id;
    /**
     * 配置名称 标题
     */
    @ApiModelProperty(value = "配置名称/标题")
    @Length(max = 50,message = "标题长度不能超过50个字")
    @NotNull(message = "标题不能为空")
    private String title;
    /**
     * 服务类型
     *
     * @see SmallProServiceTypeEnum
     */
    @ApiModelProperty(value = "服务类型")
    private Integer serviceType;

    /**
     * 是否补差价
     */
    @ApiModelProperty(value = "是否补差价")
    private Boolean isDifferentPrice;
    /**
     * 使用商品服务金额补差价
     */
    @ApiModelProperty(value = "使用商品服务金额补差价")
    private Boolean isServiceDifferentPrice;
    /**
     * 补差价方式(1 按差价金额补 2 按照换货商品金额补)
     */
    @ApiModelProperty(value = "补差价方式(1 按差价金额补 2 按照换货商品金额补)")
    private Integer differentPriceType;
    /**
     * 补差价百分比
     */
    @ApiModelProperty(value = "补差价百分比")
    private BigDecimal differentPricePercent;
    /**
     * 可换货商品配置方式(1 品牌 2分类 3 品牌+分类 4 SPU 5 SKU)
     */
    @ApiModelProperty(value = "可换货商品配置方式(1 品牌 2分类 3 品牌+分类 4 SPU 5 SKU)")
    private Integer productConfigType;

    /**
     * 品牌
     */
    @ApiModelProperty(value = "品牌ID")
    private List<Integer> brandIdList;
    /**
     * 分类ID
     */
    @ApiModelProperty(value = "分类ID")
    private List<Integer> cidList;

    /**
     * sku以逗号分隔
     */
    @ApiModelProperty(value = "sku以逗号分隔")
    private String sku;

    /**
     * spu以逗号分隔
     */
    @ApiModelProperty(value = "spu以逗号分隔")
    private String spu;

    /**
     * 是否开启
     */
    @ApiModelProperty(value = "是否开启")
    private Boolean isEnabled;

    /**
     * 换货政策
     */
    @ApiModelProperty(value = "换货政策")
    private String policyContent;
}
