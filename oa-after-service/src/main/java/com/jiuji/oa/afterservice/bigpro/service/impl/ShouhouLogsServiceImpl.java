package com.jiuji.oa.afterservice.bigpro.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.afterservice.apollo.ApolloEntity;
import com.jiuji.oa.afterservice.bigpro.bo.*;
import com.jiuji.oa.afterservice.bigpro.dao.ShouhouLogsMapper;
import com.jiuji.oa.afterservice.bigpro.dao.ShouhouOtherMapper;
import com.jiuji.oa.afterservice.bigpro.enums.OaMesTypeEnum;
import com.jiuji.cloud.after.enums.ShouHouLogTypeEnum;
import com.jiuji.oa.afterservice.bigpro.enums.ShouhouTimePointTypeEnum;
import com.jiuji.oa.afterservice.bigpro.po.Shouhou;
import com.jiuji.oa.afterservice.bigpro.po.ShouhouLogs;
import com.jiuji.oa.afterservice.bigpro.po.ShouhouTimer;
import com.jiuji.oa.afterservice.bigpro.po.ShouhoutestInfo;
import com.jiuji.oa.afterservice.bigpro.service.*;
import com.jiuji.oa.afterservice.bigpro.vo.req.ShouhouTestLogAddReq;
import com.jiuji.oa.afterservice.common.bo.OaUserBO;
import com.jiuji.oa.afterservice.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.afterservice.common.constant.ShouhouConstants;
import com.jiuji.oa.afterservice.common.enums.ESmsChannelTypeEnum;
import com.jiuji.oa.afterservice.common.enums.MTableInfoEnum;
import com.jiuji.oa.afterservice.common.enums.XtenantEnum;
import com.jiuji.oa.afterservice.common.exception.RRException;
import com.jiuji.oa.afterservice.common.source.MoaUrlSource;
import com.jiuji.oa.afterservice.common.util.CommenUtil;
import com.jiuji.oa.afterservice.common.util.DateUtil;
import com.jiuji.oa.afterservice.sys.service.SysConfigService;
import com.jiuji.oa.nc.SmsConfigCloud;
import com.jiuji.oa.nc.sms.SmsConfigCodeEnum;
import com.jiuji.oa.nc.sms.SmsConfigVO;
import com.jiuji.oa.nc.sms.SmsPushMethodEnum;
import com.jiuji.oa.orginfo.areainfo.client.AreaInfoClient;
import com.jiuji.oa.orginfo.areainfo.vo.AreaInfo;
import com.jiuji.oa.orginfo.userinfo.client.UserInfoClient;
import com.jiuji.oa.orginfo.userinfo.vo.Ch999UserVo;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.common.vo.ResultCode;
import com.jiuji.tc.utils.common.CommonUtils;
import com.jiuji.tc.utils.constants.SysConfigConstant;
import com.jiuji.tc.utils.xtenant.Namespaces;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-25
 */
@Service
public class ShouhouLogsServiceImpl extends ServiceImpl<ShouhouLogsMapper, ShouhouLogs> implements ShouhouLogsService {

    @Lazy
    @Autowired
    private ShouhouService shouhouService;
    @Autowired
    private ShouhouMsgService shouhouMsgService;
    @Autowired
    private ShouhouTimePointService shouhouTimePointService;
    @Autowired
    private ShouhouOtherService shouhouOtherService;
    @Autowired
    private ShouhouTimerService shouhouTimerService;
    @Resource
    private ShouhouOtherMapper shouhouOtherMapper;
    @Resource
    private AreaInfoClient areaInfoClient;
    @Resource
    private UserInfoClient userInfoClient;
    @Resource
    private ShouhouLogsMapper shouhouLogsMapper;
    @Autowired
    private SmsService smsService;
    @Autowired
    private ShouhoutestInfoService shouhoutestInfoService;
    @Resource
    private MoaUrlSource moaUrlSource;
    @Resource
    private AbstractCurrentRequestComponent abstractCurrentRequestComponent;
    @Autowired
    private WeixinUserService weixinUserService;
    @Autowired
    private ShouhouConstants shouhouConstants;
    @Autowired
    private SysConfigService sysConfigService;
    @Resource
    private SmsConfigCloud smsConfigCloud;
    @Resource
    private ApolloEntity apolloEntity;


    @Override
    public void addShouhouLog(String inuser, Integer wxid, Integer type, String content, ShouhouLogNoticeBo notice,
                              Boolean issendZy, Integer messagetplid) {
        addShouhouLog(inuser, wxid, type, content, notice, issendZy, messagetplid, false);
    }

    @Override
    public void addShouhouLog(String inuser, Integer wxid, Integer type, String content, ShouhouLogNoticeBo notice,
                              Boolean issendZy, Integer messagetplid, Boolean isWeb) {

        if (StringUtils.isBlank(content)) {
            return;
        }
        if (issendZy == null) {
            issendZy = false;
        }
        if (messagetplid == null) {
            messagetplid = 0;
        }
        String postMsg = content;

        //Shouhou shouhouEntity = shouhouService.getById(wxid);
        Shouhou shouhouEntity = CommenUtil.autoQueryHist(() ->shouhouService.getById(wxid), MTableInfoEnum.SHOUHOU,wxid);
        if (shouhouEntity == null) {
            throw new RRException("请求的维修单不存在");
        }
        Integer issoft = Boolean.TRUE.equals(shouhouEntity.getIssoft()) ? 1 : 0;
        if (type.equals(ShouHouLogTypeEnum.WXJD.getCode())) {
            if (messagetplid == 0) {
                String comment = "* " + content;
                boolean isweb = notice.getNeedNotice() ? Boolean.TRUE.equals(isWeb) : true;
                if (isweb) {
                    shouhouService.saveShouhouLog(wxid, comment, inuser, 0, isweb,messagetplid);
                }
            }
            shouhouMsgService.sendCollectMsg(wxid, content, inuser);
        } else if (type.equals(ShouHouLogTypeEnum.CLXX.getCode())) {
            if (!notice.getNeedNotice()) {

                if (CommenUtil.isNullOrZero(messagetplid)) {
//                    boolean isweb = notice.getNeedNotice() ? false : true;
                    Boolean isweb = Boolean.TRUE.equals(isWeb);
                    //处理信息网站不可见
                    shouhouService.saveShouhouLog(wxid, content, inuser, 0, isweb,messagetplid);
                }

                shouhouMsgService.sendCollectMsg(wxid, content, inuser);
            }
        } else if (type.equals(ShouHouLogTypeEnum.WXFA.getCode())) {
            if (CommenUtil.isNullOrZero(messagetplid)) {
                addGcsLog(content, wxid, issoft, inuser,messagetplid);
            }
        } else {
            throw new RRException("请求的备注类型错误");
        }
        CommenUtil.autoWriteHist(()->shouhouService.lambdaUpdate().set(Shouhou::getResultDtime,LocalDateTime.now())
                .eq(Shouhou::getId,shouhouEntity.getId()).update());

        this.sendShouhouLogNotice(inuser, wxid, content, notice, messagetplid, isWeb);
        //记录售后处理进度
        //Shouhou shouhou1 = shouhouService.getById(wxid);
        Shouhou shouhou1 = CommenUtil.autoQueryHist(() ->shouhouService.getById(wxid), MTableInfoEnum.SHOUHOU,wxid);
        if (shouhou1 != null) {
            shouhouOtherService.recordProgress(wxid, shouhou1.getWeixiuren(), inuser);
        }

        if (type.equals(ShouHouLogTypeEnum.WXFA.getCode())) {
            content = "增加维修方案为：" + content;
            shouhouMsgService.sendCollectMsg(wxid, content, inuser);
            if (shouhouEntity.getModidate() != null) {
                ShouhouTimer shouhouTimer = new ShouhouTimer();
                shouhouTimer.setShouhouid(wxid);
                shouhouTimer.setChuli((LocalDateTime.now().getMinute() - (shouhouEntity.getModidate().getMinute())));
                shouhouTimerService.addShouhouTimer(shouhouTimer);
            }

        }
        if (messagetplid.equals(0) && (notice.getNeedNotice() == null || !notice.getNeedNotice()) && (type.equals(ShouHouLogTypeEnum.WXJD.getCode()) || type.equals(ShouHouLogTypeEnum.WXFA.getCode()))) {
            Integer nowAreaId = shouhouOtherMapper.getNowAreaId(wxid);

            R<AreaInfo> areaInfoRes = areaInfoClient.getAreaInfoById(nowAreaId);
            if (areaInfoRes.getCode() == ResultCode.SUCCESS && areaInfoRes.getData() != null) {
                AreaInfo areaInfo = areaInfoRes.getData();
                boolean isZhongYou = (!StringUtils.isNotBlank(areaInfo.getPrintName()) && areaInfo.getPrintName().contains("中邮"));

                shouhouMsgService.sendWeixinMsg(wxid, content, isZhongYou, issendZy);
            }

        }
        //如果发的是模板消息
        if (messagetplid > 0) {
            Integer wxUserId = shouhouOtherMapper.getWxUserId(wxid);
            Integer nowAreaId = shouhouOtherMapper.getNowAreaId(wxid);

            //售后消息推送
            ShouhouMsgPushMessageBo shouHouMsgBo = new ShouhouMsgPushMessageBo();
            shouHouMsgBo.setMsgId(messagetplid);
            shouHouMsgBo.setPostmsg(postMsg);
            shouHouMsgBo.setShouhouId(wxid);
            shouHouMsgBo.setAreaId(nowAreaId);
            shouHouMsgBo.setUserId(wxUserId);
            shouHouMsgBo.setIsSnedZhongyou(issendZy);
            shouHouMsgBo.setOptUser(inuser);
            shouHouMsgBo.setTemplateId(messagetplid);
            //维修方案指定type类型
            if(ShouHouLogTypeEnum.WXFA.getCode().equals(type)){
                shouHouMsgBo.setLogType(issoft == 1 ? 2 : 1);
            }
            shouhouService.pushMessage(shouHouMsgBo, true);
        }

    }

    @Override
    public void sendShouhouLogNotice(String inuser, Integer wxid, String content, ShouhouLogNoticeBo notice,
                                     Integer messagetplid, Boolean isWeb) {
        if (notice == null || (notice.getNeedNotice() == null || !notice.getNeedNotice())) {
            return;
        }
//        if (StringUtils.isBlank(notice.getReciver())) {
//            throw new RRException("对接人不能为空");
//        }
        List<String> reciverList = Optional.ofNullable(notice.getReciverList()).orElse(new ArrayList<>());
        if(CollectionUtils.isEmpty(reciverList)){
            throw new RRException("对接人不能为空");
        }
        String reciver = notice.getReciver();
        if(StringUtils.isNotEmpty(reciver) && !reciverList.contains(reciver)){
            reciverList.add(reciver);
        }
        String recivers = reciverList.stream().collect(Collectors.joining(","));
        if (messagetplid == null) {
            messagetplid = 0;
        }

        ShouhouBo sh = shouhouService.getOne(wxid);

 //       R<Ch999UserVo> userVoR = userInfoClient.getCh999UserByUserName(notice.getReciver());
        R<List<Ch999UserVo>> ch999UserByUserNames = userInfoClient.getCh999UserByUserNames(notice.getReciverList());

//        if (userVoR.getCode() != ResultCode.SUCCESS || userVoR.getData() == null) {
//            throw new RRException(MessageFormat.format("系统找不到对应接收人{0}，推送消息失败", notice.getReciver()));
//        }


        if (ch999UserByUserNames.getCode() != ResultCode.SUCCESS || ch999UserByUserNames.getData() == null) {
            throw new RRException(MessageFormat.format("系统找不到对应接收人{0}，推送消息失败", recivers));
        }
        List<Ch999UserVo> ch999UserVoList = ch999UserByUserNames.getData();
        //获取机型
        JiXingBo jiXing = CommenUtil.autoQueryHist(()->shouhouLogsMapper.getJiXing(wxid), MTableInfoEnum.SHOUHOU, wxid);
        if (jiXing == null) {
            throw new RRException("系统找不到对应机型信息");
        }

        String message = MessageFormat.format("机型：{0} 维修单号：{1} imei: {2} {3} ({4})", jiXing.getName(), wxid.toString() , jiXing.getImei(), content, inuser);

        String noticeLog = "已对接通知：" + recivers;
        String rMsg = "test";

        String host = moaUrlSource.getBasicUrl();
        String host1 = sysConfigService.getValueByCode(SysConfigConstant.MOA_URL);
        if (StringUtils.isNotEmpty(host1)){
            host = host1;
        }

        String url = host + "/mshouhou/edit?id=" + wxid;
        R<AreaInfo> areaInfoR = areaInfoClient.getAreaInfoById(sh.getNowarea());
        // Long xtenant = 0L;
//        if (areaInfoR.getCode() == ResultCode.SUCCESS && areaInfoR.getData() != null){
//            xtenant = areaInfoR.getData().getXtenant() == null ? 0l : Long.valueOf(areaInfoR.getData().getXtenant());
//            url = sysConfigService.getValueByCode(SysConfigConstant.M_URL);
//            url = url + "/after-service/detail/" +wxid;
//        }

        if (notice.getToWeixin() != null && notice.getToWeixin().equals(1)) {

            String link = url;
            message = MessageFormat.format("机型：{0} 维修单号：{1} imei: {2} ({3})", jiXing.getName(), wxid.toString() , jiXing.getImei(), inuser);
            String oaMessage = MessageFormat.format("机型：{0} 维修单号：{1} imei: {2} {3} ({4})", jiXing.getName(), "<a href=' " + host + "/mshouhou/edit?id=" + wxid.toString() + "'>" + wxid.toString() + "</a>", jiXing.getImei(), content, inuser);
            long xtenant = Namespaces.get();
            String openXtenantStr = apolloEntity.getSmsConfigOpenXtenant();
            if (XtenantEnum.isSaasXtenant()
                    && StringUtils.isNotEmpty(openXtenantStr)
                    && CommonUtils.covertIdStr(openXtenantStr).contains((int) xtenant)) {
                R<SmsConfigVO> smsConfigResult = smsConfigCloud.getConfigByCode(xtenant,
                        SmsConfigCodeEnum.CODE_81.getCode());
                if (com.jiuji.tc.utils.common.CommonUtils.isRequestSuccess(smsConfigResult)) {
                    SmsConfigVO smsConfig = smsConfigResult.getData();
                    // 消息内容
                    List<SmsConfigVO.SmsField> fields = smsConfig.getFields();
                    String wxMessage = smsConfig.getTemplate();
                    if (StringUtils.isNotEmpty(wxMessage)
                            && CollectionUtils.isNotEmpty(fields)) {
                        for (SmsConfigVO.SmsField field : fields) {
                            if ("<model>".equals(field.getValue())) {
                                wxMessage = wxMessage.replace(field.getValue(), jiXing.getName());
                            }
                            if ("<subId>".equals(field.getValue())) {
                                wxMessage = wxMessage.replace(field.getValue(), String.valueOf(wxid));
                            }
                            if ("<imei>".equals(field.getValue())) {
                                wxMessage = wxMessage.replace(field.getValue(), jiXing.getImei());
                            }
                            if ("<processContent>".equals(field.getValue())) {
                                wxMessage = wxMessage.replace(field.getValue(), content);
                            }
                            if ("<inUser>".equals(field.getValue())) {
                                wxMessage = wxMessage.replace(field.getValue(), inuser);
                            }
                        }
                    }
                    // 推送方式
                    List<Integer> pushMethods = smsConfig.getPushMethod();
                    // sms消息
                    boolean sendWxMessage = CollectionUtils.isNotEmpty(pushMethods)
                            && pushMethods.contains(SmsPushMethodEnum.WECHAT_MSG.getCode());
                    if (sendWxMessage) {
                        message = wxMessage;
                    }
                }
            }
            String ch999Ids = ch999UserVoList.stream().map(Ch999UserVo::getCh999Id).filter(ObjectUtil::isNotNull).map(String::valueOf).collect(Collectors.joining(","));
            weixinUserService.senWeixinAndOaMsg(message,oaMessage,link,ch999Ids,OaMesTypeEnum.SHTZ.getCode().toString());
            noticeLog = "微信(app) " + noticeLog;
        }
        if (notice.getToSms() != null && notice.getToSms().equals(1)) {

            message = MessageFormat.format("机型：{0} 维修单号：{1} imei: {2} {3} ({4})", jiXing.getName(), wxid.toString(), jiXing.getImei(), content, inuser);
            long xtenant = Namespaces.get();
            String openXtenantStr = apolloEntity.getSmsConfigOpenXtenant();
            if (XtenantEnum.isSaasXtenant()
                    && StringUtils.isNotEmpty(openXtenantStr)
                    && CommonUtils.covertIdStr(openXtenantStr).contains((int) xtenant)) {
                R<SmsConfigVO> smsConfigResult = smsConfigCloud.getConfigByCode(xtenant,
                        SmsConfigCodeEnum.CODE_81.getCode());
                if (com.jiuji.tc.utils.common.CommonUtils.isRequestSuccess(smsConfigResult)) {
                    SmsConfigVO smsConfig = smsConfigResult.getData();
                    // 消息内容
                    List<SmsConfigVO.SmsField> fields = smsConfig.getFields();
                    String smsMessage = smsConfig.getTemplate();
                    if (StringUtils.isNotEmpty(smsMessage)
                            && CollectionUtils.isNotEmpty(fields)) {
                        for (SmsConfigVO.SmsField field : fields) {
                            if ("<model>".equals(field.getValue())) {
                                smsMessage = smsMessage.replace(field.getValue(), jiXing.getName());
                            }
                            if ("<subId>".equals(field.getValue())) {
                                smsMessage = smsMessage.replace(field.getValue(), String.valueOf(wxid));
                            }
                            if ("<imei>".equals(field.getValue())) {
                                smsMessage = smsMessage.replace(field.getValue(), jiXing.getImei());
                            }
                            if ("<processContent>".equals(field.getValue())) {
                                smsMessage = smsMessage.replace(field.getValue(), content);
                            }
                            if ("<inUser>".equals(field.getValue())) {
                                smsMessage = smsMessage.replace(field.getValue(), inuser);
                            }
                        }
                    }
                    // 推送方式
                    List<Integer> pushMethods = smsConfig.getPushMethod();
                    // sms消息
                    boolean sendSmsMessage = CollectionUtils.isNotEmpty(pushMethods)
                            && pushMethods.contains(SmsPushMethodEnum.SMS.getCode());
                    if (sendSmsMessage) {
                        message = smsMessage;
                    }
                }
            }
            host = sysConfigService.getValueByCode(SysConfigConstant.M_URL);
            for (Ch999UserVo userVo : ch999UserVoList){
                R<Boolean> smsSendRes = smsService.sendSms(userVo.getMobile(), message , DateUtil.localDateTimeToString(LocalDateTime.now()),"系统", smsService.getSmsChannelByTenant(sh.getNowarea(), ESmsChannelTypeEnum.YZMTD));
                rMsg += smsSendRes.getUserMsg();
                noticeLog = "短信 " + noticeLog;
            }

        }
        if (notice.getToEmail() != null && notice.getToEmail().equals(1)) {
            String mailTitle = message.length() > 35 ? message.substring(0, 35) : message;
            String mailContent = MessageFormat.format("机型：{0} 维修单号：{1} imei: {2} {3}({4})",
                    jiXing.getName(), wxid.toString(), jiXing.getImei(), content, inuser);

            //发送内部邮件
            for (String userName : reciverList){
                rMsg += smsService.sendEmail(userName, mailTitle, mailContent);
            }
            noticeLog = "内部邮件 " + noticeLog;
        }

        noticeLog = content + " (" + noticeLog + ")";

        if (messagetplid == 0) {

            shouhouService.saveShouhouLog(wxid, noticeLog, inuser, null, Boolean.TRUE.equals(isWeb),messagetplid);
        }
        if (notice.getToEmail() != null && notice.getToEmail().equals(1)) {
            noticeLog = noticeLog.replace("/shouhou/", host + "/mshouhou");
            shouhouMsgService.sendCollectMsg(wxid, noticeLog, inuser);
        }

    }

    @Override
    public List<ShouhouLogBo> getListByShouHouIdAndOrder(Integer shouhouId, Integer order) {

        List<ShouhouLogBo> res = shouhouService.getShouhouLogs(shouhouId);
//        res.stream().filter(e->shouhouId==951219?DateUtil.stringToLocalDateTime(e.getDTime()).isAfter(DateUtil.stringToLocalDateTime("2016-04-25 15:14")):true).collect(Collectors.toList());
//
//        if (order != 1){
//            res= res.stream().sorted(Comparator.comparing(e->e.getDTime())).collect(Collectors.toList());
//            Collections.reverse(res);
//        }else {
//            res= res.stream().sorted(Comparator.comparing(e->e.getDTime())).collect(Collectors.toList());
//        }
        return res;
    }

    @Override
    public List<ShouhouLogBo> getListWithInuserDepartment(Integer shouhouId, Integer order) {
        List<ShouhouLogBo> logList = this.getListByShouHouIdAndOrder(shouhouId, order);

        if (CollectionUtils.isEmpty(logList)) {
            return Collections.emptyList();
        }
//        List<ShouhouLogBo> res = logList.stream().map(e -> {
//            R<Ch999UserVo> userVoR = userInfoClient.getCh999UserByUserName(e.getInUser());
//            if (userVoR.getCode() == ResultCode.SUCCESS && userVoR.getData() != null) {
//                e.setDeptCode(userVoR.getData().getDepartCode());
//            }
//            return e;
//        }).collect(Collectors.toList());

        return logList;
    }

    @Override
    public R<Boolean> addShouhouTestLog(ShouhouTestLogAddReq req) {

        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();
        if (req.getWucs() == null) {
            req.setWucs(0);
        }
        ShouhoutestInfo item = req.getItem();
        if (StringUtils.isEmpty(item.getInuser())){
            item.setInuser(oaUserBO.getUserName());
            req.setItem(item);
        }
        return this.shouhutestWrite(req) ? R.success("操作成功") : R.error("操作失败");
    }

    /**
     * 添加工程师日志
     *  @param content
     * @param wxid
     * @param issoft
     * @param inuser
     * @param messagetplid
     */
    private Boolean addGcsLog(String content, Integer wxid, Integer issoft, String inuser, Integer messagetplid) {
        //日志记录
        Integer type = issoft == 1 ? 2 : 1;
        shouhouService.saveShouhouLog(wxid, content, inuser, type, true,messagetplid);

        //记录处理方案给出时间
        return shouhouTimePointService.saveShouhouTimePoint(wxid, ShouhouTimePointTypeEnum.GCCLFA.getCode(), LocalDateTime.now());
    }

    /**
     * 记录售后维修单测试结果
     */
    private Boolean shouhutestWrite(ShouhouTestLogAddReq req) {

        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();

        ShouhoutestInfo testInfo = new ShouhoutestInfo();
        testInfo.setDtime(LocalDateTime.now());
        testInfo.setInuser(StringUtils.isEmpty(req.getItem().getInuser()) ? oaUserBO.getUserName() : req.getItem().getInuser());
        testInfo.setShouhouId(req.getShouhouId());
        testInfo.setTestResult(req.getItem().getTestResult());
        testInfo.setWaiguan(req.getItem().getWaiguan());
        testInfo.setTestResultInfo(req.getItem().getTestResultInfo());
        testInfo.setTesttype(1);
        testInfo.setTestParms(req.getItem().getTestResult() ? JSONObject.toJSONString(req.getParms()) : "");

        shouhoutestInfoService.save(testInfo);

        //Shouhou shouhouInfo = shouhouService.getById(req.getShouhouId());
        Shouhou shouhouInfo = CommenUtil.autoQueryHist(() ->shouhouService.getById(req.getShouhouId()), MTableInfoEnum.SHOUHOU,req.getShouhouId());
        Integer nowArea = shouhouOtherMapper.getNowAreaId(req.getShouhouId());
        Boolean iszhongyou = false;
        String name = shouhouInfo != null ? shouhouInfo.getName() : "";
        if (StringUtils.isNotEmpty(name)) {
            if (req.getItem().getTestResult()) {
                String wxMsg = "您的设备经测试员" + req.getItem().getInuser() + "检测，送检故障已经解决。";
                shouhouService.saveShouhouLog(req.getShouhouId(), "* " + wxMsg, req.getItem().getInuser(), null, true);
                if (nowArea != null) {
                    R<AreaInfo> areaInfoR = areaInfoClient.getAreaInfoById(nowArea);
                    if (areaInfoR.getCode() == ResultCode.SUCCESS && areaInfoR.getData() != null) {
                        AreaInfo areaInfo = areaInfoR.getData();
                        iszhongyou = StringUtils.isNotEmpty(areaInfo.getPrintName()) && areaInfo.getPrintName().contains("中邮");
                        shouhouMsgService.sendWeixinMsg(req.getShouhouId(), wxMsg, iszhongyou, false);
                    }
                }
            }
        }

        if (req.getItem().getTestResult()) {
            if (!shouhouConstants.getShouhouProcessStatsWUCS().equals(req.getWucs())) {
                assert shouhouInfo != null;
                String statsStr = shouhouInfo.getProcessConfirmStats();
                List<Integer> stats = new LinkedList<>();
                if (StringUtils.isNotEmpty(statsStr)) {
                    stats.addAll(Arrays.asList(statsStr.split(",")).stream().map(e -> Integer.valueOf(e)).collect(Collectors.toList()));
                }
                stats.add(shouhouConstants.getShouhouProcessStatsCSTG());
                shouhouService.update(new LambdaUpdateWrapper<Shouhou>().set(Shouhou::getProcessConfirmStats, StringUtils.join(stats, ",")).eq(Shouhou::getId, req.getShouhouId()));

                shouhouMsgService.sendCollectMsg(req.getShouhouId(), "测试通过", req.getItem().getInuser());
            } else {
                shouhouService.update(new LambdaUpdateWrapper<Shouhou>().set(Shouhou::getProcessConfirmStats, shouhouConstants.getShouhouProcessStatsWUCS()).eq(Shouhou::getId, req.getShouhouId()));
                shouhouService.saveShouhouLog(req.getShouhouId(), "免测试操作，原因：" + req.getItem().getTestResultInfo(), req.getItem().getInuser(), null, null);
            }

            if (shouhouInfo != null && shouhouInfo.getModidate() != null) {
                Long timeout = Duration.between(LocalDateTime.now(), shouhouInfo.getModidate()).toMinutes();

                if (timeout >= 0) {
                    ShouhouTimer shouhouTimer = new ShouhouTimer();
                    shouhouTimer.setShouhouid(req.getShouhouId());
                    shouhouTimer.setCeshi(timeout.intValue());
                    shouhouTimerService.addShouhouTimer(shouhouTimer);
                }
            } else {
                shouhouService.update(new LambdaUpdateWrapper<Shouhou>().set(Shouhou::getProcessConfirmStats, null).eq(Shouhou::getId, req.getShouhouId()).eq(Shouhou::getStats, 1));

                String wxMsg = "您的设备测试故障未完全排除，加急处理中。";
                shouhouService.saveShouhouLog(req.getShouhouId(), wxMsg, req.getItem().getInuser(), null, false);
                shouhouMsgService.sendCollectMsg(req.getShouhouId(), "测试不通过", req.getItem().getInuser());
                if (nowArea != null) {
                    R<AreaInfo> areaInfoR = areaInfoClient.getAreaInfoById(nowArea);
                    if (areaInfoR.getCode() == ResultCode.SUCCESS && areaInfoR.getData() != null) {
                        AreaInfo areaInfo = areaInfoR.getData();
                        iszhongyou = StringUtils.isNotEmpty(areaInfo.getPrintName()) && areaInfo.getPrintName().contains("中邮");
                        shouhouMsgService.sendWeixinMsg(req.getShouhouId(), wxMsg, iszhongyou, false);
                    }
                }
            }
        }

        return true;
    }
}
