package com.jiuji.oa.afterservice.config.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.afterservice.common.bo.OaUserBO;
import com.jiuji.oa.afterservice.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.afterservice.common.util.CommenUtil;
import com.jiuji.oa.afterservice.config.dao.ShouhouPpidConfigMapper;
import com.jiuji.oa.afterservice.config.po.ShouhouPpidBind;
import com.jiuji.oa.afterservice.config.po.ShouhouPpidConfig;
import com.jiuji.oa.afterservice.config.service.ShouhouPpidBindService;
import com.jiuji.oa.afterservice.config.service.ShouhouPpidConfigService;
import com.jiuji.oa.afterservice.config.vo.req.ShouhouPpidConfigQueryReq;
import com.jiuji.oa.afterservice.config.vo.req.ShouhouPpidOutPutAddReq;
import com.jiuji.oa.afterservice.config.vo.res.ShouhouPpidBindVo;
import com.jiuji.oa.afterservice.config.vo.res.ShouhouPpidOutPutConfig;
import com.jiuji.oa.afterservice.config.vo.res.XtenantInfo;
import com.jiuji.tc.common.vo.R;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-16
 */
@Service
public class ShouhouPpidConfigServiceImpl extends ServiceImpl<ShouhouPpidConfigMapper, ShouhouPpidConfig> implements ShouhouPpidConfigService {

    @Autowired
    private ShouhouPpidBindService shouhouPpidBindService;
    @Resource
    private AbstractCurrentRequestComponent abstractCurrentRequestComponent;

    @Override
    public R<Page<ShouhouPpidOutPutConfig>> getShouhouPpidConfigPage(ShouhouPpidConfigQueryReq req) {
        Integer way = 0;
        if (req.getSearchKey() != null && CommenUtil.isNumer(req.getSearchKey())) {
            way = 1;
        }
        if (CommenUtil.isNullOrZero(req.getCurrent()) || CommenUtil.isNullOrZero(req.getSize())) {
            req.setCurrent(1);
            req.setSize(15);
        }
        Page<ShouhouPpidOutPutConfig> page = new Page<>(req.getCurrent(), req.getSize());
        page.setDesc("create_time");
        page = baseMapper.getShouhouPpidConfigList(page, req, way);
        List<ShouhouPpidOutPutConfig> records = page.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return R.success(page);
        }
        List<Integer> configIdList = records.stream().map(ShouhouPpidOutPutConfig::getId).collect(Collectors.toList());
        List<ShouhouPpidBind> bindList = shouhouPpidBindService.list(new LambdaQueryWrapper<ShouhouPpidBind>().in(ShouhouPpidBind::getConfigId, configIdList));
        List<XtenantInfo> xtenantInfo = this.getXtenantInfo();
        records = records.stream().map(item -> {
            List<ShouhouPpidBind> bindItem = bindList.stream().filter(bind -> bind.getConfigId().equals(item.getId())).collect(Collectors.toList());
            item.setBindList(bindItem);
            item.setXtenantName(Optional.of(xtenantInfo.stream()
                            .filter(x -> Objects.equals(item.getXtenant(), x.getXtenant()))
                            .findFirst()
                            .orElseGet(XtenantInfo::new))
                    .get().getPrintName());
            return item;
        }).collect(Collectors.toList());
        page.setRecords(records);
        return R.success(page);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<Boolean> saveOrUpdateBindInfo(ShouhouPpidOutPutAddReq req) {

        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();
        ShouhouPpidConfig config = new ShouhouPpidConfig();
        BeanUtils.copyProperties(req, config);
        //校验配件是否重复配置
        Integer count = baseMapper.selectCount(new LambdaQueryWrapper<ShouhouPpidConfig>()
                .eq(ShouhouPpidConfig::getPpid, config.getPpid())
                .eq(ShouhouPpidConfig::getStatus,1)
                .ne(config.getId() != null, ShouhouPpidConfig::getId, config.getId()));
        if(count>0){
            return R.error(String.format("ppid[%s]已配置,请勿重复配置!", config.getPpid()));
        }
        if(CommenUtil.isNullOrZero(config.getId())){
            req.setId(null);
        }else{
            List<Integer> updateIds = Optional.ofNullable(req.getBindList()).map(b -> b.stream().map(ShouhouPpidBind::getId)
                    .filter(ObjectUtils::isNotNull).collect(Collectors.toList()))
                    .orElseGet(Collections::emptyList);
            //移除已经删除的配件
            shouhouPpidBindService.remove(new LambdaQueryWrapper<ShouhouPpidBind>().eq(ShouhouPpidBind::getConfigId
                    , config.getId()).notIn(!updateIds.isEmpty(),ShouhouPpidBind::getId,updateIds));
        }
        configDefaultValue(req, oaUserBO, config);

        super.saveOrUpdate(config);
        Integer configId = config.getId();

        List<ShouhouPpidBind> bindList = req.getBindList();
        if (CollectionUtils.isNotEmpty(bindList)) {
            bindList = bindList.stream().map(bind -> {
                bindDefaultValue(oaUserBO, bind);
                bind.setConfigId(configId);
                return bind;
            }).collect(Collectors.toList());

            bindList.stream().forEach(e -> shouhouPpidBindService.saveOrUpdate(e));
        }
        return R.success("保存或更新成功");
    }

    private static void bindDefaultValue(OaUserBO oaUserBO, ShouhouPpidBind bind) {
        if (CommenUtil.isNullOrZero(bind.getId())) {
            bind.setCreateTime(LocalDateTime.now());
            bind.setCreateUser(oaUserBO.getUserName());
            if (bind.getNegative() == null) {
                bind.setNegative(Boolean.FALSE);
            }
            if (bind.getAutoOutPut() == null) {
                bind.setAutoOutPut(Boolean.FALSE);
            }
        }else{
            bind.setUpdateTime(LocalDateTime.now());
        }
    }

    private static void configDefaultValue(ShouhouPpidOutPutAddReq req, OaUserBO oaUserBO, ShouhouPpidConfig config) {
        if (CommenUtil.isNullOrZero(req.getId())) {
            config.setCreateTime(LocalDateTime.now());
            config.setCreateUser(oaUserBO.getUserName());
            if (config.getNegative() == null) {
                config.setNegative(Boolean.FALSE);
            }
        } else {
            config.setUpdateTime(LocalDateTime.now());
        }
    }

    @Override
    public R<List<ShouhouPpidBindVo>> getBindPpidInfo(Integer ppid, Integer xtenant, Integer areaId) {
        List<ShouhouPpidBindVo> bindList = shouhouPpidBindService.listBindPpidInfo(ppid, false,xtenant,areaId);

        return R.success(bindList);
    }

    @Override
    public List<XtenantInfo> getXtenantInfo() {
        return baseMapper.getXtenantInfo();
    }
}
