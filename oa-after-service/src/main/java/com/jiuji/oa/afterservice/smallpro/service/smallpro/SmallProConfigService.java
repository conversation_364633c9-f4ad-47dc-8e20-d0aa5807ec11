package com.jiuji.oa.afterservice.smallpro.service.smallpro;

import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jiuji.cloud.after.vo.req.SmallExchangeReq;
import com.jiuji.oa.afterservice.bigpro.po.SmallConfigProductInfoPo;
import com.jiuji.oa.afterservice.common.constant.DataSourceConstants;
import com.jiuji.oa.afterservice.shouhou.vo.req.SelectConfigToWebReq;
import com.jiuji.oa.afterservice.shouhou.vo.res.SelectConfigToWebRes;
import com.jiuji.oa.afterservice.smallpro.enums.ExchangeGoodsEnums;
import com.jiuji.oa.afterservice.smallpro.enums.RefundGoodsEnums;
import com.jiuji.oa.afterservice.smallpro.po.exchange.ProductExchangePo;
import com.jiuji.oa.afterservice.smallpro.po.exchange.SmallExchangeConfigPo;
import com.jiuji.oa.afterservice.smallpro.po.exchange.SmallExchangeProductConfigPo;
import com.jiuji.oa.afterservice.smallpro.po.refund.SmallRefundConfigPo;
import com.jiuji.oa.afterservice.smallpro.po.refund.SmallRefundProductConfigPo;
import com.jiuji.oa.afterservice.smallpro.vo.req.*;
import com.jiuji.oa.afterservice.smallpro.vo.res.ExchangeGoodsConfigRes;
import com.jiuji.oa.afterservice.smallpro.vo.res.ExchangeGoodsConfigResV2;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.foundation.db.annotation.DS;
import lombok.NonNull;

import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * 小件配置服务类
 * <AUTHOR>
 * @since 2022/4/28 10:15
 */
public interface SmallProConfigService {
    int ADVANCE_RANK_CONFIG_ID = -1;


    /**
     * 配置查询
     * @param req
     * @return
     */
    SelectConfigToWebRes selectConfigToWeb(SelectConfigToWebReq req);

    /**
     * 获取换货配置信息
     * 如果确实匹配了多条配置 获取最新的一条
     * @param configId
     * @return
     */
    @DS(DataSourceConstants.CH999_OA_NEW)
    SmallExchangeConfigPo getConfigById(Integer configId);
    /**
     * 获取换货配置信息
     * 如果确实匹配了多条配置 获取最新的一条
     *
     * @param serviceType         服务类型，详情可见：{@lonk SmallProServiceTypeEnum}
     * @param configTypeAndValues 集合中的条件是且的关系
     * @param wordKey
     * @param key
     * @return
     */
    @Cached(name = "afterservice:SmallProConfigService:getConfig", expire = 2, timeUnit = TimeUnit.MINUTES,cacheType = CacheType.BOTH,cacheNullValue = true)
    @DS(DataSourceConstants.CH999_OA_NEW)
    SmallExchangeConfigPo getConfig(SmallExchangeConfigPo.ProductConfigTypeEnum configType, Integer serviceType,
                                    List<SmallExchangeConfigPo.ConfigTypeAndValue> configTypeAndValues, Long wordKey, String key);

    @NonNull Set<Integer> listPpidByBarCode(Long wordKey, String key);

    /**
     * 获取换货配置信息，提供给web项目使用，区别在于根据指定的ppid查询
     * 如果确实匹配了多条配置 获取最新的一条
     * @param serviceType 服务类型，详情可见：{@lonk SmallProServiceTypeEnum}
     * @param configTypeAndValues 集合中的条件是且的关系
     * @param webKeyword 来自web的搜索关键词
     * @return
     */
    @Cached(name = "afterservice:SmallProConfigService:getConfigForWeb", expire = 2, timeUnit = TimeUnit.MINUTES,cacheType = CacheType.BOTH,cacheNullValue = true)
    @DS(DataSourceConstants.CH999_OA_NEW)
    SmallExchangeConfigPo getConfigForWeb(SmallExchangeConfigPo.ProductConfigTypeEnum configType, Integer serviceType,
                                          List<SmallExchangeConfigPo.ConfigTypeAndValue> configTypeAndValues, String webKeyword);

    @Cached(name = "afterservice:SmallProConfigService:listConfigProduct", expire = 1, timeUnit = TimeUnit.MINUTES,cacheType = CacheType.BOTH,cacheNullValue = true)
    @DS(DataSourceConstants.CH999_OA_NEW)
    List<SmallExchangeProductConfigPo> listConfigProduct(Integer configId);
    @Cached(name = "afterservice:ProductinfoService:listSmallProductInfo", expire = 1, timeUnit = TimeUnit.MINUTES,cacheType = CacheType.LOCAL,localLimit = 200,cacheNullValue = true)
    @DS(DataSourceConstants.CH999_OA_NEW)
    List<ProductExchangePo> listSmallProductInfo(SmallConfigProductInfoPo smallConfigProductInfo, Long wordKey, String key);

    @Cached(name = "afterservice:ProductinfoService:listSmallProductInfoForWeb", expire = 1, timeUnit = TimeUnit.MINUTES,cacheType = CacheType.LOCAL,localLimit = 200,cacheNullValue = true)
    @DS(DataSourceConstants.CH999_OA_NEW)
    List<ProductExchangePo> listSmallProductInfo(SmallConfigProductInfoPo smallConfigProductInfo, SmallExchangeReq req);

    @DS(DataSourceConstants.CH999_OA_NEW)
    int countSmallProductInfo(SmallConfigProductInfoPo smallConfigProductInfo, SmallExchangeReq req);

    /**
     * 查询小件换货配置列表
     * @param req req
     * @return 小件换货配置列表
     */
    R<Page<ExchangeGoodsConfigRes>> getExchangeGoodsConfigInfo(ExchangeGoodsConfigReq req);

    /**
     * 获取小件枚举值
     * @return 小件枚举值列表
     */
    R<List<ExchangeGoodsEnums>> getExchangeGoodsEnums();

    /**
     * 更新
     * @param req req
     * @return
     */
    R<Boolean> updateExchangeGoodsConfig(ExchangeGoodsConfigSaveReq req);

    /**
     * 新增
     * @param req req
     * @return
     */
    R<Boolean> addExchangeGoodsConfig(ExchangeGoodsConfigSaveReq req);

    /**
     * 删除接口
     * @param id
     * @return
     */
    R<Boolean> delExchangeGoodsConfig(Integer id);



    /**
     * 根据当前退货的商品 ppid获取退货配置信息
     * 如果确实匹配了多条配置 获取最新的一条
     * @return
     */
    @Cached(name = "afterservice:SmallProConfigService:getRefundConfig", expire = 2, timeUnit = TimeUnit.MINUTES,cacheType = CacheType.BOTH,cacheNullValue = true)
    @DS(DataSourceConstants.CH999_OA_NEW)
    SmallRefundConfigPo getRefundConfig();
    @Cached(name = "afterservice:SmallProConfigService:listRefundConfigProduct", expire = 1, timeUnit = TimeUnit.MINUTES,cacheType = CacheType.BOTH,cacheNullValue = true)
    @DS(DataSourceConstants.CH999_OA_NEW)
    List<SmallRefundProductConfigPo> listRefundConfigProduct(Integer configId);
    @Cached(name = "afterservice:ProductinfoService:listSmallRefundProductInfo", expire = 1, timeUnit = TimeUnit.MINUTES,cacheType = CacheType.LOCAL,localLimit = 200,cacheNullValue = true)
    @DS(DataSourceConstants.CH999_OA_NEW)
    List<ProductExchangePo> listSmallRefundProductInfo(SmallConfigProductInfoPo smallConfigProductInfo, Integer wordKey);

    R<Page<ExchangeGoodsConfigRes>> getRefundGoodsConfigInfo(RefundGoodsConfigReq req);

    R<List<RefundGoodsEnums>> getRefundGoodsEnums();

    R<Boolean> updateRefundGoodsConfig(RefundGoodsConfigSaveReq req);

    R<Boolean> addRefundGoodsConfig(RefundGoodsConfigSaveReq req);

    R<Boolean> delRefundGoodsConfig(Integer id);

    /**
     * 构建高级权限的配置
     * @param type
     * @return
     */
    SmallExchangeConfigPo buildAdvancedExchangeConfig(Integer type);

    /**
     * 更新小件换货配置v2
     * @param req
     * @return
     */
    R<Boolean> updateExchangeGoodsConfigV2(ExchangeGoodsConfigSaveReqV2 req);

    /**
     * 新增小件换货配置v2
     * @param req
     * @return
     */
    R<Boolean> addExchangeGoodsConfigV2(ExchangeGoodsConfigSaveReqV2 req);

    /**
     * 根据id查询小件换货配置v2
     * @param id
     * @return
     */
    ExchangeGoodsConfigResV2 getExchangeGoodsConfigById(Integer id);
}
