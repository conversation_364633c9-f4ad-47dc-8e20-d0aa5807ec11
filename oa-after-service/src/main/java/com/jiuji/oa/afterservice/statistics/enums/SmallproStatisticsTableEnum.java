package com.jiuji.oa.afterservice.statistics.enums;

import com.jiuji.oa.afterservice.statistics.bo.smallpro.FiledBO;
import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * description: <小件接件统计字段枚举类>
 * translation: <Enumeration class for small piece statistics>
 *
 * <AUTHOR>
 * @date 2020/4/29
 * @since 1.0.0
 */
@Getter
@AllArgsConstructor
public enum SmallproStatisticsTableEnum implements CodeMessageEnumInterface {

    /**
     * 字段名-字段注释
     */
    SMALLPRO_STATISTICS_FIELD_18("areaId", "地区Id","地区Id",0,0,0),
    SMALLPRO_STATISTICS_FIELD_20("area", "地区","地区",1,0,0),
    SMALLPRO_STATISTICS_FIELD_21("productName", "商品名称","商品名称",0,2,0),
    SMALLPRO_STATISTICS_FIELD_22("userName", "客户名","客户名",0,0,3),
    SMALLPRO_STATISTICS_FIELD_23("userLink", "用户跳转链接","用户跳转链接",0,0,0),
    SMALLPRO_STATISTICS_FIELD_1("receiveCount", "排除删除的订单、九机服务的小件接件商品数量","接件量",1,2,3),
    SMALLPRO_STATISTICS_FIELD_2("saleCount", "排除九机服务的小件交易数量","销售量",1,2,3),
    SMALLPRO_STATISTICS_FIELD_15("saleAmount", "排除九机服务的小件交易数量","销售额",0,0,0),
    SMALLPRO_STATISTICS_FIELD_3("saleProfit", "排除九机服务的小件交易销售利润","销售利润",1,2,3),
    SMALLPRO_STATISTICS_FIELD_4("beforeCount", "现货量=售前量","售前量",1,2,3),
    SMALLPRO_STATISTICS_FIELD_5("beforeProportion", "售前量/销量","售前占比",0,0,0),
    SMALLPRO_STATISTICS_FIELD_6("afterProportion", "(接件量-售前量)/销量","售后占比",1,2,3),
    SMALLPRO_STATISTICS_FIELD_7("damagedCount", "排除九机服务的报损小件数量","报损量",1,2,3),
    SMALLPRO_STATISTICS_FIELD_8("damagedProportion", "报损量/销量","报损占比",1,1,3),
    SMALLPRO_STATISTICS_FIELD_9("damagedAmount", "排除九机服务的报损小件金额","报损金额",1,2,3),
    SMALLPRO_STATISTICS_FIELD_10("convertInventoryCount", "排除九机服务的转现小件数量","转现量",1,2,0),
    SMALLPRO_STATISTICS_FIELD_16("convertInventoryAmount", "转现金额","转现金额",1,2,3),
    SMALLPRO_STATISTICS_FIELD_11("convertInventoryProportion", "转现占比","转现占比",1,2,0),
    SMALLPRO_STATISTICS_FIELD_12("maintainCount", "维修接件量","维修量",1,2,3),
    SMALLPRO_STATISTICS_FIELD_13("maintainProportion", "维修占比","维修占比",1,2,3),
    SMALLPRO_STATISTICS_FIELD_14("processedCount", "待处理","待处理",1,2,3),
    SMALLPRO_STATISTICS_FIELD_17("maximumNumberOfPickups", "最多接件次数","最多接件次数",0,0,3);

    public static Map<String, FiledBO> getClassTable(int type){
        Map<String, FiledBO> map = new LinkedHashMap<>();
        SmallproStatisticsTableEnum[] values = SmallproStatisticsTableEnum.values();
        for (SmallproStatisticsTableEnum value : values) {
            if(value.getAreaClass().equals(type)){
                map.put(value.getCode(),new FiledBO(value.getContent(),value.getMessage()));
            }
            if(value.getProductClass().equals(type)){
                map.put(value.getCode(),new FiledBO(value.getContent(),value.getMessage()));
            }
            if(value.getCustomerClass().equals(type)){
                map.put(value.getCode(),new FiledBO(value.getContent(),value.getMessage()));
            }
        }
        return map;
    }

    /**
     * 字段名
     */
    private String code;
    /**
     * 字段注释
     */
    private String message;

    private String content;
    private Integer areaClass;
    private Integer productClass;
    private Integer customerClass;


    public static LinkedHashMap<String, String> getAllField() {
        SmallproStatisticsTableEnum[] enums = SmallproStatisticsTableEnum.values();
        LinkedHashMap<String, String> result = new LinkedHashMap<>(enums.length);
        for (SmallproStatisticsTableEnum temp : enums) {
            if (temp.getCode() != null) {
                result.put(temp.getCode(), temp.getMessage());
            }
        }
        return result;
    }
}
