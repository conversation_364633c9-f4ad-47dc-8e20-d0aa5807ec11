package com.jiuji.oa.afterservice.smallpro.bo;

import com.jiuji.oa.afterservice.cloud.vo.WarrantyMainDetailInfo;
import com.jiuji.oa.afterservice.smallpro.bo.filmCard.FilmCardInfomationBO;
import com.jiuji.oa.afterservice.smallpro.bo.smallproReceivable.ISmallproProduct;
import com.jiuji.oa.afterservice.smallpro.vo.res.WebTimeConfig;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;


/**
 * description: <小件接单-订单信息>
 * translation: <Smallpro - order information>
 *
 * <AUTHOR>
 * @date 2019/11/14
 * @since 1.0.0
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class SmallproOrderInfoBO implements Serializable, ISmallproProduct {

    private static final long serialVersionUID = 1782175405466875175L;

    /**
     * 小件商品id
     */
    private Integer smallproBillId;
    /**
     * 订单Id
     */
    @ApiModelProperty(value = "订单Id")
    private Integer subId;
    /**
     * 原订单跳转链接
     */
    @ApiModelProperty(value = "原订单跳转链接")
    private String orderLink;
    /**
     * 购买地区Id
     */
    @ApiModelProperty(value = "购买地区Id")
    private Integer subAreaId;
    /**
     * 购买地区名称
     */
    @ApiModelProperty(value = "购买地区名称")
    private String subAreaName;
    /**
     * SKU id
     */
    @ApiModelProperty(value = "SKU id")
    private Integer ppriceId;
    /**
     * 绑定SKU id
     */
    private Integer targetPpriceId;
    /**
     * 产品名称
     */
    @ApiModelProperty(value = "产品名称")
    private String productName;
    /**
     * 产品尺寸
     */
    @ApiModelProperty(value = "产品尺寸")
    private String productColor;
    /**
     * 产品数量
     */
    @ApiModelProperty(value = "产品数量")
    private Integer productCount;
    /**
     * 条形码
     */
    @ApiModelProperty(value = "条形码")
    private String barCode;

    /**
     * 条形码
     */
    @ApiModelProperty("条码")
    private List<String> barCodeList;

    /**
     * 订单实际价格
     */
    @ApiModelProperty(value = "订单实际价格")
    private BigDecimal price;
    /**
     * 商品价格
     */
    @ApiModelProperty(value = "商品价格")
    private BigDecimal memberPrice;
    /**
     * 是否为钢化膜
     */
    @ApiModelProperty(value = "是否为钢化膜[1是|0否]")
    private Integer isTemperedFilm;
    /**
     * 钢化膜30天无理由换新
     */
    @ApiModelProperty(value = "钢化膜30天无理由换新")
    private Boolean isFreeExchange;
    /**
     * 是否为保护壳
     */
    @ApiModelProperty(value = "是否为保护壳[1是|0否]")
    private Integer isProtectiveCase;

    /**
     * 是否为贴膜
     * true - 是
     */
    private Boolean isTieMo;
    /**
     * 是否为移动电源
     */
    @ApiModelProperty(value = "是否为移动电源")
    private Integer isMobilePower;
    /**
     * 移动电源是否可换 buydays<365
     */
    @ApiModelProperty(value = "移动电源是否可换 buydays<365")
    private Integer isMobilePowerHuan;
    /**
     * 是否为单选项
     */
    @ApiModelProperty(value = "是否为单选项")
    private Integer isSingleOption;
    /**
     * 条目Id
     */
    @ApiModelProperty(value = "条目Id")
    private Integer basketId;

    /**
     * 处理次数
     */
    private Integer handleCount;
    /**
     * 条目对应的年包服务信息
     */
    @ApiModelProperty(value = "条目对应的年包服务信息")
    private FilmCardInfomationBO filmCardInfo;
    /**
     * 类别Id
     */
    @ApiModelProperty(value = "类别Id")
    private Integer cid;
    /**
     * 商品Id
     */
    @ApiModelProperty(value = "商品Id")
    private Integer productId;
    /**
     * 是否可换[1是|0否]
     */
    @ApiModelProperty(value = "是否可换[1是|0否]")
    private Integer isChange;
    /**
     * 是否可修[1是|0否]
     */
    @ApiModelProperty(value = "是否可修[1是|0否]")
    private Integer isWarranty;
    /**
     * 是否可退[1是|0否]
     */
    @ApiModelProperty("是否支持退[1是|0否]")
    private Integer isSupportReturn;

    /**
     * 是否绑定SN码
     */
    @ApiModelProperty(value = "是否绑定SN码")
    private Boolean isSn;
    /**
     * 商品对应可用九机服务信息
     */
    @ApiModelProperty(value = "商品对应可用九机服务信息")
    private SubServiceRecordBO serviceInfo;
    /**
     * 是否为服务类商品
     */
    @ApiModelProperty(value = "是否为服务类商品")
    private Boolean isServiceProduct;

    /**
     * 是否为年包服务商品
     */
    @ApiModelProperty(value = "是否为年包服务商品")
    private Boolean isYearCardProduct;

    /**
     * 退款金额
     */
    @ApiModelProperty(value = "退款金额")
    private BigDecimal priceReturn;

    @ApiModelProperty(value = "网站退换修日期配置")
    private WebTimeConfig webTimeConfig;

    /**
     * 运营商业务是否可退？ 是否返销？
     */
    @ApiModelProperty(value = "是否返销？ true是未返销")
    private Boolean isReturnOperatorBasket;

    /**
     * 是否是小件优品
     */
    @ApiModelProperty(value = "是否是小件优品")
    private Boolean isYouPin;

    /**
     * 售后政策
     */
    @ApiModelProperty(value = "售后政策")
    private WarrantyMainDetailInfo shPolicy;


    /**
     * 是否大件换货
     */
    @ApiModelProperty(value = "是否大件换货")
    private Boolean mobileExchangeFlag;

    /**
     * 大件换货信息
     */
    private SmallproMobileInfoBO smallproMobileInfo;

    /**
     * 分摊扣减金额
     */
    private BigDecimal allocatedDeductionAmount;

    /**
     * 不显示选择
     */
    private Boolean isNotSelect;

    /**
     * isNotSelect 为true的时候展示的文案
     */
    private String showMsg;

}
