package com.jiuji.oa.afterservice.smallpro.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.jiuji.oa.afterservice.bigpro.enums.SubSubTypeEnum;
import com.jiuji.oa.afterservice.bigpro.po.InstallServicesRecord;
import com.jiuji.oa.afterservice.bigpro.po.WeixinUser;
import com.jiuji.oa.afterservice.bigpro.po.refund.ShouhouTuiHuanPo;
import com.jiuji.oa.afterservice.bigpro.service.InstallServicesRecordService;
import com.jiuji.oa.afterservice.bigpro.service.ShouhouRefundService;
import com.jiuji.oa.afterservice.bigpro.service.WeixinUserService;
import com.jiuji.oa.afterservice.common.bo.OaUserBO;
import com.jiuji.oa.afterservice.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.afterservice.common.constant.PayConstants;
import com.jiuji.oa.afterservice.common.enums.MTableInfoEnum;
import com.jiuji.oa.afterservice.common.enums.RankEnum;
import com.jiuji.oa.afterservice.common.enums.TuihuanKindEnum;
import com.jiuji.oa.afterservice.common.enums.XtenantEnum;
import com.jiuji.oa.afterservice.common.exception.CustomizeException;
import com.jiuji.oa.afterservice.common.util.CommenUtil;
import com.jiuji.oa.afterservice.common.util.HttpClientUtil;
import com.jiuji.oa.afterservice.common.util.OaVerifyUtil;
import com.jiuji.oa.afterservice.other.bo.AlipayAuthBO;
import com.jiuji.oa.afterservice.other.bo.PingzhengResultBO;
import com.jiuji.oa.afterservice.other.bo.SaveMoneyInfoBO;
import com.jiuji.oa.afterservice.other.bo.SaveMoneyInfoReqBO;
import com.jiuji.oa.afterservice.other.dao.AlipayToAccountLogMapper;
import com.jiuji.oa.afterservice.other.dao.PayWxenterpriseMapper;
import com.jiuji.oa.afterservice.other.dao.ShouhouTuihuanMapper;
import com.jiuji.oa.afterservice.other.enums.SaveMoneyEkindEnum;
import com.jiuji.oa.afterservice.other.po.*;
import com.jiuji.oa.afterservice.other.service.*;
import com.jiuji.oa.afterservice.refund.vo.req.GroupTuihuanFormVo;
import com.jiuji.oa.afterservice.smallpro.bo.SmallproBasketTinyInfoBO;
import com.jiuji.oa.afterservice.smallpro.bo.SmallproOrderInfoBO;
import com.jiuji.oa.afterservice.smallpro.bo.filmCard.FilmCardInfomationBO;
import com.jiuji.oa.afterservice.smallpro.bo.smallproExchange.*;
import com.jiuji.oa.afterservice.smallpro.bo.smallproInfo.LastExchangeSmallproBo;
import com.jiuji.oa.afterservice.smallpro.bo.smallproInfo.SmallproInfoFileBO;
import com.jiuji.oa.afterservice.smallpro.bo.smallproSendMsgCode.DIYTimoCardBO;
import com.jiuji.oa.afterservice.smallpro.constant.SmallProRelativePathConstant;
import com.jiuji.oa.afterservice.smallpro.constant.SmallproRefundConstant;
import com.jiuji.oa.afterservice.smallpro.dao.SmallproMapper;
import com.jiuji.oa.afterservice.smallpro.enums.*;
import com.jiuji.oa.afterservice.smallpro.po.Smallpro;
import com.jiuji.oa.afterservice.smallpro.po.SmallproBill;
import com.jiuji.oa.afterservice.smallpro.service.*;
import com.jiuji.oa.afterservice.smallpro.vo.req.SmallproAddLogReq;
import com.jiuji.oa.afterservice.smallpro.vo.req.SmallproRefundSubmitReq;
import com.jiuji.oa.afterservice.smallpro.vo.req.oaApiReq.JiujiCoinItemReq;
import com.jiuji.oa.afterservice.smallpro.vo.req.oaApiReq.OaApiReq;
import com.jiuji.oa.afterservice.smallpro.vo.res.SmallproNormalCodeMessageRes;
import com.jiuji.oa.afterservice.sub.po.Sub;
import com.jiuji.oa.afterservice.sub.service.SubService;
import com.jiuji.oa.afterservice.sys.service.SysConfigService;
import com.jiuji.oa.loginfo.order.service.SubLogsCloud;
import com.jiuji.oa.loginfo.order.vo.req.SubLogsNewReq;
import com.jiuji.oa.orginfo.sysconfig.client.SysConfigClient;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.common.vo.ResultCode;
import com.jiuji.tc.utils.constants.NumberConstant;
import com.jiuji.tc.utils.constants.SysConfigConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.redisson.client.RedisException;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static java.util.stream.Collectors.toList;

/**
 * description: <小件退款扩展服务实现类>
 * translation: <Smallpro refund extension service implementation class>
 *
 * <AUTHOR>
 * @date 2020/4/10
 * @since 1.0.0
 */
@Service
@Slf4j
public class SmallproRefundExServiceImpl implements SmallproRefundExService {

    // region autowired

    public static final int JIUJI_COIN = 276;
    private static final Pattern PATH_MATCH_NAME = Pattern.compile("^(([\u4e00-\u9fa5]{2,8})|([a-zA-Z]{2,16}))$");

    @Autowired
    private WeixinUserService weixinUserService;
    @Autowired
    private ShouhouTuihuanService shouhouTuihuanService;
    @Autowired
    private ReturnsDetailService returnsDetailService;
    @Autowired
    private NetPayRefundInfoService netPayRefundInfoService;
    @Autowired
    private SubService subService;
    @Autowired
    private AreainfoService areainfoService;
    @Autowired
    private SmallproService smallproService;
    @Autowired
    private InstallServicesRecordService installServicesRecordService;
    @Autowired
    private BasketService basketService;
    @Autowired
    private VoucherService voucherService;
    @Autowired
    private SmallproDetailsExServiceImpl smallproDetailsExService;
    @Autowired
    private SmallproMapper smallproMapper;
    @Autowired
    private AlipayToAccountLogMapper alipayToAccountLogMapper;
    @Autowired
    private PayWxenterpriseMapper payWxenterpriseMapper;
    @Autowired
    private ShouhouTuihuanMapper shouhouTuihuanMapper;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Autowired
    private SubLogsCloud subLogsCloud;
    @Autowired
    private SmallproBillService smallproBillService;

    @Autowired
    private SmallproFilmCardService smallproFilmCardService;
    @Autowired
    private ShouyinOtherService shouyinOtherService;

    @Autowired
    private RedissonClient redisson;

    @Resource
    private SysConfigClient sysConfigClient;
    @Autowired
    private SmallproRefundConstant smallproRefundConstant;
    @Autowired
    private SysConfigService sysConfigService;
    @Autowired
    private SmallProAdapterService smallProAdapterService;
    @Autowired
    @Lazy
    private SmallproLogService smallproLogService;
    @Resource
    @Lazy
    private SmallproExchangePurchaseService smallproExchangePurchaseService;


    // endregion

    // region 逻辑方法

    // region 获取退款微信验证二维码 getReturnWxCode

    @Override
    public String getReturnWxCode(Integer shouhouTuihuanId, Integer areaId, Integer userId) {
        return weixinUserService.getReturnWxCode(shouhouTuihuanId, areaId, userId);
    }

    // endregion

    // region 是否有支付宝原路返回 isReturnAlipay

    @Override
    public SmallproNormalCodeMessageRes isReturnAlipay(Integer returnFactoryId) {
        SmallproNormalCodeMessageRes result = new SmallproNormalCodeMessageRes();
        Integer id = smallproMapper.isReturnAlipay(returnFactoryId);
        if (id == null || id <= 0) {
            result.setCode(500);
            result.setMessage("没有支付宝原路返回路径！");
            return result;
        }
        result.setCode(0);
        result.setMessage("");
        return result;
    }

    // endregion

    // region 提交退款 refundSubmit

    @Override
    public SmallproNormalCodeMessageRes refundSubmit(OaUserBO oaUserBO, Integer smallproId,
                                                     List<SmallproBasketTinyInfoBO> details,
                                                     SmallproRefundSubmitReq req) {
        // 临时变量
        Integer area = 0;
        Double inPirce = 0.0;
        Integer userId = 0;

        SmallproNormalCodeMessageRes result = new SmallproNormalCodeMessageRes();
        Smallpro smallpro = smallproMapper.getByIdSqlServer(smallproId);
        if (smallpro == null || SmallProStatsEnum.SMALL_PRO_STATS_DELETED.getCode().equals(smallpro.getStats())) {
            result.setCode(500);
            result.setMessage("小件单不存在或已删除！");
            return result;
        }
        if (SmallProStatsEnum.SMALL_PRO_STATS_PROCESS_COMPLETED.getCode().equals(smallpro.getStats())) {
            result.setCode(500);
            result.setMessage("小件单已取机！");
            return result;
        }
        if (!smallpro.getAreaId().equals(oaUserBO.getAreaId())) {
            result.setCode(500);
            result.setMessage("接件地和当前登陆地区不符，请切换到接件地重试");
            return result;
        }
        Integer areaId = 0;
        if (!smallpro.getIsToArea()) {
            areaId = smallpro.getAreaId();

        } else {
            areaId = smallpro.getToAreaId();
        }
        if (areaId == null || areaId == 0) {
            result.setCode(500);
            result.setMessage("接件单门店ID错误！");
            return result;
        }
        SmallproRefundAreaInfoBO areaInfoBO = smallproMapper.getRefundAreaInfo(areaId);
        if (areaInfoBO == null) {
            result.setCode(500);
            result.setMessage("接件单门店ID不存在记录！");
            return result;
        }
        // 验证退款验证码
        if (smallpro.getCodeMsg() == null || smallpro.getCodeMsg().equals("")) {
            result.setCode(500);
            result.setMessage("请先保存验证码！当前售后接件单未授权验证！");
            return result;
        }
        if (req.getTuihuanKind() == 7 && (details == null || details.size() <= 0)) {
            result.setCode(500);
            result.setMessage("请选择退货项！");
            return result;
        }

        if (StringUtils.isEmpty(req.getTuiWay())) {
            result.setCode(500);
            result.setMessage("退款方式为空！");
            return result;
        }
        // 加盟店不退余额
        if (areaInfoBO.getAreaKind1() != null && areaInfoBO.getAreaKind1() != 1 && req.getTuiWay().equals("余额") && XtenantEnum.isSaasXtenant()) {
            result.setCode(500);
            result.setMessage("加盟店不支持余额退款！");
            return result;
        }

        // 自提点余额校验
        if (req.getTuihuanKind() == 7 && req.getTuiWay().equals("自提点余额")) {
            int flag = smallproMapper.checkZitidianType(smallpro.getSubId());
            if (flag <= 0) {
                result.setCode(500);
                result.setMessage("自提点账户查找失败!");
                return result;
            }
        }
        // 获取小件订单关联ppriceId和basketId
        QueryWrapper<SmallproBill> smallproBillQueryWrapper = new QueryWrapper<>();
        smallproBillQueryWrapper.lambda().eq(SmallproBill::getSmallproID, smallproId);
        List<SmallproBill> smallproBillList =
                smallproBillService.getListWithSqlServer(smallproBillQueryWrapper);
        // 获取额外数据，包括订单和用户
        List<SmallproOrderInfoBO> smallproOrderInfoBOList =
                smallproDetailsExService.getSmallproOrderListInfo(smallproBillList, smallpro.getSubId(), null);
        //获取年包信息
        // 如果没有返回null
        Optional.ofNullable(smallproOrderInfoBOList).filter(spList -> spList.size() == 1)
                .map(List::stream).flatMap(Stream::findFirst)
                .ifPresent(spob -> spob.setFilmCardInfo(smallproFilmCardService.getFilmCardInfoByBasketIdByWrite(spob.getBasketId())));
        // 校验金额是否超出
        R<String> tuiMoneyCheckR = checkTuiKuanMoney(smallproId, smallpro, req.getTuikuanM(), smallproOrderInfoBOList);
        if (tuiMoneyCheckR.getCode() != ResultCode.SUCCESS) {
            result.setCode(500);
            result.setMessage(tuiMoneyCheckR.getUserMsg());
            return result;
        }
        // 校验是否DIY年包（用过就不能退）
        if (CollectionUtils.isNotEmpty(smallproBillList)) {
//            6565 +
//                    List < DIYTimoCardBO > diyTimoCardBOS = smallproMapper.getDIYYearCard(smallproBillList.get(0).getBasketId());
//            if (CollectionUtils.isNotEmpty(diyTimoCardBOS)) {
//                boolean b =
//                        diyTimoCardBOS.stream().anyMatch(e -> (e.getIsdel() == null || e.getIsdel().equals(0)) && e.getBasketId() != null);
//                if (b) {
//                    result.setCode(500);
//                    result.setMessage("DIY年包已使用过，不能退款");
//                    return result;
//                }
//                List<DIYTimoCardBO> collect =
//                        diyTimoCardBOS.stream().filter(e -> e.getIsdel().equals(1)).collect(toList());
//                if (CollectionUtils.isNotEmpty(collect) && collect.size() == diyTimoCardBOS.size()) {
//                    result.setCode(500);
//                    result.setMessage("DIY年包已失效，不能退款");
//                    return result;
//                }
//            }
            checkTieMoRefundTimes(smallproBillList.stream().findFirst().map(SmallproBill::getBasketId).orElse(null));
            Integer ppid =
                    smallproMapper.getproductIdByPPid(smallproBillList.stream().map(SmallproBill::getPpriceid).collect(toList()));
            if (null != ppid) {
                List<SmallproInfoFileBO> smallproFileInfoList = smallproMapper.getSmallproFileInfo(smallproId);
                if (CollectionUtils.isEmpty(smallproFileInfoList)) {
                    result.setCode(500);
                    result.setMessage("DIY定制钢化玻璃保护壳退款需要上传附件");
                    return result;
                }
            }
        }
        //是否存在退款记录校验
        QueryWrapper<ShouhouTuihuan> shouhouTuihuanQueryWrapper = new QueryWrapper<>();
        shouhouTuihuanQueryWrapper.lambda()
                .and(wrapper -> wrapper.isNull(ShouhouTuihuan::getIsdel).or().eq(ShouhouTuihuan::getIsdel, 0))
                .eq(ShouhouTuihuan::getTuihuanKind, req.getTuihuanKind());
        if (req.getTuihuanKind() == 7) {
            shouhouTuihuanQueryWrapper.lambda().and(cnd -> cnd.eq(ShouhouTuihuan::getSubId, smallpro.getSubId())
                    .or().eq(ShouhouTuihuan::getSmallproid,smallproId));
        } else if (req.getTuihuanKind() == 10) {
            shouhouTuihuanQueryWrapper.lambda().eq(ShouhouTuihuan::getShouhouId, smallproId);
        }
        List<ShouhouTuihuan> shouhouTuihuanList = shouhouTuihuanService.listSqlServer(shouhouTuihuanQueryWrapper);
        if (shouhouTuihuanList != null && shouhouTuihuanList.size() > 0) {
            result.setCode(500);
            result.setMessage(StrUtil.format("已经存在退款记录！小件单号:{}",shouhouTuihuanList.stream()
                    .map(st->ObjectUtil.defaultIfNull(st.getSmallproid(),st.getShouhouId())).map(StrUtil::toString)
                    .collect(Collectors.joining(" "))));
            return result;
        }

        // 计算价格？
        if (req.getTuihuanKind() == 7) {
            List<Integer> basketIdList = details.stream().map(bo -> bo.getBasketId()).collect(Collectors.toList());
            int check1Flag = smallproMapper.refundServiceCheck1(basketIdList);
            if (check1Flag > 0 && !oaUserBO.getRank().contains("7a7")) {
                result.setCode(500);
                result.setMessage("您没有提交权限，权值7a7");
                return result;
            }
            List<SmallproRefundPriceInfoBO> priceInfoBOList = smallproMapper.getRefundPrice(smallpro.getSubId(),
                    basketIdList);
            //获取上一次换货的出库成本
            Map<Integer, LastExchangeSmallproBo> lastExchangeSmallproMap = SpringUtil.getBean(SmallproExchangePurchaseService.class)
                    .listLastChangeSmallpro(smallproId, smallpro.getSubId(),
                    basketIdList.stream().collect(Collectors.toSet())).stream().collect(Collectors
                    .toMap(LastExchangeSmallproBo::getBasketId, Function.identity(), (v1, v2) -> v1));
            if (priceInfoBOList != null && priceInfoBOList.size() > 0) {
                area = priceInfoBOList.get(0).getAreaId();
                userId = priceInfoBOList.get(0).getUserId();
                BigDecimal totalInPrice = BigDecimal.ZERO;
                for (SmallproRefundPriceInfoBO bo : priceInfoBOList) {
                    totalInPrice = totalInPrice.add(Optional.ofNullable(lastExchangeSmallproMap.get(bo.getBasketId()))
                            .map(LastExchangeSmallproBo::getInprice).orElseGet(() -> Convert.toBigDecimal(bo.getInPrice(),BigDecimal.ZERO)));
                }
                inPirce = totalInPrice.doubleValue();
            }
        }

        // 秒退验证
        if (req.getTuiWay().equals("微信秒退") || req.getTuiWay().equals("支付宝秒退")) {
            SmallproRefundAreaInfoBO areaInfoBOMiaoTuiCheck = smallproMapper.getRefundAreaInfo(area);
            if (areaInfoBOMiaoTuiCheck != null && areaInfoBOMiaoTuiCheck.getAreaKind1() != 1) {
                Double netPayout = smallproMapper.getNetworkMoneyPayout(smallpro.getSubId());
                if (netPayout == null || netPayout <= 0) {
                    result.setCode(500);
                    result.setMessage("该订单不能使用此退款方式，原因：非网上支付订单！");
                    return result;
                } else {
                    Double wechatRefundMoney = smallproMapper.getWechatMoneyPayout(smallpro.getSubId());
                    if (wechatRefundMoney == null) {
                        wechatRefundMoney = 0.0;
                    }
                    if (netPayout < (req.getTuikuanM().doubleValue() + wechatRefundMoney)) {
                        result.setCode(500);
                        result.setMessage("退款金额大于网上支付金额！");
                        return result;
                    }
                }
            }
            if(ObjectUtil.equal(req.getTuiWay(),ShouhouRefundService.WECHAT_REFUND_WAY) && StrUtil.isBlank(req.getBankfuming())){
                result.setCode(500);
                result.setMessage("微信限制，需录入客户真实姓名，以方便退款！");
                return result;
            }
            if(ObjectUtil.equal(req.getTuiWay(),ShouhouRefundService.WECHAT_REFUND_WAY) && StrUtil.isNotBlank(req.getBankfuming())){
                boolean isCorrectName = PATH_MATCH_NAME.matcher(req.getBankfuming()).matches();
                if (!isCorrectName) {
                    result.setCode(500);
                    result.setMessage("收款姓名录入有误，请检查");
                    return result;
                }
            }
            Optional.ofNullable(SpringUtil.getBean(SubService.class).getById(smallpro.getSubId()))
                    .ifPresent(sub ->{
                        List<SubSubTypeEnum> djmbSubTypeEnums = Arrays.asList(SubSubTypeEnum.JING_DONG_DAO_JIA, SubSubTypeEnum.DOU_YIN_DING_DAN, SubSubTypeEnum.MEI_TUAN_SHAN_GOU, SubSubTypeEnum.BAO_ZUN_DING_DAN);
                        if(djmbSubTypeEnums.stream().map(SubSubTypeEnum::getCode).anyMatch(c -> Objects.equals(c,sub.getSubtype()))){
                            throw new CustomizeException("抖音，京东，美团，保尊订单不允许进行微信或支付宝秒退");
                        }
                    });
        }

        // 预约验证
//        if (th.isyuyue == "true")
//        {
//            th.curArea = area;
//        }
        // 地区验证
        if (oaUserBO.getAreaId().equals(area) && req.getTuihuanKind() != 7) {
            result.setCode(500);
            result.setMessage("地区不符，请切换至" + oaUserBO.getArea() + "再操作！");
            return result;
        }

        // C# netPayModel 模型 id(int) - price(decimal)
        List<SmallproRefundNetPayBO> netPayModelList = new ArrayList<>();
        if (req.getTuihuanKind() == 7) {
            Integer type = 1;
            boolean isOnlinePay;
            if (oaUserBO.getXTenant() >= 1000) {
                List<String> tuiWayList = smallProAdapterService.getAllOnlinePayWayList();
                if (CollectionUtils.isEmpty(tuiWayList)) {
//                    result.setCode(500);
//                    result.setMessage("获取退款方式失败！");
//                    return result;
                    //获取不到 则默认使用九机的配置
                    tuiWayList = PayConstants.getPayWayList();
                }
                isOnlinePay = tuiWayList.contains(req.getTuiWay()) || tuiWayList.contains(req.getTuiWay().replaceAll("返回", ""));
            }else{
                //九机
                isOnlinePay = PayConstants.checkOnlinePay(req.getTuiWay());
            }

            if (isOnlinePay && (req.getNetPay() == null || req.getNetPay().size() <= 0)) {
                result.setCode(500);
                result.setMessage("原路径退款请先确定交易号!");
                return result;
            } else if (isOnlinePay && req.getNetPay() != null && req.getNetPay().size() > 0) {
                // 验证网络订单
                if (req.getNetPay() != null && req.getNetPay().size() > 0) {
                    netPayModelList.addAll(req.getNetPay());
                    List<Integer> idList =
                            netPayModelList.stream().map(bo -> bo.getId()).collect(Collectors.toList());
                    List<SmallproRefundNetPayInfoBO> smallproRefundNetPayInfoBOList =
                            smallproMapper.getRefundNetPayInfo(idList, type);
                    Map<Integer, SmallproRefundNetPayBO> idBoMap =
                            netPayModelList.stream().collect(Collectors.toMap(SmallproRefundNetPayBO::getId, bo -> bo));
                    if (smallproRefundNetPayInfoBOList != null && smallproRefundNetPayInfoBOList.size() > 0) {
                        for (SmallproRefundNetPayInfoBO bo : smallproRefundNetPayInfoBOList) {
                            SmallproRefundNetPayBO payBo = idBoMap.get(bo.getId());
                            if (payBo.getPrice().compareTo((bo.getMoney().subtract(bo.getRefundPrice()))) > 0) {
                                result.setCode(500);
                                StringBuilder message = new StringBuilder("交易号[");
                                message.append(bo.getTradeNo());
                                message.append("]退款金额不能大于");
                                message.append(bo.getMoney().subtract(bo.getRefundPrice()).setScale(2,
                                        RoundingMode.HALF_UP).doubleValue());
                                message.append("！");
                                result.setMessage(message.toString());
                                return result;
                            }
                        }
                        BigDecimal price = BigDecimal.ZERO;
                        for (SmallproRefundNetPayBO bo : netPayModelList) {
                            price = price.add(bo.getPrice());
                        }
                        req.setTuikuanM(price);
                        req.setTuikuanM1(price);
                    } else {
                        result.setCode(500);
                        result.setMessage("交易号查找失败！");
                        return result;
                    }
                }
            }
            // 库白条分期自动提交退订
            if (req.getKuBaitiaoM() != null && req.getKuBaitiaoM().compareTo(BigDecimal.valueOf(0.0)) > 0) {
                Integer subId = req.getSubId() == null ? req.getShouhouId().intValue() : req.getSubId();
                List<SmallproRefundKuBaiTiaoMBO> smallproRefundKuBaiTiaoMBOList =
                        smallproMapper.getRefundKuBaiTiaoM(subId);
                if (smallproRefundKuBaiTiaoMBOList != null && smallproRefundKuBaiTiaoMBOList.size() > 0) {
                    BigDecimal realKuBaitiaoPrice = BigDecimal.valueOf(0.0);
                    BigDecimal temp = req.getKuBaitiaoM();
                    for (SmallproRefundKuBaiTiaoMBO bo : smallproRefundKuBaiTiaoMBOList) {
                        if (temp.compareTo(BigDecimal.valueOf(0.0)) > 0) {
                            if (temp.compareTo(bo.getLeftPrice()) > 0) {
                                realKuBaitiaoPrice = realKuBaitiaoPrice.add(bo.getLeftPrice());
                                netPayModelList.add(new SmallproRefundNetPayBO().setPrice(bo.getLeftPrice()).setId(bo.getId()));
                                temp = temp.subtract(bo.getLeftPrice());
                            } else {
                                realKuBaitiaoPrice = realKuBaitiaoPrice.add(bo.getLeftPrice());
                                netPayModelList.add(new SmallproRefundNetPayBO().setPrice(temp).setId(bo.getId()));
                                temp = BigDecimal.valueOf(0.0);
                            }
                        }
                    }
                    req.setKuBaitiaoM(realKuBaitiaoPrice);
                } else {
                    result.setCode(500);
                    result.setMessage("未找到库分期支付记录！");
                    return result;
                }
            }
        }
        // 判断是否退加运费和手续费
        if (req.getIsRefundFreight() != null && req.getIsRefundFreight()) {
            req.setComment(req.getComment() + " 【退运费】 ");
        }
        if (req.getIsRefundHandlingFee() != null && req.getIsRefundHandlingFee()) {
            req.setComment(req.getComment() + " 【退手续费】 ");
        }
        RLock fairLock = redisson.getLock("javaoa:refundSubmitWrite:" + smallproId);
        boolean flagSu = false;
        try {
            boolean flag = fairLock.tryLock(2, 10, TimeUnit.SECONDS);
            if (flag) {
                flagSu = ((SmallproRefundExServiceImpl) AopContext.currentProxy()).refundSubmitWrite(result, oaUserBO,
                        smallproId, inPirce, details, req, netPayModelList, shouhouTuihuanQueryWrapper);
            }
        } catch (Exception e) {
            log.error("退款提交错误", e);
        } finally {
            try {
                //解锁
                if (fairLock.isHeldByCurrentThread()) {
                    fairLock.unlock();
                }
            } catch (RedisException e) {
                log.error("refundSubmitWrite:->fairLock.unlock error", e);
            }
        }
        if (!flagSu) {
            return result;
        }
        result.setCode(0);
        result.setMessage("退款提交成功！");
        return result;
    }

    /**
     * 小件组合退款提交校验
     * @param tuihuanForm
     */
    @Override
    public void assertSmallproRefundCheckAndSet(GroupTuihuanFormVo tuihuanForm){
        try {
            Integer smallproId = tuihuanForm.getShouhouId();
            Smallpro smallpro = smallproMapper.getByIdSqlServer(smallproId);
            Assert.isFalse(smallpro == null || SmallProStatsEnum.SMALL_PRO_STATS_DELETED.getCode().equals(smallpro.getStats())
                    ,"小件单[{}]不存在或已删除！",smallproId);
            Assert.isTrue(Stream.of(SmallProKindEnum.SMALL_PRO_KIND_RETURN, SmallProKindEnum.SMALL_PRO_KIND_EXCHANGE).anyMatch(spEnum -> spEnum.getCode().equals(smallpro.getKind()))
                    ,"小件单[{}]处理方式必须为退换货！",smallproId);
            Assert.isFalse(SmallProStatsEnum.SMALL_PRO_STATS_PROCESS_COMPLETED.getCode().equals(smallpro.getStats()),"小件单[{}]已取机！",smallproId);
            // 获取小件订单关联ppriceId和basketId
            List<SmallproBill> smallproBillList = smallproBillService.lambdaQuery().eq(SmallproBill::getSmallproID, smallproId).list();
            if (CollectionUtils.isNotEmpty(smallproBillList)) {
                checkTieMoRefundTimes(smallproBillList.stream().findFirst().map(SmallproBill::getBasketId).orElse(null));
                Integer ppid =
                        smallproMapper.getproductIdByPPid(smallproBillList.stream().map(SmallproBill::getPpriceid).collect(toList()));
                if (null != ppid) {
                    List<SmallproInfoFileBO> smallproFileInfoList = smallproMapper.getSmallproFileInfo(smallproId);
                    Assert.isFalse(CollectionUtils.isEmpty(smallproFileInfoList),"DIY定制钢化玻璃保护壳退款需要上传附件");
                }
            }
            OaUserBO oaUser = SpringUtil.getBean(AbstractCurrentRequestComponent.class).getCurrentStaffId();

            List<Integer> basketIdList = smallproBillList.stream().map(bo -> bo.getBasketId()).collect(Collectors.toList());
            int check1Flag = smallproMapper.refundServiceCheck1(basketIdList);
            Assert.isFalse(check1Flag > 0 && !oaUser.getRank().contains("7a7"),"您没有提交权限，权值7a7");
            BigDecimal zhejiaM = tuihuanForm.getSubInfoBo().getTotalPrice().subtract(tuihuanForm.getRefundPrice());
            Assert.isFalse(zhejiaM.compareTo(tuihuanForm.getSubInfoBo().getZheJiaM()) < 0 && RankEnum.TKSQ.noHasAuthority(oaUser.getRank()),
                    "实际提交折价金额小于系统应折价金额{}元，如需特殊提交请联系拥有“tksq”权限人员进行操作",NumberUtil.round(tuihuanForm.getSubInfoBo().getZheJiaM(),2, RoundingMode.HALF_DOWN));

            tuihuanForm.setZhejiaM(tuihuanForm.getSubInfoBo().getTotalPrice().subtract(tuihuanForm.getRefundPrice()));
            /*调整订单折价金额(最大可退金额无需调整, 由这里控制 com.jiuji.oa.afterservice.refund.service.kind.impl.
            SmallproTuiHuanKindServiceImpl.getSuInfoWithMaxRefundPrice(java.lang.Integer, java.lang.Integer))*/
            tuihuanForm.getSubInfoBo().setZheJiaM(zhejiaM);
        } catch (IllegalArgumentException e) {
            throw new CustomizeException(e.getMessage());
        }

    }

    private void checkTieMoRefundTimes(Integer basketId) {
        if (CommenUtil.isNullOrZero(basketId)){
            throw new CustomizeException("商品编号不能为空");
        }
        List<DIYTimoCardBO> diyTimoCards = smallproMapper.getDIYYearCard(basketId);
        if (CollUtil.isEmpty(diyTimoCards)) {
            return;
        }
        if (diyTimoCards.stream().allMatch(item -> item.getEndTime().isBefore(LocalDateTime.now()))) {
            throw new CustomizeException("年包已失效，不允许退款");
        }
        Basket basket = Optional.ofNullable(CommenUtil.autoQueryHist(() -> basketService.getById(basketId), MTableInfoEnum.BASKET, basketId)).orElseThrow(() -> new CustomizeException("订单信息不存在"));
        Integer basketCount = Optional.ofNullable(basket.getBasketCount()).orElse(NumberConstant.ONE);
        //年包服务数量
        Integer totalCount = diyTimoCards.size();
        Integer diyServiceCount = totalCount / basketCount;
        //已使用数量
        Integer usedCount = Convert.toInt(diyTimoCards.stream().filter(item -> CommenUtil.isNotNullZero(item.getBasketId())).count(), NumberConstant.ZERO);
        //剩余可退数量
        if (totalCount - usedCount < diyServiceCount) {
            throw new CustomizeException("年包已使用，不允许退款");
        }
        //查询退款次数
        Integer refundTimes = Optional.ofNullable(smallproMapper.getRefundCount(basketId)).orElse(NumberConstant.ZERO);
        //剩余可退的数量
        Integer leftRefundCount = totalCount - usedCount - refundTimes * diyServiceCount;
        if (leftRefundCount < diyServiceCount) {
            throw new CustomizeException("年包已使用，不允许退款");
        }
    }

    private R<String> checkTuiKuanMoney(Integer smallproId, Smallpro smallpro, BigDecimal tuikuanM,
                                        List<SmallproOrderInfoBO> smallproOrderInfoBOList) {
        // 尝试获取贴膜年包卡信息
        boolean isFreeExchange = false;
        Boolean isServiceProduct = false;
        Boolean isYearCardProduct = false;
        FilmCardInfomationBO filmCardInfomationBO = null;
        if (smallproOrderInfoBOList != null && smallproOrderInfoBOList.size() == 1) {
            Integer ppid = smallproOrderInfoBOList.get(0).getTargetPpriceId();
            if (smallProAdapterService.getSmallRelativeConfigList(SysConfigConstant.JIUJI_HALF_YEAR_SERVICE_PPID).contains(ppid)
                    || smallProAdapterService.getSmallRelativeConfigList(SysConfigConstant.JIUJI_ONE_YEAR_SERVICE_PPID).contains(ppid)
                    || smallProAdapterService.getSmallRelativeConfigList(SysConfigConstant.JIUJI_TWO_YEAR_SERVICE_PPID).contains(ppid)) {
                isServiceProduct = true;
            } else if (smallProAdapterService.getSmallRelativeConfigList(SysConfigConstant.JIUJI_YEAR_CARD_PPID).contains(ppid)) {
                isYearCardProduct = true;
            }
        }

        // 如果没有返回null
        if (CollectionUtils.isNotEmpty(smallproOrderInfoBOList)) {
            SmallproOrderInfoBO smallproOrderInfo = smallproOrderInfoBOList.get(0);
            if (smallproOrderInfo != null) {
                Integer basketId = smallproOrderInfo.getBasketId();
                filmCardInfomationBO =
                        smallproFilmCardService.getFilmCardInfoByBasketIdByWrite(basketId);
            }
        }
        BigDecimal discountM = BigDecimal.ZERO;
        BigDecimal deductionM = BigDecimal.ZERO;
        BigDecimal installmentAmountPrice = BigDecimal.ZERO;
        //运营商抵扣金额
        BigDecimal sumOffSetMoney = BigDecimal.ZERO;
        // 计算和包分期金额（前端退款金额扣除该金额）
        List<Integer> basketIds = new LinkedList<>();
        if (CollectionUtils.isNotEmpty(smallproOrderInfoBOList)) {
            basketIds = smallproOrderInfoBOList.stream().map(SmallproOrderInfoBO::getBasketId).collect(toList());
        }
        if (CollectionUtils.isNotEmpty(basketIds)) {
            List<Basket> list =
                    basketService.list(new LambdaQueryWrapper<Basket>().select(Basket::getBasketId).eq(Basket::getType, "95").in(Basket::getBasketId,
                            basketIds));
            if (CollectionUtils.isNotEmpty(list) && null != smallpro.getSubId()) {
                installmentAmountPrice =
                        shouyinOtherService.getInstallmentAmount(smallpro.getSubId());
            }
            //计算运营商抵扣金额计算
            sumOffSetMoney =
                    shouyinOtherService.getSumOffSetMoney(basketIds);
        }
        // region 服务退款相关
        installmentAmountPrice = installmentAmountPrice == null ? BigDecimal.ZERO : installmentAmountPrice;
        if (smallproOrderInfoBOList != null && smallproOrderInfoBOList.size() == 1) {
            SmallproOrderInfoBO smallproOrderInfoBO = smallproOrderInfoBOList.get(0);
            Integer ppid = smallproOrderInfoBO.getTargetPpriceId();
            LocalDateTime buyTime = smallpro.getBuyDate();
            BigDecimal price = smallproOrderInfoBO.getPrice();
            if (isServiceProduct) {
                discountM = smallProAdapterService.calculateDiscountAmount(ppid, buyTime, price, smallproOrderInfoBO.getBasketId());
            } else if (isYearCardProduct) {
                isYearCardProduct = true;
                if (filmCardInfomationBO != null && filmCardInfomationBO.getIsCanRefund()) {
                    discountM = BigDecimal.valueOf(filmCardInfomationBO.getRefundAmount());
                }
            }
            if(XtenantEnum.isJiujiXtenant()){
                // 移动顺差让利，移动补贴
                BigDecimal mobileSubsidyPrice =
                        shouyinOtherService.getInstallmentAmountMobileSubsidy(smallproOrderInfoBO.getSubId());
                // 退款金额需要减去移动顺差让利，移动补贴
                discountM = discountM.subtract(mobileSubsidyPrice);
            }
            deductionM = price.subtract(discountM);
        }
        BigDecimal maxTuiMoney = BigDecimal.ZERO;
        if (isServiceProduct) {
            maxTuiMoney = discountM.subtract(installmentAmountPrice).subtract(sumOffSetMoney).setScale(NumberConstant.TWO,RoundingMode.HALF_UP);
            if (tuikuanM.compareTo(maxTuiMoney) > 0) {
                return R.error("退款金额超出最大限额：" + maxTuiMoney);
            } else {
                return R.success("满足退款条件");
            }
        } else {
            BigDecimal reduce = BigDecimal.ZERO;
            if (CollectionUtils.isNotEmpty(smallproOrderInfoBOList)) {
                reduce = smallproOrderInfoBOList.stream().map(e -> BigDecimal.valueOf(e.getProductCount() == null ?
                        0 : e.getProductCount())
                        .multiply(NumberUtil.max(ObjectUtil.defaultIfNull(e.getPriceReturn(),BigDecimal.ZERO),ObjectUtil.defaultIfNull(e.getPrice(),BigDecimal.ZERO))))
                        .reduce(BigDecimal.ZERO,BigDecimal::add);
            }
            maxTuiMoney = reduce.subtract(installmentAmountPrice).subtract(sumOffSetMoney).setScale(NumberConstant.TWO,RoundingMode.HALF_UP);
            if (tuikuanM.compareTo(maxTuiMoney) > 0) {
                return R.error("退款金额超出最大限额：" + maxTuiMoney);
            } else {
                return R.success("满足退款条件");
            }
        }
    }

    // endregion

    // region 小件接件退款二次审核 refundCheck2

    @Override
    public SmallproNormalCodeMessageRes refundCheck2(OaUserBO oaUserBO, Integer shouhouTuihuanId) {
        SmallproNormalCodeMessageRes result = new SmallproNormalCodeMessageRes();
        List<String> ranks = Optional.ofNullable(oaUserBO).map(OaUserBO::getRank).orElseGet(Collections::emptyList);
        QueryWrapper<ShouhouTuihuan> shouhouTuihuanQueryWrapper = new QueryWrapper<>();
        shouhouTuihuanQueryWrapper.lambda().eq(ShouhouTuihuan::getId, shouhouTuihuanId).isNull(ShouhouTuihuan::getCheck2)
                .and(temp -> temp.isNull(ShouhouTuihuan::getIsdel).or().eq(ShouhouTuihuan::getIsdel, 0));
        List<ShouhouTuihuan> shouhouTuihuanList = shouhouTuihuanService.listSqlServer(shouhouTuihuanQueryWrapper);
        if (shouhouTuihuanList == null || shouhouTuihuanList.size() != 1) {
            result.setCode(500);
            result.setMessage("查询售后退换信息失败！无记录！");
            return result;
        }
        ShouhouTuihuan shouhouTuihuan = shouhouTuihuanList.get(0);
        /*Smallpro smallpro = smallproService.getByIdSqlServer(ObjectUtil.defaultIfNull(shouhouTuihuan.getSmallproid(),shouhouTuihuan.getShouhouId()));
        if(StrUtil.endWithIgnoreCase(shouhouTuihuan.getTuiWay(), ShouhouRefundService.REFUND_WAY_SECONDS)
                && smallpro.getSubId() != null && !RankEnum.SMALL_PRO_CALL_CENTER.hasAuthority(ranks)
                && ObjectUtil.defaultIfNull(smallproMapper.getSubWithPidSubTotalMoney(smallpro.getSubId()),BigDecimal.ZERO).subtract(shouhouTuihuan.getTuikuanM())
                .compareTo(ObjectUtil.defaultIfNull(smallproMapper.remainingCashMoney(smallpro.getSubId()),BigDecimal.ZERO))<0){
            result.setCode(500);
            result.setMessage("秒退金额大于非现金收银金额，请联系呼叫中心进行审核");
            return result;
        }*/

        shouhouTuihuan.setCheck2user(oaUserBO.getUserName()).setCheck2(true).setCheck2dtime(LocalDateTime.now());
        boolean flag = ((SmallproRefundExServiceImpl) AopContext.currentProxy()).refundCheck2Write(result,
                shouhouTuihuan);
        if (!flag) {
            return result;
        }
        smallproExchangePurchaseService.virtualProductAutoScrap(shouhouTuihuan.getSmallproid());
        boolean ccbFlag = true;
        if (shouhouTuihuan.getTuiWay().equals("建行分期返回")
                && oaUserBO.getXTenant() != JiujiTenantEnum.JIUJI_TENANT_HUAWEI.getCode().intValue()
                && oaUserBO.getXTenant() != JiujiTenantEnum.JIUJI_TENANT_JIUJI.getCode().intValue()) {
            ccbFlag = false;
        }
        if (((shouhouTuihuan.getSmallproid() != null && shouhouTuihuan.getSmallproid() > 0) || (shouhouTuihuan.getIsValidt() != null && shouhouTuihuan.getIsValidt()))
                && !(shouhouTuihuan.getTuiWay().contains("银行转账") || shouhouTuihuan.getTuiWay().contains("微信秒退") || shouhouTuihuan.getTuiWay().contains("支付宝秒退"))
                && ccbFlag) {
            System.out.println("成功退款办理！");
//            refundProcessing(shouhouTuihuanId, oaUserBO);
        }
        result.setCode(0);
        result.setMessage("退款二次审核成功！");
        return result;
    }

    // endregion

    // region 获取支付信息 getPayInfo

    @Override
    public List<SmallproPayInfoBO> getPayInfo(Integer subId, Integer shouhouTuihuanId, String tuiWay,
                                              Integer tuihuanKind) {
        List<SmallproPayInfoBO> result = null;
        List<Integer> subIdList = new ArrayList<>(2);
        Integer type = 0;
        if (subId != null) {
            if (tuihuanKind == 7) {
                Sub sub = subService.getByIdSqlServer(subId);
                if (sub.getSubPid() != null && sub.getSubPid() > 0) {
                    subIdList.add(sub.getSubPid());
                }
                subIdList.add(subId);
                type = 1;
            }
            tuiWay = tuiWay.replaceAll("返回", "");
            List<String> payWayList = new ArrayList<>();
            switch (tuiWay) {
                case "微信":
                    payWayList.add("微信");
                    payWayList.add("微信APP");
                    break;
                case "兴业银行扫码":
                    payWayList.add("扫码枪");
                    payWayList.add("兴业银行扫码");
                    break;
                default:
                    payWayList.add(tuiWay);
            }
            List<SmallproPayInfoBO> selectResult = smallproMapper.getPayInfoBySubId(type,
                    subIdList.stream().map(e -> e.toString()).collect(Collectors.toList()), payWayList);
            if (selectResult != null && selectResult.size() > 0) {
                result = selectResult;
            }
        } else {
            List<SmallproPayInfoBO> selectResult = smallproMapper.getPayInfoByAfterSaleREId(shouhouTuihuanId);
            if (selectResult != null && selectResult.size() > 0) {
                result = selectResult;
            }
        }
        return result;
    }

    // endregion

    // region 撤销退货提交 cancelRefundCheck

    @Override
    public SmallproNormalCodeMessageRes cancelRefundCheck(Integer afterSaleREId, OaUserBO oaUserBO) {
        SmallproNormalCodeMessageRes result = new SmallproNormalCodeMessageRes();
        QueryWrapper<ShouhouTuihuan> shouhouTuihuanQueryWrapper = new QueryWrapper<>();
        shouhouTuihuanQueryWrapper.lambda().eq(ShouhouTuihuan::getId, afterSaleREId).isNull(ShouhouTuihuan::getCheck3)
                .and(wrapper -> wrapper.eq(ShouhouTuihuan::getIsdel, 0).or().isNull(ShouhouTuihuan::getIsdel));
        List<ShouhouTuihuan> shouhouTuihuanList = shouhouTuihuanService.listSqlServer(shouhouTuihuanQueryWrapper);
        if (shouhouTuihuanList == null || shouhouTuihuanList.size() <= 0) {
            result.setCode(500);
            result.setMessage("错误，退换记录不存在!");
            return result;
        } else {
            ShouhouTuihuan shouhouTuihuan = shouhouTuihuanList.get(0);
            shouhouTuihuan.setIsdel(true).setDelUser(oaUserBO.getUserName()).setDelTime(LocalDateTime.now());
            boolean flag = ((SmallproRefundExServiceImpl) AopContext.currentProxy())
                    .cancelRefundCheckWrite(result, shouhouTuihuan);
            if (!flag) {
                return result;
            }
            writeCancelSmallproLog(oaUserBO.getUserName(), shouhouTuihuan.getShouhouId());
            result.setCode(0);
            result.setMessage("撤销操作成功！");
            return result;
        }
    }

    private void writeCancelSmallproLog(String userName, Integer smallproId) {
        //撤销成功写入日志
        SmallproAddLogReq smallproAddLogReq = new SmallproAddLogReq();
        smallproAddLogReq.setSmallproId(smallproId);
        smallproAddLogReq.setShowType(1);
        smallproAddLogReq.setToSms(0);
        smallproAddLogReq.setToEmail(0);
        smallproAddLogReq.setToWeixin(0);
        smallproAddLogReq.setComment("撤销申请操作，业务类型：小件退货");
        smallproService.addSmallproLogWithPush(smallproAddLogReq, userName);
    }

    /**
     * 撤销组合退小件单的操作
     * @param tuiHuanPo
     */
    @Override
    public void cancelGroupRefund(ShouhouTuiHuanPo tuiHuanPo){
        if(tuiHuanPo == null){
            return;
        }
        OaUserBO oaUser = SpringUtil.getBean(AbstractCurrentRequestComponent.class).getCurrentStaffId();
        writeCancelSmallproLog(oaUser.getUserName(),ObjectUtil.defaultIfNull(tuiHuanPo.getSmallproid(),tuiHuanPo.getShouhouId()));
    }

    // endregion

    // region 获取支付宝支付验证信息 getAlipayAuthInfo

    @Override
    public AlipayAuthBO getAlipayAuthInfo(String id, String type) {
        AlipayAuthBO result = null;
        StringBuilder keyBuilder = new StringBuilder("urn:memberalipayauth:").append(id + "_" + type);
        String authInfo = redisTemplate.opsForValue().get(keyBuilder.toString());
        if (authInfo == null || authInfo.equals("")) {
            return null;
        } else {
            result = JSONObject.parseObject(authInfo, AlipayAuthBO.class);
            return result;
        }
    }

    // endregion

    // region 获取退款账号异常信息 getRefundException

    @Override
    public List<SmallproRefundExceptionItemBO> getRefundException(Integer afterREId) {
        ShouhouTuihuan shouhouTuihuan = shouhouTuihuanService.getByIdSqlServer(afterREId);
        if (shouhouTuihuan == null) {
            return new ArrayList<>();
        }
        List<String> tuiwayRight = Stream.of("支付宝秒退", "微信秒退", "银行转账").collect(Collectors.toList());
        if (!tuiwayRight.contains(shouhouTuihuan.getTuiWay())) {
            return new ArrayList<>();
        }
        List<Integer> subKindRight = Stream.of(6, 7, 8, 5, 3, 4, 11, 10).collect(Collectors.toList());
        Integer userId = 0;
        String openId = "";
        if (subKindRight.contains(shouhouTuihuan.getTuihuanKind())) {
            userId = smallproMapper.getSubUserIdByTuihuanKind(
                    shouhouTuihuan.getTuihuanKind(), shouhouTuihuan.getSubId());
            if (userId != null && userId != 0) {
                openId = smallproMapper.getUserOpenId(userId);
            }
        }

        List<SmallproRefundExceptionItemBO> items = new ArrayList<>();
        switch (shouhouTuihuan.getTuiWay()) {
            case "银行转账":
                List<ShouhouTuihuan> tuiHuanShouhouList = shouhouTuihuanMapper.listShouhouByUserId(userId);
                if (CollectionUtils.isNotEmpty(tuiHuanShouhouList)) {
                    List<SmallproRefundExceptionItemBO> tuihuanItem = tuiHuanShouhouList.stream().map(m -> {
                        SmallproRefundExceptionItemBO item = new SmallproRefundExceptionItemBO();
                        item.setExMsg("客户退款");
                        item.setSubId(m.getShouhouId());
                        item.setUrl("/shouhou/edit/" + m.getShouhouId());
                        item.setKindName("售后");
                        item.setPrice(m.getTuikuanM());
                        item.setDTime(m.getCheck3dtime());
                        return item;
                    }).collect(Collectors.toList());
                    items.addAll(tuihuanItem);
                }

                List<ShouhouTuihuan> tuiHuanSmallproList = shouhouTuihuanMapper.listSmallProByUserId(userId);
                if (CollectionUtils.isNotEmpty(tuiHuanSmallproList)) {
                    List<SmallproRefundExceptionItemBO> tuihuanItem = tuiHuanSmallproList.stream().map(m -> {
                        SmallproRefundExceptionItemBO item = new SmallproRefundExceptionItemBO();
                        item.setExMsg("客户退款");
                        item.setSubId(m.getShouhouId());
                        item.setUrl("/smallpro/edit/" + m.getShouhouId());
                        item.setKindName("小件");
                        item.setPrice(m.getTuikuanM());
                        item.setDTime(m.getCheck3dtime());
                        return item;
                    }).collect(Collectors.toList());
                    items.addAll(tuihuanItem);
                }

                String bankNumber = Optional.ofNullable(shouhouTuihuan.getBanknumber()).map(t -> t.replace(" ", "")).orElse("");
                if (StringUtils.isNotBlank(bankNumber)) {
                    List<ShouhouTuihuan> tuihuanBankList = shouhouTuihuanMapper.listSmallProByUserId(userId);
                    if (CollectionUtils.isNotEmpty(tuihuanBankList)) {
                        List<SmallproRefundExceptionItemBO> tuihuanItem = tuihuanBankList.stream()
                                .map(m -> {
                                    SmallproRefundExceptionItemBO item = new SmallproRefundExceptionItemBO();
                                    item.setExMsg("卡号退款");
                                    item.setDsc(bankNumber);
                                    item.setPrice(m.getTuikuanM());
                                    item.setDTime(m.getCheck3dtime());
                                    switch (m.getTuihuanKind()) {
                                        case 6:
                                            item.setSubId(m.getSubId());
                                            item.setUrl("/addOrder/editOrder?SubID=" + m.getSubId());
                                            item.setKindName("销售单");
                                            break;
                                        case 7:
                                        case 10:
                                            item.setSubId(m.getSubId());
                                            item.setUrl("/staticpc/#/small-refund/" + m.getSmallproid());
                                            item.setKindName("小件");
                                            break;
                                        case 5:
                                        case 3:
                                        case 4:
                                        case 11:
                                            item.setSubId(m.getShouhouId());
                                            item.setUrl("/shouhou/edit/" + m.getShouhouId());
                                            item.setKindName("售后");
                                            break;
                                        case 8:
                                            item.setSubId(m.getSubId());
                                            item.setUrl("/StockOut/editOrder?SubID=" + m.getSubId());
                                            item.setKindName("良品");
                                            break;
                                        default:
                                            break;
                                    }

                                    return item;
                                }).collect(Collectors.toList());
                        items.addAll(tuihuanItem);
                    }
                }
                break;
            case "支付宝秒退":
                AlipayAuthBO auth = getAlipayAuthInfo(shouhouTuihuan.getId().toString(), "1");
                if (auth == null) {
                    return new ArrayList<>();
                }
                List<AlipayToAccountLog> list = alipayToAccountLogMapper.listLogForException(auth.getUser_id());
                if (CollectionUtils.isEmpty(list)) {
                    return new ArrayList<>();
                }
                items = list.stream().map(m -> {
                    SmallproRefundExceptionItemBO item = new SmallproRefundExceptionItemBO();
                    item.setExMsg("支付宝秒退");
                    item.setDsc(m.getRemark());
                    item.setPrice(m.getAmount());
                    item.setDTime(m.getPayDate());
                    Integer subId = m.getRealId();
                    if (item.getDsc().contains("手机回收款项")) {
                        item.setUrl("/recoverIndex/detail?id=" + item.getSubId());
                        item.setKindName("回收");
                        item.setSubId(subId);
                    } else if (item.getDsc().contains("订单：")) {
                        item.setUrl("/addOrder/editOrder?SubID=" + subId);
                        item.setKindName("订单");
                        item.setSubId(subId);
                    } else if (item.getDsc().contains("售后单号")) {
                        item.setUrl("/shouhou/edit/" + subId);
                        item.setKindName("售后");
                        item.setSubId(subId);
                    }
                    return item;
                }).collect(Collectors.toList());
                break;
            case "微信秒退":
                List<String> openIds = new ArrayList<>();
                openIds.add(shouhouTuihuan.getPayOpenId());
                openIds.add(openId);
                openIds = openIds.stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(openIds)) {
                    return new ArrayList<>();
                }
                List<String> systems = Stream.of("huishou", "shouhou_tuihuan").collect(Collectors.toList());
                List<PayWxenterprise> payWxenterprises = payWxenterpriseMapper.listWeixinEnterprise(
                        null, openIds, systems);
                if (CollectionUtils.isEmpty(payWxenterprises)) {
                    return new ArrayList<>();
                }
                items = payWxenterprises.stream().map(m -> {
                    SmallproRefundExceptionItemBO item = new SmallproRefundExceptionItemBO();
                    item.setExMsg("微信秒退");
                    item.setDsc(m.getRemark());
                    item.setPrice(m.getAmount());
                    item.setDTime(m.getTradeDate());
                    Integer subId = 0;
                    if (item.getDsc().contains("：")) {
                        List<String> splitRes = Stream.of(item.getDsc().split("："))
                                .filter(StringUtils::isNotBlank).collect(Collectors.toList());
                        subId = Integer.valueOf(splitRes.get(1));
                    }
                    if (item.getDsc().contains("手机回收款项")) {
                        item.setSubId(subId);
                        item.setUrl("/recoverIndex/detail?id=" + subId);
                        item.setKindName("回收");
                    } else if ("shouhou_tuihuan".equals(m.getSubsystem()) && item.getDsc().indexOf("付款")>=0) {
//                        String printName = "九机网";
//                        R<AreaInfo> areaSubjectRes = areaInfoClient.getAreaInfoById(shouhouTuihuan.getAreaid());
//                        if (ResultCode.SUCCESS == areaSubjectRes.getCode() && areaSubjectRes.getData() != null) {
//                            printName = areaSubjectRes.getData().getPrintName();
//                        }

                        Integer idM = Integer.valueOf(item.getDsc().substring(item.getDsc().indexOf("付款") + "付款".length()));
                        item.setSubId(idM);
                        item.setUrl("/addOrder/editOrder?SubID=" + idM);
                        item.setKindName("订单");
                    } else if (item.getDsc().contains("退换机款项，售后单号")) {
                        item.setSubId(subId);
                        item.setUrl("/shouhou/edit/" + subId);
                        item.setKindName("售后");
                    }
                    return item;
                }).collect(Collectors.toList());
                break;
            default:
                return new ArrayList<>();
        }
        return items;
    }


    /**
     * description: <检查退款账号是否异常>  对应C# validtRefundPayInfo  验证退款信息
     *
     * @param afterREId shouhouTuihuanId
     * @return com.jiuji.oa.afterservice.smallpro.vo.res.SmallproNormalCodeMessageRes
     * <AUTHOR>
     * @date 14:16 2020/4/16
     * @point result.code=500 error no message ; result.code=501 error with message
     * @since 1.0.0
     **/
    @Override
    public SmallproNormalCodeMessageRes checkRefundException(Integer afterREId) {
        SmallproNormalCodeMessageRes result = new SmallproNormalCodeMessageRes();
        ArrayList<String> tuiWayList = new ArrayList<>(3);
        tuiWayList.add("支付宝秒退");
        tuiWayList.add("微信秒退");
        tuiWayList.add("银行转账");
        QueryWrapper<ShouhouTuihuan> shouhouTuihuanQueryWrapper = new QueryWrapper<>();
        shouhouTuihuanQueryWrapper.lambda().in(ShouhouTuihuan::getTuiWay, tuiWayList).eq(ShouhouTuihuan::getId,
                afterREId);
        List<ShouhouTuihuan> shouhouTuihuanList = shouhouTuihuanService.listSqlServer(shouhouTuihuanQueryWrapper);
        result.setCode(0);
        result.setMessage("退款账号无异常！");
        if (shouhouTuihuanList != null && shouhouTuihuanList.size() > 0) {
            ShouhouTuihuan shouhouTuihuan = shouhouTuihuanList.get(0);
            List<Integer> subKindRight = Stream.of(6, 7, 8, 5, 3, 4, 11, 10).collect(Collectors.toList());
            Integer userId = 0;
            String openId = "";
            if (subKindRight.contains(shouhouTuihuan.getTuihuanKind())) {
                userId = smallproMapper.getSubUserIdByTuihuanKind(
                        shouhouTuihuan.getTuihuanKind(), shouhouTuihuan.getSubId());
                if (userId != null && userId != 0) {
                    openId = smallproMapper.getUserOpenId(userId);
                }
            }
            switch (shouhouTuihuan.getTuiWay()) {
                case "银行转账":
                    List<ShouhouTuihuan> tuiHuanShouhouList = shouhouTuihuanMapper.listShouhouByUserId(userId);
                    if (tuiHuanShouhouList != null && tuiHuanShouhouList.size() > 0) {
                        if (tuiHuanShouhouList.get(0).getId() != null && tuiHuanShouhouList.get(0).getId() > 0) {
                            result.setMessage("客户半年内存在退款记录");
                            result.setCode(500);
                            break;
                        }
                    }

                    List<ShouhouTuihuan> tuiHuanSmallproList = shouhouTuihuanMapper.listSmallProByUserId(userId);
                    if (tuiHuanSmallproList != null && tuiHuanSmallproList.size() > 0) {
                        if (tuiHuanSmallproList.get(0).getId() != null && tuiHuanSmallproList.get(0).getId() > 0) {
                            result.setMessage("客户半年内存在退款记录");
                            result.setCode(500);
                            break;
                        }
                    }

                    String bankNumber = Optional.ofNullable(shouhouTuihuan.getBanknumber()).map(bn -> bn.replace(" ", "")).orElse("");
                    if (StringUtils.isNotBlank(bankNumber)) {
                        Integer id = shouhouTuihuanMapper.getShouhouTuihuanIdByBankNumber(bankNumber);
                        if (id != null && id > 0) {
                            result.setMessage("转账卡号半年内存在退款转账记录");
                            result.setCode(500);
                            break;
                        }
                    }

                    if (openId != null && !"".equals(openId)) {
                        Integer id = shouhouTuihuanMapper.getPayWeixinIdByOpenId(openId);
                        if (id != null && id > 0) {
                            result.setMessage("客户微信半年内存在转账记录");
                            result.setCode(500);
                            return result;
                        }
                    }
                    break;
                case "支付宝秒退":
                    AlipayAuthBO auth = getAlipayAuthInfo(shouhouTuihuan.getId().toString(), "1");
                    if (auth == null) {
                        result.setMessage("未找到支付宝验证信息，请先验证支付宝");
                        result.setCode(501);
                        break;
                    }
                    List<AlipayToAccountLog> list = alipayToAccountLogMapper.listLogForException(auth.getUser_id());
                    if (list != null && list.size() > 0) {
                        if (list.get(0).getId() != null && list.get(0).getId() > 0) {
                            result.setMessage("客户支付宝半年内存在转账记录");
                            result.setCode(500);
                            break;
                        }
                    }
                    break;
                case "微信秒退":
                    if (shouhouTuihuan.getPayOpenId() == null || "".equals(shouhouTuihuan.getPayOpenId())) {
                        result.setMessage("未找到微信验证信息，请先验证微信");
                        result.setCode(501);
                        break;
                    }
                    List<String> systems = Stream.of("huishou", "shouhou_tuihuan").collect(Collectors.toList());
                    List<PayWxenterprise> payWxenterprises = payWxenterpriseMapper.listWeixinEnterprise(
                            shouhouTuihuan.getPayOpenId() == null ? "" : shouhouTuihuan.getPayOpenId(), null, systems);
                    if (payWxenterprises != null && payWxenterprises.size() > 0) {
                        if (payWxenterprises.get(0).getId() != null && payWxenterprises.get(0).getId() > 0) {
                            result.setCode(500);
                            result.setMessage("验证微信半年内存在转账记录");
                            break;
                        }
                    }
                    if (openId != null && !"".equals(openId)) {
                        payWxenterprises = payWxenterpriseMapper.listWeixinEnterprise(openId, null, systems);
                        if (payWxenterprises != null && payWxenterprises.size() > 0) {
                            if (payWxenterprises.get(0).getId() != null && payWxenterprises.get(0).getId() > 0) {
                                result.setCode(500);
                                result.setMessage("客户微信半年内存在转账记录");
                                break;
                            }
                        }
                    }
                    break;
                default:
                    break;
            }
        }
        if (result.getCode() != null && result.getCode() == 501) {
            UpdateWrapper<ShouhouTuihuan> shouhouTuihuanUpdateWrapper = new UpdateWrapper<>();
            shouhouTuihuanUpdateWrapper.lambda().set(ShouhouTuihuan::getAccountException, 1).eq(ShouhouTuihuan::getId
                    , afterREId);
            shouhouTuihuanService.update(shouhouTuihuanUpdateWrapper);
        }
        return result;
    }

    // endregion

    // region 检测退款方式 checkRefundType


    @Override
    public List<String> checkRefundType(Integer subId, Integer type) {
        List<String> result = new ArrayList<>();
        if (Arrays.asList(TuihuanKindEnum.TDJ.getCode(), TuihuanKindEnum.TPJ.getCode(), TuihuanKindEnum.TDJ_LP.getCode()).contains(type)) {
            List<Integer> subIdList = new ArrayList<>();
            subIdList.add(subId);
            Integer subPid = smallproMapper.getSubPid(subId);
            if (subId != null) {
                subIdList.add(subId);
            }
            List<String> selectResult = smallproMapper.getEffectiveRefundType(subIdList, type);
            if (selectResult != null && selectResult.size() > 0) {
                result.addAll(selectResult);
            }
        }
        return result;
    }

    // endregion

    // region 计算九机服务退款金额 CalculateDiscountAmount

    @Override
    public BigDecimal calculateDiscountAmount(Integer ppid, LocalDateTime buyTime, BigDecimal price) {
        if (ppid == null) {
            return price;
        }
        LocalDateTime now = LocalDateTime.now();
        long day = Duration.between(buyTime, now).toDays();
        BigDecimal rate = BigDecimal.valueOf(0.0);
        BigDecimal result = BigDecimal.valueOf(0.0);
        if (smallProAdapterService.getSmallRelativeConfigList(SysConfigConstant.JIUJI_HALF_YEAR_SERVICE_PPID).contains(ppid)) {
//            rate = smallProConstant.getHALF_YEAR_DISCOUNT_RATE();
        } else if (smallProAdapterService.getSmallRelativeConfigList(SysConfigConstant.JIUJI_ONE_YEAR_SERVICE_PPID).contains(ppid)) {
//            rate = smallProConstant.getONE_YEAR_DISCOUNT_RATE();
        } else if (smallProAdapterService.getSmallRelativeConfigList(SysConfigConstant.JIUJI_TWO_YEAR_SERVICE_PPID).contains(ppid)) {
//            rate = smallProConstant.getTWO_YEAR_DISCOUNT_RATE();
        }
        if (rate.compareTo(BigDecimal.valueOf(0.0)) > 0) {
            if (day >= 0 && day <= 15) {
                result = price;
            } else if (day >= 16 && day <= 30) {
                result = price.multiply(BigDecimal.valueOf(0.8));
            } else if (day >= 31 && day <= 60) {
                result =
                        price.multiply(BigDecimal.valueOf(0.8)).subtract(price.multiply(rate).multiply(BigDecimal.valueOf(1)));
            } else if (day >= 61 && day <= 90) {
                result =
                        price.multiply(BigDecimal.valueOf(0.8)).subtract(price.multiply(rate).multiply(BigDecimal.valueOf(2)));
            } else if (day >= 91 && day <= 120) {
                result =
                        price.multiply(BigDecimal.valueOf(0.8)).subtract(price.multiply(rate).multiply(BigDecimal.valueOf(3)));
            } else if (day >= 121 && day <= 150) {
                result =
                        price.multiply(BigDecimal.valueOf(0.8)).subtract(price.multiply(rate).multiply(BigDecimal.valueOf(4)));
            } else if (day >= 151 && day <= 180) {
                result =
                        price.multiply(BigDecimal.valueOf(0.8)).subtract(price.multiply(rate).multiply(BigDecimal.valueOf(5)));
            } else if (day >= 181 && day <= 210) {
                result =
                        price.multiply(BigDecimal.valueOf(0.8)).subtract(price.multiply(rate).multiply(BigDecimal.valueOf(6)));
            } else if (day >= 211 && day <= 240) {
                result =
                        price.multiply(BigDecimal.valueOf(0.8)).subtract(price.multiply(rate).multiply(BigDecimal.valueOf(7)));
            } else if (day >= 241 && day <= 270) {
                result =
                        price.multiply(BigDecimal.valueOf(0.8)).subtract(price.multiply(rate).multiply(BigDecimal.valueOf(8)));
            } else if (day >= 271 && day <= 300) {
                result =
                        price.multiply(BigDecimal.valueOf(0.8)).subtract(price.multiply(rate).multiply(BigDecimal.valueOf(9)));
            } else if (day >= 301 && day <= 330) {
                result =
                        price.multiply(BigDecimal.valueOf(0.8)).subtract(price.multiply(rate).multiply(BigDecimal.valueOf(10)));
            } else if (day >= 331 && day <= 360) {
                result =
                        price.multiply(BigDecimal.valueOf(0.8)).subtract(price.multiply(rate).multiply(BigDecimal.valueOf(11)));
            } else if (day >= 361 && day <= 390) {
                result =
                        price.multiply(BigDecimal.valueOf(0.8)).subtract(price.multiply(rate).multiply(BigDecimal.valueOf(12)));
            } else if (day >= 391 && day <= 420) {
                result =
                        price.multiply(BigDecimal.valueOf(0.8)).subtract(price.multiply(rate).multiply(BigDecimal.valueOf(13)));
            } else if (day >= 421 && day <= 450) {
                result =
                        price.multiply(BigDecimal.valueOf(0.8)).subtract(price.multiply(rate).multiply(BigDecimal.valueOf(14)));
            } else if (day >= 451 && day <= 480) {
                result =
                        price.multiply(BigDecimal.valueOf(0.8)).subtract(price.multiply(rate).multiply(BigDecimal.valueOf(15)));
            } else if (day >= 481 && day <= 510) {
                result =
                        price.multiply(BigDecimal.valueOf(0.8)).subtract(price.multiply(rate).multiply(BigDecimal.valueOf(16)));
            } else if (day >= 511 && day <= 540) {
                result =
                        price.multiply(BigDecimal.valueOf(0.8)).subtract(price.multiply(rate).multiply(BigDecimal.valueOf(17)));
            } else if (day >= 541 && day <= 570) {
                result =
                        price.multiply(BigDecimal.valueOf(0.8)).subtract(price.multiply(rate).multiply(BigDecimal.valueOf(18)));
            } else if (day >= 571 && day <= 600) {
                result =
                        price.multiply(BigDecimal.valueOf(0.8)).subtract(price.multiply(rate).multiply(BigDecimal.valueOf(19)));
            } else if (day > 600) {
                result = BigDecimal.valueOf(0.0);
            }
            return result;
        }
        return BigDecimal.valueOf(0.0);
    }


    // endregion

    // region 退款办理 refundProcessing

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SmallproNormalCodeMessageRes refundProcessing(Integer shouhouTuihuanId, OaUserBO oaUserBO) {
        SmallproNormalCodeMessageRes result = new SmallproNormalCodeMessageRes();
        ShouhouTuihuan shouhouTuihuan = shouhouTuihuanService.getByIdSqlServer(shouhouTuihuanId);
        Areainfo areainfo = areainfoService.getByIdSqlServer(oaUserBO.getAreaId());
        if (shouhouTuihuan == null || areainfo == null || (shouhouTuihuan.getIsdel() != null && shouhouTuihuan.getIsdel())) {
            result.setCode(500);
            result.setMessage("数据库中没有对应的售后退换或当前登录用户的门店信息！");
            return result;
        }
        if (shouhouTuihuan.getCheck3() != null) {
            result.setCode(500);
            result.setMessage("当前售后退换的状态不正确！");
            return result;
        }
        Boolean processingFlag = refundProcessing(result, shouhouTuihuan, areainfo, oaUserBO);
        if (processingFlag) {

        } else {
            return result;
        }
        return null;
    }


    // endregion

    // endregion

    // region transactional


    @Transactional(rollbackFor = Exception.class)
    public boolean refundSubmitWrite(SmallproNormalCodeMessageRes result,
                                     OaUserBO oaUserBO, Integer smallproId, Double inPirce,
                                     List<SmallproBasketTinyInfoBO> details,
                                     SmallproRefundSubmitReq req, List<SmallproRefundNetPayBO> netPayModelList,
                                     QueryWrapper<ShouhouTuihuan> shouhouTuihuanQueryWrapper) {
        List<ShouhouTuihuan> shouhouTuihuanList = shouhouTuihuanService.listSqlServer(shouhouTuihuanQueryWrapper);
        if (shouhouTuihuanList != null && shouhouTuihuanList.size() > 0) {
            result.setCode(NumberConstant.FIVE_HUNDRED);
            result.setMessage(StrUtil.format("已经存在退款记录！小件单号:{}",shouhouTuihuanList.stream()
                    .map(st->ObjectUtil.defaultIfNull(st.getSmallproid(),st.getShouhouId())).map(StrUtil::toString)
                    .collect(Collectors.joining(" "))));
            return false;
        }
        // 查询订单未退九机币金额 100
        // 九机币 > tuikuanM(50)  tuikuanM 0 原退款金额为九机币金额(50)
        BigDecimal tuikuanM = Objects.isNull(req.getTuikuanM()) ? BigDecimal.ZERO : req.getTuikuanM();
        BigDecimal currencyPrice = BigDecimal.ZERO;

        if (tuikuanM.compareTo(BigDecimal.ZERO) < 0) {
            result.setCode(NumberConstant.FIVE_HUNDRED);
            result.setMessage("退款金额不能为负！");
            return false;
        }
        Integer subId = req.getSubId();
        String jiujiCoinValue = sysConfigService.getValueByCode(JIUJI_COIN);
        boolean jiujiCoinFlag = false;
        BigDecimal jiuJiCoinSum = BigDecimal.ZERO;
        if (StringUtils.isNotEmpty(jiujiCoinValue)) {
            jiuJiCoinSum = shouhouTuihuanMapper.getJiuJiCoinSum(subId, jiujiCoinValue);
            if (jiuJiCoinSum.compareTo(BigDecimal.ZERO) > 0) {
                jiujiCoinFlag = true;
                if (jiuJiCoinSum.compareTo(tuikuanM) >= 0) {
                    currencyPrice = tuikuanM;
                    tuikuanM = BigDecimal.ZERO;
                } else {
                    currencyPrice = jiuJiCoinSum;
                    tuikuanM = tuikuanM.subtract(jiuJiCoinSum);
                }
            }

        }
        //记录日志
        StringBuilder content = new StringBuilder("提交退款办理，退款金额：")
                .append(tuikuanM).append(",折价金额（包含优惠码分摊金额）：")
                .append(req.getZhejiaM());

        if (jiujiCoinFlag) {
            content.append(",员工九机币:").append(currencyPrice);
        }

        // 秒退金额：如果选择的退款方式是:微信秒退 、支付宝秒退，扣除九机币后金额为0的自动修改为余额退款方式
        if (jiujiCoinFlag && tuikuanM.compareTo(BigDecimal.ZERO) ==0 &&( req.getTuiWay().equals("微信秒退") || req.getTuiWay().equals("支付宝秒退"))) {
            content.append(" 扣除员工九机币后，可退金额为0，退款方式由【").append(req.getTuiWay()).append("】自动修改为【余额】");
            req.setTuiWay("余额");
        }

        // 插入shouhou_tuihuan
        ShouhouTuihuan insertData = new ShouhouTuihuan();
        insertData.setShouhouId(smallproId)
                .setTuihuanKind(req.getTuihuanKind())
                .setInuser(oaUserBO.getUserName())
                .setAreaid(oaUserBO.getAreaId())
                .setComment(req.getComment())
                .setTuiWay(req.getTuiWay())
                .setBankname(req.getBankName())
                .setBankfuming(req.getBankfuming())
                .setBanknumber(req.getBanknumber())
                .setTuikuanM(tuikuanM)
                .setTuikuanM1(tuikuanM)
                .setCurrencyPrice(currencyPrice)
                .setZhejiaM((req.getZhejiaM() == null) ? BigDecimal.ZERO : req.getZhejiaM())
                .setSubId(subId)
                .setInprice(BigDecimal.valueOf(inPirce))
                .setBasketIds(req.getBasketIds())
                .setCoinM(req.getCoinM())
                .setSmallproid(smallproId)
                .setBaitiaoM(req.getBaitiaoM())
                .setKuBaiTiaoM(req.getKuBaitiaoM())
                .setIsValidt(req.getIsValidt() == null ? null : Boolean.parseBoolean(req.getIsValidt()));
//        if (oaUserBO.getXTenant() > 1000){
//            insertData.setTuikuanM1(req.getTuikuanM());
//        }
        boolean flag = shouhouTuihuanService.save(insertData);



        smallproLogService.addLogs(smallproId, Convert.toStr(content), oaUserBO.getUserName(), 0);

        if (flag) {
            if (req.getTuihuanKind() == 7) {
                for (SmallproBasketTinyInfoBO bo : details) {
                    ReturnsDetail returnsDetail = new ReturnsDetail();
                    returnsDetail.setShthid(insertData.getId().longValue())
                            .setBasketCount(bo.getBasketCount())
                            .setBasketID(bo.getBasketId().longValue());
                    returnsDetailService.save(returnsDetail);
                }
            }
            if (netPayModelList != null && netPayModelList.size() > 0) {
                for (SmallproRefundNetPayBO bo : netPayModelList) {
                    NetPayRefundInfo netPayRefundInfo = new NetPayRefundInfo();
                    netPayRefundInfo.setNetRecordId(bo.getId())
                            .setPrice(bo.getPrice())
                            .setReturnid(insertData.getId())
                            .setInuser(oaUserBO.getUserName())
                            .setDtime(LocalDateTime.now());
                    // 这里提交退款的时候，没有做金额校验，提交后C#这边出现了重复退款
                    if (bo.getPrice() != null && bo.getPrice().compareTo(BigDecimal.ZERO) > 0) {
                        netPayRefundInfoService.save(netPayRefundInfo);
                    }
                    Integer updateFlag = smallproMapper.updateNetPayRecordBySmallproSubmitRefund(bo.getId()
                            , bo.getPrice().setScale(2, RoundingMode.HALF_UP));
                    if (updateFlag == null || updateFlag <= 0) {
                        TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                        result.setCode(500);
                        result.setMessage("数据库更新NetPayRecord失败！支付金额退款错误！");
                        return false;
                    }
                }
            }
            //根据审核配置自动审核
            ShouhouTuihuanService shouhouTuihuanService = SpringUtil.getBean(ShouhouTuihuanService.class);
            shouhouTuihuanService.autoTuihuanCheck(insertData.getId());
        } else {
            result.setCode(500);
            result.setMessage("数据库插入售后退换失败！");
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return false;
        }
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    public boolean refundCheck2Write(SmallproNormalCodeMessageRes result, ShouhouTuihuan shouhouTuihuan) {
        boolean flag = shouhouTuihuanService.updateById(shouhouTuihuan);
        if (!flag) {
            result.setCode(500);
            result.setMessage("数据库更新失败！");
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return false;
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean cancelRefundCheckWrite(SmallproNormalCodeMessageRes result, ShouhouTuihuan shouhouTuihuan) {
        boolean flag = shouhouTuihuanService.deleteById(shouhouTuihuan);
        if (flag) {
            QueryWrapper<ReturnsDetail> returnsDetailQueryWrapper = new QueryWrapper<>();
            returnsDetailQueryWrapper.lambda().eq(ReturnsDetail::getShthid, shouhouTuihuan.getId());
            boolean deleteFlag = returnsDetailService.remove(returnsDetailQueryWrapper);
            // 删除支付批次
            Integer netPayDeleteFlag = smallproMapper.deleteNetPayRecordByShouhouTuihuanId(shouhouTuihuan.getId());
            Integer netPayRefundInfoFlag = smallproMapper.updateNetPayRefundInfoWhenCancelRefundCheck(shouhouTuihuan.getId());
        } else {
            result.setCode(500);
            result.setMessage("售后退换数据库删除失败！");
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return false;
        }
        return true;
    }

    // endregion

    // region privateMethod

    private Long CalculateRefundIntegral(Integer subId, BigDecimal price, Integer isShouhou) {
        Long result = 0L;
        Long allIntegral = 0L;
        BigDecimal allPrice;
        switch (isShouhou) {
            case 0:
                allIntegral = smallproMapper.getPointsBySubId(subId,
                        price.setScale(2, RoundingMode.HALF_DOWN).doubleValue());
                allPrice = smallproMapper.getAllPriceBySubId(subId);
                BigDecimal lv = BigDecimal.ZERO;
                if (allPrice.compareTo(BigDecimal.ZERO) > 0) {
                    lv = price.divide(allPrice, NumberConstant.FOUR, BigDecimal.ROUND_HALF_UP);
                    result = (long) Math.floor(allIntegral * lv.doubleValue());
                }
                if (lv.compareTo(BigDecimal.valueOf(0.5)) > 0 || lv.compareTo(BigDecimal.valueOf(0.5)) == 0) {
                    Sub sub = subService.getByIdSqlServer(subId);
                    BigDecimal youhuiPrice = sub.getYouhui1M();
                    if (youhuiPrice.compareTo(BigDecimal.ZERO) > 0) {
                        Integer updateSize = smallproMapper.updateNumberCardByRefund1(subId);
                        if (updateSize > 0) {
                            ArrayList<Integer> cardTypeList = new ArrayList<>();
                            cardTypeList.add(JiujiNumberCardTypeEnum.JIUJI_NUMBER_CARD_TYPE_8.getCode());
                            cardTypeList.add(JiujiNumberCardTypeEnum.JIUJI_NUMBER_CARD_TYPE_9.getCode());
                            cardTypeList.add(JiujiNumberCardTypeEnum.JIUJI_NUMBER_CARD_TYPE_34.getCode());
                            cardTypeList.add(JiujiNumberCardTypeEnum.JIUJI_NUMBER_CARD_TYPE_15.getCode());
                            cardTypeList.add(JiujiNumberCardTypeEnum.JIUJI_NUMBER_CARD_TYPE_13.getCode());
                            smallproMapper.deleteNumberCardByRefund1(cardTypeList, subId);
                            smallproMapper.deleteCardLogsByRefund1(subId);
                        }
                    }
                }
                if (result > allPrice.longValue()) {
                    result = allPrice.longValue();
                }
                break;
            case 1:
                allPrice = smallproMapper.getYingfuMFromRecoverMarketInfoByRefund(subId);
                allIntegral = smallproMapper.getPointByRefund1(subId);
                if (allPrice.compareTo(BigDecimal.ZERO) > 0 && price.compareTo(BigDecimal.ZERO) > 0 && allIntegral > 0) {
                    result = (long) Math.floor(allIntegral * ((price.divide(allPrice, NumberConstant.FOUR, BigDecimal.ROUND_HALF_UP)).doubleValue()));
                }
                break;
            default:
                break;
        }
        return result;
    }

    // #point 退款办理暂时走老接口
    private boolean refundProcessing(SmallproNormalCodeMessageRes result, ShouhouTuihuan shouhouTuihuan,
                                     Areainfo areainfo, OaUserBO oaUserBO) {
        boolean flag = true;
        List<String> rankList = oaUserBO.getRank();
        String curUser = oaUserBO.getUserName();
        Integer curAreaId = oaUserBO.getAreaId();
        Integer areaKind1 = areainfo.getKind1();
        String productName = "";
        String saveMoneyComment = "";
        Integer saveMoneySubId = 0;
        String text = "";
        Integer area = 0;
        String mobile = "";
        Integer userId = 0;
        Integer subId = 0;
        Integer tuiMobile_basketID = 0;
        Integer newSubId = 0;
        // 金额相关
        BigDecimal servicePrice = BigDecimal.ZERO;
        BigDecimal baitiaoPirce = BigDecimal.ZERO;
        BigDecimal zhongyiPrice = BigDecimal.ZERO;
        BigDecimal hebaoPrice = BigDecimal.ZERO;
        BigDecimal zero = BigDecimal.ZERO;
        OaApiReq req = new OaApiReq();
        req.setTimeStamp(LocalDateTime.now().toEpochSecond(ZoneOffset.of("+8")));
        // region 权限判断
        if (!rankList.contains("777")) {
            if ("银行转账".equals(shouhouTuihuan.getTuiWay())) {
                if (!rankList.contains("92")) {
                    result.setCode(500);
                    result.setMessage("无权限！权值：92");
                    return false;
                }
                if (!rankList.contains("27")) {
                    result.setCode(500);
                    result.setMessage("无权限！权值：27");
                    return false;
                }
                if (!rankList.contains("11")) {
                    result.setCode(500);
                    result.setMessage("无权限！权值：11");
                    return false;
                }
            }
        }
        // endregion

        Smallpro smallpro = smallproService.getByIdSqlServer(shouhouTuihuan.getShouhouId());
        List<SmallproRefundProcessingInfoBO> smallproRefundProcessingInfoBOList = null;
        switch (shouhouTuihuan.getTuihuanKind()) {
            case 7:
                subId = shouhouTuihuan.getSubId();
                baitiaoPirce = shouhouTuihuan.getBaitiaoM();
                smallproRefundProcessingInfoBOList =
                        smallproMapper.getSmallproRefundProcessingInfo(
                                shouhouTuihuan.getSubId(), shouhouTuihuan.getId());
                boolean isHis = false;
                if (smallproRefundProcessingInfoBOList == null || smallproRefundProcessingInfoBOList.isEmpty()) {
                    smallproRefundProcessingInfoBOList =
                            smallproMapper.getSmallproRefundProcessingInfoWithNoOne(shouhouTuihuan.getSubId());
                    if (smallproRefundProcessingInfoBOList != null && smallproRefundProcessingInfoBOList.size() > 0) {
                        QueryWrapper<ReturnsDetail> returnsDetailQueryWrapper = new QueryWrapper<>();
                        returnsDetailQueryWrapper.lambda().eq(ReturnsDetail::getShthid, shouhouTuihuan.getId());
                        List<ReturnsDetail> returnsDetailList =
                                returnsDetailService.listSqlServer(returnsDetailQueryWrapper);
                        if (returnsDetailList != null && returnsDetailList.size() > 0) {
                            isHis = true;
                            boolean forFlag = false;
                            for (ReturnsDetail temp : returnsDetailList) {
                                for (SmallproRefundProcessingInfoBO bo : smallproRefundProcessingInfoBOList) {
                                    if (temp.getBasketID().equals(bo.getBasketId())) {
                                        bo.setBasketCount(temp.getBasketCount());
                                        forFlag = true;
                                        break;
                                    }
                                }
                                if (forFlag) {
                                    forFlag = false;
                                    break;
                                }
                            }
                        }
                    }
                } else if (smallproRefundProcessingInfoBOList.size() > 0) {
                    BigDecimal price = BigDecimal.ZERO;
                    mobile = smallproRefundProcessingInfoBOList.get(0).getSubMobile();
                    area = smallproRefundProcessingInfoBOList.get(0).getAreaId();
//                    if (!isAreaid)
                    if (false) {
                        area = curAreaId;
                    }
                    userId = smallproRefundProcessingInfoBOList.get(0).getUserId();
                    saveMoneySubId = shouhouTuihuan.getSubId();
                    saveMoneyComment = "退配件，单号：" + shouhouTuihuan.getSubId();
                    BigDecimal subCoinM = smallproRefundProcessingInfoBOList.get(0).getCoinM();
                    BigDecimal tuiCoinM = shouhouTuihuan.getCoinM();
                    if (tuiCoinM.compareTo(subCoinM) > 0) {
                        result.setCode(500);
                        String printName = (areainfo.getPrintName().equals("九机网")) ? "九机" :
                                areainfo.getPrintName();
                        result.setMessage("所退" + printName + "币大于实际支付" + printName + "币" + tuiCoinM.doubleValue() +
                                ":" + subCoinM.doubleValue());
                        return false;
                    }
                    List<Integer> basketidStr = new ArrayList<>(smallproRefundProcessingInfoBOList.size());
                    List<Integer> service_basket_id =
                            new ArrayList<>(smallproRefundProcessingInfoBOList.size());
                    List<Integer> installService_basket_id =
                            new ArrayList<>(smallproRefundProcessingInfoBOList.size());
                    productName =
                            smallproRefundProcessingInfoBOList.get(0).getProductName() + " " + smallproRefundProcessingInfoBOList.get(0).getProductColor();
                    int returnCount = 0;
                    for (SmallproRefundProcessingInfoBO bo : smallproRefundProcessingInfoBOList) {
                        basketidStr.add(bo.getBasketId().intValue());
                        price = price.add(bo.getPrice().multiply(BigDecimal.valueOf(bo.getBasketCount())));
                        returnCount += bo.getBasketCount();
                        if (smallproRefundConstant.getServicePPIds().contains(bo.getPpriceId1())) {
                            service_basket_id.add(bo.getBasketId().intValue());
                        }
                        if (smallproRefundConstant.getInstallServicesPPIds().contains(bo.getPpriceId1())) {
                            installService_basket_id.add(bo.getBasketId().intValue());
                        }
                    }
                    if (smallproRefundProcessingInfoBOList.size() > 1) {
                        productName += "，等" + returnCount + "件商品";
                    }
                    if (installService_basket_id.size() > 0) {
                        QueryWrapper<InstallServicesRecord> installServicesRecordQueryWrapper =
                                new QueryWrapper<>();
                        installServicesRecordQueryWrapper.lambda()
                                .and(temp -> temp.eq(InstallServicesRecord::getIsdel, 0)
                                        .or().isNull(InstallServicesRecord::getIsdel))
                                .eq(InstallServicesRecord::getStats, 1)
                                .in(InstallServicesRecord::getBasketId, installService_basket_id);
                        List<InstallServicesRecord> installServicesRecordList =
                                installServicesRecordService.listSqlServer(installServicesRecordQueryWrapper);
                        if (installServicesRecordList != null && installServicesRecordList.size() > 0) {
                            result.setCode(500);
                            result.setMessage("上门安装服务已使用，不允许退货！");
                            return false;
                        }
                    }
                    if (shouhouTuihuan.getComment().contains("【退手续费】")) {
                        price = price.add(smallproRefundProcessingInfoBOList.get(0).getShouxuM());
                    }
                    if (shouhouTuihuan.getComment().contains("【退运费】")) {
                        price = price.add(smallproRefundProcessingInfoBOList.get(0).getFeeM());
                    }
                    if (price.compareTo(shouhouTuihuan.getTuikuanM()) < 0) {
                        result.setCode(500);
                        result.setMessage("退款金额大于付款金额！");
                        return false;
                    }
                    long subId1 = smallproRefundProcessingInfoBOList.get(0).getSubId();
                    if (price.compareTo(BigDecimal.ZERO) > 0) {
                        // #todo 扣减积分规则

//                            var realReduceJifenM = CalculateRefundJifen(cmd, dr["sub_id"].ToIntDB(), price);
//                            if (realReduceJifenM > 0)
//                            {
//                                        #todo
//                                dal.JifenManageControl.jifenManage1(new jifenModel()
//                                {
//                                    userid = userid,
//                                    comment = "退货",
//                                    inuser = curUser,
//                                    isinput = -1,
//                                    price = price,
//                                    jifen = realReduceJifenM,
//                                    sub_id = dr["sub_id"].ToString()
//                                }, conn, cmd, tran);
//                            }
                    }

                    if (!isHis) {
                        Integer subOrder =
                                smallproMapper.getRefundSubCountInfo(shouhouTuihuan.getSubId(),
                                        shouhouTuihuan.getId(), tuiMobile_basketID);
                        Sub selectSub = subService.getByIdSqlServer(shouhouTuihuan.getSubId());

                        // 拆单退款
                        if (subOrder != null && subOrder != 0) {
                            BigDecimal totalPrice = BigDecimal.ZERO;
                            for (SmallproRefundProcessingInfoBO temp : smallproRefundProcessingInfoBOList) {
                                totalPrice =
                                        totalPrice.add(temp.getPrice().multiply(BigDecimal.valueOf(temp.getBasketId())));
                            }
                            Sub insertSub = new Sub();
                            insertSub.setSubDate(selectSub.getSubDate()).setSubCheck(9).setSubTo(selectSub.getSubTo())
                                    .setSubTel(selectSub.getSubTel()).setSubPay(selectSub.getSubPay()).setComment(selectSub.getComment())
                                    .setInUser(oaUserBO.getUserName()).setSubMobile(selectSub.getSubMobile()).setAreaId(selectSub.getAreaId())
                                    .setZitidianId(selectSub.getZitidianId()).setUserId(selectSub.getUserId()).setOnlinePay(selectSub.getOnlinePay())
                                    .setMarketingId(selectSub.getMarketingId()).setSubtype(selectSub.getSubtype()).setDelivery(selectSub.getDelivery())
                                    .setYingfuM(totalPrice).setYifuM(zero).setFeeM(zero).setYouhui1M(zero)
                                    .setShouxuM(zero).setJidianM(zero).setSubPid(selectSub.getSubId())
                                    .setTradeDate(selectSub.getTradeDate()).setTradeDate1(selectSub.getTradeDate1()).setReturnDate(LocalDateTime.now());
                            boolean saveFlag = subService.save(insertSub);
                            newSubId = insertSub.getSubId();
                            if (!saveFlag) {
                                result.setCode(500);
                                result.setMessage("拆单退款，新增子订单时数据库错误！");
                                return false;
                            }
                            if (tuiCoinM.compareTo(BigDecimal.ZERO) > 0 && tuiCoinM.compareTo(subCoinM) == 0) {
                                // #todo
//退配件 如果使用了九机币 退换九机币
//                                var coinResult = new coinManageServices().ValidtNumberCardIsCoin(userid.ToIntDB(),
//                                        dr["sub_id"].ToIntDB(), curAreaid, curUser, E.eCoinKinds.订单退货, 1, cmd,
//                                        tuiCoinM);
//                                if (coinResult.stats != 1)
//                                {
//                                    json.msg = coinResult.msg;
//
//                                    istrue = false;
//                                    return json;
//                                }


                            }
                            BigDecimal amountDiff = totalPrice.subtract(tuiCoinM);
                            BigDecimal coinDiff = subCoinM.subtract(tuiCoinM);
                            selectSub.setYingfuM(selectSub.getYingfuM().subtract(amountDiff))
                                    .setYifuM(selectSub.getYifuM().subtract(amountDiff))
                                    .setCoinM(coinDiff);
                            boolean updateFlag = subService.updateById(selectSub);
                            SubLogsNewReq reqOld = new SubLogsNewReq();
                            reqOld.setInUser("系统");
                            reqOld.setSubId(smallproRefundProcessingInfoBOList.get(0).getSubId());
                            reqOld.setShowType(true);
                            reqOld.setType(1);
                            //原订单
                            reqOld.setComment("退款办理，系统自动拆单，子订单【<a href='/addOrder/editOrder?SubID=" + newSubId
                                    + "' title='点击查看订单详细' >" + newSubId + "</a>】");
                            R<Boolean> resOld = subLogsCloud.addSubLog(reqOld);
                            reqOld.setSubId(newSubId);
                            reqOld.setComment("退款办理，系统自动拆单，原始订单【<a href='/addOrder/editOrder?SubID="
                                    + smallproRefundProcessingInfoBOList.get(0).getSubId()
                                    + "' title='点击查看订单详细' >" + smallproRefundProcessingInfoBOList.get(0).getSubId() + "</a>】");
                            resOld = subLogsCloud.addSubLog(reqOld);
                        }
                        // 整单退款
                        else {
                            if (subCoinM.compareTo(zero) > 0) {
                                JiujiCoinItemReq jiujiCoinItemReq = new JiujiCoinItemReq();
                                jiujiCoinItemReq.setUserid(oaUserBO.getUserId()).setSub_id(shouhouTuihuan.getSubId())
                                        .setAreaid(oaUserBO.getAreaId()).setInuser(oaUserBO.getUserName())
                                        .setKinds((byte) 3).setType(1).setCoins(subCoinM.doubleValue()).setPrices(0.0);
                                req.setData(jiujiCoinItemReq);
                                req.setSign(OaVerifyUtil.createSign(
                                        SmallProRelativePathConstant.PAY_JIUJI_COIN_SIGN_SECRET, req));
                                String url = sysConfigService.getValueByCode(SysConfigConstant.MOA_URL) + SmallProRelativePathConstant.PAY_JIUJI_COIN;
//                                String url = moaUrlSource.getPayJiujiCoin();
                                Map<String, String> headMap = new HashMap<>(1);
                                headMap.put("Authorization", oaUserBO.getToken());
                                String jiujiCoinResult = HttpClientUtil.post(url, JSONObject.toJSONString(req),
                                        headMap);
                            }
                            selectSub.setSubCheck(9).setReturnDate(LocalDateTime.now()).setYifuM(zero).setJidianM(zero)
                                    .setCoinM(zero);
                            boolean updateFlag = subService.updateById(selectSub);
                            if (updateFlag) {
                                Integer ch999UserId =
                                        smallproMapper.getCh999UserIdForJifen(shouhouTuihuan.getSubId());
                                // #todo
//如果使用积点大于0 删除订单返积分
//                                    jifenModel jifen = new jifenModel()
//                                    {
//                                        userid = userid,
//                                        sub_id = id.ToString(),
//                                        jifen = long.Parse(Math.Round(objsub.toDecimalDB()).ToString()) * 100,
// 每个积点100个积分
//                                        inuser = "系统",
//                                        comment = "订单退款，积分返回",
//                                        isinput = 2,
//                                        price = 0
//                                    };
//                                    dal.JifenManageControl.jifenManage1(jifen, conn, cmd, tran);
                            }
                        }
                    }
                    if (flag) {
                        Integer updateSize = 0;
                        StringBuilder comment = new StringBuilder();
                        for (SmallproRefundProcessingInfoBO bo : smallproRefundProcessingInfoBOList) {
                            if (!isHis) {
                                comment.append(bo.getProductName())
                                        .append(bo.getProductColor())
                                        .append(" 退货操作，数量：")
                                        .append(bo.getBasketCount())
                                        .append("，单价：")
                                        .append(bo.getPrice().doubleValue())
                                        .append(",");
                                //联通卡号退款
                                if (smallproRefundConstant.getCardNumberCids().contains(bo.getCId())) {
                                    updateSize =
                                            smallproMapper.updateTaocanByRefund1(bo.getBasketId().intValue());
                                    updateSize =
                                            smallproMapper.updateTaocanByRefund2(bo.getBasketId().intValue());
                                    if (bo.getCId().equals(52)) {
                                        updateSize =
                                                smallproMapper.updateTaocanByRefund3(bo.getBasketId().intValue());
                                    }
                                }
                                //靓号
                                if (smallproRefundConstant.getHaomaPPIds().contains(bo.getPpriceId())) {
                                    updateSize =
                                            smallproMapper.updateHaomaByRefund(bo.getBasketId().intValue());
                                }
                                if (bo.getBasketCount().equals(bo.getBasketCount1()) && newSubId != 0) {
                                    updateSize = smallproMapper.updateBasketByRefund1(newSubId,
                                            bo.getBasketId().intValue());
                                    updateSize = smallproMapper.updateShouhouTuihuanByRefund1(newSubId,
                                            shouhouTuihuan.getId());
                                } else if (!bo.getBasketCount1().equals(bo.getBasketCount1()) && newSubId != 0) {
                                    updateSize = smallproMapper.updateBasketByRefund2(bo.getBasketCount(),
                                            bo.getBasketId().intValue());

                                    Basket selectBasket =
                                            basketService.getByIdSqlServer(bo.getBasketId().intValue());
                                    if (selectBasket.getIsdel() == null || !selectBasket.getIsdel()) {
                                        Basket insertBasket = new Basket();
                                        insertBasket.setBasketCount(bo.getBasketCount()).setBasketDate(LocalDateTime.now())
                                                .setProductPeizhi(selectBasket.getProductPeizhi()).setSeller(selectBasket.getSeller())
                                                .setIsmobile(selectBasket.getIsmobile()).setSubId(newSubId.longValue())
                                                .setPrice1(selectBasket.getPrice1()).setPpriceid(selectBasket.getPpriceid())
                                                .setInprice(selectBasket.getInprice()).setGiftid(selectBasket.getGiftid())
                                                .setType(selectBasket.getType()).setIsdel(false).setIschu(false).setPrice2(selectBasket.getPrice());
                                        boolean saveFlag = basketService.save(insertBasket);
                                        updateSize =
                                                smallproMapper.updateReturnsDetailByRefund1(insertBasket.getBasketId(), bo.getBasketCount());
                                        updateSize = smallproMapper.updateShouhouTuihuanByRefund1(newSubId,
                                                shouhouTuihuan.getId());
                                    }
                                }
                                if (bo.getType().equals(BasketTypeEnum.BASKET_TYPE_STEEL_FILM_HALF_PRICE_REPURCHASE.getCode())) {
                                    if (bo.getBasketCount().equals(bo.getBasketCount1())) {
                                        updateSize =
                                                smallproMapper.deleteBskHalfBuyRecordByRefund1(bo.getBasketId().intValue());
                                    } else if (!bo.getBasketCount().equals(bo.getBasketCount1())) {
                                        updateSize =
                                                smallproMapper.updateBskHalfBuyRecordByRefund2(bo.getBasketCount(),
                                                        bo.getBasketId().intValue());
                                    }
                                }
                            }
                        }

                        // 操作日志
                        if (!comment.toString().equals("")) {
                            SubLogsNewReq reqOld = new SubLogsNewReq();
                            reqOld.setInUser(oaUserBO.getUserName());
                            reqOld.setSubId(shouhouTuihuan.getSubId());
                            reqOld.setShowType(true);
                            reqOld.setType(1);
                            //原订单
                            reqOld.setComment(comment.toString());
                            R<Boolean> resOld = subLogsCloud.addSubLog(reqOld);
                        }

                        // 三九服务
                        if (service_basket_id != null && service_basket_id.size() > 0) {
                            updateSize = smallproMapper.deleteServiceRecordByRefund1(service_basket_id);
                        }

                        // 上门安装服务作废
                        if (installService_basket_id != null && installService_basket_id.size() > 0) {
                            updateSize =
                                    smallproMapper.deleteInstallServicesRecordByRefund1(oaUserBO.getUserName(),
                                            installService_basket_id);
                        }

                        // 年包服务遍历
                        if (basketidStr != null && basketidStr.size() > 0) {
                            updateSize = smallproMapper.deleteTiemoCardByRefund1(basketidStr);
                            updateSize = smallproMapper.deleteProductSnByRefund1(basketidStr);
                            List<Integer> cardIdList =
                                    smallproMapper.getTiemoCardUserLogToDeleteByRefund(basketidStr);
                            updateSize = smallproMapper.deleteTiemoCardUserLogByRefund(basketidStr);
                            if (cardIdList != null && cardIdList.size() > 0) {
                                updateSize = smallproMapper.updateTiemoCardByRefund2(cardIdList);
                            }
                        }
                    }
                } else {
                    result.setCode(500);
                    result.setMessage("无效单号");
                    return false;
                }
                break;
            case 10:
                if (smallpro != null && (smallpro.getAreaId().equals(curAreaId) || smallpro.getToAreaId().equals(curAreaId))
                        && (smallpro.getIsTui() == null || !smallpro.getIsTui())) {
                    if (smallpro.getFeiyong().multiply(BigDecimal.valueOf(1.15)).compareTo(shouhouTuihuan.getTuikuanM()) < 0) {
                        result.setCode(500);
                        result.setMessage("退款金额大于维修费用！");
                        return false;
                    }
                }
                area = smallpro.getAreaId();
                userId = smallpro.getUserId();
                saveMoneySubId = 0;
                saveMoneyComment = "退维修费，维修单号：" + smallpro.getId();
                UpdateWrapper<Smallpro> wrapper = new UpdateWrapper<>();
                wrapper.lambda().set(Smallpro::getIsTui, 1)
                        .and(temp -> temp.eq(Smallpro::getIsTui, 0).or().isNull(Smallpro::getIsTui))
                        .and(temp -> temp.eq(Smallpro::getStats, 0).or().isNull(Smallpro::getStats))
                        .eq(Smallpro::getId, smallpro.getId());
                boolean updateFlag = smallproService.update(wrapper);
                if (updateFlag) {
                    if (areainfo.getIsSend()) {
                        QueryWrapper<WeixinUser> weixinUserQueryWrapper = new QueryWrapper<>();
                        weixinUserQueryWrapper.lambda().eq(WeixinUser::getUserid, smallpro.getUserId())
                                .eq(WeixinUser::getType, 1).eq(WeixinUser::getKinds, 1)
                                .eq(WeixinUser::getFollow, 1);
                        List<WeixinUser> weixinUserList =
                                weixinUserService.listSqlServer(weixinUserQueryWrapper);
                        if (weixinUserList != null && weixinUserList.size() > 0) {
                            String openId = weixinUserList.get(0).getOpenid();
                            Integer wxId = weixinUserList.get(0).getWxid();
                            String remark =
                                    "维修单号：" + smallpro.getId() + "\n办理时间：" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy年M月d日 H:m")) + "\n点击【详情】可查看维修单详细。";
//                            String tenant = EnumUtil.getMessageByCode(JiujiTenantEnum.class, areainfo.getXtenant().longValue());
//                            String murl = EnumUtil.getMessageByCode(JiujiJumpUrlEnum.class, tenant + "mUrl");
                            String murl = "";
                            try {
                                murl = sysConfigClient.getValueByCodeAndXtenant(SysConfigConstant.M_URL, areainfo.getXtenant()).getData();
                            } catch (Exception e) {
                                log.error("sysConfigClient 微服务调用异常", e);
                            }
                            String url = murl + "/user/mysevrdetail.aspx?id=" + smallpro.getId();
                            // #todo 发送模板消息
//                            Fun.SendWeixinMsg(wxInfo.openid, "", "您的退款需求已办理成功，请留意查收！", DateTime.Now.ToString
//                            ("M月d日"), remark, url, wxInfo.wxid); //主体已处理
                        }
                    }
                }
                break;
            default:
                result.setCode(500);
                result.setMessage("退换类别错误！");
                return false;
        }

        PingzhengResultBO pingzhengResultBO = new PingzhengResultBO();

        // 退款办理/凭证办理
        if (flag) {
            String zhaiyao = "";
            String kemu = "";
            String jief = "";
            String daif = "";
            String fzhs = "";
            Areainfo tempAreaInfo =
                    areainfoService.getByIdSqlServer(smallproRefundProcessingInfoBOList.get(0).getAreaId());
            Areainfo shouhouTuihuanAreaInfo = areainfoService.getByIdSqlServer(shouhouTuihuan.getAreaid());
            Areainfo refundArea = areainfoService.getByIdSqlServer(area);
            boolean joinPzFlag = true;
            ShouhouTuihuan updateShouhouTuihuan =
                    shouhouTuihuanService.getByIdSqlServer(shouhouTuihuan.getId());
            if (updateShouhouTuihuan.getIsdel() != null || updateShouhouTuihuan.getIsdel() || updateShouhouTuihuan.getCheck3() != null) {
                result.setCode(500);
                result.setMessage("退款申请不存在或已被删除！");
                return false;
            }
            updateShouhouTuihuan.setCheck3(true).setCheck3dtime(LocalDateTime.now()).setCheck3user(oaUserBO.getUserName());
            boolean updateFlag = shouhouTuihuanService.updateById(updateShouhouTuihuan);
            if (!updateFlag) {
                result.setCode(500);
                result.setMessage("售后退换数据库更新失败！");
                return false;
            }

            // 金额/凭证处理
            if (("余额".equals(shouhouTuihuan.getTuiWay()) || "自提点余额".equals(shouhouTuihuan.getTuiWay()))
                    && shouhouTuihuan.getTuikuanM().compareTo(zero) > 0) {
                if ("自提点余额".equals(shouhouTuihuan.getTuiWay())) {
                    Integer bbsxpUserId =
                            smallproMapper.getBBSXPUserIdByRefund(shouhouTuihuan.getSubId());
                    if (bbsxpUserId == null) {
                        result.setCode(500);
                        result.setMessage("自提点账户查找失败！");
                        return false;
                    }
                    userId = bbsxpUserId;
                    String url = sysConfigService.getValueByCode(SysConfigConstant.IN_WCF_HOST) + SmallProRelativePathConstant.MONEY_SAVE;
                    Map<String, String> headers = new HashMap<>();
                    headers.put("Content-Type", "application/json");
                    SaveMoneyInfoBO saveMoneyInfoBO = new SaveMoneyInfoBO();
                    saveMoneyInfoBO.setUserid(userId).setAmount(shouhouTuihuan.getTuikuanM().doubleValue()).setEkind(SaveMoneyEkindEnum.SAVE_MONEY_EKIND_8.getCode()).setSubid(saveMoneySubId).setMemo(saveMoneyComment);
                    SaveMoneyInfoReqBO saveMoneyInfoReqBO = new SaveMoneyInfoReqBO().setItem(saveMoneyInfoBO);
                    String params = JSONObject.toJSONString(saveMoneyInfoReqBO);
                    String apiResult = HttpClientUtil.post(url, params, headers);
                    if (apiResult != null) {
                        JSONObject jsonObject = JSONObject.parseObject(JSONObject.parse(apiResult).toString());
                        if (jsonObject.get("code") == null || !jsonObject.get("code").toString().equals("0")) {
                            result.setCode(500);
                            result.setMessage("请求操作余额接口失败！返回信息：" + apiResult);
                            return false;
                        }
                    } else {
                        result.setCode(500);
                        result.setMessage("请求操作余额接口失败！");
                        return false;
                    }
                }


                // 如果订单为加盟店，订单，加盟店余额增加
                boolean joinFlag = false;
                if (("余额".equals(shouhouTuihuan.getTuiWay()) || (
                        smallproRefundConstant.getShopTuiWaiy().contains(shouhouTuihuan.getTuiWay()) && shouhouTuihuan.getTuihuanKind() == 7))
                        && shouhouTuihuan.getTuihuanKind() != 8 && area != 0 && joinFlag) {
//                    // 如果订单为加盟店 订单 加盟店余额增加
//                    long userid_ = 0;
//                    query = "select isnull(userid,0) from areainfo where id='" + area + "' and kind1<>1 ";
//                    cmd.CommandText = query;
//                    userid_ = cmd.ExecuteScalar().ToLongDB();
//                    if (userid_ != 0)
//                    {
//                        joinFlag = true;
//                        string result_ = dal.save_money(userid_, save_moneysubid.ToLongDB(), 0 - dr["tuikuanM"]
//                        .toDecimalDB(), curUser, "加盟店：" + save_moneycomment, E.eSaveMoneyKind.退款, curAreaid, cmd,
//                        conn, tran);
//                        if (result_ != "1")
//                        {
//                            json.msg = result_;
//                            return json;
//                        }
//                    }
                }

                SmallproRefundShopMallInfoBO smallproRefundShopMallInfoBO = null;
                if (smallproRefundProcessingInfoBOList != null && smallproRefundProcessingInfoBOList.size() > 0) {
                    if (tempAreaInfo.getKind1() == 3 && shouhouTuihuan.getTuihuanKind() == 7) {
                        BigDecimal kMoney = BigDecimal.ZERO;
                        BigDecimal pMoney = shouhouTuihuan.getTuikuanM();
                        BigDecimal pOemMoney = BigDecimal.ZERO;
                        for (SmallproRefundProcessingInfoBO bo : smallproRefundProcessingInfoBOList) {
                            if (bo.getPrice().compareTo(bo.getOemPrice()) > 0) {
                                kMoney = kMoney.add(bo.getPrice()).subtract(bo.getOemPrice());
                                pOemMoney = pOemMoney.add(bo.getOemPrice());
                            }
                        }
                        if (kMoney.compareTo(zero) > 0) {
                            if (tempAreaInfo.getKind1() == 3 && tempAreaInfo.getUserid() != null && tempAreaInfo.getUserid() > 0) {
                                String url = sysConfigService.getValueByCode(SysConfigConstant.IN_WCF_HOST) + SmallProRelativePathConstant.MONEY_SAVE;
                                Map<String, String> headers = new HashMap<>();
                                headers.put("Content-Type", "application/json");
                                SaveMoneyInfoBO saveMoneyInfoBO = new SaveMoneyInfoBO();
                                saveMoneyInfoBO.setUserid(tempAreaInfo.getUserid()).setAmount(-(kMoney.doubleValue()))
                                        .setEkind(SaveMoneyEkindEnum.SAVE_MONEY_EKIND_8.getCode()).setSubid(saveMoneySubId).setMemo("代小店退配件，扣除售价-OEM价部分：" + kMoney.doubleValue() + "元");
                                SaveMoneyInfoReqBO saveMoneyInfoReqBO =
                                        new SaveMoneyInfoReqBO().setItem(saveMoneyInfoBO);
                                String params = JSONObject.toJSONString(saveMoneyInfoReqBO);
                                String apiResult = HttpClientUtil.post(url, params, headers);
                                if (apiResult != null) {
                                    JSONObject jsonObject =
                                            JSONObject.parseObject(JSONObject.parse(apiResult).toString());
                                    if (jsonObject.get("code") == null || !jsonObject.get("code").toString().equals(
                                            "0")) {
                                        result.setCode(500);
                                        result.setMessage("请求操作余额接口失败！返回信息：" + apiResult);
                                        return false;
                                    }
                                    smallproRefundShopMallInfoBO = new SmallproRefundShopMallInfoBO();
                                    smallproRefundShopMallInfoBO.setArea(tempAreaInfo.getArea()).setAreaId(tempAreaInfo.getId())
                                            .setSellPrice(BigDecimal.valueOf(Math.abs(kMoney.doubleValue())))
                                            .setCostPrice(BigDecimal.valueOf(Math.abs(pOemMoney.doubleValue())));
                                } else {
                                    result.setCode(500);
                                    result.setMessage("请求操作余额接口失败！");
                                    return false;
                                }
                            }
                        }
                    }
                }

                // region 九机小店生成凭证
                if (smallproRefundShopMallInfoBO != null) {
                    kemu = "";
                    fzhs = "";
                    if ("现金".equals(shouhouTuihuan.getTuiWay()) &&
                            (areainfo.getKind1() == 1 || smallproRefundConstant.getSyArea().contains(area)
                                    || smallproRefundConstant.getBsArea().contains(area) || smallproRefundConstant.getHunanAreaAuthorizeId().contains(refundArea.getAuthorizeid()))
                    ) {
                        if (smallproRefundConstant.getBsArea().contains(area)) {
                            kemu = "112201";
                        } else {
                            kemu = "112203";
                        }
                        fzhs = shouhouTuihuanAreaInfo.getArea();
                    } else if ("银行转账".equals(shouhouTuihuan.getTuiWay())) {
                        if (areainfo.getKind1() == 1) {
                            kemu = "1012104";
                            fzhs = "无";
                        } else if (smallproRefundConstant.getSyArea().contains(area)) {
                            kemu = "101505";
                            fzhs = "无";
                        } else if (smallproRefundConstant.getHunanAreaAuthorizeId().contains(refundArea.getAuthorizeid())) {
                            kemu = "1002011";
                            fzhs = "无";
                        } else if (smallproRefundConstant.getBsArea().contains(area)) {
                            kemu = "101201";
                            fzhs = "无";
                        } else if (JiujiTenantEnum.JIUJI_TENANT_YAYA.getCode().equals(areainfo.getXtenant().longValue())) {
                            kemu = "101206";
                            fzhs = "无";
                        }
                    } else if ("刷卡返回".equals(shouhouTuihuan.getTuiWay()) && areainfo.getKind1() == 1) {
                        kemu = "112204";
//                        fzhs = apiServices.getAreaByID(11);
                        fzhs = "YN_bn";
                    } else if ("支付宝返回".equals(shouhouTuihuan.getTuiWay())) {
                        kemu = JiujiPingzhengKemuEnum.JIUJI_PINGZHENG_KEMU_ALIPAY.getMessage();
                        fzhs = "无";
                        if (smallproRefundConstant.getSyArea().contains(area)) {
                            kemu = "112303";
                            fzhs = "无";
                        } else if (smallproRefundConstant.getHunanAreaAuthorizeId().contains(refundArea.getAuthorizeid())) {
                            kemu = "122120";
                            fzhs = "无";
                        } else if (smallproRefundConstant.getBsArea().contains(area)) {
                            kemu = "112301";
                            fzhs = "无";
                        }
                    } else if ("余额".equals(shouhouTuihuan.getTuiWay()) && !smallproRefundConstant.getSyArea().contains(area) && !smallproRefundConstant.getHunanAreaAuthorizeId().contains(refundArea.getAuthorizeid())) {
                        kemu = "220301";
                        fzhs = "无";
                    } else if ("自提点余额".equals(shouhouTuihuan.getTuiWay()) && areainfo.getKind1() == 1) {
                        kemu = "220301";
                        fzhs = "无";
                    } else if ("微信返回".equals(shouhouTuihuan.getTuiWay())) {
                        kemu = "122112";
                        fzhs = "无";
                        if (smallproRefundConstant.getSyArea().contains(area)) {
                            kemu = "112303";
                            fzhs = "无";
                        } else if (smallproRefundConstant.getHunanAreaAuthorizeId().contains(refundArea.getAuthorizeid())) {
                            kemu = "122120";
                            fzhs = "无";
                        }
                    } else if ("ApplePay返回".equals(shouhouTuihuan.getTuiWay())) {
                        kemu = "1221101";
                        fzhs = "无";
                    } else if ("支付宝（兴业）返回".equals(shouhouTuihuan.getTuiWay())) {
                        kemu = "122118";
                        fzhs = "无";
                    } else if ("支付宝(pay1)返回".equals(shouhouTuihuan.getTuiWay())) {
                        kemu = "1002225";
                        fzhs = "无";
                    } else if ("支付宝(dzpay)返回".equals(shouhouTuihuan.getTuiWay())) {
                        kemu = "100231";
                        fzhs = "无";
                    } else if ("兴业扫码(叁玖)返回".equals(shouhouTuihuan.getTuiWay())) {
                        kemu = "101867";
                        fzhs = "无";
                    } else if ("浦发扫码(92653)返回".equals(shouhouTuihuan.getTuiWay())) {
                        kemu = "122127";
                        fzhs = "无";
                    } else if ("浦发扫码(92427)返回".equals(shouhouTuihuan.getTuiWay())) {
                        kemu = "122128";
                        fzhs = "无";
                    } else if ("中信扫码(06306)返回".equals(shouhouTuihuan.getTuiWay())) {
                        kemu = "1221110";
                        fzhs = "无";
                    } else if ("平安扫码(39878)返回".equals(shouhouTuihuan.getTuiWay())) {
                        kemu = "1221117";
                        fzhs = "无";
                    } else if ("首信易扫码支付返回".equals(shouhouTuihuan.getTuiWay())) {
                        kemu = "1221118";
                        fzhs = "无";
                    } else if ("中信扫码(06305)返回".equals(shouhouTuihuan.getTuiWay())) {
                        kemu = "1221111";
                        fzhs = "无";
                    } else if ("浦发扫码(93057)返回".equals(shouhouTuihuan.getTuiWay())) {
                        kemu = "1221113";
                        fzhs = "无";
                    } else if ("中信扫码(74026)返回".equals(shouhouTuihuan.getTuiWay())) {
                        kemu = "1221114";
                        fzhs = "无";
                    } else if ("中信扫码(25762)返回".equals(shouhouTuihuan.getTuiWay())) {
                        kemu = "1221115";
                        fzhs = "无";
                    } else if ("京东返回".equals(shouhouTuihuan.getTuiWay())) {
                        kemu = "101723";
                        fzhs = "无";
                        if (smallproRefundConstant.getSyArea().contains(area)) {
                            kemu = "112303";
                            fzhs = "无";
                        }
                    } else if ("兴业银行扫码返回".equals(shouhouTuihuan.getTuiWay())) {
                        kemu = "122118";
                        fzhs = "无";
                        if (smallproRefundConstant.getSyArea().contains(area)) {
                            kemu = "112303";
                            fzhs = "无";
                        } else if (smallproRefundConstant.getHunanAreaAuthorizeId().contains(refundArea.getAuthorizeid())) {
                            kemu = "122120";
                            fzhs = "无";
                        }
                    } else if ("浦发扫码返回".equals(shouhouTuihuan.getTuiWay())) {
                        kemu = "122124";
                        fzhs = "无";
                        if (smallproRefundConstant.getSyArea().contains(area)) {
                            kemu = "112303";
                            fzhs = "无";
                        } else if (smallproRefundConstant.getHunanAreaAuthorizeId().contains(refundArea.getAuthorizeid())) {
                            kemu = "122120";
                            fzhs = "无";
                        }
                    } else if ("微信秒退".equals(shouhouTuihuan.getTuiWay())) {
                        kemu = "122119";
                        fzhs = "无";
                        if (smallproRefundConstant.getSyArea().contains(area)) {
                            kemu = "112303";
                            fzhs = "无";
                        } else if (smallproRefundConstant.getHunanAreaAuthorizeId().contains(refundArea.getAuthorizeid())) {
                            kemu = "122120";
                            fzhs = "无";
                        }
                    } else if ("支付宝秒退".equals(shouhouTuihuan.getTuiWay())) {
                        kemu = "100231";
                        fzhs = "无";
                    }
                    // 生成凭证
                    double costPrice =
                            smallproRefundShopMallInfoBO.getCostPrice().setScale(2, RoundingMode.HALF_UP).doubleValue();
                    double sellPrice =
                            smallproRefundShopMallInfoBO.getSellPrice().setScale(2, RoundingMode.HALF_UP).doubleValue();
                    String tempZy =
                            "代" + smallproRefundShopMallInfoBO.getArea() + "小店退款，小件单号：" + shouhouTuihuan.getSmallproid();
                    String sfzhs =
                            "无|" + "HQ" + "|" + smallproRefundShopMallInfoBO.getArea() + "|" + "HQ" + "|" + fzhs;
                    String szhaiyao = tempZy + "|" + tempZy + "|" + tempZy + "|" + tempZy + "|" + tempZy;
                    String skemu = "140502|640115|220405|600117|" + kemu;
                    String sjief = costPrice + "|-" + costPrice + "|" + sellPrice + "|0|0";
                    String sdaif = "0|0|0|-" + costPrice + "|" + (costPrice + sellPrice);


                    pingzhengResultBO = voucherService.addPingZheng(voucherService.buildPingzheng(szhaiyao, skemu,
                            sjief, sdaif, sfzhs));
                    if (!pingzhengResultBO.getFlag()) {
                        //#todo 接口补偿操作
                        TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                        return false;
                    }
                }
                // endregion
                // region 非九机小店生成凭证
                else {
                    String ztid = "1";
                    if (smallproRefundConstant.getSyArea().contains(area)) {
                        ztid = "12";
                    } else if (smallproRefundConstant.getBsArea().contains(area)) {
                        ztid = "13";
                    } else if (smallproRefundConstant.getHunanAreaAuthorizeId().contains(refundArea.getAuthorizeid())) {
                        ztid = "20";
                    } else if (refundArea.getXtenant().longValue() == JiujiTenantEnum.JIUJI_TENANT_YAYA.getCode()) {
                        ztid = "32";
                    }
                    kemu = "";
                    fzhs = "";
                    zhaiyao = "";
                    kemu = "";
                    fzhs = "";
                    jief = "";
                    daif = "";
                    if (areaKind1 == 1
                            || refundArea.getXtenant().longValue() == JiujiTenantEnum.JIUJI_TENANT_YAYA.getCode()
                            || smallproRefundConstant.getHunanAreaAuthorizeId().contains(refundArea.getAuthorizeid())
                            || (joinFlag && smallproRefundConstant.getFranchiseTuiWay().contains(shouhouTuihuan.getTuiWay()))
                            || (joinFlag && smallproRefundConstant.getSyArea().contains(area))) {
                        BigDecimal ccbPayPrice = BigDecimal.ZERO;

                        // region 建行分期返回
                        if ("建行分期返回".equals(shouhouTuihuan.getTuiWay()) && areaKind1 == 1) {
                            List<SmallproBankInstallmentInfoBO> bankInstallmentInfoBOList =
                                    smallproMapper.getBankInstallmentInfoByRefund(shouhouTuihuan.getId());
                            if (bankInstallmentInfoBOList != null && bankInstallmentInfoBOList.size() > 0) {
                                SmallproBankInstallmentInfoBO bankInfo = bankInstallmentInfoBOList.get(0);
                                BigDecimal freePrice = BigDecimal.ZERO;
                                String otherDsc = bankInfo.getOtherDsc();
                                Integer midplatform = bankInfo.getMidplatform() == null ? 0 : bankInfo.getMidplatform();
                                Integer fenQiNum = bankInfo.getFenQiNum();
                                String[] dscArray = otherDsc.split("\\|");
                                if (dscArray.length == 3) {
                                    freePrice = new BigDecimal(dscArray[1]);
                                }

                                if (freePrice.compareTo(BigDecimal.ZERO) > 0 || midplatform == 1) {
                                    BigDecimal refundPrice = shouhouTuihuan.getTuikuanM1();
                                    switch (fenQiNum) {
                                        case 3:
                                            ccbPayPrice = refundPrice.multiply(BigDecimal.valueOf(0.015));
                                            break;
                                        case 6:
                                            ccbPayPrice = refundPrice.multiply(BigDecimal.valueOf(0.02));
                                            break;
                                        case 12:
                                            ccbPayPrice = refundPrice.multiply(BigDecimal.valueOf(0.04));
                                            break;
                                        case 18:
                                            ccbPayPrice = refundPrice.multiply(BigDecimal.valueOf(0.06));
                                            break;
                                        case 24:
                                            ccbPayPrice = refundPrice.multiply(BigDecimal.valueOf(0.08));
                                            break;
                                        default:
                                            break;
                                    }
                                }
                            }
                        }
                        // endregion

                        // region 退小件
                        else if (shouhouTuihuan.getTuihuanKind() == 7 &&
                                ((shouhouTuihuan.getTuikuanM() != null && shouhouTuihuan.getTuikuanM().compareTo(zero) != 0)
                                        || (shouhouTuihuan.getInprice() != null && shouhouTuihuan.getInprice().compareTo(zero) != 0))
                        ) {
                            kemu = "";
                            fzhs = "";

                            if ("现金".equals(shouhouTuihuan.getTuiWay())
                                    && (areaKind1 == 1
                                    || areainfo.getXtenant().longValue() == JiujiTenantEnum.JIUJI_TENANT_YAYA.getCode()
                                    || smallproRefundConstant.getSyArea().contains(area) || smallproRefundConstant.getBsArea().contains(area)
                                    || smallproRefundConstant.getHunanAreaAuthorizeId().contains(refundArea.getAuthorizeid()))
                            ) {
                                kemu = "112203";
                                fzhs = shouhouTuihuanAreaInfo.getArea();
                                if (smallproRefundConstant.getBsArea().contains(area)) {
                                    kemu = "112201";
                                    fzhs = shouhouTuihuanAreaInfo.getArea();
                                }
                            } else if ("银行转账".equals(shouhouTuihuan.getTuiWay())) {
                                if (areaKind1 == 1) {
                                    kemu = "1012104";
                                    fzhs = "无";
                                } else if (smallproRefundConstant.getSyArea().contains(area)) {
                                    kemu = "101505";
                                    fzhs = "无";
                                } else if (smallproRefundConstant.getHunanAreaAuthorizeId().contains(refundArea.getAuthorizeid())) {
                                    kemu = "1002011";
                                    fzhs = "无";
                                } else if (smallproRefundConstant.getBsArea().contains(area)) {
                                    kemu = "101201";
                                    fzhs = "无";
                                } else if (areainfo.getXtenant().longValue() == JiujiTenantEnum.JIUJI_TENANT_YAYA.getCode()) {
                                    kemu = "101206";
                                    fzhs = "无";
                                }

                            } else if ("刷卡返回".equals(shouhouTuihuan.getTuiWay()) && areaKind1 == 1) {
                                kemu = "112204";
//                                fzhs = apiServices.getAreaByID(11);
                                fzhs = "YN_bn";
                            } else if ("支付宝返回".equals(shouhouTuihuan.getTuiWay())) {
                                kemu = JiujiPingzhengKemuEnum.JIUJI_PINGZHENG_KEMU_ALIPAY.getMessage();
                                fzhs = "无";
                                if (smallproRefundConstant.getSyArea().contains(area)) {
                                    kemu = "112303";
                                    fzhs = "无";
                                } else if (smallproRefundConstant.getHunanAreaAuthorizeId().contains(refundArea.getAuthorizeid())) {
                                    kemu = "122120";
                                    fzhs = "无";
                                } else if (smallproRefundConstant.getBsArea().contains(area)) {
                                    kemu = "112301";
                                    fzhs = "无";
                                }
                            } else if ("余额".equals(shouhouTuihuan.getTuiWay()) && !smallproRefundConstant.getSyArea().contains(area) && !smallproRefundConstant.getHunanAreaAuthorizeId().contains(refundArea.getAuthorizeid())) {
                                kemu = "220301";
                                fzhs = "无";
                            } else if ("自提点余额".equals(shouhouTuihuan.getTuiWay()) && areaKind1 == 1) {
                                kemu = "220301";
                                fzhs = "无";
                            } else if ("微信返回".equals(shouhouTuihuan.getTuiWay())) {
                                kemu = "122112";
                                fzhs = "无";
                                if (smallproRefundConstant.getSyArea().contains(area)) {
                                    kemu = "112303";
                                    fzhs = "无";
                                } else if (smallproRefundConstant.getHunanAreaAuthorizeId().contains(refundArea.getAuthorizeid())) {
                                    kemu = "122120";
                                    fzhs = "无";
                                }
                            } else if ("ApplePay返回".equals(shouhouTuihuan.getTuiWay())) {
                                kemu = "1221101";
                                fzhs = "无";
                            } else if ("支付宝（兴业）返回".equals(shouhouTuihuan.getTuiWay())) {
                                kemu = "122118";
                                fzhs = "无";
                            } else if ("支付宝(pay1)返回".equals(shouhouTuihuan.getTuiWay())) {
                                kemu = "1002225";
                                fzhs = "无";
                            } else if ("支付宝(dzpay)返回".equals(shouhouTuihuan.getTuiWay())) {
                                kemu = "100231";
                                fzhs = "无";
                            } else if ("兴业扫码(叁玖)返回".equals(shouhouTuihuan.getTuiWay())) {
                                kemu = "101867";
                                fzhs = "无";
                            } else if ("浦发扫码(92653)返回".equals(shouhouTuihuan.getTuiWay())) {
                                kemu = "122127";
                                fzhs = "无";
                            } else if ("浦发扫码(92427)返回".equals(shouhouTuihuan.getTuiWay())) {
                                kemu = "122128";
                                fzhs = "无";
                            } else if ("中信扫码(06306)返回".equals(shouhouTuihuan.getTuiWay())) {
                                kemu = "1221110";
                                fzhs = "无";
                            } else if ("平安扫码(39878)返回".equals(shouhouTuihuan.getTuiWay())) {
                                kemu = "1221117";
                                fzhs = "无";
                            } else if ("首信易扫码支付返回".equals(shouhouTuihuan.getTuiWay())) {
                                kemu = "1221118";
                                fzhs = "无";

                            } else if ("中信扫码(06305)返回".equals(shouhouTuihuan.getTuiWay())) {
                                kemu = "1221111";
                                fzhs = "无";
                            } else if ("浦发扫码(93057)返回".equals(shouhouTuihuan.getTuiWay())) {
                                kemu = "1221113";
                                fzhs = "无";
                            } else if ("中信扫码(74026)返回".equals(shouhouTuihuan.getTuiWay())) {
                                kemu = "1221114";
                                fzhs = "无";
                            } else if ("中信扫码(25762)返回".equals(shouhouTuihuan.getTuiWay())) {
                                kemu = "1221115";
                                fzhs = "无";
                            } else if ("京东返回".equals(shouhouTuihuan.getTuiWay())) {
                                kemu = "101723";
                                fzhs = "无";
                                if (smallproRefundConstant.getSyArea().contains(area)) {
                                    kemu = "112303";
                                    fzhs = "无";
                                }
                            } else if ("兴业银行扫码返回".equals(shouhouTuihuan.getTuiWay())) {
                                kemu = "122118";
                                fzhs = "无";
                                if (smallproRefundConstant.getSyArea().contains(area)) {
                                    kemu = "112303";
                                    fzhs = "无";
                                } else if (smallproRefundConstant.getHunanAreaAuthorizeId().contains(refundArea.getAuthorizeid())) {
                                    kemu = "122120";
                                    fzhs = "无";
                                }
                            } else if ("浦发扫码返回".equals(shouhouTuihuan.getTuiWay())) {
                                kemu = "122124";
                                fzhs = "无";
                                if (smallproRefundConstant.getSyArea().contains(area)) {
                                    kemu = "112303";
                                    fzhs = "无";
                                } else if (smallproRefundConstant.getHunanAreaAuthorizeId().contains(refundArea.getAuthorizeid())) {
                                    kemu = "122120";
                                    fzhs = "无";
                                }
                            } else if ("微信秒退".equals(shouhouTuihuan.getTuiWay())) {
                                kemu = "122119";
                                fzhs = "无";
                                if (smallproRefundConstant.getSyArea().contains(area)) {
                                    kemu = "112303";
                                    fzhs = "无";
                                } else if (smallproRefundConstant.getHunanAreaAuthorizeId().contains(refundArea.getAuthorizeid())) {
                                    kemu = "122120";
                                    fzhs = "无";
                                }
                            } else if ("支付宝秒退".equals(shouhouTuihuan.getTuiWay())) {
                                kemu = "100231";
                                fzhs = "无";
                            }


                            // endregion

                            if (!"".equals(kemu)) {


                            }


                        }
                    }
                }

                // endregion

            }

        }


        return true;
    }

    // endregion
}
