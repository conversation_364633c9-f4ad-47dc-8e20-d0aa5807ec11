package com.jiuji.oa.afterservice.bigpro.po;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.jiuji.cloud.after.enums.BaoXiuTypeEnum;
import com.jiuji.oa.afterservice.bigpro.enums.FaultTypeEnum;
import com.jiuji.oa.afterservice.bigpro.enums.WxStatusEnum;
import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-16
 */
@ApiModel
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("shouhou")
public class Shouhou extends Model<Shouhou> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "编号")
    private Integer id;

    /**
     * 返修单id
     */
    private Integer repairOrderId;

    /**
     * 订单来源
     * @see com.jiuji.oa.afterservice.bigpro.enums.ShouhouFromSourceEnum
     */
    @ApiModelProperty("订单来源")
    private Integer fromSource;

    /**
     * 接件方式
     */
    private String connectionMethod;

    /**
     * 接件Fid
     */
    @TableField(exist = false)
    private String connectionFid;

    @ApiModelProperty(value = "机型")
    private String name;

    @ApiModelProperty(value = "配置")
    private String peizhi;

    @ApiModelProperty(value = "故障")
    private String problem;

    @ApiModelProperty(value = "备注")
    private String comment;

    @ApiModelProperty(value = "用户名")
    private String username;

    @ApiModelProperty(value = "手机")
    private String mobile;

    @ApiModelProperty(value = "电话")
    private String tel;
    /**
     * @see WxStatusEnum
     */
    @ApiModelProperty(value = "维修状态：0处理中，1已修好，3修不好")
    private Integer stats;
    /**
     * @see com.jiuji.oa.afterservice.bigpro.enums.BaoxiuStatusEnum
     */
    @ApiModelProperty(value = "保修状态：0不在保，1在保,2外修")
    private Integer baoxiu;

    @ApiModelProperty(value = "登记人")
    private String inuser;

    @ApiModelProperty(value = "串号")
    private String imei;

    @ApiModelProperty(value = "是否显示")
    private Boolean xianshi;

    @ApiModelProperty(value = "测试时间")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime contentcsdate;

    @ApiModelProperty(value = "购买时间")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime tradedate;

    @ApiModelProperty(value = "送修时间")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime modidate;
    /**
     * 应收费用 总配件费用-优惠费用
     */
    @ApiModelProperty(value = "维修费用")
    private BigDecimal feiyong;

    @ApiModelProperty(value = "成本价")
    private BigDecimal costprice;

    @ApiModelProperty(value = "维修人")
    private String weixiuren;

    @ApiModelProperty(value = "备用机id")
    private Integer dyjid;

    @ApiModelProperty(value = "结束时间")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime offtime;

    @ApiModelProperty(value = "接件地区")
    private String area;

    @ApiModelProperty(value = "收银锁定")
    private Integer shouyinglock;

    @ApiModelProperty(value = "收银时间")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime shouyingdate;

    @ApiModelProperty(value = "收银人")
    private String shouyinguser;

    @ApiModelProperty(value = "购买用户ID")
    @NotNull
    private Long userid;

    @ApiModelProperty(value = "地区分类：bd,dz")
    private String kinds;

    @ApiModelProperty(value = "是否提成")
    private Boolean isticheng;

    /**
     * @see WaiGuanStatusEnum
     */
    @ApiModelProperty(value = "外观情况")
    @TableField("waiguan_status")
    private Integer waiGuanStatus;

    @ApiModelProperty(value = "外观描述")
    private String waiguan;

    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "跟进时间")
    private LocalDateTime resultDtime;

    @ApiModelProperty(value = "是否软件接件")
    private Boolean issoft;

    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "修好时间")
    private LocalDateTime modidtime;

    @ApiModelProperty(value = "商品id")
    private Long productId;

    @ApiModelProperty(value = "商品规格")
    private String productColor;

    @ApiModelProperty(value = "购买地区")
    private String buyarea;

    @ApiModelProperty(value = "是否盘点")
    private Boolean pandian;

    /**
     * 故障类型
     * （1-有故障 2-无故障） 因为历史数据可能会有为空的情况
     * @see FaultTypeEnum
     */
    private Integer faultType;


    @ApiModelProperty(value = "盘点时间")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime pandiandate;

    @ApiModelProperty(value = "转到地区id")
    private String toarea;

    @ApiModelProperty(value = "是否退款")
    private Boolean istui;

    @ApiModelProperty(value = "盘点人")
    private String pandianinuser;

    @ApiModelProperty(value = "ppid")
    private Integer ppriceid;

    @ApiModelProperty(value = "mkcId")
    private Integer mkcId;

    @ApiModelProperty(value = "是否快速")
    private Boolean isquick;

    @ApiModelProperty(value = "维修次数")
    private Integer wcount;

    @ApiModelProperty(value = "维修组id")
    @JSONField(serialzeFeatures = SerializerFeature.WriteMapNullValue)
    private Integer weixiuzuid;

    @ApiModelProperty(value = "维修组记录")
    private Integer weixiuzuidJl;

    @ApiModelProperty(value = "是否维修")
    private Boolean isweixiu;

    @ApiModelProperty(value = "维修组完成时间")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime weixiudtime;

    @ApiModelProperty(value = "维修组分配时间")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime weixiuStartdtime;

    @ApiModelProperty(value = "短单号")
    private String orderid;

    @ApiModelProperty(value = "是否取机")
    private Boolean isquji;
    /**
     * 前端传过来的值 通过接口: /api/bigpro/shouhou/checkFanxiu 获取
     */
    @ApiModelProperty(value = "是否返修")
    private Boolean isfan;

    @ApiModelProperty(value = "评价类型")
    private Integer pingjia;

    @ApiModelProperty(value = "评价类型1")
    private Integer pingjia1;

    @ApiModelProperty(value = "对应订单id")
    private Integer subId;

    @ApiModelProperty(value = "1 修 2换 3 退")
    private Integer webtype1;
    /**
     * @see com.jiuji.oa.afterservice.bigpro.enums.YuYueSTypeEnum
     */
    @ApiModelProperty(value = "1预约到店  2上门取机  3快递至三九")
    private Integer webtype2;

    @ApiModelProperty(value = "网站状态 1 未审核 2 已审核 3 已收到 4 已发出 5已完成")
    private Integer webstats;
    /**
     * @see BaoXiuTypeEnum
     */
    @ApiModelProperty(value = "九机服务类型")
    @TableField("ServiceType")
    @JSONField(serialzeFeatures = SerializerFeature.WriteMapNullValue)
    private Integer serviceType;

    @ApiModelProperty(value = "对应basket_id")
    private Integer basketId;

    /**
     * @see com.jiuji.oa.afterservice.bigpro.enums.IshuishouEnum
     */
    @ApiModelProperty(value = "是否回购机 1是，0不是")
    private Integer ishuishou;

    @ApiModelProperty(value = "预约id")
    private Integer yuyueid;

    @ApiModelProperty(value = "回执单打印次数")
    private Integer huiprint;

    @ApiModelProperty(value = "维修人时间")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime weixiurentime;

    @ApiModelProperty(value = "维修人")
    private Boolean reweixiuren;

    @ApiModelProperty(value = "送修人姓名")
    private String sxname;

    @ApiModelProperty(value = "送修人手机")
    private String sxmobile;

    @ApiModelProperty(value = "送修人性别")
    private Integer sxsex;

    @ApiModelProperty(value = "送修人id")
    private Integer sxuserid;

    @ApiModelProperty(value = "锁屏密码")
    private String lockpwd;

    @ApiModelProperty(value = "测试人")
    private String testuser;

    @ApiModelProperty(value = "维修处理方式 1修 2换 3多 4送 5显示总成置换 6快")
    private Integer wxkind;

    @ApiModelProperty(value = "维修配件")
    @TableField("wxConfig")
    private String wxConfig;

    @ApiModelProperty(value = "通知时间")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime noticetime;

    @ApiModelProperty(value = "测试时间")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime testtime;

    @ApiModelProperty(value = "设备账号ID")
    private String deviceid;

    @ApiModelProperty(value = "设备密码 ")
    private String devicepwd;

    @ApiModelProperty(value = "优惠码")
    private String youhuima;

    @ApiModelProperty(value = "接件确认")
    @TableField("yuyueCheck")
    private Boolean yuyueCheck;

    @ApiModelProperty(value = "是否瑕疵机")
    @TableField("isXcMkc")
    private Boolean isXcMkc;

    @ApiModelProperty(value = "瑕疵机来源")
    @TableField("isXcMkcInfo")
    private String isXcMkcInfo;

    @TableField("wxTestTime")
    @ApiModelProperty(value = "维修测试时间")
    private LocalDateTime wxTestTime;

    @ApiModelProperty(value = "维修测试信息")
    @TableField("wxTestInfo")
    private String wxTestInfo;

    @ApiModelProperty(value = "维修等级：1更换维修，2芯片维修，3总成置换，4硬盘升级")
    @TableField("RepairLevel")
    private Integer RepairLevel;

    @ApiModelProperty(value = "门店id")
    @NotNull(message = "接件门店不能为空")
    private Integer areaid;

    @ApiModelProperty(value = "转地区id")
    private Integer toareaid;

    @ApiModelProperty(value = "购买地区id")
    private Integer buyareaid;

    @ApiModelProperty(value = "维修测试状态1:测试通过 2;测试不通过")
    @TableField("wxTestStats")
    private Integer wxTestStats;

    @ApiModelProperty(value = "跟进人")
    @TableField("gjUser")
    private String gjUser;

    @ApiModelProperty(value = "业务处理状态")
    @TableField("ProcessConfirmStats")
    private String ProcessConfirmStats;

    @ApiModelProperty(value = "旧id")
    private Integer oldshouhouid;

    @ApiModelProperty(value = "是否备份数据 0 不备份 1 备份")
    @TableField("isBakData")
    private Integer isBakData;

    @ApiModelProperty(value = "是否解绑QQ/微信")
    private Boolean isjbanwxqq;

    @ApiModelProperty(value = "业务确认人")
    @TableField("yuyueCheckuser")
    private String yuyueCheckuser;

    @ApiModelProperty(value = "取机通知时间")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime qujitongzhitime;

    @ApiModelProperty(value = "倒计时")
    private Integer daojishi;

    @TableField("codeMsg")
    private String codeMsg;

    @ApiModelProperty(value = "处理人")
    private String resultUser;

    @ApiModelProperty(value = "短信发送时间")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime smstime;

    @ApiModelProperty(value = "电话跟进时间")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime teltime;

    @ApiModelProperty(value = "保证金订单号")
    @TableField("EarnestMoneySubid")
    private Integer EarnestMoneySubid;

    @ApiModelProperty(value = "服务出险人")
    @TableField("serversOutUser")
    private String serversOutUser;

    @TableField("serversOutDtime")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "服务出险时间")
    private LocalDateTime serversOutDtime;

    @ApiModelProperty(value = "优惠费用")
    private BigDecimal youhuifeiyong;

    private String truename;

    @ApiModelProperty(value = "是否中邮")
    private Boolean iszy;

    @TableField("wxAreaid")
    @ApiModelProperty(value = "维修地区id")
    private Integer wxAreaid;

    @ApiModelProperty(value = "接件拍照图")
    private String imeifid;

    @TableField(exist = false)
    private BigDecimal yingfum = BigDecimal.ZERO;

    @ApiModelProperty(value = "已付金额")
    private BigDecimal yifum;

    @ApiModelProperty(value = "是否是快修")
    @TableField("kuaixiuFlag")
    private Integer kuaixiuFlag;

    @ApiModelProperty(value = "快修提示消息推送时间")
    @TableField("kuaixiuSendTime")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime kuaixiuSendTime;

    @ApiModelProperty(value = "是否是赠品")
    private Boolean iszp;

    @ApiModelProperty(value = "无理由")
    private String wuliyou;

    /**
     * 手机清洁服务: 1 是 0 否
     */
    @ApiModelProperty(value = "额外服务类型， 1=手机清洁服务 ")
    @TableField("mobileServeiceType")
    private Integer mobileServeiceType;

    /**
     * 0 完好 1 缺漏
     */
    @ApiModelProperty(value = "良品配置情况")
    private Integer lppeizhi;

    @ApiModelProperty(value = "原维修单")
    private Integer fromshouhouid;

    @ApiModelProperty(value = "服务成本")
    @TableField("ServiceCostprice")
    @JSONField(serialzeFeatures = SerializerFeature.WriteNullNumberAsZero)
    private Double ServiceCostprice;

    @ApiModelProperty(value = "故障类型")
    private String questionType;

    @ApiModelProperty(value = "凭证号")
    private Integer pzid;

    @ApiModelProperty(value = "品牌ID")
    private Integer brandId;

    @ApiModelProperty("退货备注")
    private String refundRemark;
    /**
     * 程序里面判断非空即为增值机业务,无法进行扩展
     */
    @ApiModelProperty("维修单来源")
    private Integer orderSource;

    /**
     * 是否大疆维修单
     * 0-否 1-是
     */
    @ApiModelProperty("是否大疆维修单")
    private Integer isDji;

    @ApiModelProperty("是否有外送渠道 0或者null是没有  1是有")
    @TableField(exist = false)
    private Integer isQuDao;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

    @Getter
    @AllArgsConstructor
    public enum WaiGuanStatusEnum implements CodeMessageEnumInterface {
        MO_SHUN(0, "磨损"),
        WAN_HAO(1, "完好"),
        NO_SELECT(2, "未选")
        ;

        private final Integer code;
        private final String message;
    }

}
