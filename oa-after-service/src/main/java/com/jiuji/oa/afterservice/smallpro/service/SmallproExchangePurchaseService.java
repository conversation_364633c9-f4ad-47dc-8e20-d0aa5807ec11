package com.jiuji.oa.afterservice.smallpro.service;

import com.jiuji.oa.afterservice.common.bo.OaUserBO;
import com.jiuji.oa.afterservice.smallpro.bo.CashOrScrapRes;
import com.jiuji.oa.afterservice.smallpro.bo.SmallproKcReqBO;
import com.jiuji.oa.afterservice.smallpro.bo.smallproExchange.SmallproJiujiServiceInpriceInfoBO;
import com.jiuji.oa.afterservice.smallpro.bo.smallproExchange.SmallproPickUpOutboundInfoBO;
import com.jiuji.oa.afterservice.smallpro.bo.smallproInfo.LastExchangeSmallproBo;
import com.jiuji.oa.afterservice.smallpro.enums.SmallProKindEnum;
import com.jiuji.oa.afterservice.smallpro.po.Smallpro;
import com.jiuji.oa.afterservice.smallpro.vo.req.FilmScrapReq;
import com.jiuji.oa.afterservice.smallpro.vo.req.PickUpExtendReq;
import com.jiuji.oa.afterservice.smallpro.vo.req.SmallproRefundReq;
import com.jiuji.oa.afterservice.smallpro.vo.res.CashOrScrapV2Res;
import com.jiuji.oa.afterservice.smallpro.vo.res.SmallproNormalCodeMessageRes;
import com.jiuji.oa.afterservice.statistics.vo.req.CashOrScrapBatchReq;
import com.jiuji.tc.common.vo.R;

import java.io.UnsupportedEncodingException;
import java.util.List;
import java.util.Set;

/**
 * description: <小件退换相关操作Service>
 * translation: <Small piece return related operations>
 *
 * <AUTHOR>
 * @date 2019/12/26
 * @since 1.0.0
 */
public interface SmallproExchangePurchaseService {

    /**
     * 消息推送
     * @param smallpro
     * @param filmScrapReq
     */
     void pushCashMsg(Smallpro smallpro, FilmScrapReq filmScrapReq);

    // region 逻辑方法

    // region 取件操作

    /**
     * description: <小件接件-取件操作>
     * translation: <Smallpro Pickup Operation>
     *
     * @param smallproId 小件接件单Id
     * @param kind       小件接件单类别
     * @param oaUserBO   操作人信息
     * @return com.jiuji.oa.afterservice.smallpro.vo.res.SmallproNormalCodeMessageRes
     * <AUTHOR>
     * @date 14:21 2019/12/27
     * @since 1.0.0
     **/
    SmallproNormalCodeMessageRes pickup(Integer smallproId, Integer kind, OaUserBO oaUserBO, PickUpExtendReq pickUpExtendReq);

    // endregion

    // region 换货审核

    /**
     * description: <换货审核>
     * translation: <Check exchange>
     *
     * @param smallproId 小件接件订单Id
     * @param userName   操作用户名称
     * @return com.jiuji.oa.afterservice.smallpro.vo.res.SmallproNormalCodeMessageRes
     * <AUTHOR>
     * @date 9:16 2019/12/20
     * @since 1.0.0
     **/
    SmallproNormalCodeMessageRes checkExchange(Integer smallproId, String userName);

    // endregion

    // region 撤销换货审核

    /**
     * description: <撤销换货审核>
     * translation: <Revocation exchange review>
     *
     * @param smallproId 小件接件订单Id
     * @param userName   操作用户名称
     * @return com.jiuji.oa.afterservice.smallpro.vo.res.SmallproNormalCodeMessageRes
     * <AUTHOR>
     * @date 9:58 2019/12/24
     * @since 1.0.0
     **/
    SmallproNormalCodeMessageRes cancelCheckExchange(Integer smallproId, String userName);

    // endregion

    // region 校验验证码

    /**
     * description: <校验验证码>
     * translation: <Verification code>
     *
     * @param smallproId 小件售后接件Id
     * @param code       验证码
     * @param type       验证码类别[1换货|2退货]
     * @return com.jiuji.oa.afterservice.smallpro.vo.res.SmallproNormalCodeMessageRes
     * <AUTHOR>
     * @date 10:58 2020/1/8
     * @since 1.0.0
     **/
    SmallproNormalCodeMessageRes checkCodeMessage(Integer smallproId, String code, Integer type,Integer validType);

    // endregion

    // region 发送验证码给用户

    /**
     * description: <发送验证码给用户>
     * translation: <Send verification code to user>
     *
     * @param smallproId 小件售后接件Id
     * @param areaId     地区Id
     * @param subId      订单Id
     * @param type       验证码类别[1换货|2退货]
     * @return com.jiuji.oa.afterservice.smallpro.vo.res.SmallproNormalCodeMessageRes
     * <AUTHOR>
     * @date 10:56 2020/1/8
     * @since 1.0.0
     **/
    SmallproNormalCodeMessageRes pushCodeMessageToUser(Integer smallproId, Integer areaId, Integer subId, Integer type);

    // endregion


    // endregion


    /**
     * description: <取件写操作不要升级为public,需要进行事务性补偿回滚>
     * translation: <Pickup and write operations>
     *
     * @param result                        param
     * @param kind                          param
     * @see SmallProKindEnum
     * @param oaUserBO                      param
     * @param smallpro                      param
     * @param outboundInfoList              param
     * @param jiujiServiceInpriceInfoBOList param
     * @param returnKcReqList
     * @return boolean
     * <AUTHOR>
     * @date 14:48 2020/4/13
     * @since 1.0.0
     **/
    boolean handlePickupWrite(SmallproNormalCodeMessageRes result, Integer kind, OaUserBO oaUserBO,
                              Smallpro smallpro, List<SmallproPickUpOutboundInfoBO> outboundInfoList,
                              List<SmallproJiujiServiceInpriceInfoBO> jiujiServiceInpriceInfoBOList,
                              List<SmallproKcReqBO> returnKcReqList,PickUpExtendReq pickUpExtendReq);

    void sendEvaluateInfo(Smallpro smallpro, OaUserBO oaUserBO);

    void joinAreaMoneyDeal(Smallpro smallpro);

    /**
     * description: <验证用户二级密码>
     * translation: <Verify user secondary password>
     *
     * @param userId 用户Id
     * @param pwd2   用户二级密码
     * @return com.jiuji.oa.afterservice.smallpro.vo.res.SmallproNormalCodeMessageRes
     * <AUTHOR>
     * @date 14:32 2020/3/13
     * @since 1.0.0
     **/
    SmallproNormalCodeMessageRes checkPwd2(Integer userId, String pwd2) throws UnsupportedEncodingException;


    /**
     * 小件退款
     *
     * @return
     */
    R<Boolean> smallproRefund(SmallproRefundReq refundReq);

    /**
     * 钢化膜绑定验证
     * @param imei
     * @param smallproId
     * @param basketId
     * @param fid
     * @param kind
     * @return
     */
    SmallproNormalCodeMessageRes checkTemperedFilmBinding(String imei, Integer smallproId, Long basketId, String fid, Integer kind);

    R<CashOrScrapRes> CashOrScrap(int smallproId);


    /**
     * 对小件单或者物流单进行添加
     *
     * @param orderId    小件单/物流单
     * @return
     */
    R<List<CashOrScrapV2Res>> cashOrScrapV2(int orderId);

    /**
     * 批量异常提交
     *
     * @param smallproIds 小件单id集合
     * @return
     */
    R<Boolean> abnormalBatch(List<Integer> smallproIds);

    List<Integer> CashOrScrapBatch(CashOrScrapBatchReq cashOrScrapBatchReq, OaUserBO oaUserBO);

    Integer correctStatus();

    Boolean checkPpid(List<String> ppId, Integer type);

    void assertJoinArea(Integer smallproId);

    boolean isNotAllowAutoToArea(OaUserBO oaUserBO, Smallpro smallpro);

    /**
     * 虚拟商品自动报废
     * @param smallProId
     */
    void virtualProductAutoScrap(Integer smallProId);


    /**
     * 批量获取最近一次换货商品信息
     * @param smallproId
     * @param subId
     * @param basketIds
     * @return
     */
    List<LastExchangeSmallproBo> listLastChangeSmallpro(Integer smallproId, Integer subId, Set<Integer> basketIds);
}
