package com.jiuji.oa.afterservice.smallpro.vo.res;

import com.alibaba.fastjson.annotation.JSONField;
import com.jiuji.oa.afterservice.smallpro.enums.SmallProServiceTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/5/23 14:19
 * @Description 小件置换配置实体
 */
@Data
@Accessors(chain = true)
@ApiModel("小件退货配置实体")
public class RefundGoodsConfigRes {
    @ApiModelProperty(value = "编号")
    private Integer id;
    /**
     * 配置名称
     */
    @ApiModelProperty(value = "配置名称/标题")
    private String title;
    /**
     * 服务类型
     * @see SmallProServiceTypeEnum
     */
    @ApiModelProperty(value = "服务类型")
    private Integer serviceType;

    /**
     * 服务类型名称
     */
    @ApiModelProperty(value = "服务类型名称")
    private String serviceTypeName;

    /**
     * 是否补差价
     */
    @ApiModelProperty(value = "是否补差价")
    private Boolean isDifferentPrice;

    /**
     * 可换货商品配置方式(1 品牌 2分类 3 品牌+分类 4 SPU 5 SKU)
     */
    @ApiModelProperty(value = "可换货商品配置方式(1 品牌 2分类 3 品牌+分类 4 SPU 5 SKU)")
    private Integer productConfigType;
    /**
     * 可换货商品配置方式(1 品牌 2分类 3 品牌+分类 4 SPU 5 SKU)
     */
    @ApiModelProperty(value = "可换货商品配置方式名称")
    private String productConfigTypeName;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createUser;
    /**
     * 配置值
     */
    @ApiModelProperty(value = "配置值类型")
    private List<ExchangeGoodsConfigValueBO> exchangeGoodsConfigValueBO;
    /**
     * 配置值名称 以逗号隔开
     */
    @ApiModelProperty(value = "配置值名称 以逗号隔开")
    private String configValueName;

    /**
     * 换货政策
     */
    @ApiModelProperty(value = "换货政策")
    private String policyContent;

    /**
     * 补差价方式(1 按差价金额补 2 按照换货商品金额补)
     */
    @ApiModelProperty(value = "补差价方式")
    private Integer differentPriceType;
    /**
     * 补差价百分比
     */
    @ApiModelProperty(value = "补差价百分比")
    private BigDecimal differentPricePercent;

    /**
     * 使用商品服务金额补差价
     */
    @ApiModelProperty(value = "使用商品服务金额补差价")
    private Boolean isServiceDifferentPrice;


    @Data
    public static class ExchangeGoodsConfigValueBO implements Serializable {
        /**
         * 配置类型(1 品牌 2分类 4 SPU 5 SKU)
         */
        private Integer configType;

        /**
         * 配置值
         */
        private List<Integer> configValue;
    }
}
