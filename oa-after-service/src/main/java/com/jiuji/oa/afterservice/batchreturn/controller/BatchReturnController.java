package com.jiuji.oa.afterservice.batchreturn.controller;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jiuji.oa.afterservice.batchreturn.bo.BatchReturnSubBO;
import com.jiuji.oa.afterservice.batchreturn.dao.BatchReturnDao;
import com.jiuji.oa.afterservice.batchreturn.enums.BatchReturnStatusEnum;
import com.jiuji.oa.afterservice.batchreturn.enums.BatchReturnWayEnum;
import com.jiuji.oa.afterservice.batchreturn.po.BatchReturn;
import com.jiuji.oa.afterservice.batchreturn.service.BatchReturnService;
import com.jiuji.oa.afterservice.batchreturn.vo.BatchReturnInfoVO;
import com.jiuji.oa.afterservice.batchreturn.vo.BatchReturnSubInfoVO;
import com.jiuji.oa.afterservice.batchreturn.vo.req.BatchReturnLogAddReq;
import com.jiuji.oa.afterservice.batchreturn.vo.req.BatchReturnQueryReq;
import com.jiuji.oa.afterservice.batchreturn.vo.req.BatchReturnReviewReq;
import com.jiuji.oa.afterservice.batchreturn.vo.req.BatchReturnTakeReq;
import com.jiuji.oa.afterservice.batchreturn.vo.res.BatchReturnPageVO;
import com.jiuji.oa.afterservice.batchreturn.vo.res.HandleReturnRes;
import com.jiuji.oa.afterservice.batchreturn.wrapper.BatchReturnWrapper;
import com.jiuji.oa.afterservice.bigpro.service.ShouhouRefundService;
import com.jiuji.oa.afterservice.bigpro.service.SmsService;
import com.jiuji.oa.afterservice.common.aspect.RepeatSubmitCheck;
import com.jiuji.oa.afterservice.common.bo.OaUserBO;
import com.jiuji.oa.afterservice.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.afterservice.common.constant.RequestAttrKeys;
import com.jiuji.oa.afterservice.common.enums.TuihuanKindEnum;
import com.jiuji.oa.afterservice.common.exception.RRExceptionHandler;
import com.jiuji.oa.afterservice.common.util.SpringContextUtil;
import com.jiuji.oa.afterservice.common.util.ValidatorUtil;
import com.jiuji.oa.afterservice.refund.service.way.ThirdOriginWayService;
import com.jiuji.oa.afterservice.refund.vo.res.way.ThirdOriginRefundVo;
import com.jiuji.oa.loginfo.batchreturn.client.BatchReturnLogClient;
import com.jiuji.oa.loginfo.common.pojo.AddLogReq;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.common.vo.ResultCode;
import com.jiuji.tc.utils.common.DecideUtil;
import com.jiuji.tc.utils.enums.EnumUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @since 2020/3/12
 */
@RestController
@RequestMapping("api/batchReturn")
@Api(tags = "批量退款办理")
@Slf4j
public class BatchReturnController {
    private static final Pattern PATH_MATCH_NAME = Pattern.compile("^(([\u4e00-\u9fa5]{2,8})|([a-zA-Z]{2,16}))$");

    @Resource
    private BatchReturnService batchReturnService;
    @Resource
    private AbstractCurrentRequestComponent requestComponent;
    @Resource
    private BatchReturnLogClient batchReturnLogClient;
    @Autowired
    private BatchReturnDao batchReturnDao;
    @Autowired
    private ShouhouRefundService shouhouRefundService;
    @Resource
    private ThirdOriginWayService thirdOriginWayService;
    @Resource
    private SmsService smsService;

    @GetMapping("subInfo")
    @ApiOperation(value = "获取订单信息", notes = "通过订单，获取批量退款相关信息", httpMethod = "GET")
    public R<BatchReturnSubInfoVO> getSubInfo(@RequestParam("subId") Integer subId) {
        if (subId <= 0) {
            return R.error(ResultCode.PARAM_ERROR, "请输入订单号");
        }

        BatchReturnSubInfoVO res = new BatchReturnSubInfoVO();
        //todo 调整优化
        LambdaQueryWrapper<BatchReturn> existTakeQ = new LambdaQueryWrapper<BatchReturn>()
                .eq(BatchReturn::getStatus, BatchReturnStatusEnum.TAKE.getCode())
                .eq(BatchReturn::getSubId, subId);
        List<BatchReturn> list = batchReturnService.list(existTakeQ);
        List<BatchReturnPageVO> returnInfoList = new LinkedList<>();
        if (CollectionUtils.isNotEmpty(list)) {
            // 该订单有退款中的订单
            list.stream().map(BatchReturn::getId).collect(Collectors.toList());
            returnInfoList = batchReturnDao.getReturnInfoByReturnId(list.stream().map(BatchReturn::getId).collect(Collectors.toList()));
            res.setReturnInfoList(returnInfoList);
            res.setExistReturnInfo(Boolean.TRUE);
            return R.success("该订单有退款中的订单" ,res);
        }
        List<BatchReturnSubBO> subBoList = batchReturnService.getBatchReturnSubBO(subId);
        if (subBoList.isEmpty()) {
            return R.error(ResultCode.NO_DATA, "订单未完成，或无可退大件商品");
        }
        return R.success(BatchReturnWrapper.toSubInfoBo(subBoList));
    }

    @PostMapping("take")
    @ApiOperation(value = "批量退款接件", httpMethod = "POST", notes = "仅保存")
    @RepeatSubmitCheck(argIndexs = 0,seconds = 30)
    public R<Integer> takeReturn(@RequestBody BatchReturnTakeReq req) {
        ValidatorUtil.validateEntity(req);
        if (req.getSubId() == null) {
            return R.error(ResultCode.PARAM_ERROR, "参数异常，接件的订单为必填项");
        }
        if(ObjectUtil.equal(req.getReturnWay(),ShouhouRefundService.WECHAT_REFUND_WAY) && StrUtil.isBlank(req.getBankUserName())){
            return R.error(ResultCode.PARAM_ERROR, "微信限制，需录入客户真实姓名，以方便退款！");
        }
        if(ObjectUtil.equal(req.getReturnWay(),ShouhouRefundService.WECHAT_REFUND_WAY) && StrUtil.isNotBlank(req.getBankUserName())){
            boolean isCorrectName = PATH_MATCH_NAME.matcher(req.getBankUserName()).matches();
            if (!isCorrectName) {
                return R.error(ResultCode.PARAM_ERROR, "收款姓名录入有误，请检查");
            }
        }

        List<BatchReturnSubBO> subBOList = batchReturnService.getBatchReturnSubBO(req.getSubId());
        if (subBOList.isEmpty()) {
            return R.error(ResultCode.NO_DATA, "订单未完成，或无可退大件商品");
        }
        R<Integer> checkR = batchReturnService.checkTake(req.getSubId(), subBOList, req.getMkcInfo());
        if (checkR.getCode() == ResultCode.SUCCESS && checkR.getData() < 0) {
            return R.error(ResultCode.PARAM_ERROR, checkR.getUserMsg());
        }
        OaUserBO inUser = requestComponent.getCurrentStaffId();
        return R.success(batchReturnService.takeReturn(inUser, subBOList.get(0).getTradeDate1(), checkR.getData(), req));
    }

    @GetMapping("info")
    @ApiOperation(value = "获取批量退款详情", notes = "通过批量退款id，获取批量退款详情", httpMethod = "GET")
    public R<BatchReturnInfoVO> getReturnInfo(@RequestParam("id") Integer id) {
        return batchReturnService.getInfoVOById(id);
    }

    @PostMapping("update")
    @ApiOperation(value = "保存退款方式", notes = "保存退款方式及接件信息")
    @RepeatSubmitCheck(argIndexs = 0)
    public R<Boolean> updateReturnWay(@RequestBody BatchReturnTakeReq req) {
        ValidatorUtil.validateEntity(req);
        if (req.getId() == null) {
            return R.error(ResultCode.PARAM_ERROR, "请先在对应的批量退款接件");
        }
        List<String> batchReturnInfo = Optional.ofNullable(shouhouRefundService.listRefundWay(req.getSubId(), TuihuanKindEnum.TK.getCode()))
                .filter(R::isSuccess).map(R::getData).orElse(new ArrayList<>());

        List<ThirdOriginRefundVo> thirdOriginRefundVos = thirdOriginWayService.listAll(req.getSubId(), TuihuanKindEnum.getByCode(TuihuanKindEnum.BATCH_TK.getCode()));
        if(CollectionUtils.isNotEmpty(thirdOriginRefundVos)){
            thirdOriginRefundVos.forEach(item->{
                String refundTypeName = item.getReturnWayName();
                batchReturnInfo.add(refundTypeName+"返回");
            });
        }
        if (!batchReturnInfo.contains(req.getReturnWay())) {
            return R.error(ResultCode.PARAM_ERROR, "退款方式不在可用范围之内");
        }
        OaUserBO inUser = requestComponent.getCurrentStaffId();

        BatchReturn batchReturn = batchReturnService.getById(req.getId());
        if (batchReturn == null) {
            return R.error(ResultCode.NO_DATA, "批量退款不存在");
        }

        if (!BatchReturnStatusEnum.TAKE.getCode().equals(batchReturn.getStatus())
                ) {
            return R.error(ResultCode.PARAM_ERROR, "当前数据禁止修改");
        }
        return R.success(batchReturnService.updateBatchReturn(inUser, batchReturn, req));
    }

    @PostMapping("review")
    @ApiOperation(value = "审核信息", notes = "包括:1-审核1，2-审核2，99-退款办理", httpMethod = "POST")
    @RepeatSubmitCheck(argIndexs = 0)
    public R<Boolean> review(@Valid @RequestBody BatchReturnReviewReq req) {
        BatchReturn batchReturn = batchReturnService.getById(req.getId());
        if (batchReturn == null) {
            return R.error(ResultCode.PARAM_ERROR, "参数异常，批量退款不存在");
        }
        OaUserBO inUser = requestComponent.getCurrentStaffId();
        boolean ismyHandleReturnWay = Arrays.asList(BatchReturnWayEnum.BALANCE.getMessage(), BatchReturnWayEnum.BANK.getMessage())
                .contains(batchReturn.getReturnWay());
        req.setIsmyHandleReturnWay(ismyHandleReturnWay);
        if (req.getType() == 1) {
            if (batchReturn.getStatus().equals(BatchReturnStatusEnum.TAKE.getCode())
                    && batchReturn.getReview1Ch999Id() == null) {
                return R.success(batchReturnService.review(inUser, batchReturn, req));
            }
        } else if (req.getType() == 2) {
            if (batchReturn.getStatus().equals(BatchReturnStatusEnum.TAKE.getCode())
                    && batchReturn.getReview1Ch999Id() != null
                    && batchReturn.getReview2Ch999Id() == null) {
                return R.success(batchReturnService.review(inUser, batchReturn, req));
            }
        } else if (req.getType() == 99 && batchReturn.getStatus().equals(BatchReturnStatusEnum.TAKE.getCode())
                && batchReturn.getReview1Ch999Id() != null
                && batchReturn.getReview2Ch999Id() != null) {
            R<HandleReturnRes> result = batchReturnService.handleReturn(inUser, batchReturn, req);
            if(!result.isSuccess()){
                return R.error(result.getCode(),result.getUserMsg());
            }
            HandleReturnRes handleReturnRes = result.getData();
            //提出事务防止调用的时候出现死锁
            //审核通过，且不是自己办理退款的，且有退款单，且退款单未审核
            if(Boolean.TRUE.equals(req.getIsPass()) && !ismyHandleReturnWay && handleReturnRes.getTuiHuanPo() != null
                    && !Boolean.TRUE.equals(handleReturnRes.getTuiHuanPo().getCheck3())){
                try {
                    shouhouRefundService.mobileHttpTuikuan(handleReturnRes.getTuiHuanPo(),inUser);
                } catch (Exception e) {
                    //通知, 人工介入处理
                    RRExceptionHandler.logError("批量退款办理接口",req,e,smsService::sendOaMsgTo9JiMan);
                }

            }
            return R.success(Boolean.TRUE).addAllBusinessLog(SpringContextUtil.getRequestKeyListMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG));
        }


        return R.error(StrUtil.format("当前数据不可{}", DecideUtil.iif(Boolean.TRUE.equals(req.getIsPass()),"审核","取消")));
    }

    @PostMapping("addLog")
    @ApiOperation(value = "添加进程信息", httpMethod = "POST")
    @RepeatSubmitCheck(argIndexs = 0,seconds = 30)
    public R<Boolean> addLog(@RequestBody BatchReturnLogAddReq req) {
        ValidatorUtil.validateEntity(req);
        BatchReturn batchReturn = batchReturnService.getById(req.getId());
        if (batchReturn == null) {
            return R.error(ResultCode.PARAM_ERROR, "参数异常，批量退款不存在");
        }
        OaUserBO inUser = requestComponent.getCurrentStaffId();
        AddLogReq addLogReq = new AddLogReq();
        addLogReq.setId(req.getId());
        addLogReq.setInUser(inUser.getUserName());
        addLogReq.setCh999Id(inUser.getUserId());
        addLogReq.setShowAll(true);
        addLogReq.setComment(req.getContext());
        return batchReturnLogClient.addLog(addLogReq);
    }

    /**
     * 批量退款单列表字段：批量退款单号（链接）、接件地区、客户号码、商品名称规格、接件人、接件时间、退款单状态
     * 筛选：退款单号/接件人/原始订单号/客户号码/商品名称/ppid的搜索，地区筛选，接件时间/退款时间选择，退款单状态选择
     *
     * @param req
     * @return
     */
    @PostMapping("search")
    @ApiOperation(value = "搜索", httpMethod = "POST")
    public R<Page<BatchReturnPageVO>> search(@RequestBody BatchReturnQueryReq req) {
        if (req == null) {
            return R.error(ResultCode.PARAM_ERROR, "参数为空");
        }
        if (req.getCurrent() <= 0) {
            req.setCurrent(1);
        }
        if (req.getSize() == null || req.getSize() == 0) {
            req.setSize(10);
        }
        Page<BatchReturnPageVO> page = batchReturnService.page(req);
        if (page != null && CollectionUtils.isNotEmpty(page.getRecords())) {
            for (BatchReturnPageVO record : page.getRecords()) {
                record.setStatus(EnumUtil.getMessageByCode(
                        BatchReturnStatusEnum.class, record.getStatusCode()));
            }
        }
        return R.success(page);
    }
}
