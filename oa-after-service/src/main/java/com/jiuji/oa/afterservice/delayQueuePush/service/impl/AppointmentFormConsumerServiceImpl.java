package com.jiuji.oa.afterservice.delayQueuePush.service.impl;

import cn.hutool.json.JSONUtil;
import com.jiuji.infra.lmstfy.anotation.LmstfyConsume;
import com.jiuji.oa.afterservice.bigpro.enums.OaMesTypeEnum;
import com.jiuji.oa.afterservice.bigpro.enums.YuyueStatusEnum;
import com.jiuji.oa.afterservice.bigpro.po.ShouhouYuyue;
import com.jiuji.oa.afterservice.delayQueuePush.appointmentFormStrategy.impl.CommonStrategy;
import com.jiuji.oa.afterservice.delayQueuePush.service.AppointmentFormPushService;
import com.jiuji.oa.afterservice.delayQueuePush.vo.AnalysisResultBO;
import com.jiuji.oa.afterservice.delayQueuePush.vo.AppointmentFormPush;
import com.jiuji.oa.afterservice.other.bo.LogisticsRecipientBO;
import com.jiuji.oa.afterservice.other.dao.ReceivePersonConfigMapper;
import com.jiuji.oa.afterservice.other.po.Areainfo;
import com.jiuji.oa.afterservice.other.service.AreainfoService;
import com.jiuji.tc.utils.constants.NumberConstant;
import com.meitu.platform.lmstfy.Job;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.*;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Optional;
import java.util.StringJoiner;

/**
 * 售后预约单延迟队列消费者
 * <AUTHOR>
 */
@Slf4j
@Service
public class AppointmentFormConsumerServiceImpl extends CommonStrategy {

    private static final String SMALL_PIECES_QUEUE = "${lmstfy.mult.first-lmstfy-client.smallPiecesQueue}";
    private static final String LARGE_SIZE_DOBJECT_QUEUE = "${lmstfy.mult.first-lmstfy-client.largeSizedObjectQueue}";
    private static final String DOOR_TO_DOOR_PICKUP_QUEUE = "${lmstfy.mult.first-lmstfy-client.doorToDoorPickupQueue}";
    private static final String MAIL_APPOINTMENTFORM_QUEUE = "${lmstfy.mult.first-lmstfy-client.mailAppointmentFormQueue}";


    /**
     * 封顶推送次数
     */
    private static final Integer CAPPING_NUMBER=NumberConstant.FIVE;
    /**
     * 大区物流负责人
     */
    private static final Integer LOGISTICS_RESPONSIBLE=317;
    /**
     * 大区分公司售后经理
     */
    private static final Integer AFTER_SALES_MANAGER=314;


    @Resource
    private AreainfoService areainfoService;

    @Resource
    private ReceivePersonConfigMapper receivePersonConfigMapper;

    @Resource
    private AppointmentFormPushService appointmentFormPushService;










    /**
     * 计算延迟队列时间
     * @param appointmentFormPush
     */
    public void setCalculationDelaySecond(AppointmentFormPush appointmentFormPush){
        LocalDateTime now = LocalDateTime.now();
        //判断当前时间在当天0点到9点30之间 如果过不在区间内需要把延迟时间清空以免后续计算问题
        if(now.isAfter(getTimeInfo().getStartTime()) && now.isBefore(getTimeInfo().getEndTime())){
            //计算当前时间到9点30相差几秒
            long seconds = Math.abs(getTimeInfo().getEndTime().until(now, ChronoUnit.SECONDS));
            appointmentFormPush.setDelaySecond((int) seconds);
            appointmentFormPush.setIsPush(Boolean.FALSE);
        } else {
            appointmentFormPush.setIsPush(Boolean.TRUE);
        }
    }



    /**
     * 延迟队列监听消费
     * 处理方式为预约到店且订单类型为小件预约单
     * @param job
     */
    @LmstfyConsume(queues = SMALL_PIECES_QUEUE, clientBeanName = "firstLmstfyClient")
    public void consumeSmallPiecesQueue(Job job) {
        log.warn("SMALL_PIECES_QUEUE消费到数据：{}", JSONUtil.toJsonStr(job));
        AnalysisResultBO analysisResult = getAnalysisResult(job);
        ShouhouYuyue shouhouYuyue = analysisResult.getShouhouYuyue();
        AppointmentFormPush appointmentFormPush = analysisResult.getAppointmentFormPush();
        log.warn("SMALL_PIECES_QUEUE消费到数据详情：{}", JSONUtil.toJsonStr(appointmentFormPush));
        Integer pushNumber = Optional.ofNullable(appointmentFormPush.getPushNumber()).orElse(NumberConstant.SIX);
        Integer stats = shouhouYuyue.getStats();
        Boolean isdel = Optional.ofNullable(shouhouYuyue.getIsdel()).orElse(Boolean.FALSE);
        /**
         * 超时未进行业务确认的预约单，超30分钟未业务确认，给预约单门店对应的店长/主管/第—负责人（同时推送）进行超时信息推送，没有确认每隔30分钟进行一次推送5次封顶;
         * 推送方式：oa “售后通知”
         * 推送内容：你负责的区域存在超时未确认的 预约到店小件预约单，请及时跟进确认。订单号 XXXX，XXXX，XXX
         */
        if(YuyueStatusEnum.WQR.getCode().equals(stats) && pushNumber <= CAPPING_NUMBER && !isdel){
            //计算延迟队列时间
            setCalculationDelaySecond(appointmentFormPush);
            //消息重复推送
            appointmentFormPushService.pushDelayDetectionMsg(appointmentFormPush);
            //拼接推送人员
            StringJoiner ch999IdsJoiner = new StringJoiner(",");
            //获取店长/主管/第—负责人
            getShopownerAndExecutiveDirectorAndResponsible(ch999IdsJoiner,shouhouYuyue);
            String msg="你负责的区域存在超时未确认的 预约到店小件预约单，请及时跟进确认。订单号:"+shouhouYuyue.getId();
            //OA消息推送
            sendOaShouhouMsg(ch999IdsJoiner,msg,shouhouYuyue.getId(),appointmentFormPush);
        }
    }

    /**
     * 获取店长/主管/第—负责人
     * @param ch999IdsJoiner
     * @param shouhouYuyue
     * @return
     */
    private void getShopownerAndExecutiveDirectorAndResponsible(StringJoiner ch999IdsJoiner,ShouhouYuyue shouhouYuyue){
        //获取预约单门店对应的店长/主管/
        Integer areaid = Optional.ofNullable(shouhouYuyue.getAreaid()).orElse(Integer.MAX_VALUE);
        List<LogisticsRecipientBO> normalReceiver = receivePersonConfigMapper.getExecutiveDirectorAndShopowner(areaid);
        if(CollectionUtils.isNotEmpty(normalReceiver)){
            normalReceiver.forEach(item->{
                Integer receiveUserId = item.getReceiveUserId();
                if(receiveUserId!=null){
                    ch999IdsJoiner.add(receiveUserId.toString());
                }
            });
        }
        //获取第—负责人
        Areainfo areainfo = Optional.ofNullable(areainfoService.getById(areaid)).orElse(new Areainfo());
        String curAdmin = areainfo.getCurAdmin();
        if(StringUtils.isNumeric(curAdmin)){
            ch999IdsJoiner.add(curAdmin);
        }
    }

    /**
     * 延迟队列监听消费
     * 处理方式为预约到店且订单类型为大件预约单
     * @param job
     */
    @LmstfyConsume(queues = LARGE_SIZE_DOBJECT_QUEUE, clientBeanName = "firstLmstfyClient")
    public void consumeLargeSizedObjectQueue(Job job) {
        log.warn("LARGE_SIZE_DOBJECT_QUEUE消费到数据：{} ", JSONUtil.toJsonStr(job));
        AnalysisResultBO analysisResult = getAnalysisResult(job);
        ShouhouYuyue shouhouYuyue = analysisResult.getShouhouYuyue();
        AppointmentFormPush appointmentFormPush = analysisResult.getAppointmentFormPush();
        log.warn("LARGE_SIZE_DOBJECT_QUEUE消费到数据详情：{}", JSONUtil.toJsonStr(appointmentFormPush));
        Integer pushNumber = Optional.ofNullable(appointmentFormPush.getPushNumber()).orElse(NumberConstant.TEN);
        Integer stats = shouhouYuyue.getStats();
        Boolean isdel = Optional.ofNullable(shouhouYuyue.getIsdel()).orElse(Boolean.FALSE);
        /**
         * 超时未进行业务确认的预约单，超30分钟未业务确认给预约单门店对应小区的"分公司售后主管/售后主管"推送超时信息，
         * 超60分钟未确认给预约单门店对应区域的“分公司售后主管/售后主管”和“售后经理”推送超时信息。
         * 后续若还未确认每隔1小时推送一次超时信息 5次封顶
         */

        if(YuyueStatusEnum.WQR.getCode().equals(stats) && pushNumber <= CAPPING_NUMBER && !isdel){
            //计算延迟队列时间
            setCalculationDelaySecond(appointmentFormPush);
            //拼接推送人员
            StringJoiner ch999IdsJoiner = new StringJoiner(",");
            //查询对应门店对应小区的"分公司售后主管/售后主管"
            List<String> afterSalesSupervisor = receivePersonConfigMapper.getAfterSalesSupervisor(shouhouYuyue.getAreaid());
            if(CollectionUtils.isNotEmpty(afterSalesSupervisor)){
                afterSalesSupervisor.forEach(ch999IdsJoiner::add);
            }
            //小于超过一个小时推送场景(前两次推送延迟时间为半个小时，推送人为售后主管)
            //当推送次数大于两次的时候延迟时间为1小时，推送人为售后主管、售后经理
            if(pushNumber>NumberConstant.ONE){
                //查询对应门店的大区售后经理
                List<String> afterSalesManager = receivePersonConfigMapper.selectRegionByAreaIdAndMainRole(shouhouYuyue.getAreaid(),AFTER_SALES_MANAGER);
                if(CollectionUtils.isNotEmpty(afterSalesManager)){
                    afterSalesManager.forEach(ch999IdsJoiner::add);
                }
                Boolean isPush = Optional.ofNullable(appointmentFormPush.getIsPush()).orElse(Boolean.TRUE);
                if(isPush){
                    //设置延迟队列推送时间为一小时
                    appointmentFormPush.setDelaySecond(ONE_HOUR);
                }
            }
            //消息重复推送
            appointmentFormPushService.pushDelayDetectionMsg(appointmentFormPush);
            //OA消息推送
            String msg="你负责的区域存在超时未确认的 预约到店大件预约单，请及时跟进确认。订单号:"+shouhouYuyue.getId();
            sendOaShouhouMsg(ch999IdsJoiner,msg,shouhouYuyue.getId(),appointmentFormPush);

        }
    }

    /**
     * 延迟队列监听消费
     * 处理方式为上门取件的预约单
     * @param job
     */
    @LmstfyConsume(queues = DOOR_TO_DOOR_PICKUP_QUEUE, clientBeanName = "firstLmstfyClient")
    public void consumeDoorToDoorPickupQueue(Job job) {
        log.warn("DOOR_TO_DOOR_PICKUP_QUEUE消费到数据：{}}", JSONUtil.toJsonStr(job));
        AnalysisResultBO analysisResult = getAnalysisResult(job);
        ShouhouYuyue shouhouYuyue = analysisResult.getShouhouYuyue();
        AppointmentFormPush appointmentFormPush = analysisResult.getAppointmentFormPush();
        log.warn("DOOR_TO_DOOR_PICKUP_QUEUE消费到数据详情：{}", JSONUtil.toJsonStr(appointmentFormPush));
        Integer pushNumber = Optional.ofNullable(appointmentFormPush.getPushNumber()).orElse(NumberConstant.TEN);
        Integer stats = shouhouYuyue.getStats();
        Boolean isdel = Optional.ofNullable(shouhouYuyue.getIsdel()).orElse(Boolean.FALSE);
        /**
         * 超时未进行业务确认的预约单，超30分钟未业务确认给预约单所属门店对应区域的“物流负责人”推送超时信息，后续若还未确认每隔1小时推送一次超时信息 5次封顶。
         */
        if(YuyueStatusEnum.WQR.getCode().equals(stats) && pushNumber <= CAPPING_NUMBER && !isdel){
            //计算延迟队列时间
            setCalculationDelaySecond(appointmentFormPush);
            //拼接推送人员
            StringJoiner ch999IdsJoiner = new StringJoiner(",");
            //判断推送次数等于1也就是超时30分钟的情况
            if(pushNumber.equals(NumberConstant.ONE)){
                //获取店长/主管/第—负责人
                getShopownerAndExecutiveDirectorAndResponsible(ch999IdsJoiner, shouhouYuyue);
            } else {
                //查询对应区域的物流负责人
                List<String> list = receivePersonConfigMapper.selectRegionByAreaIdAndMainRole(shouhouYuyue.getAreaid(), LOGISTICS_RESPONSIBLE);
                if(CollectionUtils.isNotEmpty(list)){
                    list.forEach(ch999IdsJoiner::add);
                }
                Boolean isPush = Optional.ofNullable(appointmentFormPush.getIsPush()).orElse(Boolean.TRUE);
                if(isPush){
                    //设置延迟队列推送时间为一小时
                    appointmentFormPush.setDelaySecond(ONE_HOUR);
                }
            }
            //消息重复推送
            appointmentFormPushService.pushDelayDetectionMsg(appointmentFormPush);
            //OA消息推送
            String msg="你负责的区域存在超时未确认的 上门取件预约单，请及时跟进确认。订单号："+shouhouYuyue.getId();
            sendOaShouhouMsg(ch999IdsJoiner,msg,shouhouYuyue.getId(),appointmentFormPush);
        }
    }

    /**
     * 延迟队列监听消费
     * 处理方式为邮寄送修的预约单
     * @param job
     */
    @LmstfyConsume(queues = MAIL_APPOINTMENTFORM_QUEUE, clientBeanName = "firstLmstfyClient")
    public void consumeMailAppointmentFormQueue(Job job) {
        log.warn("MAIL_APPOINTMENTFORM_QUEUE消费到数据：{}", JSONUtil.toJsonStr(job));
        AnalysisResultBO analysisResult = getAnalysisResult(job);
        ShouhouYuyue shouhouYuyue = analysisResult.getShouhouYuyue();
        AppointmentFormPush appointmentFormPush = analysisResult.getAppointmentFormPush();
        log.warn("MAIL_APPOINTMENTFORM_QUEUE消费到数据详情：{}", JSONUtil.toJsonStr(appointmentFormPush));
        Integer stats = shouhouYuyue.getStats();
        Boolean isdel = Optional.ofNullable(shouhouYuyue.getIsdel()).orElse(Boolean.FALSE);
        /**
         * 预约单在“业务确认”状态超72小时未变更状态，系统给订单业务确认人推送超时信息，推送一次即可
         * 推送方式：oa “售后通知
         * 推送内容：你负责的区域存在超时未确认的 邮寄送修预约单，请即时跟进确认。订单号 XXXX，XXXX，XXX
         */
        if(YuyueStatusEnum.YWQR.getCode().equals(stats) && !isdel){
            String checkUser = Optional.ofNullable(shouhouYuyue.getCheckUser()).orElse("");
            String ch999Id = receivePersonConfigMapper.getCh999IdByName(checkUser);
            StringJoiner ch999IdsJoiner = new StringJoiner(",");
            //OA消息推送
            String msg="你负责的区域存在超时未确认的 上门取件预约单，请及时跟进确认。订单号："+shouhouYuyue.getId();
            sendOaShouhouMsg(ch999IdsJoiner.add(ch999Id),msg,shouhouYuyue.getId(),appointmentFormPush);
        }
    }
}
