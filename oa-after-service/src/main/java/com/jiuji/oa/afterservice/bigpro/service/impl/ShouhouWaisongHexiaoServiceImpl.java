package com.jiuji.oa.afterservice.bigpro.service.impl;

import cn.hutool.core.net.URLDecoder;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.http.ContentType;
import cn.hutool.http.HttpUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ch999.common.util.vo.Result;
import com.jiuji.oa.afterservice.bigpro.bo.OpenIdInfoBo;
import com.jiuji.oa.afterservice.bigpro.bo.OpenValidInfoBo;
import com.jiuji.oa.afterservice.bigpro.bo.WaisongHexiaoApplyBO;
import com.jiuji.oa.afterservice.bigpro.bo.WaisongHexiaoQuery;
import com.jiuji.oa.afterservice.bigpro.bo.logistics.LogisticsOrderBO;
import com.jiuji.oa.afterservice.bigpro.dao.WaisongBindUserMapper;
import com.jiuji.oa.afterservice.bigpro.dao.WaisongHexiaoMapper;
import com.jiuji.oa.afterservice.bigpro.enums.AttachmentsEnum;
import com.jiuji.oa.afterservice.bigpro.enums.AttachmentsKindEnum;
import com.jiuji.oa.afterservice.bigpro.enums.OaMesTypeEnum;
import com.jiuji.oa.afterservice.bigpro.po.Shouhou;
import com.jiuji.oa.afterservice.bigpro.po.WaiSongAlipayBindUserPO;
import com.jiuji.oa.afterservice.bigpro.po.WaisongHexiaoPo;
import com.jiuji.oa.afterservice.bigpro.po.Wxkcoutput;
import com.jiuji.oa.afterservice.bigpro.service.*;
import com.jiuji.oa.afterservice.bigpro.vo.req.FileReq;
import com.jiuji.oa.afterservice.bigpro.vo.res.WaisongHexiaoApplyVO;
import com.jiuji.oa.afterservice.bigpro.vo.res.WaisongHexiaoVO;
import com.jiuji.oa.afterservice.cloud.service.StockCloud;
import com.jiuji.oa.afterservice.common.bo.OaUserBO;
import com.jiuji.oa.afterservice.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.afterservice.common.constant.DataSourceConstants;
import com.jiuji.oa.afterservice.common.constant.RedisKeys;
import com.jiuji.oa.afterservice.common.constant.RequestAttrKeys;
import com.jiuji.oa.afterservice.common.enums.MTableInfoEnum;
import com.jiuji.oa.afterservice.common.enums.RankEnum;
import com.jiuji.oa.afterservice.common.enums.XtenantEnum;
import com.jiuji.oa.afterservice.common.exception.CustomizeException;
import com.jiuji.oa.afterservice.common.source.InwcfUrlSource;
import com.jiuji.oa.afterservice.common.util.CommenUtil;
import com.jiuji.oa.afterservice.common.util.DateUtil;
import com.jiuji.oa.afterservice.common.util.SpringContextUtil;
import com.jiuji.oa.afterservice.common.vo.res.ListBean;
import com.jiuji.oa.afterservice.log.po.ShouhouWaisongReimbursementLog;
import com.jiuji.oa.afterservice.log.service.ShouhouWaisongReimbursementLogService;
import com.jiuji.oa.afterservice.other.bo.PingzhengBO;
import com.jiuji.oa.afterservice.other.bo.PingzhengResultBO;
import com.jiuji.oa.afterservice.other.service.VoucherService;
import com.jiuji.oa.afterservice.sys.service.AuthConfigService;
import com.jiuji.oa.afterservice.sys.service.SysConfigService;
import com.jiuji.oa.orginfo.areainfo.client.AreaInfoClient;
import com.jiuji.oa.orginfo.areainfo.vo.AreaInfo;
import com.jiuji.oa.orginfo.sysconfig.client.SysConfigClient;
import com.jiuji.oa.orginfo.userinfo.client.UserInfoClient;
import com.jiuji.oa.orginfo.userinfo.vo.Ch999UserVo;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.common.vo.ResultCode;
import com.jiuji.tc.foundation.db.annotation.DS;
import com.jiuji.tc.utils.common.DecideUtil;
import com.jiuji.tc.utils.constants.IntConstant;
import com.jiuji.tc.utils.constants.NumberConstant;
import com.jiuji.tc.utils.constants.SysConfigConstant;
import com.jiuji.tc.utils.transaction.MultipleTransaction;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.Charset;
import java.text.MessageFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.function.BiFunction;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * 售后外送报账服务类
 * <AUTHOR>
 * @since 2021/5/25 11:54
 */
@Service
@Slf4j
public class ShouhouWaisongHexiaoServiceImpl extends ServiceImpl<WaisongHexiaoMapper, WaisongHexiaoPo> implements ShouhouWaisongHexiaoService {
    @Autowired
    private WxkcoutputService wxkcoutputService;
    @Autowired
    private AttachmentsService attachmentsService;
    @Autowired
    private ShouhouWaisongReimbursementLogService waisongReimbursementLogService;

    @Autowired
    private AbstractCurrentRequestComponent currentRequestComponent;
    @Autowired
    private ShouhouService shouhouService;
    @Autowired
    private ShouhouWaisongReimbursementLogService logService;
    @Autowired
    private AuthConfigService authConfigService;
    @Autowired
    private SmsService smsService;
    @Autowired
    private UserInfoClient userInfoClient;
    @Autowired
    private WaisongBindUserMapper waisongBindUserMapper;
    @Autowired
    private SysConfigService sysConfigService;
    @Autowired
    private StockCloud stockCloud;
    @Autowired
    private RedissonClient redissonClient;
    @Resource
    private SysConfigClient sysConfigClient;
    @Autowired
    @Qualifier("oaRabbitTemplate")
    private RabbitTemplate rabbitTemplate;
    @Autowired
    private AreaInfoClient areaInfoClient;
    @Autowired
    private VoucherService voucherService;
    @Resource
    private InwcfUrlSource inwcfUrlSource;
    /**维修id分布式锁定key格式化*/
    private static final String WX_ID_LOCK_FORMAT = "OA:JAVA:AFTER:LOCK_WAISONG_HEXIAO_WX_ID:%s";
    /**物流详情key*/
    private static final String WULIU_DETAIL_URL_MESSAGE_FORMAT = "{0}/addOrder/wuliu?wuliuid={1,number,#}";
    /**物理idkey*/
    private static final String LOGISTICS_ID_KEY = "logisticsId";
    /**维修id锁提示信息*/
    private static final String LOCK_WX_ID_USER_MSG = "此维修单处于报销操作中,请稍后重试!";

    @Override
    public R<Map<String, Object>> getDataForList() {
        Map<String, Object> result = new HashMap<>(NumberConstant.THREE);
        //1.检索类型 维修单号 机型 配件名称 支出单号 物流单
        result.put("searchTypeEnum", Arrays.stream(WaisongHexiaoQuery.SearchTypeEnum.values())
                .map(e->new ListBean.OptionsBean(e.getCode()+"",e.getMessage())).collect(Collectors.toList()));
        //2.地区
        //3.处理状态 待审核 已打款 待财务审核 已结算 业务驳回 财务驳回 删除
        result.put("statusEnum", Arrays.stream(WaisongHexiaoPo.StatusEnum.values())
                .map(e->new ListBean.OptionsBean(e.getCode()+"",e.getMessage())).collect(Collectors.toList()));
        //4.票据是否收回
        result.put("recoverBillStatusEnum", Arrays.stream(WaisongHexiaoPo.RecoverBillStatusEnum.values())
                .map(e->new ListBean.OptionsBean(e.getCode()+"",e.getMessage())).collect(Collectors.toList()));
        //5.添加时间类型
        result.put("timeType",Arrays.stream(WaisongHexiaoQuery.TimeTypeEnum.values())
                .map(e->new ListBean.OptionsBean(e.getCode()+"",e.getMessage())).collect(Collectors.toList()));
        //查询模型
        WaisongHexiaoQuery queryModel = new WaisongHexiaoQuery();
        queryModel.setTimeType(1);
        queryModel.setTimeRange(new LocalDateTime[0]);
        queryModel.setSize(NumberConstant.TWENTY);
        queryModel.setCurrent(1);
        result.put("queryModel", queryModel);
        return R.success(result);
    }

    @Override
    public R<WaisongHexiaoApplyVO> apply(WaisongHexiaoApplyBO apply) {
        RLock lock = redissonClient.getLock(String.format(WX_ID_LOCK_FORMAT, apply.getWxId()));
        if (!lock.tryLock()) {
            return R.error(LOCK_WX_ID_USER_MSG);
        }
        try {
            if (apply.getId() == 0) {
                apply.setId(null);
            }
            //入库
            OaUserBO oaUser = currentRequestComponent.getCurrentStaffId();
            //查询收款账号是否已经绑定
            WaiSongAlipayBindUserPO waiSongAlipayBindUser = waisongBindUserMapper.selectOne(new LambdaQueryWrapper<WaiSongAlipayBindUserPO>()
                    .eq(WaiSongAlipayBindUserPO::getWxId, apply.getWxId()));
            //校验报销费用
//            Wxkcoutput wxkcoutput = wxkcoutputService.getByIdAndXtenantAndAuthPart(apply.getWxkId(), oaUser.getXTenant()
//                    , oaUser.getAreaId(), Boolean.TRUE.equals(authConfigService.isAuthPart(oaUser)), oaUser.getAuthorizeId());

            Wxkcoutput wxkcoutput =CommenUtil.autoQueryHist(() ->wxkcoutputService.getByIdAndXtenantAndAuthPart(apply.getWxkId(), oaUser.getXTenant()
                    , oaUser.getAreaId(), Boolean.TRUE.equals(authConfigService.isAuthPart(oaUser)), oaUser.getAuthorizeId()), MTableInfoEnum.wxkcoutput,apply.getWxkId());

            //数据未删除 且 业务未删除 一个维修单只能报销一个配件
            WaisongHexiaoPo old = baseMapper.selectOne(new LambdaQueryWrapper<WaisongHexiaoPo>()
                    .eq(apply.getId() != null, WaisongHexiaoPo::getId, apply.getId())
                    .eq(WaisongHexiaoPo::getWxId, apply.getWxId()).eq(WaisongHexiaoPo::getDel, false)
                    .ne(WaisongHexiaoPo::getStatus, WaisongHexiaoPo.StatusEnum.DELETED.getCode()));
            //校验申请数据
            checkApplyData(apply, waiSongAlipayBindUser, wxkcoutput, old);

            //设置绑定账号和物流单号
            apply.setAlipayBindAccount(waiSongAlipayBindUser.getUserId());
            apply.setLogisticsId(waiSongAlipayBindUser.getLogisticsId());

            WaisongHexiaoPo waisongHexiao = WaisongHexiaoPo.from(old, apply, oaUser.getUserId(), oaUser.getUserName(),
                    oaUser.getAreaId(), oaUser.getArea(), wxkcoutput.getPpriceid());

            MultipleTransaction.build()
                    .execute(DataSourceConstants.DEFAULT, () ->
                            this.saveWaisongHexiao(apply, oaUser, waisongHexiao)
                    )
                    .execute(DataSourceConstants.DEFAULT, () ->
                            //存入操作日志
                            waisongReimbursementLogService.save(new ShouhouWaisongReimbursementLog().setBusinessId(waisongHexiao.getId())
                                    .setBusinessType(ShouhouWaisongReimbursementLog.BusinessTypeEnum.HE_XIAO_ORDER.getCode())
                                    .setContent(String.format("添加申请 ，状态变为[%s]", WaisongHexiaoPo.StatusEnum.CHECKING.getMessage()))
                                    .setCreateTime(LocalDateTime.now()).setCreateUser(oaUser.getUserName())
                                    .setIsDel(false).setShowType(1))
                    ).commit();

            return R.success("提交成功,等待审核!", this.getDataForApply(waisongHexiao.getId(), waisongHexiao.getWxId()
                    , waisongHexiao.getWxkId()).getData());
        } finally {
            lock.unlock();
        }
    }

    private void saveWaisongHexiao(WaisongHexiaoApplyBO apply, OaUserBO oaUserBO, WaisongHexiaoPo waisongHexiao) {
        int n;
        if (waisongHexiao.getId() == null) {
            n = baseMapper.insert(waisongHexiao);
        } else {
            n = baseMapper.updateById(waisongHexiao);
        }
        if(n<1){
            throw new CustomizeException("维修报销保存失败!");
        }
        //保存附件
        attachmentsService.saveAttachemnts(apply.getFiles(), waisongHexiao.getId(), AttachmentsEnum.SHOUHOU.getCode(),
                oaUserBO.getUserId(), AttachmentsKindEnum.DELIVERY_REIMBURSEMENT.getCode(), null);
    }

    /**
     * 校验申请数据
     * @param apply
     * @param waiSongAlipayBindUser
     * @param wxkcoutput
     * @param old
     * @return
     */
    private static void checkApplyData(WaisongHexiaoApplyBO apply, WaiSongAlipayBindUserPO waiSongAlipayBindUser
            , Wxkcoutput wxkcoutput, WaisongHexiaoPo old) {
        if(waiSongAlipayBindUser == null || StringUtils.isEmpty(waiSongAlipayBindUser.getUserId())){
            throw new CustomizeException("收款账号未绑定!");
        }
        if(waiSongAlipayBindUser.getLogisticsId() == null){
            throw new CustomizeException("物流单未绑定!");
        }
        if(wxkcoutput == null){
            throw new CustomizeException("配件单号不对！");
        }
        if(Objects.compare(wxkcoutput.getInprice(), apply.getPrice(),BigDecimal::compareTo) != 0){
            throw new CustomizeException("报销费用不对,不允许报销!");
        }
        //新建 已经报销过,不允许重复报销
        if(old != null){
            if( apply.getId() == null){
                throw new CustomizeException("不允许重复报销!");
            }else if(!Objects.equals(old.getStatus(), WaisongHexiaoPo.StatusEnum.BUSINESS_REJECT.getCode())){
                // 只有 业务驳回的时候才可以编辑
                throw new CustomizeException("当前状态,不允许编辑!");
            }
        }else if( apply.getId() != null){
            throw new CustomizeException("无法编辑,找不到报销数据!");
        }
    }

    @Override
    public R<WaisongHexiaoApplyVO> getDataForApply(Integer id, Integer wxId, Integer wxkId) {
        WaisongHexiaoApplyVO result = new WaisongHexiaoApplyVO();
        result.setProcessLogs(new LinkedList<>());

        OaUserBO oaUser = currentRequestComponent.getCurrentStaffId();

        //查询申请单信息
        WaisongHexiaoPo waisongHexiao = CommenUtil.autoQueryHist(()->{
            return baseMapper.getHexiao(id,wxId,wxkId, oaUser.getXTenant()
                    , oaUser.getAreaId(),Boolean.TRUE.equals(authConfigService.isAuthPart( oaUser)), oaUser.getAuthorizeId());
        });
        if(id != null && waisongHexiao == null){
            return R.error("外送报账申请不存在!");
        }
        Integer tempWxId = Optional.ofNullable(waisongHexiao).map(WaisongHexiaoPo::getWxId).orElse(wxId);
        Integer tempWxkId = Optional.ofNullable(waisongHexiao).map(WaisongHexiaoPo::getWxkId).orElse(wxkId);
        LocalDateTime today = LocalDateTime.now();
        WaisongHexiaoApplyBO waisongHexiaoApplyBO = null;
        if (waisongHexiao == null){
            result.setPageType(WaisongHexiaoApplyVO.PageTypeEnum.EDIT.getCode());
            waisongHexiaoApplyBO = new WaisongHexiaoApplyBO();
            //查询维修单信息
            Shouhou shouhou = CommenUtil.autoQueryHist(()->shouhouService.getById(tempWxId), MTableInfoEnum.SHOUHOU,tempWxId);
            if(shouhou == null){
                return R.error("维修单不存在!");
            }
            waisongHexiaoApplyBO.setWxCreateTime(shouhou.getModidate());
            waisongHexiaoApplyBO.setWxId(shouhou.getId());
            //查询配件单信息
           // Wxkcoutput wxkcoutput =CommenUtil.autoQueryHist(()->wxkcoutputService.getById(tempWxkId));
            Wxkcoutput wxkcoutput = CommenUtil.autoQueryHist(() ->wxkcoutputService.getById(tempWxkId), MTableInfoEnum.wxkcoutput,tempWxkId);
            if(wxkcoutput == null){
                return R.error("维修配件不存在!");
            }
            waisongHexiaoApplyBO.setWxkName(wxkcoutput.getName());
            waisongHexiaoApplyBO.setPrice(wxkcoutput.getInprice());
            waisongHexiaoApplyBO.setWxkId(wxkcoutput.getId());
        }else{
            waisongHexiaoApplyBO = getWaisongHexiaoApplyDetail(result, oaUser, waisongHexiao, today);
        }
        handleAlipayInfo(result, waisongHexiaoApplyBO, oaUser, tempWxId, tempWxkId, today);
        handleReimbursementInfo(result, waisongHexiaoApplyBO, tempWxId);
        return R.success(result).addAllBusinessLog(SpringContextUtil.getRequestKeyListMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG));
    }

    private void handleReimbursementInfo(WaisongHexiaoApplyVO result, WaisongHexiaoApplyBO waisongHexiaoApplyBO, Integer tempWxId) {
        //查询绑定的账号和物流单日志
        List<ShouhouWaisongReimbursementLog> logs = logService.listEffect(tempWxId, 1);
        result.getProcessLogs().addAll(logs);
        result.setApplyBO(waisongHexiaoApplyBO);

        //票据枚举
        result.setBillSelect(Arrays.stream(WaisongHexiaoPo.BillEnum.values())
                .map(e->new ListBean.OptionsBean(e.getCode()+"",e.getMessage())).collect(Collectors.toList()));
        //服务类型枚举
        result.setServiceTypeSelect(Arrays.stream(WaisongHexiaoPo.ServiceTypeEnum.values())
                .map(e->new ListBean.OptionsBean(e.getCode()+"",e.getMessage())).collect(Collectors.toList()));
    }

    private void handleAlipayInfo(WaisongHexiaoApplyVO result, WaisongHexiaoApplyBO waisongHexiaoApplyBO, OaUserBO oaUser, Integer tempWxId, Integer tempWxkId, LocalDateTime today) {
        //查询支付宝绑定账号和物流单号
        //设置绑定地址
        if (XtenantEnum.isJiujiXtenant()) {
            OpenValidInfoBo openValidInfoBo = new OpenValidInfoBo().setId(tempWxkId)
                    .setIdTypeEnum(OpenValidInfoBo.IdTypeEnum.WAISONG_ID)
                    .setOrderBusinessTypeEnum(OpenValidInfoBo.OrderBusinessTypeEnum.WEIXIU_BAOXIAO)
                    .setSubId(tempWxId)
                    .setOpenType(OpenIdInfoBo.OpenType.ALIPAY);
            result.setAlipayBindUrl(SpringUtil.getBean(TuiHuanOpenIdService.class).getValidUrlV2(openValidInfoBo, oaUser).map(OpenIdInfoBo.OpenIdUrlBo::getUrl).orElse(""));
        } else {
        result.setAlipayBindUrl(MessageFormat.format("{0}/notifypay/alipayUserInfo/index.aspx?id={1,number,#}_4_{2,number,#}"
                ,sysConfigService.getValueByCode(SysConfigConstant.M_URL), tempWxkId, oaUser.getUserId()));
        }
        WaiSongAlipayBindUserPO waiSongAlipayBindUser = CommenUtil.autoQueryHist(()-> waisongBindUserMapper.selectOne(new LambdaQueryWrapper<WaiSongAlipayBindUserPO>()
                .eq(WaiSongAlipayBindUserPO::getWxId, tempWxId)));
        if(waiSongAlipayBindUser != null){
            if(waiSongAlipayBindUser.getLogisticsId() != null){
                waisongHexiaoApplyBO.setLogisticsId(waiSongAlipayBindUser.getLogisticsId());
                result.setLogisticsDetailUrl(MessageFormat.format(WULIU_DETAIL_URL_MESSAGE_FORMAT
                        ,sysConfigService.getValueByCode(SysConfigConstant.OA_URL)
                        ,waiSongAlipayBindUser.getLogisticsId()));
            }
            if(StringUtils.isNotEmpty(waiSongAlipayBindUser.getUserId())){
                waisongHexiaoApplyBO.setAlipayBindAccount(waiSongAlipayBindUser.getUserId());
                //支付保账号当月收款次数
                Integer alipayReceiveCount = CommenUtil.autoQueryHist(() -> baseMapper
                        .selectCount(new LambdaQueryWrapper<WaisongHexiaoPo>()
                                .eq(WaisongHexiaoPo::getAlipayBindAccount, waiSongAlipayBindUser.getUserId())
                                .in(WaisongHexiaoPo::getStatus, NumberConstant.ONE, NumberConstant.TWO, NumberConstant.THREE, NumberConstant.FIVE)
                                .between(WaisongHexiaoPo::getCreateTime, today.with(TemporalAdjusters.firstDayOfMonth())
                                        .withHour(0).withMinute(0).withSecond(0).withNano(0), today.with(TemporalAdjusters.lastDayOfMonth())
                                        .withHour(NumberConstant.TWENTY_THREE).withMinute(NumberConstant.FIFTY_NINE).withSecond(NumberConstant.FIFTY_NINE))));
                result.setAlipayReceiveCount(alipayReceiveCount);
            }
            result.setAlipayBindUserAvatar(waiSongAlipayBindUser.getUserAvatar());
            result.setAlipayBindNickname(waiSongAlipayBindUser.getNickname());
        }
    }

    private WaisongHexiaoApplyBO getWaisongHexiaoApplyDetail(WaisongHexiaoApplyVO result, OaUserBO oaUser, WaisongHexiaoPo waisongHexiao, LocalDateTime today) {
        WaisongHexiaoApplyBO waisongHexiaoApplyBO;
        waisongHexiaoApplyBO = waisongHexiao.toApply();
        //统计申请人的当月的报账
        WaisongHexiaoApplyVO.StatisticsApplicantMonth statisticsMonth = CommenUtil.autoQueryHist(()->{
            return baseMapper.statisticsByApplicantId(waisongHexiao.getApplicantId()
                    , today.with(TemporalAdjusters.firstDayOfMonth()).withHour(0).withMinute(0).withSecond(0).withNano(0)
                    , today.with(TemporalAdjusters.lastDayOfMonth()).withHour(NumberConstant.TWENTY_THREE)
                            .withMinute(NumberConstant.FIFTY_NINE).withSecond(NumberConstant.FIFTY_NINE));
        });
        result.setApplicantMonthTotal(statisticsMonth.getApplicantMonthTotal());
        result.setApplicantMonthCount(statisticsMonth.getApplicantMonthCount());
        //查询配件单信息
        Optional<Wxkcoutput> wxkcoutputOpt = Optional.ofNullable(CommenUtil.autoQueryHist(()->wxkcoutputService.getById(waisongHexiao.getWxkId())));
        waisongHexiaoApplyBO.setWxkName(wxkcoutputOpt.map(Wxkcoutput::getName).orElse(null));
        //获取id值,方便后面使用
        Integer tempId = waisongHexiao.getId();
        //查询附件信息
        List<FileReq> attachmentsList = attachmentsService.getAttachmentsList(tempId, AttachmentsEnum.SHOUHOU.getCode()
                , AttachmentsKindEnum.DELIVERY_REIMBURSEMENT.getCode(),null);
        waisongHexiaoApplyBO.setFiles(attachmentsList);
        result.setStatus(waisongHexiao.getStatus());
        Optional<WaisongHexiaoPo.StatusEnum> statusOpt = WaisongHexiaoPo.StatusEnum.valueOfByCode(waisongHexiao.getStatus());
        result.setStatusText(statusOpt.map(s-> MessageFormat.format("{0} {1}",s.getMessage(),s.getRemindMessage())).orElse("无"));
        //查询操作日志
        List<ShouhouWaisongReimbursementLog> logs = logService.listEffect(tempId
                , ShouhouWaisongReimbursementLog.BusinessTypeEnum.HE_XIAO_ORDER.getCode());
        //页面类型
        WaisongHexiaoApplyVO.PageTypeEnum pageTypeEnum = getPageTypeEnum(statusOpt.orElse(null), waisongHexiao, oaUser);
        result.setPageType(pageTypeEnum.getCode());
        result.getProcessLogs().addAll(logs);
        return waisongHexiaoApplyBO;
    }

    private static WaisongHexiaoApplyVO.PageTypeEnum getPageTypeEnum(WaisongHexiaoPo.StatusEnum status
            , WaisongHexiaoPo waisongHexiao, OaUserBO oaUser){
        return Optional.ofNullable(status).map(s -> getPageTypeEnumLambda(waisongHexiao, oaUser, s)).orElse(WaisongHexiaoApplyVO.PageTypeEnum.DETAIL);
    }

    private static WaisongHexiaoApplyVO.PageTypeEnum getPageTypeEnumLambda(WaisongHexiaoPo waisongHexiao, OaUserBO oaUser, WaisongHexiaoPo.StatusEnum s) {
        WaisongHexiaoApplyVO.PageTypeEnum pageType = WaisongHexiaoApplyVO.PageTypeEnum.DETAIL;
        if (Boolean.TRUE.equals(waisongHexiao.getDel()) && Objects.equals(waisongHexiao.getStatus(), WaisongHexiaoPo.StatusEnum.DELETED.getCode())) {
            //已删除 只能是详情页
            return pageType;
        }
        switch (s) {
            case BUSINESS_REJECT:
                pageType = WaisongHexiaoApplyVO.PageTypeEnum.EDIT;
                break;
            case CHECKING:
                if (RankEnum.hasAuthority( oaUser.getRank(), RankEnum.WX_REIMBURSEMENT_CHECK)) {
                    pageType = WaisongHexiaoApplyVO.PageTypeEnum.APPROVE;
                }
                break;
            default:
                break;
        }
        return pageType;
    }

    @Override
    @DS(DataSourceConstants.CH999_OA_NEW)
    public R<Page<WaisongHexiaoVO>> list(WaisongHexiaoQuery query) {
        R<Page<WaisongHexiaoVO>> result = R.success(new Page<>());
        Map<String,Object> exData = new HashMap<>(NumberConstant.THREE);
        exData.put("totalPrice", BigDecimal.ZERO.setScale(NumberConstant.TWO, RoundingMode.DOWN));
        result.setExData(exData);
        OaUserBO oaUser = currentRequestComponent.getCurrentStaffId();
        //授权隔离
        query.setXtenant( oaUser.getXTenant());
        query.setCurrAreaId( oaUser.getAreaId());
        query.setAuthPart(Boolean.TRUE.equals(authConfigService.isAuthPart( oaUser)));
        query.setAuthorizeId( oaUser.getAuthorizeId());

        Page<WaisongHexiaoVO> page = new Page<>(query.getCurrent(), query.getSize());
        Page<WaisongHexiaoVO> finalPage = page;
        Integer selectHis = Optional.ofNullable(query.getSelectHis()).orElse(NumberConstant.ZERO);
        if(NumberConstant.ONE.equals(selectHis)){
            page = MultipleTransaction.query(DataSourceConstants.OA_NEW_HIS, () -> baseMapper.selectLists(finalPage,query));
        } else {
            page = baseMapper.selectLists(finalPage,query);
        }
        result.setData(page);
        Optional.ofNullable(page.getRecords()).ifPresent(records->{
            handleBill(records);
            //统计当前查询的总金额
            if(NumberConstant.ONE.equals(selectHis)){
                exData.put("totalPrice",MultipleTransaction.query(DataSourceConstants.OA_NEW_HIS, () -> baseMapper.sumPrice(query)));
            } else {
                exData.put("totalPrice",baseMapper.sumPrice(query));
            }

        });
        return result;
    }

    private static void handleBill(List<WaisongHexiaoVO> records) {
        for (WaisongHexiaoVO hexiaoVO : records) {
            //转换bill
            hexiaoVO.setBills(CommenUtil.intToMultiple(hexiaoVO.getBillsInt()));
            hexiaoVO.setServiceTypeName(WaisongHexiaoPo.ServiceTypeEnum.valueOfByCode(hexiaoVO.getServiceType())
                    .map(WaisongHexiaoPo.ServiceTypeEnum::getMessage).orElse(""));
            hexiaoVO.setBillNames(hexiaoVO.getBills().stream()
                    .map(WaisongHexiaoPo.BillEnum::valueOfByCode)
                    .filter(Optional::isPresent)
                    .map(opt->opt.get().getMessage())
                    .collect(Collectors.joining("+")));
            hexiaoVO.setStatusName(WaisongHexiaoPo.StatusEnum.valueOfByCode(hexiaoVO.getStatus())
                    .map(WaisongHexiaoPo.StatusEnum::getMessage).orElse(""));
            hexiaoVO.setRecoverBillStatusName(WaisongHexiaoPo.RecoverBillStatusEnum.valueOfByCode(hexiaoVO.getRecoverBillStatus())
                    .map(WaisongHexiaoPo.RecoverBillStatusEnum::getMessage).orElse(""));
        }
    }

    @Override
    public void export(WaisongHexiaoQuery query, OutputStream outputStream) {
        //设置最大数据量
        query.setSize(NumberConstant.FIVE_THOUSAND);
        // 通过工具类创建writer，默认创建xls格式
        ExcelWriter writer = ExcelUtil.getWriter();
        //自定义标题别名
        writer.addHeaderAlias("area", "门店");
        writer.addHeaderAlias("wxId", "维修单号");
        writer.addHeaderAlias("productColor", "机型");
        writer.addHeaderAlias("applicant", "提交人");
        writer.addHeaderAlias("productName", "配件名称");
        writer.addHeaderAlias("serviceTypeName", "服务");
        writer.addHeaderAlias("price", "报账费用");
        writer.addHeaderAlias("billNames", "附带单据");
        writer.addHeaderAlias("createTime", "提交时间");
        writer.addHeaderAlias("statusName", "状态");
        writer.addHeaderAlias("accountId", "财务支出单");
        writer.addHeaderAlias(LOGISTICS_ID_KEY, "物流单");
        writer.addHeaderAlias("recoverBillStatusName", "票据");

        List<Map<String,Object>> data = Optional.ofNullable(list(query).getData()).map(Page::getRecords)
                .filter(Objects::nonNull).map(records->records.stream().map(ShouhouWaisongHexiaoServiceImpl::handleExportData).collect(Collectors.toList()))
                .orElse(Collections.emptyList());

        try {
            writer.setOnlyAlias(true);
            writer.setColumnWidth(NumberConstant.FOUR,NumberConstant.THIRTY);
            writer.setColumnWidth(NumberConstant.FIVE,NumberConstant.THIRTY);
            writer.setColumnWidth(NumberConstant.SEVEN, NumberConstant.TWENTY);
            writer.setColumnWidth(NumberConstant.EIGHT,NumberConstant.TWENTY);
            // 一次性写出内容，使用默认样式，强制输出标题
            writer.write(data, true);
            //out为OutputStream，需要写出到的目标流
            writer.flush(outputStream, false);
        } finally {
            // 关闭writer，释放内存
            writer.close();
        }
    }

    private static Map<String, Object> handleExportData(WaisongHexiaoVO hexiaoVO) {
        Map<String,Object> map = new HashMap<>(NumberConstant.SIXTEEN);
        map.put("area", hexiaoVO.getArea());
        map.put("wxId", hexiaoVO.getWxId());
        map.put("productColor", hexiaoVO.getProductColor());
        map.put("applicant", hexiaoVO.getApplicant());
        map.put("productName", hexiaoVO.getProductName());
        map.put("serviceTypeName", hexiaoVO.getServiceTypeName());
        map.put("price", hexiaoVO.getPrice());
        map.put("billNames", hexiaoVO.getBillNames());
        map.put("createTime", hexiaoVO.getCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        map.put("statusName", hexiaoVO.getStatusName());
        map.put("accountId", hexiaoVO.getAccountId());
        map.put(LOGISTICS_ID_KEY, hexiaoVO.getLogisticsId());
        map.put("recoverBillStatusName", hexiaoVO.getRecoverBillStatusName());
        return map;
    }

    @Override
    public R<WaisongHexiaoApplyVO> approve(int id, int answer, String reason) {
        if(answer == 0 && StringUtils.isEmpty(reason)){
            return R.error("驳回必须填写原因!");
        }

        OaUserBO oaUser = currentRequestComponent.getCurrentStaffId();
        //申请单未删除 维修单未删除 配件单未删除
        WaisongHexiaoPo waisongHexiao = baseMapper.getHexiaoNotDelAndWxNotDelAndWxkNotDel(id,null,null, oaUser.getXTenant()
                , oaUser.getAreaId(),Boolean.TRUE.equals(authConfigService.isAuthPart( oaUser)), oaUser.getAuthorizeId());
        if(waisongHexiao == null){
            return R.error("找不到符合条件的订单!");
        }
        RLock lock = redissonClient.getLock(String.format(WX_ID_LOCK_FORMAT, waisongHexiao.getWxId()));
        if(!lock.tryLock()){
            return R.error(LOCK_WX_ID_USER_MSG);
        }
        try {
            //判断是否允许审批
            Optional<WaisongHexiaoPo.StatusEnum> statusEnum = WaisongHexiaoPo.StatusEnum.valueOfByCode(waisongHexiao.getStatus());
            Optional<WaisongHexiaoPo.StatusEnum> newStatusOpt = WaisongHexiaoPo.StatusEnum.approveNewStatus(answer, statusEnum);
            if(!newStatusOpt.isPresent()){
                return R.error(String.format("%s状态不允许审批!", statusEnum.map(WaisongHexiaoPo.StatusEnum::getMessage).orElse("未知")));
            }
            //执行审批操作
            LambdaUpdateWrapper<WaisongHexiaoPo> updateWrapper = new LambdaUpdateWrapper<>();
            WaisongHexiaoPo.StatusEnum newStatus = newStatusOpt.get();
            updateWrapper.set(WaisongHexiaoPo::getStatus, newStatus.getCode());

            switch (newStatus){
                case FINISHED:
                    updateWrapper.set(WaisongHexiaoPo::getAccountTime,LocalDateTime.now());
                    break;
                case PAID:
                    updateWrapper.set(WaisongHexiaoPo::getPayTime,LocalDateTime.now());
                    break;
                default:
                    //驳回
                    break;
            }
            updateWrapper.eq(WaisongHexiaoPo::getId,id);
            ShouhouWaisongHexiaoService waisongHexiaoService = (ShouhouWaisongHexiaoService) AopContext.currentProxy();
            handleApplyDb(reason, waisongHexiaoService, oaUser, waisongHexiao, updateWrapper, newStatus);
            return R.success(String.format("审批成功,新状态为:%s %s", newStatus.getMessage(),newStatus.getRemindMessage())
                    ,this.getDataForApply(id,null,null).getData());
        } finally {
            lock.unlock();
        }
    }

    private void handleApplyDb(String reason, ShouhouWaisongHexiaoService waisongHexiaoService, OaUserBO oaUser, WaisongHexiaoPo waisongHexiao
            , LambdaUpdateWrapper<WaisongHexiaoPo> updateWrapper, WaisongHexiaoPo.StatusEnum newStatus) {
        BigDecimal payPrice = waisongHexiao.getPrice().setScale(NumberConstant.TWO, RoundingMode.HALF_UP);
        waisongHexiao.setPrice(payPrice);
        //记账生产凭证
        int pzId = addPingzheng(oaUser, waisongHexiao, payPrice);
        updateWrapper.set(WaisongHexiaoPo::getPzId,pzId);
        try {
            MultipleTransaction.build()
                    .execute(DataSourceConstants.DEFAULT, () -> waisongHexiaoService.update(updateWrapper))
                    .execute(DataSourceConstants.DEFAULT, () -> executePay(reason, oaUser, waisongHexiao, newStatus,pzId))
                    .commit();
        } catch (RuntimeException e) {
            //删除凭证
            voucherService.delPingZheng(pzId);
            throw e;
        }
    }

    private void executePay(String reason, OaUserBO oaUser, WaisongHexiaoPo waisongHexiao, WaisongHexiaoPo.StatusEnum newStatus, int pzId) {
        //存入操作日志
        String logContent = newStatus.getMessage();
        String sendMsg = null;
        if(WaisongHexiaoPo.StatusEnum.PAID == newStatus){
            //打款,接口会返回已打款
            String url = MessageFormat.format("{0}/oaapi.svc/rest/MaintenanceFeePay",sysConfigService.getValueByCode(SysConfigConstant.IN_WCF_HOST));
            JSONObject param = new JSONObject(NumberConstant.FOUR);

            param.put("businessId", waisongHexiao.getWxkId());

            param.put("payPrice",waisongHexiao.getPrice());
            param.put("payUserId", waisongHexiao.getAlipayBindAccount());
            //添加售后id参数 ->打款备注优化
            param.put("shouhouId",waisongHexiao.getWxId());
            //支付返回结果
            String payBody = Optional.ofNullable(SpringUtil.getBean(StringRedisTemplate.class).opsForValue()
                    .get(StrUtil.format(RedisKeys.SHOUHOU_WAISONG_HEXIAO_PAY_RESULT,waisongHexiao.getWxId(),waisongHexiao.getWxkId(),waisongHexiao.getAlipayBindAccount())))
                    .orElseGet(()-> HttpUtil.createPost(url).body(param.toJSONString(), ContentType.JSON.getValue()).timeout(NumberConstant.ONE_THOUSAND*NumberConstant.TEN).execute()
                            .body());
            try {
                Result<String> payResult = JSON.parseObject(payBody, Result.class);
                if(payResult.getCode() == Result.SUCCESS){
                    //发送通知
                    sendMsg = MessageFormat.format("你提交维修单：{0,number,#} 的费用报销已打款成功，支付金额：{1,number,#.##}已支付到支付宝账号，请注意查看"
                            , waisongHexiao.getWxId(), waisongHexiao.getPrice());
                    //支付流水号
                    logContent = MessageFormat.format("打款成功，支付金额：{0,number,#.##}已支付到支付宝账号，交易号：{1}，凭证：{3,number,#}。状态变为[{2}]"
                            , waisongHexiao.getPrice(),payResult.getData(), newStatus.getMessage(),pzId);

                }else{
                    //打款失败,回滚
                    log.warn("维修报账打款失败: {}",payResult.getMsg());
                    throw new CustomizeException(payResult.getUserMsg());
                }
            } catch (RuntimeException e) {
                log.error("支付地址: {},参数: {},返回结果: {}",url,param.toJSONString(),payBody);
                log.error("支付异常: ",e);
                throw e;
            }
        }else if(WaisongHexiaoPo.StatusEnum.BUSINESS_REJECT == newStatus){
            //发送通知
            sendMsg = MessageFormat.format("你提交的维修单：{0} 的费用报销已被驳回申请，原因：{1}，请进入维修单进行重新申报"
                    , waisongHexiao.getWxId(), reason);
            logContent = MessageFormat.format("驳回申请，原因：{1} 状态变为[{0}]", newStatus.getMessage(), reason);

        }
        if (StringUtils.isNotEmpty(sendMsg)){
            String host = sysConfigClient.getValueByCode(SysConfigConstant.MOA_URL).getData();
            String link = host + "/mshouhou/edit/" +  waisongHexiao.getWxId();
            smsService.sendOaMsg(sendMsg, URLDecoder.decode(link, Charset.defaultCharset()), waisongHexiao.getApplicantId().toString(), OaMesTypeEnum.SHTZ);
        }

        waisongReimbursementLogService.save(new ShouhouWaisongReimbursementLog().setBusinessId(waisongHexiao.getId())
                .setBusinessType(ShouhouWaisongReimbursementLog.BusinessTypeEnum.HE_XIAO_ORDER.getCode())
                .setContent(logContent).setCreateTime(LocalDateTime.now()).setCreateUser( oaUser.getUserName())
                .setIsDel(false).setShowType(1));
    }

    private int addPingzheng(OaUserBO oaUser, WaisongHexiaoPo waisongHexiao, BigDecimal payPrice) {
        if(!XtenantEnum.isJiujiXtenant(oaUser.getXTenant())){
            throw new CustomizeException("目前不支持非九机租户创建凭证!");
        }
        PingzhengBO pz = new PingzhengBO();
        String zhaiYao = MessageFormat.format("门店{0}，{1}人报销外送维修费，维修单号：{2,number,#}，金额{3,number,#.##}", oaUser.getArea()
                , oaUser.getUserName(), waisongHexiao.getWxId(), payPrice);
        pz.setZhaiyao(MessageFormat.format("{0}|{0}",zhaiYao));
        pz.setKemu("140504|100252");
        pz.setFzhs("无|无");
        pz.setJief(String.format("%s|0",payPrice));
        pz.setDaif(String.format("0|%s", payPrice));
        Integer ztId = Optional.ofNullable(areaInfoClient.getAreaInfoById(waisongHexiao.getAreaId())).map(R::getData)
                .map(AreaInfo::getAuthorizeId).map(authId -> authConfigService.getZtIdByAuId(authId))
                .orElseGet(() -> {
                    throw new CustomizeException("获取不到账套id!");
                });
        pz.setZtid(Integer.toString(ztId));
        pz.setPzdate(DateUtil.localDateTimeToString(LocalDateTime.now()));
        Optional<PingzhengResultBO> resultOpt = Optional.ofNullable(voucherService.addPingZheng(pz));
        if (resultOpt.filter(r -> Boolean.TRUE.equals(r.getFlag())).isPresent()) {
            return resultOpt.get().getPzId();
        }else{
            String errMsg = MessageFormat.format("报销外送维修费凭证生成失败,维修单号({0,number,#}),配件id:{1,number,#},原因：{2}",waisongHexiao.getWxId(),waisongHexiao.getWxkId()
                    ,resultOpt.map(PingzhengResultBO::getErrorMsg).orElse(""));
            Optional.ofNullable(sysConfigService.getDevopsExceptionUserId(oaUser.getXTenant())).filter(StringUtils::isNotEmpty)
                    .ifPresent(userIds->{
                        String url = inwcfUrlSource.getNoticeQywxMessage(userIds, oaUser.getUserIp(), "旧件返厂凭证生成异常推送", errMsg, "", "");
                        HttpUtil.get(url);
                    });
            throw new CustomizeException(errMsg);
        }
    }

    @Override
    public R<Object> accountApprove(List<Integer> ids, int answer, Integer accountId, String operator) {
        if(answer == 1 && accountId == null){
            return R.error("审批通过,支出单id不能为空!");
        }
        List<WaisongHexiaoPo> waisongHexiaos = baseMapper.selectList(new LambdaQueryWrapper<WaisongHexiaoPo>().in(WaisongHexiaoPo::getWxId,ids)
                .in(WaisongHexiaoPo::getStatus, WaisongHexiaoPo.StatusEnum.ACCOUNT_REJECT.getCode()
                        , WaisongHexiaoPo.StatusEnum.ACCOUNT_CHECKING.getCode()
                        , WaisongHexiaoPo.StatusEnum.PAID.getCode()));
        if (waisongHexiaos.isEmpty()){
            return R.error("审批列表中无符合审批状态！");
        }
        LambdaUpdateWrapper<WaisongHexiaoPo> hexiaoUpdateWrapper = new LambdaUpdateWrapper<>();
        //只有待财务审核和财务驳回才能进行操作
        hexiaoUpdateWrapper.in(WaisongHexiaoPo::getWxId, waisongHexiaos.stream().map(WaisongHexiaoPo::getWxId).collect(Collectors.toList()));

        WaisongHexiaoPo.StatusEnum newStatus;
        boolean isSubmit = false;
        String deleteMessage = "";
        //审批结果 1 提交 2 审批结束 3 审批拒绝   4 删除
        switch (answer){
            //提交 ACCOUNT_CHECKING
            case IntConstant.ONE:
                //当提交时，应该吧日志输出为
                newStatus = WaisongHexiaoPo.StatusEnum.ACCOUNT_CHECKING;
                isSubmit=true;
                break;
            case IntConstant.TWO:
                hexiaoUpdateWrapper
                        .set(WaisongHexiaoPo::getAccountId, accountId)
                        .set(WaisongHexiaoPo::getAccountTime, LocalDateTime.now());
                newStatus = WaisongHexiaoPo.StatusEnum.FINISHED;
                break;
            case IntConstant.THREE:
                //设置结算时间为空 eq accountId
                newStatus = WaisongHexiaoPo.StatusEnum.ACCOUNT_REJECT;
                hexiaoUpdateWrapper
                        .eq(WaisongHexiaoPo::getAccountId,accountId)
                        .setSql("account_time=null");
                break;
            case IntConstant.FOUR:
                //增加条件 eq accountId
                hexiaoUpdateWrapper.eq(WaisongHexiaoPo::getAccountId,accountId).set(WaisongHexiaoPo::getAccountId, null).setSql("account_time=null");
                newStatus = WaisongHexiaoPo.StatusEnum.PAID;
                deleteMessage = "由删除操作变更，";
                break;
            default:
                return R.error("审批结果错误!");
        }
        hexiaoUpdateWrapper.set(WaisongHexiaoPo::getStatus,newStatus.getCode());
        boolean finalIsSubmit = isSubmit;
        String logContent = deleteMessage+MessageFormat.format("财务单号：<a href={0}/caiwu/caiwuApply?applyId={1,number,#}>{1,number,#}</a>",sysConfigService.getValueByCode(SysConfigConstant.OA_URL),accountId);
        List<ShouhouWaisongReimbursementLog> shouhouWaisongReimbursementLogs = waisongHexiaos.stream()
                .map(waisongHexiao -> new ShouhouWaisongReimbursementLog().setBusinessId(waisongHexiao.getId())
                .setBusinessType(ShouhouWaisongReimbursementLog.BusinessTypeEnum.HE_XIAO_ORDER.getCode())
                .setContent(DecideUtil.iif(finalIsSubmit ,"审批提交"+logContent ,newStatus.getMessage()+logContent)).setCreateTime(LocalDateTime.now()).setCreateUser(operator)
                .setIsDel(false).setShowType(1)
        ).collect(Collectors.toList());
        boolean[] updateFlag = new boolean[]{false};
        MultipleTransaction.build().execute(DataSourceConstants.DEFAULT, () ->
                //批量更新
                updateFlag[0] = update(hexiaoUpdateWrapper)
        ).execute(DataSourceConstants.DEFAULT, () -> {
                    //批量更新操作日志
                    if (updateFlag[0]) {
                        shouhouWaisongReimbursementLogs.forEach(waisongReimbursementLogService::save);
                    }
                }
        ).commit();


        return R.success("财务审批操作成功!");
    }

    @Override
    public R<WaiSongAlipayBindUserPO> alipayBindUser(Integer wxkId,Integer operatorId,String userId, String userAvatar, String nickname) {
        //查询操作人信息
        R<Ch999UserVo> ch999UserVoR = userInfoClient.getCh999UserByUserId(operatorId);

        if(ch999UserVoR.getCode() != ResultCode.SUCCESS || ch999UserVoR.getData() == null){
            log.warn("绑定失败,获取不到操作人!维修配件id:{},操作人:{},支付宝账号:{},图片:{},昵称:{}",wxkId,operatorId,userId,userAvatar,nickname);
            return R.error("绑定失败,获取不到操作人!");
        }

        //维修配件校验
       // Wxkcoutput wxkcoutput = wxkcoutputService.getById(wxkId);
        Wxkcoutput wxkcoutput = CommenUtil.autoQueryHist(() ->wxkcoutputService.getById(wxkId), MTableInfoEnum.wxkcoutput,wxkId);
        if(wxkcoutput == null){
            log.warn("绑定失败,维修配件不存在!维修配件id:{},操作人:{},支付宝账号:{},图片:{},昵称:{}",wxkId,operatorId,userId,userAvatar,nickname);
            return R.error("绑定失败,维修配件不存在!");
        }
        //维修单是否存在校验
        //Shouhou shouhou = shouhouService.getById(wxkcoutput.getWxid());
        Shouhou shouhou = CommenUtil.autoQueryHist(() ->shouhouService.getById(wxkcoutput.getWxid()), MTableInfoEnum.SHOUHOU,wxkcoutput.getWxid());
        if(shouhou == null || !Boolean.TRUE.equals(shouhou.getXianshi())){
            log.warn("绑定失败,维修单不存在!维修配件id:{},操作人:{},支付宝账号:{},图片:{},昵称:{}",wxkId,operatorId,userId,userAvatar,nickname);
            return R.error("绑定失败,维修单不存在!");
        }
        RLock lock = redissonClient.getLock(String.format(WX_ID_LOCK_FORMAT, shouhou.getId()));
        if(!lock.tryLock()){
            return R.error(LOCK_WX_ID_USER_MSG);
        }
        try {
            Ch999UserVo ch999UserVo = ch999UserVoR.getData();
            //绑定入库,支持重新绑定 同一个维修单只能绑定一个账号
            WaiSongAlipayBindUserPO alipayBindUser = Optional.ofNullable(
                    waisongBindUserMapper.selectOne(new LambdaQueryWrapper<WaiSongAlipayBindUserPO>().eq(WaiSongAlipayBindUserPO::getWxId, shouhou.getId())))
                    .orElseGet(() ->
                            //新绑定
                            new WaiSongAlipayBindUserPO().setUserId(userId).setUserAvatar(userAvatar).setNickname(nickname)
                                    .setCreateUserId(ch999UserVo.getCh999Id()).setCreateUser(ch999UserVo.getCh999Name())
                                    .setCreateTime(LocalDateTime.now()).setDel(false).setWxId(shouhou.getId()).setWxkId(wxkId)
                    );
            MultipleTransaction.build().execute(DataSourceConstants.DEFAULT,()->{
                if(alipayBindUser.getId() == null){
                    waisongBindUserMapper.insert(alipayBindUser);
                }else{
                    //更新绑定
                    alipayBindUser.setUserId(userId).setUserAvatar(userAvatar).setNickname(nickname);
                    waisongBindUserMapper.updateById(alipayBindUser);
                }
            }).execute(DataSourceConstants.DEFAULT,()->
                //记录到绑定日志
                waisongReimbursementLogService.save(new ShouhouWaisongReimbursementLog().setBusinessId(shouhou.getId())
                        .setBusinessType(ShouhouWaisongReimbursementLog.BusinessTypeEnum.WX_ORDER.getCode())
                        .setContent(String.format("绑定收款账号为【%s】", Optional.ofNullable(nickname).orElse(userId)))
                        .setCreateTime(LocalDateTime.now()).setCreateUser(ch999UserVo.getCh999Name())
                        .setIsDel(false).setShowType(1))
            ).commit();
            //绑定成功发送mq信息
            String routingKey = String.format("waisong_hexiao_alipay_%s_%s", operatorId,wxkId);
            rabbitTemplate.convertAndSend("oaupload", routingKey,JSON.toJSONString(alipayBindUser));
            return R.success("绑定成功",alipayBindUser);
        } finally {
            lock.unlock();
        }
    }

    /**
     * 验证物流单创建
     * @param wxkId
     * @param oaUser
     * @param invoke
     * @return
     */
    private R<JSONObject> validateSendBills(Integer wxkId, OaUserBO oaUser, BiFunction<Shouhou,WaiSongAlipayBindUserPO,R<JSONObject>> invoke){
        if(wxkId == null){
            return R.error("维修配件id不能为空!");
        }

        //检验是否已经有对应的物流单 提示: 存在对应的物流单
        //维修配件校验
//        Wxkcoutput wxkcoutput = wxkcoutputService.getByIdAndXtenantAndAuthPart(wxkId,oaUser.getXTenant()
//                ,oaUser.getAreaId(),Boolean.TRUE.equals(authConfigService.isAuthPart(oaUser)),oaUser.getAuthorizeId());
        Wxkcoutput wxkcoutput = CommenUtil.autoQueryHist(() ->wxkcoutputService.getByIdAndXtenantAndAuthPart(wxkId,oaUser.getXTenant()
                ,oaUser.getAreaId(),Boolean.TRUE.equals(authConfigService.isAuthPart(oaUser)),oaUser.getAuthorizeId()), MTableInfoEnum.wxkcoutput,wxkId);
        if(wxkcoutput == null){
            return R.error("维修配件查不到!");
        }
        //维修单是否存在校验
        Shouhou shouhou = shouhouService.getShouhouNotDel(wxkcoutput.getWxid(),oaUser.getXTenant()
                ,oaUser.getAreaId(),Boolean.TRUE.equals(authConfigService.isAuthPart(oaUser)),oaUser.getAuthorizeId());
        if(shouhou == null || !Boolean.TRUE.equals(shouhou.getXianshi())){
            return R.error("维修单查不到!");
        }
        //检验是否重复绑定 一个维修单,只能绑定一次
        WaiSongAlipayBindUserPO waiSongAlipayBindUser = Optional.ofNullable(
                waisongBindUserMapper.selectOne(new LambdaQueryWrapper<WaiSongAlipayBindUserPO>().eq(WaiSongAlipayBindUserPO::getWxId, shouhou.getId())))
                .orElseGet(() ->
                        //新绑定
                        new WaiSongAlipayBindUserPO().setCreateUserId(oaUser.getUserId()).setCreateUser(oaUser.getUserName())
                                .setCreateTime(LocalDateTime.now()).setDel(false).setWxId(shouhou.getId()).setWxkId(wxkId)
                );
        if(waiSongAlipayBindUser.getLogisticsId() != null){
            return R.success("物流单已经发回",new JSONObject().fluentPut(LOGISTICS_ID_KEY,waiSongAlipayBindUser.getLogisticsId())
                    .fluentPut("logisticsDetailUrl",MessageFormat.format(WULIU_DETAIL_URL_MESSAGE_FORMAT
                            ,sysConfigService.getValueByCode(SysConfigConstant.OA_URL),waiSongAlipayBindUser.getLogisticsId())));
        }
        return invoke.apply(shouhou,waiSongAlipayBindUser);
    }

    @Override
    public R<JSONObject> sendBills(Integer wxkId) {
        OaUserBO oaUser = currentRequestComponent.getCurrentStaffId();
        return this.validateSendBills(wxkId,oaUser,(Shouhou shouhou,WaiSongAlipayBindUserPO waiSongAlipayBindUser)->sendBillsLambda(oaUser, shouhou, waiSongAlipayBindUser));
    }

    private R<JSONObject> sendBillsLambda(OaUserBO oaUser, Shouhou shouhou, WaiSongAlipayBindUserPO waiSongAlipayBindUser) {
        RLock lock = redissonClient.getLock(String.format(WX_ID_LOCK_FORMAT, shouhou.getId()));
        if(!lock.tryLock()){
            return R.error(LOCK_WX_ID_USER_MSG);
        }
        try {
            //查询操作人信息
            R<Ch999UserVo> ch999UserVoR = userInfoClient.getCh999UserByUserId(oaUser.getUserId());
            Ch999UserVo ch999UserVo = ch999UserVoR.getData();
            Integer logisticsOrderId = addLogisticsOrder(oaUser, ch999UserVoR, ch999UserVo);
            waiSongAlipayBindUser.setLogisticsId(logisticsOrderId);

            //保存物流单号
            MultipleTransaction.build().execute(DataSourceConstants.DEFAULT,()->{
                if(waiSongAlipayBindUser.getId() == null){
                    waisongBindUserMapper.insert(waiSongAlipayBindUser);
                }else{
                    //更新绑定
                    waisongBindUserMapper.updateById(waiSongAlipayBindUser);
                }
            }).execute(DataSourceConstants.DEFAULT,()->
                    //保存操作日志
                    waisongReimbursementLogService.save(new ShouhouWaisongReimbursementLog().setBusinessId(shouhou.getId())
                            .setBusinessType(ShouhouWaisongReimbursementLog.BusinessTypeEnum.WX_ORDER.getCode())
                            .setContent(String.format("物流单号为【%s】", logisticsOrderId)).setCreateTime(LocalDateTime.now()).setCreateUser(ch999UserVo.getCh999Name())
                            .setIsDel(false).setShowType(1))
            ).commit();
            return R.success("物流单已经生成!",new JSONObject().fluentPut(LOGISTICS_ID_KEY,logisticsOrderId)
                    .fluentPut("logisticsDetailUrl",MessageFormat.format(WULIU_DETAIL_URL_MESSAGE_FORMAT
                    ,sysConfigService.getValueByCode(SysConfigConstant.OA_URL),logisticsOrderId)));
        } finally {
            lock.unlock();
        }
    }

    /**
     * 添加物流单
     * @param oaUser
     * @param ch999UserVoR
     * @param ch999UserVo
     * @return
     */
    private Integer addLogisticsOrder(OaUserBO oaUser, R<Ch999UserVo> ch999UserVoR, Ch999UserVo ch999UserVo) {
        if(ch999UserVoR.getCode() != ResultCode.SUCCESS || ch999UserVo == null){
            throw new CustomizeException("获取用户信息失败!");
        }
        if(StringUtils.isEmpty(ch999UserVo.getMobile())){
            throw new CustomizeException("寄件人号码获取失败!");
        }
        LogisticsOrderBO logisticsOrderBO = buildLogisticsOrderBO( oaUser, ch999UserVo);
        JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(logisticsOrderBO));
        List<String> emptyKeys = jsonObject.entrySet().stream()
                .filter(entry -> Objects.isNull(entry.getValue()))
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());
        for (String emptyKey : emptyKeys) {
            jsonObject.remove(emptyKey);
        }
        R<Integer> logisticsOrderR = stockCloud.addLogisticsOrder( oaUser.getToken(),jsonObject);
        Integer logisticsOrderId = logisticsOrderR.getData();
        if(logisticsOrderR.getCode() != ResultCode.SUCCESS || logisticsOrderId == null){
            throw new CustomizeException(String.format("物流单创建失败!%s", logisticsOrderR.getUserMsg()));
        }
        return logisticsOrderId;
    }

    private static LogisticsOrderBO buildLogisticsOrderBO(OaUserBO oaUser, Ch999UserVo ch999UserVo) {
        //生成物流单
        LogisticsOrderBO logisticsOrderBO = new LogisticsOrderBO();
        logisticsOrderBO.setWCateId(NumberConstant.EIGHT);
        logisticsOrderBO.setWutype(NumberConstant.ONE);
        logisticsOrderBO.setSname( oaUser.getUserName());
        logisticsOrderBO.setSmobile(ch999UserVo.getMobile());
        logisticsOrderBO.setSareaid( oaUser.getAreaId());
        logisticsOrderBO.setRname("黄仁荣");
        logisticsOrderBO.setRmobile("15912493089");
        logisticsOrderBO.setRareaid(NumberConstant.TEN+NumberConstant.THREE);
        logisticsOrderBO.setComment("维修报账单据发回总部");
        return logisticsOrderBO;
    }

    /**
     * 删除申请表
     * @param id
     * @return
     */
    @Override
    public R<WaisongHexiaoApplyVO> deleteLogic(Integer id) {
        if(id == null){
            return R.error("id不能为空!");
        }
        //校验状态待审批 记录操作日志
        OaUserBO oaUser = currentRequestComponent.getCurrentStaffId();
        WaisongHexiaoPo waisongHexiao = baseMapper.getHexiao(id, null,null, oaUser.getXTenant()
                , oaUser.getAreaId(), Boolean.TRUE.equals(authConfigService.isAuthPart(oaUser)), oaUser.getAuthorizeId());
        RLock lock = redissonClient.getLock(String.format(WX_ID_LOCK_FORMAT, waisongHexiao.getWxId()));
        if(!lock.tryLock()){
            return R.error(LOCK_WX_ID_USER_MSG);
        }
        try {
            boolean[] isDel = new boolean[]{false};
            MultipleTransaction.build().execute(DataSourceConstants.DEFAULT, () ->
                    isDel[0] = this.update(new LambdaUpdateWrapper<WaisongHexiaoPo>()
                            .set(WaisongHexiaoPo::getStatus, WaisongHexiaoPo.StatusEnum.DELETED.getCode())
                            .eq(WaisongHexiaoPo::getId,id)
                            .in(WaisongHexiaoPo::getStatus, WaisongHexiaoPo.StatusEnum.CHECKING.getCode()
                                    , WaisongHexiaoPo.StatusEnum.BUSINESS_REJECT.getCode()))
            ).execute(DataSourceConstants.DEFAULT, () -> {
                if (isDel[0]) {
                    waisongReimbursementLogService.saveHexiaoLog(id, String.format("删除操作 状态变为[%s]"
                            , WaisongHexiaoPo.StatusEnum.DELETED.getMessage()));
                }
            }).commit();

            if(!isDel[0]){
                return R.error("不允许删除!");
            }
            return R.success("删除成功!",this.getDataForApply(id,null,null).getData());
        } finally {
            lock.unlock();
        }
    }

    /**
     * 更新回收状态
     * @param id
     * @param status
     * @return
     */
    @Override
    public R<Boolean> updateRecoverBillStatus(Integer id, Integer status) {
        //校验状态值
        Optional<WaisongHexiaoPo.RecoverBillStatusEnum> recoverBillStatusOpt = WaisongHexiaoPo.RecoverBillStatusEnum.valueOfByCode(status);
        if(!recoverBillStatusOpt.isPresent()){
            return R.error("请输入正确的状态值!");
        }
        WaisongHexiaoPo.RecoverBillStatusEnum recoverBillStatusEnum = recoverBillStatusOpt.get();
        //更新数据状态,写入操作日志
        int[] n = new int[]{0};
        MultipleTransaction.build().execute(DataSourceConstants.DEFAULT, () ->
                n[0] = baseMapper.update(null, new LambdaUpdateWrapper<WaisongHexiaoPo>().set(WaisongHexiaoPo::getRecoverBillStatus, status)
                        .eq(WaisongHexiaoPo::getId, id).ne(WaisongHexiaoPo::getRecoverBillStatus, status))
        ).execute(DataSourceConstants.DEFAULT, () -> {
            if (n[0] > 0) {
                waisongReimbursementLogService.saveHexiaoLog(id, String.format("票据回收状态改为:%s", recoverBillStatusEnum.getMessage()));
            }
        }).commit();

        if(n[0]<1){
            return R.error("修改回收票据状态,失败!");
        }
        return R.success(String.format("操作成功,状态为: %s",recoverBillStatusEnum.getMessage()));
    }
}
