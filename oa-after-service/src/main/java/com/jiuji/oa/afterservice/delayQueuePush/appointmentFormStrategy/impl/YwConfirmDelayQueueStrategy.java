package com.jiuji.oa.afterservice.delayQueuePush.appointmentFormStrategy.impl;

import cn.hutool.json.JSONUtil;
import com.jiuji.oa.afterservice.delayQueuePush.vo.YwConfirmDelayPush;
import com.jiuji.tc.utils.constants.NumberConstant;
import com.meitu.platform.lmstfy.client.LmstfyClient;
import com.meitu.platform.lmstfy.exception.LmstfyException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 业务确认延迟队列策略
 * <AUTHOR>
 */
@Slf4j
@Service(value = "YwConfirmDelayQueueStrategy")
public class YwConfirmDelayQueueStrategy {

    @Value("${lmstfy.mult.first-lmstfy-client.ywConfirmDelayQueue}")
    private String ywConfirmDelayQueue;
    
    @Resource(name = "firstLmstfyClient")
    private LmstfyClient firstLmstfyClient;

    /**
     * 推送业务确认延迟队列
     * @param ywConfirmDelayPush 推送数据
     * @param delaySecond 延迟时间（秒）
     */
    public void pushData(YwConfirmDelayPush ywConfirmDelayPush, Integer delaySecond) {
        try {
            String publish = firstLmstfyClient.publish(ywConfirmDelayQueue, JSONUtil.toJsonStr(ywConfirmDelayPush).getBytes(), 0, (short) 1, delaySecond);
            log.warn("业务确认延迟队列推送成功，队列名称{}，推送参数{}，延迟时间{}秒，返回结果{}", ywConfirmDelayQueue, JSONUtil.toJsonStr(ywConfirmDelayPush), delaySecond, publish);
        } catch (LmstfyException e) {
            log.error("业务确认延迟队列推送异常，队列名称{}，推送参数{}，延迟时间{}秒", ywConfirmDelayQueue, JSONUtil.toJsonStr(ywConfirmDelayPush), delaySecond, e);
        }
    }
}
