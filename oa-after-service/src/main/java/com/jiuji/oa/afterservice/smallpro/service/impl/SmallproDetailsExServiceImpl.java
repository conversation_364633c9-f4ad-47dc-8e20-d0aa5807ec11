package com.jiuji.oa.afterservice.smallpro.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.lang.Tuple;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ch999.common.util.vo.Result;
import com.jiuji.cloud.after.util.BaoXiuUtil;
import com.jiuji.cloud.after.vo.baoxiu.BaoXiuParam;
import com.jiuji.cloud.after.vo.baoxiu.BaoXiuVo;
import com.jiuji.cloud.after.vo.res.ChangeInfoRes;
import com.jiuji.cloud.org.vo.enums.RoleTermModuleEnum;
import com.jiuji.oa.afterservice.api.service.OaApiService;
import com.jiuji.oa.afterservice.apollo.ApolloEntity;
import com.jiuji.oa.afterservice.bigpro.bo.ImeiQueryInfoBo;
import com.jiuji.oa.afterservice.bigpro.bo.UserClassConfig;
import com.jiuji.oa.afterservice.bigpro.entity.Productbarcode;
import com.jiuji.oa.afterservice.bigpro.entity.WxProductServiceOpeningType;
import com.jiuji.oa.afterservice.bigpro.enums.AttachmentsEnum;
import com.jiuji.oa.afterservice.bigpro.enums.SubSubTypeEnum;
import com.jiuji.oa.afterservice.bigpro.po.Attachments;
import com.jiuji.oa.afterservice.bigpro.po.Productinfo;
import com.jiuji.oa.afterservice.bigpro.po.ServiceRecord;
import com.jiuji.oa.afterservice.bigpro.po.Wxkcoutput;
import com.jiuji.oa.afterservice.bigpro.service.*;
import com.jiuji.oa.afterservice.bigpro.vo.req.ReturnAnnualPackageReq;
import com.jiuji.oa.afterservice.cloud.service.WebCloud;
import com.jiuji.oa.afterservice.cloud.vo.AfterServiceTimeCfg;
import com.jiuji.oa.afterservice.cloud.vo.ProRelateInfoService;
import com.jiuji.oa.afterservice.cloud.vo.ProductServiceOpeningVO;
import com.jiuji.oa.afterservice.cloud.vo.WarrantyMainInfoVO;
import com.jiuji.oa.afterservice.common.bo.OaUserBO;
import com.jiuji.oa.afterservice.common.bo.Platform;
import com.jiuji.oa.afterservice.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.afterservice.common.config.properties.ImageProperties;
import com.jiuji.oa.afterservice.common.constant.*;
import com.jiuji.oa.afterservice.common.enums.*;
import com.jiuji.oa.afterservice.common.exception.CustomizeException;
import com.jiuji.oa.afterservice.common.exception.RRExceptionHandler;
import com.jiuji.oa.afterservice.common.util.BusinessUtil;
import com.jiuji.oa.afterservice.common.util.CommenUtil;
import com.jiuji.oa.afterservice.common.util.DateUtil;
import com.jiuji.oa.afterservice.common.util.SpringContextUtil;
import com.jiuji.oa.afterservice.common.vo.CheckDataViewScopeReq;
import com.jiuji.oa.afterservice.common.vo.RAMPager;
import com.jiuji.oa.afterservice.mapstruct.CommonStructMapper;
import com.jiuji.oa.afterservice.other.bo.AreaBelongsDcHqD1AreaId;
import com.jiuji.oa.afterservice.other.po.*;
import com.jiuji.oa.afterservice.other.service.*;
import com.jiuji.oa.afterservice.refund.bo.RefundSubInfoBo;
import com.jiuji.oa.afterservice.refund.bo.smallpro.SmallproSubBuyInfoBo;
import com.jiuji.oa.afterservice.refund.po.OperatorBasket;
import com.jiuji.oa.afterservice.refund.service.OperatorBasketService;
import com.jiuji.oa.afterservice.refund.service.way.ThirdOriginWayService;
import com.jiuji.oa.afterservice.refund.service.way.decorator.OperatorBusinessRefundService;
import com.jiuji.oa.afterservice.refund.utils.RefundMoneyUtil;
import com.jiuji.oa.afterservice.refund.vo.res.way.ThirdOriginRefundVo;
import com.jiuji.oa.afterservice.smallpro.bo.*;
import com.jiuji.oa.afterservice.smallpro.bo.filmCard.FilmAccessoriesV2Item;
import com.jiuji.oa.afterservice.smallpro.bo.filmCard.FilmCardInfomationBO;
import com.jiuji.oa.afterservice.smallpro.bo.filmCard.FilmCardLastChangePPidBO;
import com.jiuji.oa.afterservice.smallpro.bo.filmCard.FilmUseLog;
import com.jiuji.oa.afterservice.smallpro.bo.smallproExchange.SmallproPayInfoBO;
import com.jiuji.oa.afterservice.smallpro.bo.smallproExchange.SmallproRefundPriceInfoBO;
import com.jiuji.oa.afterservice.smallpro.bo.smallproInfo.*;
import com.jiuji.oa.afterservice.smallpro.bo.smallproReceivable.ISmallproProduct;
import com.jiuji.oa.afterservice.smallpro.bo.smallproReceivable.SmallproReceivableProductBO;
import com.jiuji.oa.afterservice.smallpro.constant.SmallProConstant;
import com.jiuji.oa.afterservice.smallpro.constant.SmallProRelativePathConstant;
import com.jiuji.oa.afterservice.smallpro.dao.SmallproMapper;
import com.jiuji.oa.afterservice.smallpro.enums.*;
import com.jiuji.oa.afterservice.smallpro.po.*;
import com.jiuji.oa.afterservice.smallpro.service.*;
import com.jiuji.oa.afterservice.smallpro.vo.req.*;
import com.jiuji.oa.afterservice.smallpro.vo.res.*;
import com.jiuji.oa.afterservice.smallpro.yearcardtransfer.dto.YearPackageTransferDetailDto;
import com.jiuji.oa.afterservice.smallpro.yearcardtransfer.entity.YearPackageTransferPo;
import com.jiuji.oa.afterservice.smallpro.yearcardtransfer.service.IYearPackageTransferService;
import com.jiuji.oa.afterservice.stock.enums.StockBasketTypeEnum;
import com.jiuji.oa.afterservice.stock.po.ProductKc;
import com.jiuji.oa.afterservice.stock.po.ProductMkc;
import com.jiuji.oa.afterservice.stock.po.StockStatusCountBo;
import com.jiuji.oa.afterservice.stock.service.ProductKcService;
import com.jiuji.oa.afterservice.stock.service.ProductMkcService;
import com.jiuji.oa.afterservice.sub.po.Category;
import com.jiuji.oa.afterservice.sub.po.Sub;
import com.jiuji.oa.afterservice.sub.service.CategoryService;
import com.jiuji.oa.afterservice.sub.service.SubService;
import com.jiuji.oa.afterservice.sys.service.AuthConfigService;
import com.jiuji.oa.afterservice.sys.service.RetryService;
import com.jiuji.oa.loginfo.smallprobill.client.SmallproBillLogClient;
import com.jiuji.oa.loginfo.smallprobill.client.vo.LastLogReq;
import com.jiuji.oa.loginfo.smallprobill.client.vo.SmallproBillLogVo;
import com.jiuji.oa.nc.CutScreenRecordCloud;
import com.jiuji.oa.orginfo.areainfo.client.AreaInfoClient;
import com.jiuji.oa.orginfo.sysconfig.client.SysConfigClient;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.common.vo.ResultCode;
import com.jiuji.tc.foundation.db.annotation.DS;
import com.jiuji.tc.foundation.db.config.DynamicContextHolder;
import com.jiuji.tc.utils.business.small.SmallproUtil;
import com.jiuji.tc.utils.common.CommonUtils;
import com.jiuji.tc.utils.common.DecideUtil;
import com.jiuji.tc.utils.constants.NumberConstant;
import com.jiuji.tc.utils.constants.SignConstant;
import com.jiuji.tc.utils.constants.SubCheckStatusEnum;
import com.jiuji.tc.utils.constants.SysConfigConstant;
import com.jiuji.tc.utils.enums.EnumUtil;
import com.jiuji.tc.utils.enums.coupon.BusinessTypeEnum;
import com.jiuji.tc.utils.transaction.MultipleTransaction;
import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;
import java.util.Objects;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * description: <小件接件详情页扩展服务实现类>
 * translation: <Expanded service implementation class for small piece detail page>
 *
 * <AUTHOR>
 * @date 2020/4/10
 * @since 1.0.0
 */
@Service
@Slf4j
public class SmallproDetailsExServiceImpl implements SmallproDetailsExService {

    // 10秒钟
    private final Long BOUND_MILLIS = 10000L;

    @Autowired
    private SmallproService smallproService;
    @Autowired
    private SmallproBillService smallproBillService;
    @Autowired
    private SmallproFilmCardService smallproFilmCardService;
    @Autowired
    private SmallproKindtypeService smallproKindtypeService;
    @Autowired
    private SmallproRefundExService smallproRefundExService;
    @Autowired
    private WxconfigGiftService wxconfigGiftService;
    @Autowired
    private SmallproLogService smallproLogService;
    @Autowired
    private ShouhouTuihuanService shouhouTuihuanService;
    @Autowired
    private ShouhouFanchangService shouhouFanchangService;
    @Autowired
    private ShouyingService shouyingService;
    @Autowired
    private PayWxenterpriseService payWxenterpriseService;
    @Autowired
    private AlipayToAccountLogService alipayToAccountLogService;
    @Autowired
    private ShouyinOtherService shouyinOtherService;
    @Autowired
    private ShouhouService shouhouService;
    @Autowired
    private NetpayRecordService netpayRecordService;
    @Autowired
    private SubService subService;
    @Autowired
    private AttachmentsService attachmentsService;
    @Autowired
    private BasketService basketService;
    @Autowired
    private SmallproMapper smallproMapper;
    @Autowired
    private SmallProConstant smallProConstant;
    @Autowired
    private ImageProperties imageProperties;
    @Resource
    private SysConfigClient sysConfigClient;

    @Resource
    private OperatorBasketService operatorBasketService;
    @Resource
    private CutScreenRecordCloud cutScreenRecordCloud;

    @Autowired
    private SmallProAdapterService smallProAdapterService;
    @Resource
    private AbstractCurrentRequestComponent currentRequestComponent;
    @Autowired
    private AreainfoService areainfoService;
    @Autowired
    private AuthConfigService authConfigService;
    @Resource
    private ShouhouCommonService shouhouCommonService;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private AreaInfoClient areaInfoClient;
    @Resource
    private CategoryService categoryService;
    @Resource
    private ProductinfoService productinfoService;
    @Resource
    private ProductKcService productKcService;
    @Resource
    private ProductMkcService productMkcService;
    @Resource
    private WebCloud webCloud;
    @Resource
    private ProductbarcodeService productbarcodeService;

    @Resource
    private ApolloEntity apolloEntity;
    @Resource
    @Lazy
    private SmallproDetailsExService smallproDetailsExService;

    private final static Integer FOUR_TY_FIVE = 45;

    private final static Integer DAYS_OF_ONE_YEAR = 45;

    private final static Integer ANNUAL_PACKAGE_TYPE = 108;

    private final static String FORMAT_TIME = "+8";

    private static final Integer SCALE = 2;

    /**
     * 盲盒退货权值
     */
    private static final String BLIND_BOX_RANK = "mhth";
    /**
     * 保护壳分类
     */
    private static final List<Integer> CASE_CID_LIST = Arrays.asList(43, 385);
    private static final List<Integer> SAAS_AREA_ID_LIST = Arrays.asList(826,960,993,1146);



    @Resource
    private SmsService smsService;

    // endregion

    // region 逻辑方法

    // region 获取小件详情 getSmallproInfo


    /**
     * 设置订单跳转连接
     */
    public void setOrderUrl(List<SmallproOperationInfoBO> showOperationLogs,Integer smallProId){
        try {
            HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
            String platform = Optional.ofNullable(request.getHeader("Platform"))
                    .map(String::trim)
                    .orElse("");
            if(CollectionUtils.isNotEmpty(showOperationLogs)){
                showOperationLogs.forEach((SmallproOperationInfoBO item)->{
                    String comment = Optional.ofNullable(item.getComment()).orElse("");
                    if (platform.contains("MOA")) {
                        if (comment.contains("/staticpc/#/small-refund/")) {
                            item.setComment(comment
                                    .replaceAll("/staticpc/#/small-refund/", "/new/#/small-refund/"));
                        }
                    }

                });
            }
        }catch (Exception e){
            RRExceptionHandler.logError("小件单日志处理异常",smallProId,e,smsService::sendOaMsgTo9JiMan);
        }


    }


    @Override
    public SmallproInformationRes getSmallproInfo(Integer smallproId, OaUserBO oaUserBO) {
        // 转换数据需要局部常量
        Long now = System.currentTimeMillis();

        // region 全操作类型所需查询信息

        Smallpro smallpro = smallproMapper.getByIdSqlServerByWriter(smallproId);
        Boolean authPart = authConfigService.isAuthPart(oaUserBO);
        if (authPart) {
            //当前登录地区和小件单所属地区不在同一个授权体系下不能进行查看
            Integer currAreaId = CommonUtils.defaultIfNullOrZero(smallpro.getToAreaId(), smallpro.getAreaId());
            Areainfo areaInfo = areainfoService.getById(currAreaId);
            if (!Objects.equals(oaUserBO.getAuthorizeId(), areaInfo.getAuthorizeid())
                    && !Objects.equals(currAreaId, oaUserBO.getAreaId())) {
                return null;
            }
        }

        // 结果为null则直接返回null
        if (smallpro == null) {
            return null;
        }
        SpringContextUtil.getRequest().ifPresent(req -> req.setAttribute(RequestAttrKeys.ANNUAL_PACKAGE_IMEI,smallpro.getImei()));
        Integer overDays = getOverDays(smallpro.getBuyDate().toInstant(ZoneOffset.of("+8")).toEpochMilli());
        // 获取小件订单关联ppriceId和basketId
        QueryWrapper<SmallproBill> smallproBillQueryWrapper = new QueryWrapper<>();
        smallproBillQueryWrapper.lambda().eq(SmallproBill::getSmallproID, smallproId);
        List<SmallproBill> smallproBillList =
                smallproBillService.getListWithSqlServer(smallproBillQueryWrapper);
        if (CollectionUtils.isEmpty(smallproBillList)) {
            return null;
        }
        // 获取额外数据，包括订单和用户
        List<SmallproOrderInfoBO> smallproOrderInfoBOList =
                ((SmallproDetailsExServiceImpl) AopContext.currentProxy()).getSmallproOrderListInfo(smallproBillList,
                        smallpro.getSubId(), overDays);

        Optional<SmallproProductInfoBO> changeProductNameOpt = Optional.empty();
        if (smallpro.getChangePpriceid() != null) {
            changeProductNameOpt = Optional.ofNullable(getProductNameByPpriceId(smallpro.getChangePpriceid()));
        }

        // 尝试获取贴膜年包卡信息
        Map<SmallproOrderInfoBO, Boolean> isFreeExchangeMap = CollUtil.newHashMap(smallproOrderInfoBOList.size());
        Map<SmallproOrderInfoBO, Boolean> isServiceProductMap = CollUtil.newHashMap(smallproOrderInfoBOList.size());
        Map<SmallproOrderInfoBO, Boolean>  isYearCardProductMap = CollUtil.newHashMap(smallproOrderInfoBOList.size());
        SubServiceRecordBO subServiceRecordBO = null;
        FilmCardInfomationBO filmCardInfomationBO = null;
        //获取处理次数
        Map<Integer,Integer> handleCountMap = new HashMap<>();
        try {
            if(CollectionUtils.isNotEmpty(smallproOrderInfoBOList)){
                handleCountMap = getHandleCount(smallproOrderInfoBOList, smallpro.getAreaId());
            }
        } catch (Exception e) {
            RRExceptionHandler.logError("小件单详情获取处理次数异常",Dict.create().set("smallproId",smallproId),e,smsService::sendOaMsgTo9JiMan);
        }
        List<Integer> ppids = smallproOrderInfoBOList.stream().map(ISmallproProduct::getPpriceId)
                .filter(ppid -> ObjectUtil.defaultIfNull(ppid, 0) >0).collect(Collectors.toList());
        //批量获取质保配置信息
        Map<String, AfterServiceTimeCfg> afterTimeCfgMap = getAfterTimeCfgMap(ppids);
        for (SmallproOrderInfoBO smallproOrderInfoBO : smallproOrderInfoBOList) {
            //设置处理次数
            smallproOrderInfoBO.setHandleCount(handleCountMap.getOrDefault(CommenUtil.currId(smallproOrderInfoBO.getBasketId(),
                    smallproOrderInfoBO.getPpriceId()),NumberConstant.ZERO));
            Integer ppid = smallproOrderInfoBO.getTargetPpriceId();
            Integer smallproBillBasketId = smallproOrderInfoBOList.stream().findFirst().map(SmallproOrderInfoBO::getBasketId).orElse(null);
            if (isServiceProduct(smallproBillBasketId, ppid)) {
                isServiceProductMap.put(smallproOrderInfoBO, true);
            } else if (isYearCardProduct(ppid)) {
                isYearCardProductMap.put(smallproOrderInfoBO, true);
            }

            // 如果没有返回null
            SpringContextUtil.getRequest().ifPresent(req -> req.setAttribute(RequestAttrKeys.SMALLPRO_ORDER_INFO,smallproOrderInfoBO));
            filmCardInfomationBO =
                    smallproFilmCardService.getFilmCardInfoByBasketIdByWrite(smallproOrderInfoBO.getBasketId());

            if (filmCardInfomationBO == null && smallpro.getKind() == 2) {
                Sub sub = subService.getByIdSqlServer(smallpro.getSubId());
                if (sub != null && sub.getTradeDate1() != null) {
                    long nowTimeStamp = LocalDateTime.now().toEpochSecond(ZoneOffset.of("+8"));
                    long pointTimeStamp;
                    if(XtenantEnum.isJiujiXtenant()){
                        pointTimeStamp = SmallproUtil.getEndTime(sub.getTradeDate1(), getAfterTimeCfgDays(afterTimeCfgMap,
                                smallproOrderInfoBO.getPpriceId(), AfterServiceTimeCfg::getReplaceDay)).toEpochSecond(ZoneOffset.of("+8"));
                    }else{
                        pointTimeStamp = CommonUtils.getEndOfDay(sub.getTradeDate1()).plusDays(30).toEpochSecond(ZoneOffset.of("+8"));
                    }

                    if (nowTimeStamp <= pointTimeStamp) {
                        isFreeExchangeMap.put(smallproOrderInfoBO, true);
                    }
                }
            } else if (filmCardInfomationBO != null) {
                isFreeExchangeMap.put(smallproOrderInfoBO, filmCardInfomationBO.getIsFreeExchange() == null ? false :
                        filmCardInfomationBO.getIsFreeExchange());
            }
            if (!(Boolean.TRUE.equals(isServiceProductMap.get(smallproOrderInfoBO)) || Boolean.TRUE.equals(isYearCardProductMap.get(smallproOrderInfoBO)))) {
                // 获取小件九机服务情况
                subServiceRecordBO =
                        getSubServiceRecord(smallproOrderInfoBOList, smallProConstant.getYYYY_MM_DD_HH_MM_SS(),
                                filmCardInfomationBO);
            }
            smallproOrderInfoBO.setFilmCardInfo(filmCardInfomationBO).setServiceInfo(subServiceRecordBO);
            if (null != filmCardInfomationBO) {
                smallproOrderInfoBO.setIsFreeExchange(filmCardInfomationBO.getIsFreeExchange());
            }
        }

        R<String> urlR = sysConfigClient.getValueByCode(SysConfigConstant.OA_URL);
        Function<Integer, SmallproUserInfoBO> buildUserInfoFun = (userId) -> {
            // 获取用户信息
            SmallproUserInfoBO smallproUserInfoBO = new SmallproUserInfoBO();
            SmallproInfoUserBO userInfo =
                    Optional.ofNullable(smallproMapper.getSmallproUserInfo(userId))
                            .orElseGet(SmallproInfoUserBO::new);
            // 转译用户会员等级信息
            int userClassCode = Optional.ofNullable(userInfo.getUserClass())
                    .orElseGet(() -> 0);
            UserClassConfig newUserClassName = UserClassEnum.getNewUserClassName(userClassCode, oaUserBO.getXTenant(), stringRedisTemplate, oaUserBO.getAreaId(), areaInfoClient);
            String userClassString;
            //兜底处理
            if (ObjectUtil.isEmpty(newUserClassName)) {
                userClassString = EnumUtil.getMessageByCode(UserClassEnum.class, userClassCode);
            } else {
                userClassString = newUserClassName.getUserClassName();
            }
            smallproUserInfoBO.setUserId(userInfo.getUserId())
                    .setMobile(userInfo.getMobile())
                    .setRealName(userInfo.getUserName())
                    .setUserName(userInfo.getUserName())
                    .setUserClassCode(userClassCode)
                    .setUserClass(userClassString);
            if (ResultCode.SUCCESS == urlR.getCode() && StringUtils.isNotEmpty(urlR.getData())) {
                String link = urlR.getData() + "/member?actionName=fromid&key=" + smallpro.getUserId();
                smallproUserInfoBO.setUserLink(link);
            }
            Optional.ofNullable(SpringUtil.getBean(OaApiService.class).getRoleNameByMobile(userInfo.getMobile()))
                    .ifPresent(ur -> {
                        smallproUserInfoBO.setCh999Name(ur.getCh999Name());
                        smallproUserInfoBO.setCh999RoleName(ur.getRoleName());
                    });

            return smallproUserInfoBO;
        };
        SmallproUserInfoBO smallproUserInfoBO = buildUserInfoFun.apply(smallpro.getUserId());
        //小件的会员信息以小件信息为主
        smallproUserInfoBO.setMobile(smallpro.getMobile());
        smallproUserInfoBO.setRealName(smallpro.getUserName());
        // 查询接件要求
        List<SmallproServiceConfigBO> smallproServiceConfigBOList =
                getSmallproServiceListInfo(smallproOrderInfoBOList, true);

        // 获取小件订单历史数据
        QueryWrapper<Smallpro> smallproQueryWrapper = new QueryWrapper<>();
        smallproQueryWrapper.lambda()
                .select(Smallpro::getId, Smallpro::getName, Smallpro::getKind, Smallpro::getInUser,
                        Smallpro::getInDate)
                .ne(Smallpro::getId, smallpro.getId())
                .eq(Smallpro::getSubId, smallpro.getSubId())
                .eq(Smallpro::getStats, 1)
                .orderByDesc(Smallpro::getId);
        // 现货历史数据过多，查最近一个月的
        if (smallpro.getKind().equals(4)) {
            smallproQueryWrapper.lambda().ge(Smallpro::getInDate,
                    LocalDateTime.now().minusMonths(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        }
        List<Smallpro> historyList = smallproMapper.listSqlServer(smallproQueryWrapper);
        List<SmallproInfoBO> historyInfoList = new ArrayList<>(historyList.size());
        historyList.forEach(smallProTemp -> {
            SmallproInfoBO smallproInfoBO = new SmallproInfoBO();
            smallproInfoBO.setProductName(smallProTemp.getName())
                    .setSamllproType(EnumUtil.getMessageByCode(SmallProKindEnum.class, smallProTemp.getKind()))
                    .setSmallproId(smallProTemp.getId())
                    .setInuser(smallProTemp.getInUser())
                    .setInDate(smallProTemp.getInDate().format(smallProConstant.getYYYY_MM_DD_HH_MM_SS()));
            historyInfoList.add(smallproInfoBO);
        });

        // 获取订单对应的所有地区名称
        setAreaInfo(smallproOrderInfoBOList, smallpro);

        // 获取小件接件单附件信息
        List<SmallproFileLinkBO> smallproFileLinkBOList = new ArrayList<>();
        List<SmallproInfoFileBO> smallproFileInfoList =
                Optional.ofNullable(smallproMapper.getSmallproFileInfo(smallproId))
                        .orElseGet(ArrayList::new);
        smallproFileInfoList.forEach(fileInfo -> {
            SmallproFileLinkBO smallproFileLinkBO = new SmallproFileLinkBO();
            smallproFileLinkBO.setFid(fileInfo.getFid())
                    .setFileName(fileInfo.getFileName())
                    .setSuffix(fileInfo.getExtension())
                    .setUrl(imageProperties.getSelectImgUrl() + "newstatic/" + fileInfo.getFid())
                    .setId(fileInfo.getId());
            smallproFileLinkBOList.add(smallproFileLinkBO);
        });

        // 查询小件接件操作日志
        List<SmallproOperationInfoBO> allOperationLogs = smallproService.getSmallproOperationLogs(smallproId, 0);
        //日志链接处理
        setOrderUrl(allOperationLogs,smallproId);
        List<SmallproOperationInfoBO> showOperationLogs = allOperationLogs.stream().filter(bo -> bo.getShowFlag() != null && bo.getShowFlag() == 1).collect(Collectors.toList());

        // 获取接件人信息
        Integer inUserId = 0;
        Integer isInService = null;
        if ("系统".equals(smallpro.getInUser())) {
            inUserId = 76783;
            isInService = 1;
        } else {
            List<String> inUserNameList = new ArrayList<>();
            inUserNameList.add(smallpro.getInUser());
            Optional<SmallproInfoInuserInfoBO> inUserInfoOpt = smallproMapper.getSmallproInUserInfoByWrite(inUserNameList).stream().findFirst();
            inUserId = inUserInfoOpt.map(SmallproInfoInuserInfoBO::getInUserId).orElse(0);
            isInService = inUserInfoOpt.map(SmallproInfoInuserInfoBO::getIsZaiZhi).map(Convert::toInt).orElse(null);
        }

        // 获取小件接件情况选项
        QueryWrapper<SmallproKindtype> smallproKindtypeQueryWrapper = new QueryWrapper<>();
        smallproKindtypeQueryWrapper.lambda().eq(SmallproKindtype::getSmallproid, smallpro.getId());
        SmallproKindtype smallproKindtype = Optional.ofNullable(
                smallproKindtypeService.getOneSqlServer(smallproKindtypeQueryWrapper))
                .orElseGet(SmallproKindtype::new);

        //获取是否启用凭证科目辅助核算验证
        R<String> valueByCode = sysConfigClient.getValueByCodeAndXtenant(SysConfigConstant.SMALL_IS_SUBJECT_VERIFY
                ,oaUserBO.getXTenant());
        Integer isSubjectVerify = NumberConstant.ZERO;
        if (StringUtils.isNotEmpty(valueByCode.getData()) && Objects.equals(NumberConstant.ONE,Integer.valueOf(valueByCode.getData()))){
            isSubjectVerify = NumberConstant.ONE;
        }
        // endregion

        // region 小件接件退货/换货信息

        List<SmallproReturnFactoryInfoBO> smallproReturnFactoryInfoBOList = new ArrayList<>();
        List<SmallproSendBackInfoBO> smallproSendBackInfoBOList = new ArrayList<>();
        List<ShouhouTuihuan> smallproShouhouTuihuanList = new ArrayList<>();
        SmallproReturnsBO smallproReturnsBO = new SmallproReturnsBO();
        List<SmallProReturnTypeBO> refundWayList = new ArrayList<>();
        List<SmallproPayInfoBO> refundPayInfoList = new ArrayList<>();
        // 折价金額
        BigDecimal deductionM = BigDecimal.valueOf(0.0);
        // 退款金額
        BigDecimal discountM = BigDecimal.valueOf(0.0);
        BigDecimal installmentAmountPrice = BigDecimal.ZERO;
        boolean isThirdDeduction = false;
        if (smallpro.getKind() == 2 || smallpro.getKind() == 3 || smallpro.getKind() == 4) {
            smallproShouhouTuihuanList =
                    getSmallproShouhouTuihuanList(smallproId);
            // 获取小件返厂件信息
            smallproReturnFactoryInfoBOList =
                    getSmallproReturnFactoryInfoList(smallproOrderInfoBOList, smallproId,
                            smallProConstant.getYYYY_MM_DD_HH_MM_SS());
            // 获取小件接件退还情况
            smallproSendBackInfoBOList =
                    getSmallproSendBackInfoList(smallproId, smallProConstant.getYYYY_MM_DD_HH_MM_SS());
            smallproReturnsBO = new SmallproReturnsBO();
            if (smallproShouhouTuihuanList == null || smallproShouhouTuihuanList.isEmpty()) {
                // item==null
                SmallproReturnsConnBO smallproReturnsConnBO = new SmallproReturnsConnBO();
                smallproReturnsConnBO.setSub_id(String.valueOf(smallproOrderInfoBOList.get(0).getSubId()));
                smallproReturnsConnBO.setRankList(oaUserBO.getRank());
                smallproReturnsConnBO.setShouhou_id(String.valueOf(smallproId));
                smallproReturnsConnBO.setCreCount(new ArrayList<SmallproCanReurnCountBO>(smallproOrderInfoBOList.size()));
                smallproReturnsConnBO.setTuihuan_kind(7);
                smallproOrderInfoBOList.forEach(bo -> {
                    SmallproCanReurnCountBO temp = new SmallproCanReurnCountBO();
                    temp.setCount(bo.getProductCount());
                    temp.setBasketid(bo.getBasketId());
                    temp.setPpid(bo.getPpriceId());
                    smallproReturnsConnBO.getCreCount().add(temp);
                });
                smallproReturnsBO.setConn(smallproReturnsConnBO);
                smallproReturnsBO.setIsrs_t1eof(true);
                BigDecimal baitiaoPrice = BigDecimal.ZERO;
                // 获取白条金额
                if (oaUserBO.getXTenant() < 1000) {
                    baitiaoPrice =
                            smallproMapper.getBaitiaoPrice(String.valueOf(smallproOrderInfoBOList.get(0).getSubId()));
                }
                smallproReturnsBO.setBaitiaoPrice(baitiaoPrice == null ? 0 : baitiaoPrice.doubleValue());
                // 获取库分期金额
                BigDecimal kuBaitiaoPrice =
                        smallproMapper.getKuBaitiaoPrice(String.valueOf(smallproOrderInfoBOList.get(0).getSubId()));
                smallproReturnsBO.setKuBaitiaoPrice(kuBaitiaoPrice == null ? 0 : kuBaitiaoPrice.doubleValue());
            } else {
                ShouhouTuihuan shouhouTuihuan1 = smallproShouhouTuihuanList.get(0);
                ShouhouTuihuan shouhouTuihuan = new ShouhouTuihuan();
                shouhouTuihuan.setKuBaiTiaoM(shouhouTuihuan1.getKuBaiTiaoM());
                shouhouTuihuan.setBaitiaoM(shouhouTuihuan1.getBaitiaoM());
                smallproReturnsBO.setIsrs_t1eof(false);
                smallproReturnsBO.setMShouHouTuiHuan(shouhouTuihuan);
            }

            // region 退款相关

            if (smallpro.getKind() == 3) {

                // 获取退款方式
//                refundWayList = oaUserBO.getXTenant() < 1000 ? getSmallproReturnWays(smallpro.getSubId(), 7,
//                        smallpro.getAreaId()) : getSmallProReturnWaysSaas(smallpro.getSubId(), 7,
//                        smallpro.getAreaId());
                refundWayList = getSmallProReturnWaysSaas(smallpro.getSubId(), TuihuanKindEnum.TPJ.getCode(), smallpro.getAreaId());
                if (smallproSendBackInfoBOList != null && smallproSendBackInfoBOList.size() > 0) {
                    SmallproSendBackInfoBO smallproSendBackInfoBO = smallproSendBackInfoBOList.get(0);
                    Integer afterREId = smallproSendBackInfoBO.getShouhouTuihuanId();
                    refundPayInfoList = smallproRefundExService.getPayInfo(null, afterREId, null, 7);
                    if (refundPayInfoList == null) {
                        refundPayInfoList = new ArrayList<>();
                    }
                    smallproSendBackInfoBO.setIsHavePayInfo(CollUtil.isNotEmpty(refundPayInfoList));
                }
                if(XtenantEnum.isJiujiXtenant()){
                    //九机小件三方收银不可退
                    jiujiThirdShouyingReturnPrice(smallpro.getSubId(),smallproOrderInfoBOList);
                }
            }
            isThirdDeduction = smallproOrderInfoBOList.stream().anyMatch(spoi -> ObjectUtil.defaultIfNull(spoi.getAllocatedDeductionAmount(), BigDecimal.ZERO)
                    .compareTo(BigDecimal.ZERO) > 0);
            // endregion
            // 计算和包分期金额（前端退款金额扣除该金额）
            List<Integer> basketIds =
                    smallproOrderInfoBOList.stream().map(SmallproOrderInfoBO::getBasketId).collect(Collectors.toList());
            if (!isThirdDeduction && CollectionUtils.isNotEmpty(basketIds)) {
                List<Basket> list =
                        basketService.list(new LambdaQueryWrapper<Basket>().select(Basket::getBasketId).eq(Basket::getType, 95).in(Basket::getBasketId,
                                basketIds));
                if (CollectionUtils.isNotEmpty(list)) {
                    installmentAmountPrice =
                            shouyinOtherService.getInstallmentAmount(smallproOrderInfoBOList.get(0).getSubId());
                }
            }
            // region 服务退款相关
            for (SmallproOrderInfoBO smallproOrderInfoBO : smallproOrderInfoBOList) {
                Integer ppid = smallproOrderInfoBO.getTargetPpriceId();
                LocalDateTime buyTime = smallpro.getBuyDate();
                BigDecimal price = smallproOrderInfoBO.getPriceReturn();
                if (Boolean.TRUE.equals(isServiceProductMap.get(smallproOrderInfoBO))) {
                    discountM = smallProAdapterService.calculateDiscountAmount(ppid, buyTime, price,smallproOrderInfoBO.getBasketId());
                } else if (Boolean.TRUE.equals(isYearCardProductMap.get(smallproOrderInfoBO))) {
                    Boolean aBoolean =
                            Optional.ofNullable(filmCardInfomationBO).map(FilmCardInfomationBO::getIsCanRefund).orElse(false);
                    if (aBoolean) {
                        if (filmCardInfomationBO.getIsCanRefund()) {
                            discountM = BigDecimal.valueOf(filmCardInfomationBO.getRefundAmount());
                        }
                    }
                }
                // 移动顺差让利，移动补贴
                BigDecimal mobileSubsidyPrice = BigDecimal.ZERO;
                if (CommenUtil.isJiuJiXtenant(oaUserBO.getXTenant())){
                    mobileSubsidyPrice =
                            shouyinOtherService.getInstallmentAmountMobileSubsidy(smallproOrderInfoBO.getSubId());
                }
                // 退款金额需要减去移动顺差让利，移动补贴
                discountM = discountM.subtract(mobileSubsidyPrice);
                /*smallproOrderInfoBOList.get(0).setPriceReturn(price.subtract(new BigDecimal(filmCardInfomationBO
                .getRefundAmount()))
                .setScale(2,RoundingMode.HALF_UP));*/
                // 单价-退款金额
                deductionM = smallproOrderInfoBO.getPrice().subtract(discountM);
            }

            // endregion
        }
        // endregion


        String changeProductInfo = changeProductNameOpt.map(SmallproProductInfoBO::getDisplay).orElse("");

        //计算运营商返利金额
        BigDecimal offsetMoney = BigDecimal.ZERO;
        Integer basketId = smallproBillList.get(0).getBasketId();
        if(!isThirdDeduction){
            SmallProVimpelComBo proVimpelComInfo = smallproMapper.getProVimpelComInfo(basketId);
            if (proVimpelComInfo != null) {
                offsetMoney = Optional.ofNullable(proVimpelComInfo.getOffsetMoney()).orElse(BigDecimal.ZERO);
            }
        }
        List<Integer> basketIds =
                smallproOrderInfoBOList.stream().map(SmallproOrderInfoBO::getBasketId).collect(Collectors.toList());
        //计算运营商抵扣金额计算
        if(!isThirdDeduction) {
            BigDecimal sumOffSetMoney = shouyinOtherService.getSumOffSetMoney(basketIds);
            if (sumOffSetMoney.compareTo(BigDecimal.ZERO) > 0) {
                offsetMoney = sumOffSetMoney;
            }
        }
        // region 装填数据
        SmallproInformationRes smallproInformationRes = new SmallproInformationRes();
        smallproInformationRes.setIsNewSmallPro(smallproService.isNewSmallPro(smallpro.getAreaId()));
        Integer currAreaId = CommenUtil.currAreaId(smallpro.getToAreaId(), smallpro.getAreaId());
        smallproInformationRes.setAreaSubject(SpringUtil.getBean(ShouhouYuyueService.class).initializationAreaSubject(currAreaId));
        // 计算超时时间
        String overInDate = getOverTime(smallpro.getInDate().toInstant(ZoneOffset.of("+8")).toEpochMilli());
        String overTradeDate = getOverTime(smallpro.getBuyDate().toInstant(ZoneOffset.of("+8")).toEpochMilli());
        // 维修成本如果为空传0.0
        BigDecimal costPrice = ((smallpro.getCostPrice() == null) ? BigDecimal.ZERO : smallpro.getCostPrice());
        // 外观选项
        Integer outwardFlag = (smallpro.getOutward() == null || smallpro.getOutward().isEmpty()) ? 1 : 0;

        Integer searchAreaId = CommonUtils.defaultIfNullOrZero(smallpro.getToAreaId(), smallpro.getAreaId());

        Integer kcCount = changeProductNameOpt.map(cp -> smallproMapper.getKcCount(
                ObjectUtil.defaultIfNull(cp.getPpriceId1(), cp.getPpriceId()),searchAreaId)).orElse(0);
        boolean isOnlyOneOrderInfo = smallproOrderInfoBOList.size() == 1;
        smallproInformationRes.setSmallproId(smallproId)
                .setProductBindType(createProductBindType(smallpro, smallproBillList))
                .setYuYueId(smallpro.getYuyueId())
                .setUserInfo(smallproUserInfoBO)
                .setIsReturnOperatorBasket(operatorBasketService.isCarrierDeduction(smallproBillList))
                .setUserName(smallpro.getUserName())
                .setOldIdType(smallpro.getOldIdType())
                .setOrderInfoList(smallproOrderInfoBOList)
                .setServiceConfig(smallproServiceConfigBOList)
                .setHistorySmallpro(historyInfoList)
                .setHistorySize(historyInfoList.size())
                .setWarrantyStatus(Boolean.TRUE.equals(smallpro.getIsBaoxiu()) ? 1 : 0)
                .setCodeMsgFid(smallpro.getFid())
                .setCodeMsg(smallpro.getCodeMsg())
                .setComment(smallpro.getComment())
                .setConfig(smallpro.getConfig())
                .setGroup(smallpro.getGroupId())
                .setImei(smallpro.getImei())
                .setInDate(smallpro.getInDate().format(smallProConstant.getYYYY_MM_DD_HH_MM_SS()))
                .setInUser(smallpro.getInUser())
                .setKind(smallpro.getKind())
                .setProblem(smallpro.getProblem())
                .setServiceType(smallpro.getServiceType() == null ? null : smallpro.getServiceType().byteValue())
                .setStats(smallpro.getStats())
                .setTradeDate(smallpro.getBuyDate().format(smallProConstant.getYYYY_MM_DD_HH_MM_SS()))
                .setOverInDate(overInDate)
                .setOverTradeDate(overTradeDate)
                .setMaintainPrice((smallpro.getFeiyong() == null) ? BigDecimal.ZERO : smallpro.getFeiyong())
                .setCostPrice(costPrice)
                .setOutward(smallpro.getOutward())
                .setOutwardFlag(outwardFlag)
                .setMaintainState(smallpro.getWxState())
                .setMaintainUser(smallpro.getWxUser())
                .setMaintainChannel(smallpro.getWxQudao())
                .setAreaId(smallpro.getAreaId())
                .setAreaName(smallpro.getArea())
                .setIsToArea((smallpro.getIsToArea()) ? 1 : 0)
                .setToAreaId(smallpro.getToAreaId())
                .setToAreaName(smallpro.getToArea())
                .setFilesList(smallproFileLinkBOList)
                .setReturnFactoryInfoBOList(smallproReturnFactoryInfoBOList)
                .setInUserId(inUserId)
                .setIsInUserInService(isInService)
                .setIsCashRegister((smallpro.getIsShouyingLock() == null) ? 0 : (smallpro.getIsShouyingLock()) ? 1 : 0)
                .setCashRegisterUser(smallpro.getShouyinUser())
                .setCashRegisterDate((smallpro.getShouyinDate() == null) ? "" :
                        smallpro.getShouyinDate().format(smallProConstant.getYYYY_MM_DD_HH_MM_SS()))
                .setIsReturn((smallpro.getIsTui() == null) ? 0 : (smallpro.getIsTui()) ? 1 : 0)
                .setPickupDate((smallpro.getQujianDate() == null) ? "" :
                        smallpro.getQujianDate().format(smallProConstant.getYYYY_MM_DD_HH_MM_SS()))
                .setLogs(allOperationLogs)
                .setShowLogs(showOperationLogs)
                .setImageLink((smallpro.getFid() == null) ? "" : sysConfigClient.getValueByCode(SysConfigConstant.IMG_URL).getData() + "/newstatic/" + smallpro.getFid())
                .setSituationKind((smallproKindtype.getKind() == null) ? 0 : smallproKindtype.getKind())
                .setSendBackInfoList(smallproSendBackInfoBOList)
                .setDataRelease(smallpro.getDataRelease())
                .setShouhouTuihuanList(smallproShouhouTuihuanList)
                .setReturns(smallproReturnsBO)
                .setRefundWay(refundWayList)
                .setChangePpriceId(smallpro.getChangePpriceid())
                .setChangeProductName(changeProductInfo)
                .setChangeProductNameStr(changeProductNameOpt.map(SmallproProductInfoBO::getProductName).orElse(null))
                .setChangeProductColorStr(changeProductNameOpt.map(SmallproProductInfoBO::getProductColor).orElse(null))
                .setChangeProductBarCode(changeProductNameOpt.map(SmallproProductInfoBO::getBarCode).orElse(null))
                .setChangeProductBarCodeList(SpringUtil.getBean(ProductbarcodeService.class).listBarCodeByPpids(Collections.singletonList(smallpro.getChangePpriceid()))
                        .getOrDefault(smallpro.getChangePpriceid(), Collections.emptyList()))
                .setKcCount(kcCount)
                .setIsFreeExchange(isOnlyOneOrderInfo ? Boolean.TRUE.equals(isFreeExchangeMap.get(smallproOrderInfoBOList.get(0))) : false)
                .setRefundPayInfoList(refundPayInfoList)
                .setIsServiceProduct(isOnlyOneOrderInfo ? Boolean.TRUE.equals(isServiceProductMap.get(smallproOrderInfoBOList.get(0))) : false)
                .setIsYearCardProduct(isOnlyOneOrderInfo ? Boolean.TRUE.equals(isYearCardProductMap.get(smallproOrderInfoBOList.get(0))) : false)
                .setDeductionM(deductionM.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue())
                .setDiscountM(discountM.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue())
                .setIsGenerateCashOrder(createIsGenerateCashOrder(smallpro))
                .setHasGeneratedCash(createHasGeneratedCash(smallpro))
                .setReservationId(smallpro.getYuyueId())
                .setReservationCheck(smallpro.getYuyueCheck() == null ? false : smallpro.getYuyueCheck())
                .setOldId(smallpro.getOldId() == null ? "" : smallpro.getOldId().toString())
                .setInstallmentAmountPrice(BigDecimal.ZERO)
                .setOffsetMoney(Optional.ofNullable(offsetMoney).filter(om -> om.compareTo(BigDecimal.ZERO) > NumberConstant.ZERO.intValue()).orElse(installmentAmountPrice))
                .setIsBaoxiu(smallpro.getIsBaoxiu())
                .setIsSubjectVerify(isSubjectVerify)
                .setIsSpecialTreatment(smallpro.getIsSpecialTreatment());
        List<ShouhouTuihuan> refundList = smallproMapper.getSmallProRefundList(smallproId);
        Optional.of(refundList.stream().anyMatch(t -> CommenUtil.isCheckTrue(t.getCheck3()))).filter(Boolean::booleanValue)
                .ifPresent(b -> smallproInformationRes.setIsReturn(NumberConstant.TWO));
        if(XtenantEnum.isJiujiXtenant()){
            // 计算售后小件收银金额与退款金额的差值
            BigDecimal difference = smallproMapper.getMaintenanceCostDifference(smallproId);
            // 获取当前小件的费用
            BigDecimal feiyong = Optional.ofNullable(smallpro).map(Smallpro::getFeiyong).orElse(BigDecimal.ZERO);
            if (difference.compareTo(BigDecimal.ZERO) == 0) {
                // 如果差值为0，设置为2 (那就说说明已经退款完成 或者 退款进行中) 不可以继续退款
                smallproInformationRes.setIsReturnMaintenance(NumberConstant.TWO);
            } else if (difference.compareTo(feiyong) == 0) {
                // 如果差值等于feiyong，设置为0 (那就说明收银完成) 可以继续退款
                smallproInformationRes.setIsReturnMaintenance(NumberConstant.ZERO);
            }
        }

        //构建退换历史记录信息
        buildRefundHistoryList(refundList, smallproInformationRes);
        //设置换货商品信息
        buildChangeProductList(changeProductNameOpt.orElse(null), smallproInformationRes);


        // endregion
        if(SmallProServiceTypeEnum.SMALL_PRO_KIND_YEAR_CARD.getCode().equals(smallpro.getServiceType())){
            boolean isYearPackageTransfer = SpringUtil.getBean(IYearPackageTransferService.class).lambdaQuery()
                    .eq(YearPackageTransferPo::getSmallId, smallpro.getId()).count() > 0;
            smallproInformationRes.setPackageTransferFlag(isYearPackageTransfer);
        }

        if (CommenUtil.isNotNullZero(smallpro.getSubId())
                && !Boolean.TRUE.equals(smallproInformationRes.getPackageTransferFlag())) {
            //获取订单表信息
            Sub subInfo = CommenUtil.autoQueryHist(()-> subService.getById(smallpro.getSubId()), MTableInfoEnum.SUB, smallpro.getSubId());
            if(subInfo != null){
                if (StringUtils.isNotEmpty(subInfo.getSubMobile())) {
                    String subMobile = subInfo.getSubMobile();
                    //隐藏中间4位
                    subMobile = subMobile.replaceAll("(\\d{3})\\d{4}(\\d{4})", "$1****$2");
                    smallproInformationRes.setPhone(subMobile);
                }
                smallproInformationRes.setSubUserInfo(buildUserInfoFun.apply(Convert.toInt(subInfo.getUserId())));
            }
        }
        boolean cidFlag = false;
        if (SmallProKindEnum.SMALL_PRO_KIND_EXCHANGE.getCode().equals(smallpro.getKind())
                &&Objects.nonNull(smallpro.getChangePpriceid())) {
            Productinfo changePpriceidProductinfo = productinfoService.getProductinfoByPpid(smallpro.getChangePpriceid());
            Integer cid = changePpriceidProductinfo.getCid();
            List<Integer> cids = Arrays.asList(662);
            if (cids.contains(cid)) {
                cidFlag = true;
            }
        }
        //查询小件绑定的手机串号信息
        if (smallpro.getKind().equals(SmallProKindEnum.SMALL_PRO_KIND_EXCHANGE.getCode())) {
            Optional<Dict> imeiInfoOpt = smallproMapper.getBindImeiBySmallId(smallproId).stream()
                    .sorted(Comparator.comparing(dic -> {
                        if (Objects.equals(dic.getStr("imei"), smallpro.getImei())) {
                            // 优先取与当前串号相等的
                            return 0;
                        } else {
                            return 1;
                        }
                    })).findFirst();
            String imei = imeiInfoOpt.map(imeiInfo -> imeiInfo.getStr("imei")).orElse("");
            //膜的billId
            Integer moBillId = imeiInfoOpt.map(imeiInfo -> imeiInfo.getInt("smallProBillId")).orElse(0);
            if (StringUtils.isEmpty(imei)) {
                if (!XtenantEnum.isJiujiXtenant()){
                    return smallproInformationRes;
                }
//                smallproInformationRes.setCutScreenError("该保护膜商品原订单没有绑定串号，请先绑定串号再进行“切膜操作”（若为不需要绑定出纳号的特殊商品，可申报呼叫中心进行审核授权。）");
            } else {
                if (XtenantEnum.isJiujiXtenant()) {
                    ImeiQueryInfoBo imeiQueryInfoNotLimitUser = shouhouService.getImeiQueryInfoNotLimitUser(imei);
                    if (Objects.isNull(imeiQueryInfoNotLimitUser)) {
//                        smallproInformationRes.setCutScreenError("该保护膜商品原订单绑定串号未识别出机型，请确认绑定串号是否正确（若绑定串号正确，但仍未识别出机型，可申报呼叫中心进行审核授权。）");
                    }
                }
            }

            if (XtenantEnum.isJiujiXtenant() && StringUtils.isEmpty(smallproInformationRes.getCutScreenError())) {
                int leftCount = CommonUtils.getResultData(cutScreenRecordCloud.leftCount(smallproBillList.get(0).getBasketId(),
                                CutScreenOrderTypeEnum.SMALLPRO.getCode()),
                        userMsg -> {
                            SpringContextUtil.addRequestKeyMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG, "获取切膜余量异常, 原因: {}", userMsg);
                            return 0;
                        });
                if (leftCount <= 0) {
                    smallproInformationRes.setCutScreenError("检测到您的切膜数据存在异常，暂时不能进行切膜操作，如需切膜请对接呼叫同事进行审核授权。");
                }
            }

            if (XtenantEnum.isJiujiXtenant()){
                Boolean authMark = CommonUtils.getResultData(cutScreenRecordCloud.authMark(smallproBillList.get(0).getBasketId(), CutScreenOrderTypeEnum.SMALLPRO.getCode()),
                        userMsg -> {
                            SpringContextUtil.addRequestKeyMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG, "获取切膜权限异常, 原因: {}", userMsg);
                            return false;
                        });
                if (authMark) {
                    smallproInformationRes.setCutScreenError("");
                }
            }
            Platform currentPlatform = CommenUtil.getCurrentPlatform();
            HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
            String platform = request.getHeader("Platform");
            if (StringUtils.isEmpty(platform) || !platform.contains("MOA")) {
                R<String> hostR = sysConfigClient.getValueByCode(SysConfigConstant.OA_URL);
                if (XtenantEnum.isJiujiXtenant()){
                    if (CollectionUtils.isNotEmpty(smallproInformationRes.getSendBackInfoList())){
                        SmallproSendBackInfoBO smallproSendBackInfoBO = smallproInformationRes.getSendBackInfoList().get(0);
                        if (!NumberConstant.TWO.equals(smallproInformationRes.getStats())
                                && cidFlag
                                && !NumberConstant.ZERO.equals(basketId)
                                && hostR.getCode() == ResultCode.SUCCESS
                                && StringUtils.isNotEmpty(hostR.getData())){
                            String url = hostR.getData() + SmallProRelativePathConstant.CUT_SCREEN_PC;
                            smallproInformationRes.setShowCutScreen(Boolean.TRUE);
                            smallproInformationRes.setCutScreenUrl(StrUtil.format(url, imei, moBillId));
                        }
                    }
                }else {
                    if (hostR.getCode() == ResultCode.SUCCESS
                            && StringUtils.isNotEmpty(hostR.getData())){
                        String url = hostR.getData() + SmallProRelativePathConstant.CUT_SCREEN_PC;
                        smallproInformationRes.setShowCutScreen(Boolean.TRUE);
                        smallproInformationRes.setCutScreenUrl(StrUtil.format(url, imei, moBillId));
                    }
                }
            } else {
                R<String> hostR = sysConfigClient.getValueByCode(SysConfigConstant.MOA_URL);
                if (XtenantEnum.isJiujiXtenant()){
                    if (CollectionUtils.isNotEmpty(smallproInformationRes.getSendBackInfoList())){
                        SmallproSendBackInfoBO smallproSendBackInfoBO = smallproInformationRes.getSendBackInfoList().get(0);
                        if (!NumberConstant.TWO.equals(smallproInformationRes.getStats())
                                && cidFlag
                                && !NumberConstant.ZERO.equals(basketId)
                                && hostR.getCode() == ResultCode.SUCCESS
                                && StringUtils.isNotEmpty(hostR.getData())){
                            String url = hostR.getData() + SmallProRelativePathConstant.CUT_SCREEN_MOA;
                            smallproInformationRes.setShowCutScreen(Boolean.TRUE);
                            smallproInformationRes.setCutScreenUrl(StrUtil.format(url, imei, moBillId));
                        }
                    }
                }else {
                    if (hostR.getCode() == ResultCode.SUCCESS
                            && StringUtils.isNotEmpty(hostR.getData())){
                        String url = hostR.getData() + SmallProRelativePathConstant.CUT_SCREEN_MOA;
                        smallproInformationRes.setShowCutScreen(Boolean.TRUE);
                        smallproInformationRes.setCutScreenUrl(StrUtil.format(url, imei, moBillId));
                    }
                }
            }
            if (XtenantEnum.isJiujiXtenant() && smallproInformationRes.getShowCutScreen() && oaUserBO.getRank().contains("777")) {
                smallproInformationRes.setShowCutScreenAudit(Boolean.TRUE);
            }
        }

        return smallproInformationRes;
    }

    /**
     * 创建小件产品绑定类型
     * @param smallpro
     * @return
     */
    private Integer createProductBindType(Smallpro smallpro,List<SmallproBill> smallproBillList){
        Integer productBindType = NumberConstant.ZERO;
        try {
            //如果不是九机或者接件商品不是贴膜分类
            if(CollectionUtils.isEmpty(smallproBillList)){
                return productBindType;
            }
            SmallproBill smallproBill = smallproBillList.get(NumberConstant.ZERO);
            if(XtenantEnum.isSaasXtenant()||!categoryService.determineFilmByPpid(Convert.toInt(smallproBill.getPpriceid()))){
                return productBindType;
            }
            //查询是否绑定串号
            Integer basketId = smallproBill.getBasketId();
            if(ObjectUtil.isNotNull(basketId) && !NumberConstant.ZERO.equals(basketId)){
                productBindType = SpringUtil.getBean(SmallproService.class).handleProductBindType(basketId);
            }
        }catch (Exception e){
            RRExceptionHandler.logError("小件单详情获取处理绑定类型异常",Dict.create().set("smallpro",smallpro),e,smsService::sendOaMsgTo9JiMan);
        }
        return productBindType;
    }

    /**
     *  判断当前门店是否模式输出门店
     * @param smallpro
     * @return
     */
    public static Boolean isSaasAreaId(Smallpro smallpro){
        //当前所在地区为9X_scc, 9X_scc2, 9X_scc3,CH 不展示“添加售前贴膜损耗按钮”
        Integer currentAreaId = Optional.ofNullable(smallpro.getToAreaId()).orElse(smallpro.getAreaId());
        return SAAS_AREA_ID_LIST.contains(currentAreaId);
    }

    /**
     * 生成是否显示现货单
     * @param smallpro
     * @return
     */
    @Override
    public Boolean createIsGenerateCashOrder(Smallpro smallpro){
        try {
            //输出也是不做该功能
            if(ObjectUtil.isNull(smallpro) || XtenantEnum.isSaasXtenant()){
                return Boolean.FALSE;
            }
            //当前所在地区为9X_scc, 9X_scc2, 9X_scc3,CH 不展示“添加售前贴膜损耗按钮”
            if(isSaasAreaId(smallpro)){
                return Boolean.FALSE;
            }
            Integer changePpriceid = Optional.ofNullable(smallpro.getChangePpriceid()).orElse(NumberConstant.ONE);
            //保护膜分类商品商品判断
            Boolean determineFilmByPpid = null;
            if(BbsxpUserIdConstants.XIAN_HUO.equals(smallpro.getUserId())){
                determineFilmByPpid = categoryService.isAutomaticScrapFilm(changePpriceid);
            } else {
                determineFilmByPpid = categoryService.determineFilmByPpid(changePpriceid);
            }
            // 是否换货
            Boolean isExchange = SmallProKindEnum.SMALL_PRO_KIND_EXCHANGE.getCode().equals(smallpro.getKind());
            //后台地区和小件当前所在地区是否一致
            OaUserBO userBO = Optional.ofNullable(currentRequestComponent.getCurrentStaffId()).orElseThrow(() -> new CustomizeException("登录超时"));
            boolean isAreaId = Optional.ofNullable(smallpro.getAreaId()).orElse(Integer.MAX_VALUE).equals(userBO.getAreaId());
            // 小件售后单没有取件 或者 取机完成不超过24H
            LocalDateTime qujianDate = smallpro.getQujianDate();
            Boolean isQuJi = Boolean.FALSE;
            if(ObjectUtil.isNotNull(qujianDate)){
                isQuJi =  LocalDateTime.now().minusDays(1L).isBefore(qujianDate);
            } else {
                isQuJi = Boolean.TRUE;
            }
            return determineFilmByPpid && isExchange && isAreaId && isQuJi;
        } catch (Exception e) {
            RRExceptionHandler.logError("生成是否显示现货单按钮显示异常", smallpro, e, smsService::sendOaMsgTo9JiMan);
            return Boolean.FALSE;
        }
    }

    /**
     * 是否提交过损耗
     * @param smallpro
     * @return
     */
    public Boolean createHasGeneratedCash(Smallpro smallpro){
      Integer num = Optional.ofNullable(smallproService.selectQuantityOfLoss(OldIdTypeEnum.SMALL_PRO_TYPE.getCode(), smallpro.getId())).orElse(NumberConstant.ZERO);
      return num > NumberConstant.ZERO;
    }

    @Override
    public List<ChangeInfoRes> selelctChangeInfoList(Integer smallProId){
        //小件单查询
        Smallpro smallpro = smallproMapper.getByIdSqlServerByWriter(smallProId);
        Integer overDays = getOverDays(smallpro.getBuyDate().toInstant(ZoneOffset.of("+8")).toEpochMilli());
        //构建参数
        SmallproProductInfoBO changeProduct = getProductNameByPpriceId(smallpro.getChangePpriceid());
        SmallproInformationRes smallproInformationRes =new SmallproInformationRes();
        QueryWrapper<SmallproBill> smallproBillQueryWrapper = new QueryWrapper<>();
        smallproBillQueryWrapper.lambda().eq(SmallproBill::getSmallproID, smallProId);
        List<SmallproBill> smallproBillList =
                smallproBillService.getListWithSqlServer(smallproBillQueryWrapper);
        if (CollectionUtils.isEmpty(smallproBillList)) {
            return null;
        }
        // 获取额外数据，包括订单和用户
        List<SmallproOrderInfoBO> smallproOrderInfoBOList =
                ((SmallproDetailsExServiceImpl) AopContext.currentProxy()).getSmallproOrderListInfo(smallproBillList,
                        smallpro.getSubId(), overDays);
        smallproInformationRes.setOrderInfoList(smallproOrderInfoBOList);
        smallproInformationRes.setSmallproId(smallProId);
        smallproInformationRes.setStats(smallpro.getStats());
        Optional<SmallproProductInfoBO> changeProductNameOpt = Optional.empty();
        if (smallpro.getChangePpriceid() != null) {
            changeProductNameOpt = Optional.ofNullable(getProductNameByPpriceId(smallpro.getChangePpriceid()));
        }
        Integer searchAreaId = CommonUtils.defaultIfNullOrZero(smallpro.getToAreaId(), smallpro.getAreaId());
        Integer kcCount = changeProductNameOpt.map(cp -> smallproMapper.getKcCount(
                ObjectUtil.defaultIfNull(cp.getPpriceId1(), cp.getPpriceId()),searchAreaId)).orElse(0);
        smallproInformationRes.setKcCount(kcCount);

        buildChangeProductList(changeProduct, smallproInformationRes);
        return smallproInformationRes.getChangeInfoList();
    }



    private void buildChangeProductList(SmallproProductInfoBO changeProduct, SmallproInformationRes smallproInformationRes) {
        if(changeProduct == null || XtenantEnum.isSaasXtenant()){
            return;
        }
        List<Productbarcode> productbarcodes = SpringUtil.getBean(ProductbarcodeService.class)
                .listByPpids(Collections.singletonList(changeProduct.getPpriceId())).get(changeProduct.getPpriceId());
        List<Integer> billIds = smallproInformationRes.getOrderInfoList().stream().map(SmallproOrderInfoBO::getSmallproBillId)
                .distinct().filter(Objects::nonNull).collect(Collectors.toList());
        // 批量获取备货量
        Map<Integer, StockStatusCountBo> stockStatusCountMap = productKcService.listStockStatusCount(billIds, StockBasketTypeEnum.SMALL_PRODUCT_ORDER.getCode())
                .stream().collect(Collectors.toMap(StockStatusCountBo::getBasketId, Function.identity(),(n1,n2)->n2));
        Map<Integer, SmallproBillLogVo> lastLogMap = CommonUtils.getResultData(SpringUtil.getBean(SmallproBillLogClient.class)
                .batchListLogLast(LastLogReq.builder().billIds(billIds).type(1).build()), userMsg -> {
            SpringContextUtil.addRequestKeyMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG,"获取最备货新日志发生[{}]异常", userMsg);
            return Collections.emptyMap();
        });

        List<ChangeInfoRes> changeInfoList = smallproInformationRes.getOrderInfoList().stream().map(oi -> {
            ChangeInfoRes.ChangeInfoResBuilder builder = ChangeInfoRes.builder();
            builder.smallproBillId(oi.getSmallproBillId());
            builder.basketId(oi.getBasketId());
            builder.ppriceid(changeProduct.getPpriceId());
            builder.productName(changeProduct.getProductName());
            builder.productColor(changeProduct.getProductColor());
            builder.memberPrice(changeProduct.getMemberPrice());
            SmallproBillLogVo lastLog = lastLogMap.get(oi.getSmallproBillId());
            if(lastLog != null){
                builder.lastWorkLog(lastLog.getContent());
            }

            builder.count(oi.getProductCount());
            builder.barCodeList(productbarcodes.stream().map(Productbarcode::getBarCode).collect(Collectors.toList()));
            StockStatusCountBo stockStatusCountBo = stockStatusCountMap.get(oi.getSmallproBillId());
            // 备货状态处理
            buildChangeStock(builder, oi, stockStatusCountBo, smallproInformationRes);
            return builder.build();
        }).collect(Collectors.toList());

        smallproInformationRes.setChangeInfoList(changeInfoList);
    }
    /**
     * 构建换货商品的备货状态
     *
     * @param builder
     * @param oi
     * @param stockStatusCountBo
     * @param smallpro
     */
    private void buildChangeStock(ChangeInfoRes.ChangeInfoResBuilder builder, SmallproOrderInfoBO oi,
                                  StockStatusCountBo stockStatusCountBo, SmallproInformationRes smallpro) {
        Integer kcCount = ObjectUtil.defaultIfNull(smallpro.getKcCount(), 0);
        builder.kcCount(kcCount);
        Optional<StockStatusCountBo> sscOpt = Optional.ofNullable(stockStatusCountBo);
        builder.onWayCount(sscOpt.map(StockStatusCountBo::getZtCount).orElse(0));
        Integer productCount = ObjectUtil.defaultIfNull(oi.getProductCount(), 0);
        builder.stockType(StockBasketTypeEnum.SMALL_PRODUCT_ORDER.getCode());
        sscOpt.ifPresent(ssc -> SpringContextUtil.addRequestKeyMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG,
                "出库数量: {}, 换货数量: {}, 备货锁定量: {}, 关联调拨在途数量: {}, 关联调拨中数量: {}, 关联进行中的采购量: {}, 当前库存: {}, 未备货提交量: {}",
                ssc.getTakeCount(), productCount, ssc.getBeiCount(), ssc.getZtCount(), ssc.getDbCount(), ssc.getCgCount(),
                kcCount, ssc.getBeihuoCount()));
        ChangeInfoRes.WorkTypeEnum workTypeEnum;
        ChangeInfoRes.StockStatusEnum stockStatusEnum;
        boolean isHqtxh = smallproService.isHqtxh(smallpro.getSmallproId());
        if(isHqtxh){
            stockStatusEnum = ChangeInfoRes.StockStatusEnum.NO_NEED_STOCK;
        }else if(SmallProStatsEnum.SMALL_PRO_STATS_PROCESS_COMPLETED.getCode().equals(smallpro.getStats())
            || sscOpt.map(StockStatusCountBo::getTakeCount).filter(takeCount -> takeCount >= productCount).isPresent()){
            //取货出库数量>=换货数量
            stockStatusEnum = ChangeInfoRes.StockStatusEnum.COMPLETED;
        }else if(sscOpt.filter(ssc -> ssc.getTakeCount()+ssc.getBeiCount() >= productCount).isPresent()){
            //备货锁定量>=换货数量
            stockStatusEnum = ChangeInfoRes.StockStatusEnum.IN_STOCK;
        }else if(sscOpt.filter(ssc -> ssc.getZtCount()>0 && ssc.getZtCount()+ssc.getTakeCount()+ssc.getBeiCount() >= productCount).isPresent()){
            //关联调拨在途数量+备货锁定量 >= 换货数量
            stockStatusEnum = ChangeInfoRes.StockStatusEnum.IN_TRANSIT;
        }else if(sscOpt.filter(ssc -> ssc.getDbCount()>0 && ssc.getDbCount()+ssc.getTakeCount()+ssc.getBeiCount() >= productCount).isPresent()){
            // 关联调拨中数量+备货锁定量 >= 换货数量
            stockStatusEnum = ChangeInfoRes.StockStatusEnum.IN_TRANSIT_ALLOCATION;
        }else if(sscOpt.filter(ssc -> ssc.getCgCount()>0).isPresent()){
            //关联进行中的采购量大于0
            stockStatusEnum = ChangeInfoRes.StockStatusEnum.IN_PURCHASE;
        }else if(sscOpt.filter(ssc -> ssc.getBeihuoCount()>0).isPresent()){
            //未备货提交量>0
            stockStatusEnum = ChangeInfoRes.StockStatusEnum.NOT_IN_STOCK;
        }else{
            // 等待备货
            stockStatusEnum = ChangeInfoRes.StockStatusEnum.WAITING_FOR_STOCK;
        }

        //操作按钮处理
        String workTypeNameFormat = "{}";
        Integer beihuoCount = sscOpt.map(StockStatusCountBo::getBeihuoCount).orElse(0);
        if(Stream.of(SmallProStatsEnum.SMALL_PRO_STATS_DELETED, SmallProStatsEnum.SMALL_PRO_STATS_PROCESS_COMPLETED)
                .anyMatch(spe -> spe.getCode().equals(smallpro.getStats()))
                || sscOpt.map(StockStatusCountBo::getTakeCount).filter(takeCount -> takeCount >= productCount).isPresent()
                || isHqtxh
        ){
            workTypeEnum = ChangeInfoRes.WorkTypeEnum.NOT_OPERABLE;
        }else if(sscOpt.filter(ssc -> ssc.getBeiCount()>0).isPresent()){
            // 取消锁定
            workTypeEnum = ChangeInfoRes.WorkTypeEnum.CANCEL_LOCK;
            workTypeNameFormat = "{}({})";
        }else if(productCount > kcCount && beihuoCount == 0){
            // 备货
            workTypeEnum = ChangeInfoRes.WorkTypeEnum.STOCK;
        }else if (beihuoCount >0){
            // 取消备货
            workTypeEnum = ChangeInfoRes.WorkTypeEnum.CANCEL_STOCK;
        }else{
            //备货锁定
            workTypeEnum = ChangeInfoRes.WorkTypeEnum.LOCK;
        }

        if(workTypeEnum != null){
            builder.workType(workTypeEnum.getCode());
            builder.workTypeName(StrUtil.format(workTypeNameFormat, workTypeEnum.getMessage(),
                    sscOpt.map(ssc -> ssc.getBeiCount()).orElse(0)));
        }
        if(stockStatusEnum != null){
            builder.stockStatus(stockStatusEnum.getCode());
            builder.stockStatusName(stockStatusEnum.getMessage());
        }

    }

    /**
     * 获取商品服务类型
     * @param ppid 商品ID
     * @return 服务类型，如果获取失败则返回null
     */
    @Override
    @Cached(name = "afterservice:getProductServiceType", expire = 10, timeUnit = TimeUnit.MINUTES, cacheType = CacheType.BOTH)
    public Integer getProductServiceType(Integer ppid) {
        if (ppid == null) {
            return null;
        }
        WebCloud webCloud = SpringUtil.getBean(WebCloud.class);
        try {
            Result<List<ProductServiceOpeningVO>> productServiceOpeningVOResult = webCloud.listProductServicesByPpids(Convert.toStr(ppid));
            log.info("webCloud.listProductServicesByPpids查询结果，result={}, 传入参数：{}", JSON.toJSONString(productServiceOpeningVOResult), ppid);
            
            if (productServiceOpeningVOResult != null && productServiceOpeningVOResult.isSuccess() && productServiceOpeningVOResult.getData() != null) {
                List<ProductServiceOpeningVO> data = productServiceOpeningVOResult.getData();
                if (CollUtil.isNotEmpty(data)) {
                    ProductServiceOpeningVO productServiceOpeningVO = Optional.ofNullable(data.get(NumberConstant.ZERO)).orElse(new ProductServiceOpeningVO());
                    return productServiceOpeningVO.getType();
                }
            }
        } catch (Exception e) {
            RRExceptionHandler.logError("调用webCloud.listProductServicesByPpids查询服务类型异常", "ppid=" + ppid, e, smsService::sendOaMsgTo9JiMan);
        }
        return null;
    }

    /**
     * 判断ppid是否为年包
     * @param ppid 商品ID
     * @return 是否为年包
     */
    @Override
    public boolean isYearCardProduct(Integer ppid) {
        if (ppid == null) {
            return false;
        }
        Integer serviceType = smallproDetailsExService.getProductServiceType(ppid);
        boolean isYearCard = ANNUAL_PACKAGE_TYPE.equals(serviceType);
        log.info("判断ppid={}是否为年包商品，serviceType={}, 结果={}", ppid, serviceType, isYearCard);
        return isYearCard;
    }

    /**
     * 判断ppid是否为服务类商品（非年包）
     * @param ppid 商品ID
     * @return 是否为服务类商品
     */
    @Override
    public boolean isServiceProduct(Integer ppid) {
        if (ppid == null) {
            return false;
        }
        Integer serviceType = smallproDetailsExService.getProductServiceType(ppid);
        if (serviceType == null) {
            return false;
        }
        boolean isServiceProduct = !ANNUAL_PACKAGE_TYPE.equals(serviceType);
        log.info("判断ppid={}是否为服务类商品（非年包），serviceType={}, 结果={}", ppid, serviceType, isServiceProduct);
        return isServiceProduct;
    }

    @Override
    public boolean isServiceProduct(Integer smallproBillBasketId, Integer ppid) {
        return smallProAdapterService.getSmallRelativeConfigList(SysConfigConstant.JIUJI_HALF_YEAR_SERVICE_PPID).contains(ppid)
                || smallProAdapterService.getSmallRelativeConfigList(SysConfigConstant.JIUJI_ONE_YEAR_SERVICE_PPID).contains(ppid)
                || smallProAdapterService.getSmallRelativeConfigList(SysConfigConstant.JIUJI_TWO_YEAR_SERVICE_PPID).contains(ppid)
                || Optional.ofNullable(smallproBillBasketId)
                .map(basketId -> {
                    Integer num = SpringUtil.getBean(TiemoCardService.class).lambdaQuery()
                            .and(cnd -> cnd.eq(TiemoCard::getBasketid, basketId))
                            .eq(TiemoCard::getType, FilmCardInfomationBO.CardTypeEnum.TIE_MO.getCode()).count();
                    if(num >0){
                        return num;
                    }
                    // 增加服务的校验
                    num = SpringUtil.getBean(ServiceRecordService.class).lambdaQuery()
                            .and(cnd -> cnd.eq(ServiceRecord::getBasketId, basketId)).count();
                    return num;
                })
                .map(c -> c > 0).orElse(false);
    }

    private void buildRefundHistoryList(List<ShouhouTuihuan> refundList, SmallproInformationRes smallproInformationRes) {
        if (CollectionUtils.isEmpty(refundList)) {
            return;
        }
        List<SmallProRefundHistoryList> refundHistoryList = refundList.stream().map(SmallproDetailsExServiceImpl::buildHistoryItem).collect(Collectors.toList());
        smallproInformationRes.setRefundHistoryList(refundHistoryList);
        Optional.of(refundList.stream().anyMatch(t -> t.getCheck3() == null))
                .filter(Boolean::booleanValue)
                .ifPresent(k -> smallproInformationRes.setIsReturn(NumberConstant.ONE));

    }

    private static SmallProRefundHistoryList buildHistoryItem(ShouhouTuihuan item) {
        SmallProRefundHistoryList history = new SmallProRefundHistoryList();
        history.setId(item.getId())
                .setBasketId(item.getBasketId())
                .setComment(item.getComment())
                .setCommitDate(item.getDtime())
                .setCommitUser(item.getInuser())
                .setRefundKind(item.getTuihuanKind())
                .setRefundWay(item.getTuiWay())
                .setRefundMoney(item.getTuikuanM())
                .setCheck1(Boolean.TRUE)
                .setCheck1User(Optional.ofNullable(item.getCheck1user()).filter(StringUtils::isEmpty).orElse(item.getInuser()))
                .setCheck1Time(Optional.ofNullable(item.getCheck1dtime()).orElse(item.getDtime()))
                .setCheck2(Optional.ofNullable(item.getCheck2()).orElse(Boolean.FALSE))
                .setCheck2User(item.getCheck2user())
                .setCheck2Time(item.getCheck2dtime())
                .setCheck3(Optional.ofNullable(item.getCheck3()).orElse(Boolean.FALSE))
                .setCheck3User(item.getCheck3user())
                .setCheck3Time(item.getCheck3dtime());
        return history;
    }
    // endregion
    //type 1:subId|2:ppid|3:barCode
    // region 获取可接件商品 getReceivableSmallpro

    /**
     * 校验商品信息
     * @param type
     * @param keyList
     * @param req
     */
    private void checkProductInfo(Integer type, List<String> keyList,ReceivableSmallProReq req){
        Integer subId = req.getSubId();
        //如果订单id不为空那就不进行校验
        if(ObjectUtil.isNotNull(subId)){
            return;
        }
        List<Integer> ppidList = new ArrayList<>();
        //订单的情况
        if(NumberConstant.ONE.equals(type)){
            //根据订单查询商品
            try {
                subId = Integer.valueOf(keyList.get(0));
            } catch (Exception e) {
                throw new CustomizeException("订单Id转换为数值型错误！");
            }
            List<Basket> list = basketService.lambdaQuery().eq(Basket::getSubId, subId).list();
            if(CollectionUtils.isNotEmpty(list)){
                list.forEach(item->{
                    ppidList.add(Optional.ofNullable(item.getPpriceid()).orElse(0L).intValue());
                });
            }
        } else if (NumberConstant.TWO.equals(type)){
            //ppid情况处理
            for (String key : keyList) {
                try {
                    ppidList.add(Integer.parseInt(key));
                } catch (Exception e) {
                    throw new CustomizeException("PPID转换为数值型错误！");
                }
            }
        } else if (NumberConstant.THREE.equals(type)){
            //条码情况处理
            keyList.forEach(key->{
                List<Productbarcode> productbarcodeList = productbarcodeService.lambdaQuery().like(Productbarcode::getBarCode, key).list();
                if(CollectionUtils.isNotEmpty(productbarcodeList)){
                    ppidList.addAll(productbarcodeList.stream().map(Productbarcode::getPpriceid)
                            .filter(Objects::nonNull).distinct()
                            .collect(Collectors.toList()));
                }
            });


        }
        //如果ppid不为空的情况
        if(CollectionUtils.isNotEmpty(ppidList)){
            for (Integer ppid : ppidList){
                if(categoryService.determineFilmByPpid(ppid)){
                    throw new CustomizeException("无关联订单，不能提交“保护膜”品类商品。");
                }
            }
        }
    }


    @Override
    public SmallproReceivableRes getReceivableSmallpro(Integer type, List<String> keyList, Integer areaId, String imei,
                                                       ReceivableSmallProReq receivableSmallProReq) {
        OaUserBO oaUserBO = currentRequestComponent.getCurrentStaffId();
        SmallproReceivableRes result = new SmallproReceivableRes();
        boolean mobileExchangeFlag = false;
        List<SmallproOrderInfoBO> orderResultList;
        List<SmallproServiceConfigBO> serviceConfigResultList;
        List<SmallproReceivableProductBO> smallproReceivableProductBOList = new ArrayList<>(0);
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        int size = 0;
        //非HQ地区，数据需要隔离,排除九机
        Boolean authPart = authConfigService.isAuthPart(oaUserBO);
        List<SmallproMobileInfoBO> smallproMobileInfoList = new ArrayList<>();
        if (authPart) {
            AreaBelongsDcHqD1AreaId backInfo = areainfoService.getAreaBelongsDcHqD1AreaId(oaUserBO.getAreaId());
            //非HQ地区，数据需要隔离,排除九机
            if (backInfo != null && oaUserBO.getXTenant() >= 1000 && !CollUtil.contains(backInfo.getHqAreaIds(), oaUserBO.getAreaId())) {
                authPart = Boolean.TRUE;
            }
        }
        // 获取转赠年包信息
        IYearPackageTransferService yearPackageTransferService = SpringUtil.getBean(IYearPackageTransferService.class);
        Optional<YearPackageTransferDetailDto> transferDetailOpt = Optional.ofNullable(yearPackageTransferService.getTransferDetail(imei));
        if(transferDetailOpt.isPresent()){
            // 校验转赠年包
            R checkTransferR = yearPackageTransferService.validateYearPackageTransfer(transferDetailOpt.get(), true);
            if(!checkTransferR.isSuccess()){
                throw new CustomizeException(checkTransferR.getUserMsg());
            }
            result.setPackageTransferFlag(Boolean.TRUE);
        }

        //无关联订单，不能提交“保护膜”品类商品。 校验
        if(XtenantEnum.isJiujiXtenant()){
            checkProductInfo(type, keyList, receivableSmallProReq);
        }
        //云雾会员PPID不允许接件
        R<String> valueR = sysConfigClient.getValueByCode(SysConfigConstant.SMOKE_FOG);
        List<Integer> smokeFogPpids = new ArrayList<>();
        if (valueR != null && valueR.getCode() == ResultCode.SUCCESS && StringUtils.isNotEmpty(valueR.getData())) {
            smokeFogPpids = Stream.of(valueR.getData().split(",")).map(Integer::valueOf).collect(Collectors.toList());
        }
        //是否历史订单
        AtomicReference<Boolean> isHistory = new AtomicReference<>(Boolean.FALSE);
        Integer subId = null;
        switch (type) {
            // region 订单号接件
            case 4:
            case 1:
                try {
                    subId = Integer.valueOf(keyList.get(0));
                } catch (Exception e) {
                    result.setCode(500);
                    result.setMessage("订单Id转换为数值型错误！");
                    return result;
                }
                Integer selectBasketId = receivableSmallProReq.getSelectBasketId();
                smallproReceivableProductBOList = Optional.ofNullable(
                        smallproMapper.getSmallproReceivableProductBySubId(subId, DateUtil.localDateTimeToString(smallProAdapterService.getFrameTime()), authPart, oaUserBO.getAuthorizeId(),selectBasketId, XtenantEnum.isJiujiXtenant()))
                        .orElseGet(ArrayList::new);
                // 没查到从历史库查 (备注：输出系统没有历史库)
                if (CollectionUtils.isEmpty(smallproReceivableProductBOList) && oaUserBO.getXTenant() < 1000) {
                    smallproReceivableProductBOList = Optional.ofNullable(
                            smallproMapper.getSmallproReceivableProductBySubIdHis(subId, DateUtil.localDateTimeToString(smallProAdapterService.getFrameTime()),selectBasketId, XtenantEnum.isJiujiXtenant()))
                            .orElseGet(ArrayList::new);
                    isHistory.set(Boolean.TRUE);
                }
                // 过滤赠送的年包数据
                smallproReceivableProductBOList= smallproReceivableProductBOList.stream()
                        .filter(item-> !(transferDetailOpt.isPresent() && ObjectUtil.notEqual(item.getBasketId(), transferDetailOpt.get().getBindBasketId())))
                        .collect(Collectors.toList());
                //type为4时，只返回对应ppid的数据
                if(NumberConstant.FOUR.equals(type) && CollectionUtils.isNotEmpty(smallproReceivableProductBOList)){
                    Integer ppid = receivableSmallProReq.getPpid();
                    if(ObjectUtil.isNotNull(ppid) && ppid>NumberConstant.ZERO){
                        smallproReceivableProductBOList=smallproReceivableProductBOList.stream().filter(item->ppid.equals(item.getPpriceId())).collect(Collectors.toList());
                    }
                }
                size = smallproReceivableProductBOList.size();
                Integer subIdFinal = subId;
                List<Basket> mobileList = CommenUtil.autoQueryHist(()-> basketService.lambdaQuery().eq(Basket::getSubId,subIdFinal)
                        .eq(Basket::getIsmobile, Boolean.TRUE).list(), MTableInfoEnum.SUB, subId);
                //获取处理次数
                Map<Integer, Integer> handleCountMap = new HashMap<>();
                try {
                    if(CollectionUtils.isNotEmpty(smallproReceivableProductBOList)){
                        handleCountMap=getHandleCount(smallproReceivableProductBOList, areaId);
                    }
                } catch (Exception e) {
                    RRExceptionHandler.logError("小件列表大件附件获取处理次数异常",Dict.create().set("type:keyList",type+":"+keyList),e,smsService::sendOaMsgTo9JiMan);
                }
                if (CollectionUtils.isNotEmpty(mobileList)) {
                    mobileExchangeFlag = true;
                    Map<Integer, Long> basketIdToPpriceidMap = mobileList.stream()
                            .collect(Collectors.toMap(Basket::getBasketId, Basket::getPpriceid, (v1, v2) -> v1));
                    Collection<Long> mobilePpid = basketIdToPpriceidMap.values();
                    Map<Integer, Productinfo> ppriceidToProductinfoMap = productinfoService.list(new LambdaQueryWrapper<Productinfo>()
                                    .in(Productinfo::getPpriceid, mobilePpid)).stream()
                            .collect(Collectors.toMap(Productinfo::getPpriceid, Function.identity(), (v1, v2) -> v1));

                    for (Basket basket : mobileList) {
                        SmallproMobileInfoBO sm = new SmallproMobileInfoBO();
                        sm.setBasketId(basket.getBasketId());
                        ProductMkc productMkc = CommenUtil.autoQueryHist(()-> productMkcService.list(new LambdaQueryWrapper<ProductMkc>()
                                                .eq(ProductMkc::getBasketId, basket.getBasketId())
//                                    .eq(ProductMkc::getPpriceid, basket.getPpriceid())
                                ), MTableInfoEnum.BASKET, basket.getBasketId())
                                .stream().findFirst().orElse(null);
                        if (Objects.isNull(productMkc)||StringUtils.isEmpty(productMkc.getImei())){
                            continue;
                        }
                        //设置处理次数
                        sm.setHandleCount(handleCountMap.getOrDefault(basket.getBasketId(), NumberConstant.ZERO));
                        sm.setImei(productMkc.getImei());
                        sm.setPpriceId(basket.getPpriceid());
                        Productinfo productinfo = ppriceidToProductinfoMap.get(Convert.toInt(basket.getPpriceid()));
                        sm.setProductName(productinfo.getProductName());
                        sm.setProductColor(productinfo.getProductColor());
                        sm.setProductCount(NumberConstant.ONE);
                        smallproMobileInfoList.add(sm);
                    }
                }
                break;
            // endregion
            // region ppid/条形码 接件
            case 2:
                for (String key : keyList) {
                    int temp = 0;
                    try {
                        temp = Integer.parseInt(key);
                    } catch (Exception e) {
                        result.setCode(500);
                        result.setMessage("PPID转换为数值型错误！");
                        return result;
                    }
                }
            case 3:
                smallproReceivableProductBOList = Optional.ofNullable(
                        smallproMapper.getSmallproReceivableProduct(type, keyList, areaId)
                ).orElseGet(ArrayList::new);
                size = smallproReceivableProductBOList.size();
                break;
            // endregion
            default:
                break;
        }

        if (CollectionUtils.isNotEmpty(smokeFogPpids) && CollectionUtils.isNotEmpty(smallproReceivableProductBOList)) {
            //过滤掉云雾会员 ppid
            List<Integer> filterPpids = smokeFogPpids;
            smallproReceivableProductBOList.removeIf(item -> filterPpids.contains(item.getPpriceId()));
            size = smallproReceivableProductBOList.size();
        }
        if(StrUtil.isNotBlank(apolloEntity.getSmallproNotRefundPpids())){
           // 过滤不可退商品
            // apolloEntity.getSmallproNotRefundPpids() 按, 切分并转为int
            List<Integer> filterPpids = StrUtil.splitTrim(apolloEntity.getSmallproNotRefundPpids(), StringPool.COMMA).stream()
                    .map(Convert::toInt).collect(Collectors.toList());
            smallproReceivableProductBOList.removeIf(item -> filterPpids.contains(item.getPpriceId())
                    // 授权可退
                    && !SpringUtil.getBean(StringRedisTemplate.class).hasKey(StrUtil.format(RedisKeys.SMALLPRO_REFUND_ALLOWED_BASKET_ID_KEY, item.getBasketId())));
            size = smallproReceivableProductBOList.size();
        }

        //查询是否返销
        List<Integer> basketIdList = Optional.of(smallproReceivableProductBOList.stream().map(SmallproReceivableProductBO::getBasketId).collect(Collectors.toList())).orElseGet(ArrayList::new);
        List<Integer> operatorBasketByStatus = smallproService.getOperatorBasketByStatus(basketIdList);

        //查询是否是优品小件
        List<Integer> basketTypeById = smallproService.getBasketTypeById(basketIdList);
        if (size == 0) {
            //注释补充  如果查不到可接件的订单信息，则校验是否存在未处理完成的小件接件信息
            if (type == 1 || type == 4) {
                QueryWrapper<Smallpro> smallproQueryWrapper = new QueryWrapper<>();
                smallproQueryWrapper.lambda().in(Smallpro::getSubId, keyList).in(Smallpro::getStats, 0, 3, 5, 6)
                        .and(bo -> bo.eq(Smallpro::getIsDel, Boolean.FALSE).or().isNull(Smallpro::getIsDel));
                List<Smallpro> smallproList = smallproService.listSqlServer(smallproQueryWrapper);
                smallproList = checkOldSmallpro(smallproList);
                if (smallproList != null && smallproList.size() > 0) {
                    result.setIsProcessing(true).setSmallproId(smallproList.get(0).getId()).setCode(0).setMessage(
                            "当前订单有进行中的接件单！");
                    return result;
                }
            }
            if (type.equals(2) || type.equals(3)) {
                result.setCode(500).setMessage("输入错误，请输入当前地区有现货库存的小件商品ppid或者条码！");
                return result;
            } else if (CollectionUtils.isEmpty(smallproMobileInfoList)){
                result.setCode(500).setMessage("当前筛选条件无可接件商品！");
                return result;
            }
        } else if (size > 0) {
            orderResultList = new ArrayList<>(size);
            AtomicBoolean flag = new AtomicBoolean(true);
            AtomicReference<LocalDateTime> tradeDate = new AtomicReference<>(null);
            Boolean dataFlag = true;
            //得到ppidList
            List<Integer> ppidList = smallproReceivableProductBOList.stream().map(SmallproReceivableProductBO::getPpriceId).collect(Collectors.toList());
            //通过ppidList获取政策
            Result<List<WarrantyMainInfoVO>> warrantyMainInfoListResult = webCloud.getWarrantyMainInfoList(ppidList,XtenantEnum.getXtenant());
            Map<Integer,WarrantyMainInfoVO> warrantyMainInfoVOMap = new HashMap<>();
            if (warrantyMainInfoListResult.isSuccess() && warrantyMainInfoListResult.getData()!=null){
                List<WarrantyMainInfoVO> warrantyMainInfoVOList = warrantyMainInfoListResult.getData();
                warrantyMainInfoVOMap = warrantyMainInfoVOList.stream().collect(Collectors.toMap(WarrantyMainInfoVO::getPpid, Function.identity(), (key1, key2) -> key2));
            }
            Map<Integer, WarrantyMainInfoVO> finalWarrantyMainInfoVOMap = warrantyMainInfoVOMap;
            //查询配置
            Map<String, AfterServiceTimeCfg> afterTimeCfgMap = getAfterTimeCfgMap(ppidList);
            //获取处理次数
            AtomicReference<Map<Integer, Integer>> handleCountMap = new AtomicReference<>(new HashMap());
            try {
                if(CollectionUtils.isNotEmpty(smallproReceivableProductBOList)){
                    Map<Integer, Integer> map = handleCountMap.get();
                    Map<Integer, Integer> handleCount = getHandleCount(smallproReceivableProductBOList, areaId);
                    map.putAll(handleCount);
                    handleCountMap.set(map);
                }
            } catch (Exception e) {
                RRExceptionHandler.logError("小件列表获取处理次数异常",Dict.create().set("type:keyList",type+":"+keyList),e,smsService::sendOaMsgTo9JiMan);
            }
            // 批量获取条形码
            Map<Integer, List<String>> barCodeListMap = SpringUtil.getBean(ProductbarcodeService.class).listBarCodeByPpids(ppidList);
            smallproReceivableProductBOList.forEach(bo -> {
                SmallproOrderInfoBO tempBO = new SmallproOrderInfoBO();
                int isTemperedFilm = 0;
                int isMobilePower = 0;
                // getTradeDate 对应库 tradeDate1 交易完成时间
                Integer overDays = getOverDays(CommonUtils.getEndOfDay(bo.getTradeDate()).toInstant(ZoneOffset.of("+8")).toEpochMilli());
//                log.error("排查" + smallProConstant.getJIUJI_TEMPERED_FILM_CID().toString() + bo.getBasketId() + " " + bo.getCid());
                List<Integer> smallRelativeConfigList = smallProAdapterService.getSmallRelativeConfigList(SysConfigConstant.JIUJI_TEMPERED_FILM_CID);
                if(XtenantEnum.isJiujiXtenant()){
                    List<Integer> caseCidList = smallProAdapterService.getSmallRelativeConfigList(SysConfigConstant.JIUJI_PROTECTIVE_SHELL_CID);
                    smallRelativeConfigList.addAll(caseCidList);
                }
                if (smallRelativeConfigList.contains(bo.getCid())) {
                    isTemperedFilm = 1;
                    //todo 为啥钢化膜45天内可以免费换新
                    Integer replaceDay = Optional.of(XtenantEnum.isJiujiXtenant())
                            .filter(Boolean.TRUE::equals)
                            .map(v -> afterTimeCfgMap.get(Convert.toStr(bo.getPpriceId())))
                            .filter(v -> Objects.nonNull(v.getReplaceDay()))
                            .map(AfterServiceTimeCfg::getReplaceDay).orElse(FOUR_TY_FIVE);
                    if (overDays != null && overDays <= replaceDay) {
                        tempBO.setIsFreeExchange(true);
                    }

                }
                if (smallProAdapterService.getSmallRelativeConfigList(SysConfigConstant.JIUJI_MOBILE_POWER_CID).contains(bo.getCid())) {
                    isMobilePower = 1;
                    if (overDays < 365) {
                        tempBO.setIsMobilePower(1);
                    }
                }
                tempBO.setBasketId(bo.getBasketId())
                        .setPpriceId(bo.getPpriceId())
                        .setProductId(bo.getProductId())
                        //获取处理次数
                        .setHandleCount(handleCountMap.get().getOrDefault(CommenUtil.currId(bo.getBasketId(), bo.getPpriceId()),NumberConstant.ZERO))
                        .setProductName(bo.getProductName())
                        .setProductColor(bo.getProductColor())
                        .setProductCount(transferDetailOpt.map(td -> 1).orElse(bo.getBasketCount()))
                        .setPrice(bo.getPrice())
                        .setMemberPrice(bo.getMemberPrice())
                        .setCid(bo.getCid())
                        .setIsTemperedFilm(isTemperedFilm)
                        .setIsMobilePower(isMobilePower)
                        .setBarCode(bo.getBarCode())
                        .setBarCodeList(barCodeListMap.get(bo.getPpriceId()))
                        .setIsSn(Optional.ofNullable(bo.getIsSn()).orElseGet(() -> false))
                        .setTargetPpriceId(bo.getTargetPpriceId())
                        // BasketId是否是运营商业务并且返销
                        .setIsReturnOperatorBasket(operatorBasketByStatus.contains(tempBO.getBasketId()))
                        .setIsYouPin(basketTypeById.contains(tempBO.getBasketId()));
                if (finalWarrantyMainInfoVOMap.get(bo.getPpriceId())!=null){
                    tempBO.setShPolicy(finalWarrantyMainInfoVOMap.get(bo.getPpriceId()).getDetail());
                }
                // 判断是否为保护壳
                if (smallProAdapterService.getSmallRelativeConfigList(SysConfigConstant.JIUJI_PROTECTIVE_SHELL_CID).contains(bo.getCid())) {
                    tempBO.setIsProtectiveCase(NumberConstant.ONE);
                }
                // 判断是否为膜
                List<Integer> tieMoCids = categoryService.tieMoCids();
                tempBO.setIsTieMo(tieMoCids.contains(bo.getCid()));
                List<Integer> addOneHandle = Stream.concat(tieMoCids.stream(), categoryService.cardCids().stream()).collect(Collectors.toList());
                if((XtenantEnum.isJiujiXtenant() && addOneHandle.contains(bo.getCid()))){
                    AfterServiceTimeCfg timeConfig = afterTimeCfgMap.getOrDefault(Convert.toStr(bo.getPpriceId()), new AfterServiceTimeCfg());
                    Integer replaceDay = Optional.ofNullable(timeConfig.getReplaceDay()).orElse(NumberConstant.ZERO) > NumberConstant.ZERO ? timeConfig.getReplaceDay() : FOUR_TY_FIVE;
                    //到期时间
                    LocalDateTime expirationDate = Optional.ofNullable(bo.getTradeDate()).orElse(LocalDateTime.MIN).minusDays(Convert.toLong(replaceDay));
                    //获取质保换新使用记录
                    List<FilmUseLog> filmHuanList = smallproMapper.getFilmHuanList(Collections.singletonList(bo.getBasketId()));
                    Optional<FilmUseLog> first = filmHuanList.stream().filter(item -> expirationDate.isBefore(item.getUseDateTime())).findFirst();
                    //获取年包贴膜使用记录
                    List<FilmCardInfomationBO> tieMoCardUse = smallproMapper.getTieMoCardUse(Collections.singletonList(bo.getBasketId()));
                    if(CollectionUtils.isEmpty(tieMoCardUse) && !first.isPresent()){
                            tempBO.setIsFreeExchange(true);
                    }
                }
                if (type == 1 || type==4) {
                    tempBO.setSubId(Integer.valueOf(keyList.get(0)));
                }
                if (isServiceProduct(bo.getTargetPpriceId())) {
                    tempBO.setIsServiceProduct(true);
                    SmallproInfoServiceRecordBO smallproInfoServiceRecordBO = Optional.ofNullable(
                            smallproMapper.getSubServiceRecordByServiceProduct(bo.getBasketId()))
                            .orElseGet(SmallproInfoServiceRecordBO::new);
                    if (Optional.ofNullable(smallproInfoServiceRecordBO.getCost()).orElseGet(() -> 0.0) > 0) {
                        return;
                    }
                }
                if (isYearCardProduct(bo.getTargetPpriceId())) {
                    tempBO.setIsYearCardProduct(true);
                }
                List<SmallproOrderInfoBO> tempBOList = new ArrayList<>(1);
                tempBOList.add(tempBO);
                SubServiceRecordBO serviceInfo;
                FilmCardInfomationBO filmCardInfo;
                if ((tempBO.getIsServiceProduct() != null && tempBO.getIsServiceProduct())) {
                    serviceInfo = null;
                    filmCardInfo = null;
                } else {
                    // 获取可接件信息时，需要获取每个商品可用的九机服务信息
                    SpringContextUtil.getRequest().ifPresent(req -> req.setAttribute(RequestAttrKeys.SMALLPRO_ORDER_INFO,tempBO));
                    SpringContextUtil.getRequest().ifPresent(req -> req.setAttribute(RequestAttrKeys.ANNUAL_PACKAGE_IMEI,imei));
                    filmCardInfo = smallproFilmCardService.getFilmCardInfoByBasketId(bo.getBasketId());
                    if(Boolean.TRUE.equals(isHistory.get())){
                        DynamicContextHolder.setDB(DataSourceConstants.OA_NEW_HIS);
                        try {
                            serviceInfo = getSubServiceRecord(tempBOList, DateTimeFormatter.ofPattern("yyyy-MM" +
                                    "-dd HH:mm:ss"), filmCardInfo);
                        } finally {
                            DynamicContextHolder.clearDB();
                        }
                    }else{
                        serviceInfo = getSubServiceRecord(tempBOList, DateTimeFormatter.ofPattern("yyyy-MM" +
                                "-dd HH:mm:ss"), filmCardInfo);
                    }

                    if (filmCardInfo != null && filmCardInfo.getIsFreeExchange() != null) {
                        tempBO.setIsFreeExchange(filmCardInfo.getIsFreeExchange());
                    }
                }
                tempBO.setServiceInfo(serviceInfo);
                tempBO.setFilmCardInfo(filmCardInfo);
                if (filmCardInfo != null && tempBO.getIsYearCardProduct() != null
                        && tempBO.getIsYearCardProduct() && !filmCardInfo.getIsCanRefund()) {
                    return;
                }
                if (serviceInfo != null && serviceInfo.getWarrantyType() != null && filmCardInfo != null && filmCardInfo.getIsNotExpired() != null && filmCardInfo.getIsUseCount() != null &&
                        serviceInfo.getWarrantyType() == 4 && (!filmCardInfo.getIsNotExpired() || !filmCardInfo.getIsUseCount())) {
                    serviceInfo.setIsServiceRecord(0);
                }
                orderResultList.add(tempBO);
                if (flag.get()) {
                    tradeDate.set(transferDetailOpt.map(YearPackageTransferDetailDto::getStartTime).orElse(bo.getTradeDate()));
                    result.setBuyDate(tradeDate.get().format(dateTimeFormatter))
                            .setMobile(transferDetailOpt.map(YearPackageTransferDetailDto::getReceiverMobile).orElse(bo.getMobile()))
                            .setUserId(transferDetailOpt.map(YearPackageTransferDetailDto::getReceiverId).orElse(bo.getUserId()))
                            .setOverBuyDate((int) Duration.between(tradeDate.get(), LocalDateTime.now()).toDays());
                    String realName = transferDetailOpt.map(YearPackageTransferDetailDto::getReceiverUserName)
                            .orElse(bo.getRealName());
                    if(StringUtils.isNotEmpty(realName)){
                        result.setUserName(realName);
                    } else {
                        String userName = bo.getUserName();
                        result.setUserName(StringUtils.isNotEmpty(userName)?userName:bo.getMobile());
                    }
                    LocalDateTime lastUserTime =
                            Optional.ofNullable(filmCardInfo).map(FilmCardInfomationBO::getLastUseTime).orElse(null);
                    if (null != lastUserTime) {
                        result.setLastAnnualPackageUseDate((int) Duration.between(lastUserTime, LocalDateTime.now()).toDays());
                    } else {
                        result.setLastAnnualPackageUseDate(999);
                    }
                    flag.set(false);
                }
            });
            if (CollectionUtils.isEmpty(orderResultList)) {
                result.setCode(500);
                result.setMessage("没有可退商品！");
                return result;
            }
            serviceConfigResultList = getSmallproServiceListInfo(orderResultList, false);
            //获取小件是否可退换修
            getSmallProAfterCfg(orderResultList, tradeDate);
            result.setOrderInfoList(orderResultList)
                    .setServiceConfigList(serviceConfigResultList)
                    .setIsReceivable(1)
                    .setIsProcessing(false)
                    .setSmallproId(0);
        }
        if (StrUtil.isNotBlank(imei)) {
            result.setIsAuthority(Boolean.TRUE);
        } else {
            result.setIsAuthority(Boolean.FALSE);
        }
        result.setSmallproMobileInfoList(smallproMobileInfoList);
        if (mobileExchangeFlag && Objects.nonNull(subId)) {
            Integer finalSubId = subId;
            Boolean finalAuthPart = authPart;
            List <SmallproReceivableProductBO> tempList = Optional.ofNullable(
                    CommenUtil.autoQueryHist(()-> smallproMapper.getMobileReceivableProductBySubIdV2(finalSubId,
                            DateUtil.localDateTimeToString(smallProAdapterService.getFrameTime()), finalAuthPart,
                            oaUserBO.getAuthorizeId(), XtenantEnum.isJiujiXtenant()), MTableInfoEnum.SUB, subId))
                    .orElseGet(ArrayList::new);
            for (SmallproReceivableProductBO bo : tempList) {
                result.setBuyDate(transferDetailOpt.map(YearPackageTransferDetailDto::getStartTime).orElse(bo.getTradeDate()).format(dateTimeFormatter))
                        .setMobile(transferDetailOpt.map(YearPackageTransferDetailDto::getReceiverMobile)
                                .orElse(ObjectUtil.defaultIfBlank(result.getMobile(), bo.getMobile())))
                        .setUserId(transferDetailOpt.map(YearPackageTransferDetailDto::getReceiverId).orElse(bo.getUserId()))
                        .setOverBuyDate((int) Duration.between(transferDetailOpt.map(YearPackageTransferDetailDto::getStartTime)
                                .orElse(bo.getTradeDate()), LocalDateTime.now()).toDays());
                String realName = transferDetailOpt.map(YearPackageTransferDetailDto::getReceiverUserName).orElse(bo.getRealName());
                if(StrUtil.isBlank(result.getUserName())) {
                    if (StringUtils.isNotEmpty(realName)) {
                        result.setUserName(realName);
                    } else {
                        String userName = bo.getUserName();
                        result.setUserName(StringUtils.isNotEmpty(userName) ? userName : bo.getMobile());
                    }
                }
            }
            if (Objects.nonNull(result.getUserId()) && 0 != result.getUserId()) {
                result.setIsReceivable(1);
            }
        }
        result.setMobileExchangeFlag(mobileExchangeFlag);
        result.setCode(0).setMessage("获取可接件商品信息成功！");
        return result;
    }


    /**
     * 获取处理次数
     *
     * @param smallproProductList
     * @param areaId
     * @return
     */
    private Map<Integer,Integer> getHandleCount(List<? extends ISmallproProduct> smallproProductList, Integer areaId){
        if(CollectionUtils.isEmpty(smallproProductList)){
            return Collections.emptyMap();
        }
        //放入原始的basketId
        Map<Integer,Integer> oldBasketIdMap = new HashMap<>();
        Map<Integer,Integer> result = CollUtil.newHashMap(smallproProductList.size());
        List<Integer> basketIdList = smallproProductList.stream().map(ISmallproProduct::getBasketId)
                .filter(basketId -> ObjectUtil.defaultIfNull(basketId, 0) >0).collect(Collectors.toList());
        List<Integer> ppids = smallproProductList.stream().map(ISmallproProduct::getPpriceId)
                .filter(ppid -> ObjectUtil.defaultIfNull(ppid, 0) >0).collect(Collectors.toList());
        for (Integer basketId : basketIdList) {
            oldBasketIdMap.put(basketId,basketId);
            result.put(basketId,0);
        }
        if(CollUtil.isNotEmpty(basketIdList)){
            selectAboutBasketId(basketIdList, oldBasketIdMap);
        }

        //oldBasketIdMap的key转化为list
        List<Integer> allBasketIds = oldBasketIdMap.keySet().stream().distinct().collect(Collectors.toList());
        //批量查询处理次数
        List<HandleCountReq> list = smallproMapper.getHandleCountList(allBasketIds, ppids, areaId);

        if(allBasketIds.isEmpty()){
            //ppid作为key
            return list.stream().collect(Collectors.toMap(HandleCountReq::getBasketId, HandleCountReq::getNum));
        }

        //批量结果循环, sum(目标累计商品 = oldBasketIdMap.get(处理次数的basketId) )
        for (HandleCountReq o : list) {
            Integer targetBasketId = oldBasketIdMap.get(o.getBasketId());
            result.put(targetBasketId, result.get(targetBasketId)+o.getNum());
        }
        return result;
    }

    /**
     * 获取相关查询的basketId
     * @param basketIdList
     * @param oldBasketIdMap
     * @return
     */
    @Override
    public List<Integer> selectAboutBasketId(List<Integer> basketIdList, Map<Integer, Integer> oldBasketIdMap) {
        List<Integer> list = new ArrayList<>(basketIdList);
        List<ShouHouBasketReq> hqtxhList = smallproMapper.getShouHouBasketIdList(basketIdList);
        for (ShouHouBasketReq hqtxh : hqtxhList) {
            // 获取原始的basketId
            list.add(hqtxh.getOldBasketId());
            oldBasketIdMap.put(hqtxh.getOldBasketId(), oldBasketIdMap.get(hqtxh.getNewBasketId()));
        }
        return list;
    }

    /**
     * 获取小件是否可退换修
     *
     * @param orderResultList 小件接件订单信息
     * @param tradeDate      交易时间
     */
    private void getSmallProAfterCfg(List<SmallproOrderInfoBO> orderResultList, AtomicReference<LocalDateTime> tradeDate) {
        List<Integer> ppids = orderResultList.stream().map(SmallproOrderInfoBO::getPpriceId).distinct().collect(Collectors.toList());
        Map<String, AfterServiceTimeCfg> shouhouTimeCfgMap = getAfterTimeCfgMap(ppids);
        LocalDateTime now = LocalDateTime.now();
        OaUserBO oaUserBO = Optional.ofNullable(currentRequestComponent.getCurrentStaffId()).orElse(new OaUserBO());
        List<String> rankList = Optional.ofNullable(oaUserBO.getRank()).orElse(new ArrayList<>());
        AtomicReference<LocalDateTime> endStartTimeRef = new AtomicReference<>(tradeDate.get());
        //阿波罗盲盒商品获取
        AtomicReference<List<Integer>> blindBoxPpidList = new AtomicReference<>();
        Optional.ofNullable(apolloEntity.getBlindBoxPpid()).ifPresent(item->blindBoxPpidList.set(Arrays.stream(item.split(","))
                .filter(StringUtils::isNotEmpty)
                .map(Integer::new).collect(Collectors.toList())));
        orderResultList.stream().forEach(bo -> {
            if (tradeDate.get() == null || ObjectUtil.defaultIfNull(bo.getProductCount(),0) <=0) {
                return;
            }
            AfterServiceTimeCfg tempTimeCfg = shouhouTimeCfgMap.get(Convert.toStr(bo.getPpriceId()));
            BaoXiuParam.OrderTypeEnum orderTypeEnum;
            if(Boolean.TRUE.equals(bo.getIsYouPin())){
                orderTypeEnum = BaoXiuParam.OrderTypeEnum.EXCELLENT_PRODUCT;
            }else{
                orderTypeEnum = BaoXiuParam.OrderTypeEnum.NEW_MACHINE;
            }

            BaoXiuParam baoXiuParam = BaoXiuParam.builder().orderTypeEnum(orderTypeEnum)
                    .isMobile(Boolean.FALSE).tradeCompleteTime(tradeDate.get()).subDateTime(null)
                    .outStockTime(null).xtenant(Convert.toLong(XtenantEnum.getXtenant()))
                    .build();
            SpringContextUtil.addRequestKeyMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG, "basketId:{}质保时间配置获取结果: {}", bo.getBasketId(), tempTimeCfg);
            SpringUtil.getBean(CommonStructMapper.class).setBaoXiuParam(tempTimeCfg, baoXiuParam);
            BaoXiuVo baoXiuVo = BaoXiuUtil.calculateBaoXiu(baoXiuParam);
            SpringContextUtil.addRequestKeyMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG, "basketId:{}质保期计算结果: {}", bo.getBasketId(), baoXiuVo);
            //包换时间
            bo.setIsChange(Convert.toInt(baoXiuVo.getIsExchange()));
            //包换时间,钢化膜,保护壳 年包未过期 可以进行换货
            boolean falg = NumberConstant.ONE.equals(bo.getIsTemperedFilm()) || NumberConstant.ONE.equals(bo.getIsProtectiveCase());
            if(falg && Optional.ofNullable(bo.getFilmCardInfo())
                    .map(FilmCardInfomationBO::getIsNotExpired).filter(Boolean::booleanValue).isPresent()){
                bo.setIsChange(NumberConstant.ONE);
            }
            //包退时间
            bo.setIsSupportReturn(Convert.toInt(baoXiuVo.getIsReturn()));
            //保修时间
            bo.setIsWarranty(Convert.toInt(baoXiuVo.getIsRepair()));
            //1.判断商品有没有在盲盒配置里面
            if(Optional.ofNullable(blindBoxPpidList.get()).orElse(new ArrayList<>()).contains(bo.getPpriceId())){
                //如果过是盲盒商品那就首先全部是不可选择状态
                bo.setIsNotSelect(Boolean.TRUE);
                bo.setShowMsg("盲盒属于特殊产品，非质量问题不支持退换货，请联系呼叫中心进行处理");
                String inquiryValue = stringRedisTemplate.opsForValue().get(StrUtil.format(RedisKeys.BLIND_BOX_BASKET_ID, bo.getBasketId()));
                //当权值符合的情况下才能开启选择
                if(rankList.contains(BLIND_BOX_RANK) || StringUtils.isNotEmpty(inquiryValue)){
                    bo.setIsNotSelect(Boolean.FALSE);
                }
            }
        });
    }

    @Override
    public Integer getAfterTimeCfgDays(Map<String, AfterServiceTimeCfg> afterTimeCfgMap, Integer ppid,
                                       Function<AfterServiceTimeCfg, Integer> cfgFun) {
        if(afterTimeCfgMap == null || cfgFun == null){
            return null;
        }
        AfterServiceTimeCfg afterServiceTimeCfg = afterTimeCfgMap.get(Convert.toStr(ppid));
        if(afterServiceTimeCfg == null){
            return null;
        }
        return cfgFun.apply(afterServiceTimeCfg);
    }

    @Override
    public Map<String, AfterServiceTimeCfg> getAfterTimeCfgMap(List<Integer> ppids) {
        return SpringContextUtil.reqCache(() -> {
            Map<String, AfterServiceTimeCfg> shouhouTimeCfgMap = new HashMap<>(ppids.size());
            Optional.ofNullable(ppids)
                    .map(ppriceids -> SpringUtil.getBean(WebCloud.class).listShouhouTimeConfig(ppriceids, Convert.toLong(XtenantEnum.getXtenant())))
                    .filter(r -> r.getCode() == ResultCode.SUCCESS).map(Result::getData)
                    .ifPresent(stcs -> stcs.forEach(stc -> StrUtil.splitTrim(stc.getPpid(), ",").forEach(ppid -> shouhouTimeCfgMap.put(ppid, stc))));
            return shouhouTimeCfgMap;
        }, RequestCacheKeys.GET_AFTER_TIME_CFG_MAP, ppids);
    }

    @Override
    public R<SmallproReceivableRes> getReceivableSmallProV2(Integer subId,String imei,Integer selectBasketId) {
        OaUserBO currentStaff = currentRequestComponent.getCurrentStaffId();
        SmallproReceivableRes result = new SmallproReceivableRes();
        List<SmallproOrderInfoBO> orderResultList;
        List<SmallproServiceConfigBO> serviceConfigResultList;
        List<SmallproReceivableProductBO> smallProReceivableProductList;
        int size = 0;
        //非HQ地区，输出系统数据需要隔离
        Boolean authPart = authConfigService.isAuthPart(currentStaff);
        if (authPart) {
            AreaBelongsDcHqD1AreaId backInfo = areainfoService.getAreaBelongsDcHqD1AreaId(currentStaff.getAreaId());
            //非HQ地区，数据需要隔离,排除九机
            if (backInfo != null && currentStaff.getXTenant() >= ConfigConsts.TENANT_THRESHOLD && !CollUtil.contains(backInfo.getHqAreaIds(), currentStaff.getAreaId())) {
                authPart = Boolean.TRUE;
            }
        }

        //云雾会员PPID不允许接件
        R<String> smokeR = sysConfigClient.getValueByCode(SysConfigConstant.SMOKE_FOG);
        List<Integer> smokeFogPpidList = new ArrayList<>();
        if (smokeR != null && smokeR.getCode() == ResultCode.SUCCESS && StringUtils.isNotEmpty(smokeR.getData())) {
            smokeFogPpidList = Stream.of(smokeR.getData().split(",")).map(Integer::valueOf).collect(Collectors.toList());
        }

        smallProReceivableProductList = Optional.ofNullable(
                smallproMapper.getSmallproReceivableProductBySubId(subId, DateUtil.localDateTimeToString(smallProAdapterService.getFrameTime()), authPart, currentStaff.getAuthorizeId(),selectBasketId, XtenantEnum.isJiujiXtenant()))
                .orElseGet(ArrayList::new);
        // 没查到从历史库查 (备注：输出系统没有历史库)
        if (CollectionUtils.isEmpty(smallProReceivableProductList) && currentStaff.getXTenant() < ConfigConsts.TENANT_THRESHOLD) {
            smallProReceivableProductList = Optional.ofNullable(
                    smallproMapper.getSmallproReceivableProductBySubIdHis(subId, DateUtil.localDateTimeToString(smallProAdapterService.getFrameTime()),selectBasketId, XtenantEnum.isJiujiXtenant()))
                    .orElseGet(ArrayList::new);
        }
        size = smallProReceivableProductList.size();

        if (CollectionUtils.isNotEmpty(smokeFogPpidList) && CollectionUtils.isNotEmpty(smallProReceivableProductList)) {
            //过滤掉云雾会员 ppid
            List<Integer> filterPpidList = smokeFogPpidList;
            smallProReceivableProductList.removeIf(item -> filterPpidList.contains(item.getPpriceId()));
            size = smallProReceivableProductList.size();
        }
        //查询是否返销
        List<Integer> basketIdList = Optional.of(smallProReceivableProductList.stream().map(SmallproReceivableProductBO::getBasketId).collect(Collectors.toList())).orElseGet(ArrayList::new);
        List<Integer> operatorBasketByStatus = smallproService.getOperatorBasketByStatus(basketIdList);
        //查询是否是优品小件
        List<Integer> basketTypeById = smallproService.getBasketTypeById(basketIdList);
        if (size == 0) {
            //注释补充  如果查不到可接件的订单信息，则校验是否存在未处理完成的小件接件信息
            QueryWrapper<Smallpro> smallProQueryWrapper = new QueryWrapper<>();
            smallProQueryWrapper.lambda().eq(Smallpro::getSubId, subId).in(Smallpro::getStats, 0, 3, 5, 6)
                    .and(bo -> bo.eq(Smallpro::getIsDel, Boolean.FALSE).or().isNull(Smallpro::getIsDel));
            List<Smallpro> smallProList = smallproService.listSqlServer(smallProQueryWrapper);
            smallProList = checkOldSmallpro(smallProList);
            if (smallProList != null && smallProList.size() > 0) {
                result.setIsProcessing(true).setSmallproId(smallProList.get(0).getId()).setCode(0).setMessage(
                        "当前订单有进行中的接件单！");
                return R.success("当前订单有进行中的接件单！", result);
            }
            return R.error("暂无可接件商品信息");
        } else if (size > 0) {
            orderResultList = new ArrayList<>(size);
            AtomicBoolean flag = new AtomicBoolean(true);
            AtomicReference<LocalDateTime> tradeDate = new AtomicReference<>(null);
            List<Integer> ppidList = smallProReceivableProductList.stream().map(SmallproReceivableProductBO::getPpriceId).collect(Collectors.toList());
            Map<String, AfterServiceTimeCfg> afterTimeCfgMap = getAfterTimeCfgMap(ppidList);
            smallProReceivableProductList.forEach(bo -> {
                SmallproOrderInfoBO orderInfo = new SmallproOrderInfoBO();
                int isTemperedFilm = 0;
                int isMobilePower = 0;
                Integer overDays = getOverDays(CommonUtils.getEndOfDay(bo.getTradeDate()).toInstant(ZoneOffset.of("+8")).toEpochMilli());
                if (smallProAdapterService.getSmallRelativeConfigList(SysConfigConstant.JIUJI_TEMPERED_FILM_CID).contains(bo.getCid())) {
                    isTemperedFilm = 1;
                    //指定钢化膜45天内可以免费换新
                    Integer replaceDay = Optional.of(XtenantEnum.isJiujiXtenant())
                            .filter(Boolean.TRUE::equals)
                            .map(v -> afterTimeCfgMap.get(Convert.toStr(bo.getPpriceId())))
                            .filter(v -> Objects.nonNull(v.getReplaceDay()))
                            .map(AfterServiceTimeCfg::getReplaceDay).orElse(FOUR_TY_FIVE);
                    if (overDays != null && overDays <= replaceDay) {
                        orderInfo.setIsFreeExchange(true);
                    }
                    //质保换新超期可以存在+1处理
                    if(XtenantEnum.isJiujiXtenant() && overDays != null && overDays > replaceDay ){
                        //获取质保换新使用记录
                        List<FilmUseLog> filmHuanList = smallproMapper.getFilmHuanList(Collections.singletonList(bo.getBasketId()));
                        List<LocalDateTime> collect = filmHuanList.stream().filter(log -> ObjectUtil.isNotNull(log.getUseDateTime())).map(FilmUseLog::getUseDateTime).collect(Collectors.toList());
                        //获取年包贴膜使用记录
                        List<FilmCardInfomationBO> tieMoCardUse = smallproMapper.getTieMoCardUse(Collections.singletonList(bo.getBasketId()));
                    }
                }
                if (smallProAdapterService.getSmallRelativeConfigList(SysConfigConstant.JIUJI_MOBILE_POWER_CID).contains(bo.getCid())) {
                    isMobilePower = 1;
                    if (overDays < DAYS_OF_ONE_YEAR) {
                        orderInfo.setIsMobilePower(1);
                    }
                }
                orderInfo.setBasketId(bo.getBasketId())
                        .setPpriceId(bo.getPpriceId())
                        .setProductId(bo.getProductId())
                        .setProductName(bo.getProductName())
                        .setProductColor(bo.getProductColor())
                        .setProductCount(bo.getBasketCount())
                        .setPrice(bo.getPrice())
                        .setMemberPrice(bo.getMemberPrice())
                        .setCid(bo.getCid())
                        .setIsTemperedFilm(isTemperedFilm)
                        .setIsMobilePower(isMobilePower)
                        .setBarCode(bo.getBarCode())
                        .setIsSn(Optional.ofNullable(bo.getIsSn()).orElse(Boolean.FALSE))
                        .setSubId(subId)
                        .setTargetPpriceId(bo.getTargetPpriceId())
                        // BasketId是否是运营商业务并且返销
                        .setIsReturnOperatorBasket(operatorBasketByStatus.contains(orderInfo.getBasketId()))
                        .setIsYouPin(basketTypeById.contains(orderInfo.getBasketId()));

                if (smallProAdapterService.getSmallRelativeConfigList(SysConfigConstant.JIUJI_SERVICE_PPID).contains(bo.getTargetPpriceId())) {
                    orderInfo.setIsServiceProduct(true);
                    SmallproInfoServiceRecordBO smallproInfoServiceRecordBO = Optional.ofNullable(
                            smallproMapper.getSubServiceRecordByServiceProduct(bo.getBasketId()))
                            .orElseGet(SmallproInfoServiceRecordBO::new);
                    if (Optional.ofNullable(smallproInfoServiceRecordBO.getCost()).orElse(0.0) > 0) {
                        return;
                    }
                }
                if (isYearCardProduct(bo.getTargetPpriceId())) {
                    orderInfo.setIsYearCardProduct(true);
                }
                List<SmallproOrderInfoBO> tempBOList = new ArrayList<>(1);
                tempBOList.add(orderInfo);
                SubServiceRecordBO serviceInfo;
                FilmCardInfomationBO filmCardInfo;
                if (orderInfo.getIsServiceProduct() != null && orderInfo.getIsServiceProduct()) {
                    serviceInfo = new SubServiceRecordBO();
                    filmCardInfo = new FilmCardInfomationBO();
                } else {
                    // 获取可接件信息时，需要获取每个商品可用的九机服务信息
                    filmCardInfo = smallproFilmCardService.getFilmCardInfoByBasketId(bo.getBasketId());
                    serviceInfo = getSubServiceRecord(tempBOList, DateUtil.df, filmCardInfo);
                    if (filmCardInfo != null && filmCardInfo.getIsFreeExchange() != null) {
                        orderInfo.setIsFreeExchange(filmCardInfo.getIsFreeExchange());
                    }
                }
                orderInfo.setServiceInfo(serviceInfo);
                orderInfo.setFilmCardInfo(filmCardInfo);
                if (filmCardInfo != null && orderInfo.getIsYearCardProduct() != null
                        && orderInfo.getIsYearCardProduct() && Boolean.FALSE.equals(filmCardInfo.getIsCanRefund())) {
                    return;
                }
                if (serviceInfo != null && serviceInfo.getWarrantyType() != null && filmCardInfo != null && filmCardInfo.getIsNotExpired() != null && filmCardInfo.getIsUseCount() != null &&
                        serviceInfo.getWarrantyType() == 4 && (!filmCardInfo.getIsNotExpired() || !filmCardInfo.getIsUseCount())) {
                    serviceInfo.setIsServiceRecord(0);
                }
                orderResultList.add(orderInfo);
                if (flag.get()) {
                    tradeDate.set(bo.getTradeDate());
                    result.setBuyDate(bo.getTradeDate().format(DateUtil.df))
                            .setMobile(bo.getMobile())
                            .setUserId(bo.getUserId())
                            .setUserName(bo.getRealName())
                            .setOverBuyDate((int) Duration.between(bo.getTradeDate(), LocalDateTime.now()).toDays());
                    LocalDateTime lastUserTime =
                            Optional.ofNullable(filmCardInfo).map(FilmCardInfomationBO::getLastUseTime).orElse(null);
                    if (null != lastUserTime) {
                        result.setLastAnnualPackageUseDate((int) Duration.between(lastUserTime, LocalDateTime.now()).toDays());
                    } else {
                        result.setLastAnnualPackageUseDate(999);
                    }
                    flag.set(false);
                }
            });
            if (CollectionUtils.isEmpty(orderResultList)) {
                return R.error("没有可退商品！");
            }
            serviceConfigResultList = getSmallproServiceListInfo(orderResultList, false);
            //获取小件是否可退换修
            getSmallProAfterCfg(orderResultList, tradeDate);
            result.setOrderInfoList(orderResultList)
                    .setServiceConfigList(serviceConfigResultList)
                    .setIsReceivable(1)
                    .setIsProcessing(false)
                    .setSmallproId(0);
        }
        if (StrUtil.isNotBlank(imei)) {
            result.setIsAuthority(Boolean.TRUE);
        } else {
            result.setIsAuthority(Boolean.FALSE);
        }
        result.setCode(0).setMessage("获取可接件商品信息成功！");
        return R.success("获取可接件商品信息成功！", result);
    }

    @Override
    public Boolean isRecentMonthSub(Long subId){
        if(ObjectUtil.isNull(subId)){
            return Boolean.FALSE;
        }
        Sub sub = CommenUtil.autoQueryHist(() -> subService.getById(subId), MTableInfoEnum.SUB, subId);
        if(ObjectUtil.isNull(sub)){
            return Boolean.FALSE;
        }
        LocalDateTime tradeDate1 = sub.getTradeDate1();
        if(ObjectUtil.isNull(tradeDate1)){
            return Boolean.FALSE;
        }
        // 订单完成当前月开始时间
        LocalDateTime startOfMonth = tradeDate1.with(TemporalAdjusters.firstDayOfMonth()).toLocalDate().atStartOfDay();
        // 订单完成当前月结束时间
        LocalDateTime endOfMonth = tradeDate1.with(TemporalAdjusters.lastDayOfMonth()).toLocalDate().atTime(23, 59, 59, 999999999);
        LocalDateTime currentTime = LocalDateTime.now();
        return currentTime.isAfter(startOfMonth) && currentTime.isBefore(endOfMonth);
    }
    // endregion

    // region 更新小件接件信息 updateSmallproInfo

    @Override
    public SmallproInfoUpdateRes updateSmallproInfo(Integer smallproId, OaUserBO oaUserBO,
                                                    SmallproInfoUpdateReq smallproInfoUpdateReq) {
        log.debug("小件更新" + smallproId + ":" + JSONObject.toJSONString(smallproInfoUpdateReq));
        SmallproInfoUpdateRes result = null;
        Smallpro smallpro = smallproService.getByIdSqlServer(smallproId);
        QueryWrapper<SmallproBill> smallproBillQueryWrapper = new QueryWrapper<>();
        smallproBillQueryWrapper.lambda().eq(SmallproBill::getSmallproID, smallproId);
        if (smallpro == null) {
            return result;
        }
        if(Boolean.TRUE.equals(smallpro.getIsDel())
                || SmallProStatsEnum.SMALL_PRO_STATS_DELETED.getCode().equals(smallpro.getStats())){
            //已删除,不允许更新
            return result;
        }
        Smallpro updateSmallpro = new Smallpro();
        boolean repairFlag = false;
        if (smallpro.getKind().equals(SmallProKindEnum.SMALL_PRO_KIND_SERVICE.getCode())) {
            repairFlag = true;
        }
        try {
            if (XtenantEnum.isJiujiXtenant() && smallpro.getKind().equals(SmallProKindEnum.SMALL_PRO_KIND_RETURN.getCode())) {
                List<SmallproBill> smallproBillList = smallproBillService.getListWithSqlServer(smallproBillQueryWrapper);
                //接件商品是否存在 运营商抵扣
                List<OperatorBasket> operatorBasketList = operatorBasketService.selectOperatorByBasketId(smallproBillList.stream().map(SmallproBill::getBasketId).collect(Collectors.toList()))
                .stream().filter(obj -> Boolean.TRUE.equals(obj.getIsChecked())).collect(Collectors.toList());
                ///维修费用金额修改，若运营商业超过当月（使用退款订单的完成时间是否在当前自然月内进行判断），金额限制不能修改为0。（异常提示：超过当月的运营商业务维修费用不能修改为0）
                BigDecimal updatePrice = Optional.ofNullable(smallproInfoUpdateReq.getMaintainPrice()).orElse(BigDecimal.ZERO);
                if (updatePrice.compareTo(BigDecimal.ZERO) == 0 && CollUtil.isNotEmpty(operatorBasketList)) {
                    OperatorBasket operatorBasket = Optional.ofNullable(operatorBasketList.get(NumberConstant.ZERO)).orElse(new OperatorBasket());
                    //只有当抵扣金额大于0的时候才进行该校验
                    if(Optional.ofNullable(operatorBasket.getOffsetMoney()).orElse(BigDecimal.ZERO).compareTo(BigDecimal.ZERO)>0){
                        Integer basketId = operatorBasket.getBasketId();
                        Basket basket = Optional.ofNullable(CommenUtil.autoQueryHist(() -> basketService.getById(basketId), MTableInfoEnum.BASKET, basketId)).orElse(new Basket());
                        Long subId = basket.getSubId();
                        if (!isRecentMonthSub(subId)) {
                            throw new CustomizeException("超过当月的运营商业务维修费用不能修改为0");
                        }
                    }

                }
                repairFlag = true;
            }
        }catch (CustomizeException cu){
            throw cu;
        }catch (Exception e){
            RRExceptionHandler.logError("小件单运营商抵扣修改维修费异常：", "小件更新" + smallproId + ":" + JSONObject.toJSONString(smallproInfoUpdateReq), e, smsService::sendOaMsgTo9JiMan);
        }
        updateSmallpro.setId(smallproId)
                .setIsBaoxiu((smallproInfoUpdateReq.getWarrantyStatus() == null) ? smallproInfoUpdateReq.getIsBaoxiu() :
                        Boolean.valueOf(smallproInfoUpdateReq.getWarrantyStatus() == 1))
                .setConfig(smallproInfoUpdateReq.getConfig())
                .setProblem(smallproInfoUpdateReq.getProblem())
                .setServiceType((smallproInfoUpdateReq.getServiceType() == null) ? null :
                        smallproInfoUpdateReq.getServiceType().intValue())
                .setKind(smallproInfoUpdateReq.getKind())
                //.setStats(smallproInfoUpdateReq.getStats())
                .setGroupId(smallproInfoUpdateReq.getGroup())
                .setComment(smallproInfoUpdateReq.getComment())
                .setCostPrice(smallproInfoUpdateReq.getCostPrice())
                .setFeiyong(smallproInfoUpdateReq.getMaintainPrice())
                .setWxUser(smallproInfoUpdateReq.getMaintainUser())
                .setDataRelease(smallproInfoUpdateReq.getDataRelease())
                .setOutward(smallproInfoUpdateReq.getOutward())
                .setUserName(smallproInfoUpdateReq.getUserName())
                .setMobile(smallproInfoUpdateReq.getMobile());
        //.setChangePpriceid(smallproInfoUpdateReq.getChangePpriceId());
        if (repairFlag) {
            updateSmallpro.setCostPrice(smallproInfoUpdateReq.getCostPrice())
                    .setFeiyong(smallproInfoUpdateReq.getMaintainPrice());
        }
        boolean exchangeFlag = false;
        //换货
        if (smallpro.getKind().equals(SmallProKindEnum.SMALL_PRO_KIND_EXCHANGE.getCode())) {
            updateSmallpro.setFeiyong(smallproInfoUpdateReq.getDifference());
            if (RankEnum.hasAuthority(oaUserBO.getRank(), RankEnum.SMALL_PRO_CALL_CENTER) && smallproInfoUpdateReq.getIsUpdateByPrice() != null && smallproInfoUpdateReq.getIsUpdateByPrice()) {
                updateSmallpro.setFeiyong(smallproInfoUpdateReq.getMaintainPrice());
                exchangeFlag = true;
            }
            if (!RankEnum.hasAuthority(oaUserBO.getRank(), RankEnum.SMALL_PRO_CALL_CENTER) && smallproInfoUpdateReq.getIsUpdateByPrice() != null && smallproInfoUpdateReq.getIsUpdateByPrice()) {
                return result;
            }
        }


        boolean flag = ((SmallproDetailsExServiceImpl) AopContext.currentProxy()).updateSmallproInfoWrite(result,
                updateSmallpro, smallproInfoUpdateReq, smallproId, oaUserBO);
        if (!flag) {
            return result;
        } else {
            result = new SmallproInfoUpdateRes(smallproInfoUpdateReq, smallproId);
        }
        // 修改维修费需要添加日志
        BigDecimal costPrice = smallpro.getCostPrice() == null ? BigDecimal.ZERO : smallpro.getCostPrice();
        BigDecimal costPriceUpt = smallproInfoUpdateReq.getCostPrice() == null ? BigDecimal.ZERO :
                smallproInfoUpdateReq.getCostPrice();

        BigDecimal feiYong = smallpro.getFeiyong() == null ? BigDecimal.ZERO : smallpro.getFeiyong();
        BigDecimal feiYongUpt = smallproInfoUpdateReq.getMaintainPrice() == null ? BigDecimal.ZERO :
                smallproInfoUpdateReq.getMaintainPrice();

        if (repairFlag && (costPrice.compareTo(costPriceUpt) != 0 || feiYong.compareTo(feiYongUpt) != 0) || exchangeFlag) {
            StringBuilder commonBuilder = new StringBuilder(StrUtil.format("【维修费修改】维修费由{}改为：",feiYong.setScale(NumberConstant.ZERO, RoundingMode.HALF_UP)));
            commonBuilder.append(smallproInfoUpdateReq.getMaintainPrice() == null ? 0 :
                    smallproInfoUpdateReq.getMaintainPrice().setScale(NumberConstant.ZERO, RoundingMode.HALF_UP));
            commonBuilder.append(StrUtil.format("，成本由{}改为：",costPrice.setScale(NumberConstant.ZERO, RoundingMode.HALF_UP)));
            commonBuilder.append(smallproInfoUpdateReq.getCostPrice() == null ? 0 :
                    smallproInfoUpdateReq.getCostPrice().setScale(NumberConstant.ZERO, RoundingMode.HALF_UP));
            smallproLogService.addLogs(smallproId, commonBuilder.toString(), oaUserBO.getUserName(), 0);
        }

        if (smallpro.getKind().equals(SmallProKindEnum.SMALL_PRO_KIND_EXCHANGE.getCode()) && smallproInfoUpdateReq.getDifference() != null
                && smallproInfoUpdateReq.getDifference().compareTo(BigDecimal.ZERO) > 0) {
            smallproLogService.addLogs(smallproId, "商品置换补差额：" + smallproInfoUpdateReq.getDifference() + "元", oaUserBO.getUserName(), 0);
        }

        return result;
    }

    // endregion

    // region 校验置换商品 checkChangeProduct

    @Override
    public SmallproProductInfoBO checkChangeProduct(Integer ppriceId, BigDecimal price, Integer serviceType) {
        SmallproProductInfoBO result = smallproMapper.getProductInfoForCheck(ppriceId);
        if (result == null) {
            result = new SmallproProductInfoBO();
            result.setIsCanChange(false);
            result.setMessage("无效ppid：" + ppriceId);
            return result;
        }
        List<String> cidFamilyList = StrUtil.splitTrim(result.getCidFamily(), ",");
        //手机的分类id从配置中获取
        if (serviceType != null && serviceType == 4 && CollUtil.intersection(cidFamilyList,
                Optional.ofNullable(sysConfigClient.getValueByCode(SysConfigConstant.JIUJI_TEMPERED_FILM_CID)).filter(R::isSuccess)
                .map(R::getData).filter(StrUtil::isNotBlank).map(cidsStr ->StrUtil.splitTrim(cidsStr,","))
                .orElseGet(()->Arrays.asList("219","465","386","220","662","297"))).isEmpty() &&
                //保护壳的cid
                !result.getCidFamily().contains("217") &&
                !result.getCidFamily().contains("218") &&
                !result.getCidFamily().contains("295") &&
                !result.getCidFamily().contains("464")
        ) {
            result.setIsCanChange(false);
            result.setMessage("年包只支持跟换“手机贴膜/手机保护壳”下商品");
            return result;
        }
        if (serviceType != null && serviceType != 4 && result.getMemberPrice().compareTo(price) > 0) {
            result.setIsCanChange(false);
            result.setMessage("所换配件金额不能高于：" + price.setScale(2, RoundingMode.HALF_UP).doubleValue()
                    + "，ppid：" + ppriceId + "，金额：" + result.getMemberPrice().setScale(2, RoundingMode.HALF_UP).doubleValue());
            return result;
        }
        price = result.getMemberPrice().subtract(price);
        if (price.compareTo(BigDecimal.valueOf(0)) < 0) {
            price = BigDecimal.valueOf(0.0);
        }
        SmallproProductInfoBO productInfoBO = getProductNameByPpriceId(ppriceId);
        if (productInfoBO != null) {
            result.setProductName(productInfoBO.getProductName()).setProductColor(productInfoBO.getProductColor())
                    .setDisplay(productInfoBO.getDisplay()).setBarCode(productInfoBO.getBarCode());
        }
        result.setIsCanChange(true).setMessage("可置换商品").setPayPrice(price);
        return result;
    }


    // endregion

    // region 获取最后一次置换的商品PPID getLastChangePpid

    @Override
    public SmallproLastChangePpidRes getLastChangePpid(Integer subId, Integer basketId, Integer ppriceId,
                                                       Integer changePpriceId) {
        SmallproLastChangePpidRes result = new SmallproLastChangePpidRes();
        result.setCode(0);
        FilmCardLastChangePPidBO lastChangePPidBO = smallproMapper.getLastChangePPID(basketId, subId);
        result.setChangePpidInfo(lastChangePPidBO);
        if (lastChangePPidBO == null) {
            if (changePpriceId != null && changePpriceId > 0) {
                result.setIsGetLastPpid(false).setPpriceId(changePpriceId);
                SmallproProductInfoBO productInfoBO = getProductNameByPpriceId(changePpriceId);
                result.setChangeProductInfo(productInfoBO);
                return result;
            }
            result.setCode(500);
            result.setMessage("该订单号无数据!");
            return result;
        } else {
            if (!smallProAdapterService.getSmallRelativeConfigList(SysConfigConstant.JIUJI_TEMPERED_FILM_CID).contains(lastChangePPidBO.getCId())) {
                result.setCode(500);
                result.setMessage("只有钢化膜类商品才会选择上一次默认的置换商品!");
                return result;
            }
            long buyTimeStamp = lastChangePPidBO.getBuyTime().toEpochSecond(ZoneOffset.of("+8"));
            LocalDateTime frameTime = smallProAdapterService.getFrameTime();
            boolean newFlag = false;
            if (buyTimeStamp > frameTime.toEpochSecond(ZoneOffset.of("+8"))) {
                newFlag = true;
            }
            int replaceDay = Optional.of(XtenantEnum.isJiujiXtenant())
                    .filter(Boolean.TRUE::equals)
                    .map(v -> SpringUtil.getBean(SmallproDetailsExService.class).getAfterTimeCfgMap(Collections.singletonList(ppriceId)))
                    .map(afterTimeCfgMap -> afterTimeCfgMap.get(Convert.toStr(ppriceId)))
                    .map(AfterServiceTimeCfg::getReplaceDay).orElse(30);
            long nowTimeStamp = LocalDateTime.now().toEpochSecond(ZoneOffset.of("+8"));
            long pointTimeStamp = SmallproUtil.getEndTime(lastChangePPidBO.getBuyTime(), replaceDay).toEpochSecond(ZoneOffset.of("+8"));
            if (newFlag) {
                pointTimeStamp =
                        lastChangePPidBO.getDTime() != null ?
                                SmallproUtil.getEndTime(lastChangePPidBO.getDTime(), replaceDay).toEpochSecond(ZoneOffset.of("+8"))
                                : SmallproUtil.getEndTime(lastChangePPidBO.getBuyTime(), replaceDay).toEpochSecond(ZoneOffset.of("+8"));
            }
            if (nowTimeStamp <= pointTimeStamp) {
                result.setIsFreeExchange(true);
            } else {
                result.setIsFreeExchange(false);
            }

            if (lastChangePPidBO.getChangePpriceId() != null && result.getIsFreeExchange() && newFlag) {
                result.setPpriceId(lastChangePPidBO.getChangePpriceId()).setIsGetLastPpid(true);
            } else {
                result.setPpriceId((changePpriceId == null || changePpriceId <= 0) ? ppriceId : changePpriceId).setIsGetLastPpid(false);
            }

            if (result.getPpriceId() != null) {
                SmallproProductInfoBO productInfoBO = getProductNameByPpriceId(result.getPpriceId());
                result.setChangeProductInfo(productInfoBO);
            }
        }
        return result;
    }

    // endregion

    // region 删除小件单附件 deleteSmallproFile

    @Override
    public SmallproNormalCodeMessageRes deleteSmallproFile(Integer smallproId, Integer fileId) {
        SmallproNormalCodeMessageRes result = new SmallproNormalCodeMessageRes();
        List<SmallproInfoFileBO> smallproFileInfoList = smallproMapper.getSmallproFileInfo(smallproId);
        if (smallproFileInfoList == null || smallproFileInfoList.size() <= 0) {
            result.setCode(500);
            result.setMessage("该小件接件单无附件！");
            return result;
        }
        for (SmallproInfoFileBO temp : smallproFileInfoList) {
            if (temp.getId().equals(fileId)) {
                boolean flag =
                        ((SmallproDetailsExServiceImpl) AopContext.currentProxy()).deleteSmallproFileWrite(smallproId,
                                fileId);
                if (!flag) {
                    result.setCode(500);
                    result.setMessage("删除附件数据库操作失败！");
                    return result;
                }
                result.setCode(0);
                result.setMessage("删除附件操作成功!");
            }
        }
        result.setCode(500);
        result.setMessage("此小件单无此附件！");
        return result;
    }

    // endregion

    // endregion

    // region transactional

    @Transactional(rollbackFor = Exception.class)
    public boolean updateSmallproInfoWrite(SmallproInfoUpdateRes result,
                                           Smallpro updateSmallpro,
                                           SmallproInfoUpdateReq smallproInfoUpdateReq,
                                           Integer smallproId,
                                           OaUserBO oaUserBO) {
        boolean flag = smallproService.updateById(updateSmallpro);
        if (!flag) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return false;
        }
        if (smallproInfoUpdateReq.getOutwardFlag() != null && smallproInfoUpdateReq.getOutwardFlag() == 1) {
            UpdateWrapper<Smallpro> wrapper = new UpdateWrapper<>();
            wrapper.lambda().set(Smallpro::getOutward, null).eq(Smallpro::getId, smallproId);
            flag = smallproService.update(wrapper);
            if (!flag) {
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return false;
            }
        }
        if (smallproInfoUpdateReq.getSituationKind() != null
                && !smallproInfoUpdateReq.getSituationKind().equals(0)) {
            flag = updateSmallproKindType(smallproId, smallproInfoUpdateReq.getSituationKind());
            if (!flag) {
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return false;
            }
        }
        smallproService.saveSmallproAttachments(smallproInfoUpdateReq.getFilesList(), oaUserBO, smallproId);
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    public boolean deleteSmallproFileWrite(Integer smallproId, Integer fileId) {
        QueryWrapper<Attachments> deleteWrapper = new QueryWrapper<>();
        deleteWrapper.lambda().eq(Attachments::getId, fileId).eq(Attachments::getLinkedID, smallproId).eq(Attachments::getType, 38);
        boolean flag = attachmentsService.remove(deleteWrapper);
        if (!flag) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return false;
        }
        return true;

    }


    // endregion

    // region private Method

    // region getOverTime

    /**
     * description: <计算目标时间与当前差多少天>
     * translation: <Calculate how many days the target time differs from the current>
     *
     * @param targetTime 目标时间
     * @return java.lang.String
     * <AUTHOR>
     * @date 16:14 2019/12/2
     * @since 1.0.0
     **/
    private String getOverTime(Long targetTime) {
        return getOverDays(targetTime) + "天";
    }

    /**
     * description: <计算目标时间与当前差多少天>
     * translation: <Calculate how many days the target time differs from the current>
     *
     * @param targetTime 目标时间
     * @return java.lang.Integer
     * <AUTHOR>
     * @date 11:58 2020/4/10
     * @since 1.0.0
     **/
    private Integer getOverDays(Long targetTime) {
        long now = System.currentTimeMillis();
        Long overDate =
                ((now - targetTime) / (24 * 60 * 60 * 1000));
        return overDate.intValue();
    }

    // endregion

    // region 获取小件接件商品相关信息 getSmallproOrderListInfo

    /**
     * description: <获取小件接件商品相关信息>
     * translation: <Get information about smallpro goods>
     *
     * @param smallproBillList 小件关联信息列表
     * @param subId            订单Id
     * @return java.util.List<com.jiuji.oa.afterservice.smallpro.bo.SmallproOrderInfoBO>
     * <AUTHOR>
     * @date 11:02 2019/11/29
     * @since 1.0.0
     **/
    @Override
    public List<SmallproOrderInfoBO> getSmallproOrderListInfo(
            List<SmallproBill> smallproBillList, Integer subId, Integer overDays) {
        List<SmallproOrderInfoBO> resultList = new ArrayList<>(smallproBillList.size());

        OaUserBO currentStaff = currentRequestComponent.getCurrentStaffId();
        // 查询商品信息、订单信息逻辑
        // 查询数据库
        List<SmallproInfoOrderBO> orderList = Optional.ofNullable(smallproMapper.getSmallproOrderInfo(
                smallproBillList.stream().map(SmallproBill::getBasketId).collect(Collectors.toList()), XtenantEnum.getXtenant()))
                .orElseGet(ArrayList::new);

//        //美团京东订单类型判断
//        Integer existCount = subService.lambdaQuery().eq(Sub::getSubId, subId)
//                .in(Sub::getSubtype, OrderSubTypeEnum.JD_TO_HOME.getCode(), OrderSubTypeEnum.MEITUAN.getCode())
//                .count();
//        if (existCount > 0 && XtenantEnum.isJiujiXtenant() && !SpringUtil.getBean(RefundMoneyService.class).enable(null, null)) {
//            //九机美团京东可退金额  如果最早的收银时间与商品添加时间小于10s 可退金额为0
//            List<Shouying> shouyingList = shouyingService.lambdaQuery().eq(Shouying::getSubId, subId)
//                    .in(Shouying::getShouyingType, "交易", "订金")
//                    .orderByAsc(Shouying::getDtime).list();
//            for (SmallproInfoOrderBO x : orderList) {//商品添加时间
//                LocalDateTime basketDate = x.getBasketDate();
//                if (Objects.isNull(basketDate)) {
//                    continue;
//                }
//                Shouying recentShouying = shouyingList.stream().filter((Shouying y) -> {
//                    //收银时间
//                    LocalDateTime dtime = y.getDtime();
//                    if (dtime.isAfter(basketDate)) {
//                        Duration duration = Duration.between(basketDate, dtime);
//                        return duration.toMillis() <= BOUND_MILLIS;
//                    }
//                    return false;
//                }).findFirst().orElse(null);
//                if (Objects.nonNull(recentShouying)) {
//                    x.setPriceReturn(BigDecimal.ZERO);
//                }
//            }
//        }

        // 从老库查
        if (CollectionUtils.isEmpty(orderList) && currentStaff.getXTenant() < 1000) {
            orderList = Optional.ofNullable(CommenUtil.autoQueryHist(() -> smallproMapper.getSmallproOrderInfoHis(
                    smallproBillList.stream().map(SmallproBill::getBasketId).collect(Collectors.toList())), MTableInfoEnum.SUB, subId))
                    .orElseGet(ArrayList::new);
        }
        List<Long> ppidList = smallproBillList.stream().map(SmallproBill::getPpriceid).collect(Collectors.toList());
        List<SmallproInfoProductBO> productList = Optional.ofNullable(smallproMapper.getSmallproShopInfo(ppidList))
                .orElseGet(ArrayList::new);
        // 构建索引Map
        Map<Integer, SmallproInfoOrderBO> basketIdOrderMap =
                orderList.stream().collect(Collectors.toMap(SmallproInfoOrderBO::getBasketId, bo -> bo));
        Map<Integer, SmallproInfoProductBO> ppriceIdProductMap =
                productList.stream().collect(Collectors.toMap(SmallproInfoProductBO::getPpriceId, bo -> bo));
        // 批量获取条形码
        Map<Integer, List<String>> barCodeListMap = SpringUtil.getBean(ProductbarcodeService.class).listBarCodeByPpids(Convert.toList(Integer.class, ppidList));
        smallproBillList.stream().forEach(bill -> {
            SmallproOrderInfoBO smallproOrderInfoBO = new SmallproOrderInfoBO();
            // 需要运用到的变量
            int basketId = bill.getBasketId();
            int ppriceId = bill.getPpriceid().intValue();
            SmallproInfoOrderBO tempOrderBO =
                    Optional.ofNullable(basketIdOrderMap.get(basketId))
                            .orElseGet(SmallproInfoOrderBO::new);
            SmallproInfoProductBO tempProductBO =
                    Optional.ofNullable(ppriceIdProductMap.get(ppriceId))
                            .orElseGet(SmallproInfoProductBO::new);

            int cid = Optional.ofNullable(tempProductBO.getCid())
                    .orElseGet(() -> 0);

            int isTemperedFilm = 0;
            int isProtectiveCase = 0;
            // 判断是否为钢化膜
            if (smallProAdapterService.getSmallRelativeConfigList(SysConfigConstant.JIUJI_TEMPERED_FILM_CID).contains(cid)) {
                isTemperedFilm = 1;
            }
            // 判断是否为保护壳
            if (smallProAdapterService.getSmallRelativeConfigList(SysConfigConstant.JIUJI_PROTECTIVE_SHELL_CID).contains(cid)) {
                isProtectiveCase = 1;
            }

            SmallproMobileInfoBO smallproMobileInfo = new SmallproMobileInfoBO();
            if (Convert.toBool(bill.getMobileExchangeFlag())) {
                QueryWrapper<Basket> basketQueryWrapper = new QueryWrapper<>();
                basketQueryWrapper.lambda().eq(Basket::getBasketId, bill.getBasketId());
                List<Basket> list = basketService.list(basketQueryWrapper);
                if (CollectionUtils.isNotEmpty(list)) {
                    Basket basket = list.get(0);
                    Collection<Long> mobilePpid = Arrays.asList(basket.getPpriceid());
                    Map<Integer, Productinfo> ppriceidToProductinfoMap = productinfoService.list(new LambdaQueryWrapper<Productinfo>()
                            .in(Productinfo::getPpriceid, mobilePpid)).stream()
                            .collect(Collectors.toMap(Productinfo::getPpriceid, Function.identity(), (v1, v2) -> v1));
                    smallproMobileInfo.setBasketId(bill.getBasketId());
                    ProductMkc productMkc = productMkcService.list(new LambdaQueryWrapper<ProductMkc>()
                            .eq(ProductMkc::getBasketId, basket.getBasketId())
                            .eq(ProductMkc::getPpriceid, basket.getPpriceid()))
                            .stream().findFirst().orElse(null);
                    smallproMobileInfo.setImei(Objects.isNull(productMkc) || StringUtils.isEmpty(productMkc.getImei()) ? "" : productMkc.getImei());
                    smallproMobileInfo.setPpriceId(basket.getPpriceid());
                    Productinfo productinfo = ppriceidToProductinfoMap.get(Convert.toInt(basket.getPpriceid()));
                    smallproMobileInfo.setProductName(productinfo.getProductName());
                    smallproMobileInfo.setProductColor(productinfo.getProductColor());
                    smallproMobileInfo.setProductCount(NumberConstant.ONE);
                }
            }
            smallproOrderInfoBO.setPrice(Optional.ofNullable(tempOrderBO.getPrice())
                    .orElseGet(() -> BigDecimal.valueOf(0.0)))
                    .setPriceReturn(Optional.ofNullable(tempOrderBO.getPriceReturn())
                            .orElseGet(() -> BigDecimal.valueOf(0.0)))
                    .setMemberPrice(Optional.ofNullable(tempOrderBO.getMemberPrice())
                            .orElseGet(() -> BigDecimal.valueOf(0.0)))
                    .setBarCode(Optional.ofNullable(tempProductBO.getBarCode())
                            .orElseGet(() -> ""))
                    .setBarCodeList(barCodeListMap.get(Convert.toInt(bill.getPpriceid())))
                    .setProductName(Optional.ofNullable(tempProductBO.getProductName())
                            .orElseGet(() -> ""))
                    .setProductColor(Optional.ofNullable(tempProductBO.getProductColor())
                            .orElseGet(() -> ""))
                    .setProductCount(bill.getCount())
                    .setPpriceId(ppriceId)
                    .setBasketId(basketId)
                    .setSmallproBillId(bill.getId())
                    .setCid(cid)
                    .setSubId(subId)
                    .setSubAreaId(Optional.ofNullable(tempOrderBO.getAreaId())
                            .orElseGet(() -> 0))
                    .setIsTemperedFilm(isTemperedFilm)
                    .setIsProtectiveCase(isProtectiveCase)
                    .setMobileExchangeFlag(Convert.toBool(bill.getMobileExchangeFlag()))
                    .setSmallproMobileInfo(smallproMobileInfo)
                    .setOrderLink(sysConfigClient.getValueByCode(SysConfigConstant.OA_URL).getData() + "/addOrder/editOrder?SubID=" + subId)
                    .setProductId(Optional.ofNullable(tempProductBO.getProductId())
                            .orElseGet(() -> 0))
                    .setTargetPpriceId(Optional.ofNullable(tempProductBO.getTargetPpriceId())
                            .orElseGet(() -> 0))
                    .setIsSn(Optional.ofNullable(tempProductBO.getIsSn()).orElseGet(() -> false));
            if(XtenantEnum.isJiujiXtenant()){
                jiujiYearCardReturnPrice(subId, overDays, smallproOrderInfoBO, ppriceId);
            }

            resultList.add(smallproOrderInfoBO);
        });
        return resultList;
    }

    @Override
    public void jiujiThirdShouyingReturnPrice(Integer subId, List<SmallproOrderInfoBO> smallproOrderInfos) {
        //设置已付金额
        Optional<Sub> subOpt = subService.lambdaQuery().eq(Sub::getSubId, subId).list().stream().findFirst();
        if(!subOpt.isPresent()){
            return;
        }
        Sub sub = subOpt.get();
        if(Stream.of(SubSubTypeEnum.JING_DONG_DAO_JIA, SubSubTypeEnum.MEI_TUAN_SHAN_GOU, SubSubTypeEnum.DOU_YIN_DING_DAN)
                .anyMatch(subTypeEnum -> Objects.equals(subTypeEnum.getCode(),sub.getSubtype()))){
            //京东/美团/抖音可以退
            return;
        }

        //当前要退的总金额
        BigDecimal totalPriceReturn = smallproOrderInfos.stream().map(SmallproOrderInfoBO::getPriceReturn).filter(ObjectUtil::isNotNull)
                .reduce(BigDecimal::add).orElse(BigDecimal.ZERO);

        //计算剩余可退金额
        BigDecimal yifuM = ObjectUtil.defaultIfNull(sub.getYifuM(),BigDecimal.ZERO);
        List<ThirdOriginRefundVo> thirdOriginRefundVos = SpringUtil.getBean(ThirdOriginWayService.class).listAll(subId, TuihuanKindEnum.TPJ);
        List<Integer> operatorShouyingIds = SpringUtil.getBean(OperatorBusinessRefundService.class).listOperatorShouyingId(subId);
        List<Integer> currencyPays = StrUtil.splitTrim(CommonUtils.getResultData(sysConfigClient.getValueByCode(SysConfigConstant.NINE_MACHINE_CURRENCY_PAY_WAY),
                userMsg -> {
                    throw new CustomizeException("获取九机币配置异常");
                }), StringPool.COMMA).stream().map(Convert::toInt).filter(Objects::nonNull)
                .collect(Collectors.toList());
        BigDecimal totalThirdRefundPrice = thirdOriginRefundVos.stream()
                .peek(tor -> SpringContextUtil.addRequestKeyMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG,"[{}]收银id[{}]已退金额[{}],金额[{}]",
                        tor.getReturnWayName(),tor.getShouyingId(),tor.getRefundedPrice(),tor.getRefundPrice()))
                // 过滤掉九机币的类型
                .filter(tor -> !currencyPays.contains(tor.getOtherType()))
                // 过滤掉运营商收银类型
                .filter(tor -> !operatorShouyingIds.contains(tor.getShouyingId()))
                .map(ThirdOriginRefundVo::getRefundPrice)
                .filter(ObjectUtil::isNotNull).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);

        BigDecimal maxRefundPrice = NumberUtil.max(yifuM.subtract(totalThirdRefundPrice),BigDecimal.ZERO);
        if(totalPriceReturn.compareTo(maxRefundPrice) <=0){
            //在最大可退范围内,不需要处理
            return;
        }
        //超出的金额进行分摊扣减
        BigDecimal excessAmount  = totalPriceReturn.subtract(maxRefundPrice);
        CommonUtils.splitPrice(excessAmount,totalPriceReturn,2,smallproOrderInfos,SmallproOrderInfoBO::getPriceReturn,
                SmallproOrderInfoBO::getProductCount,SmallproOrderInfoBO::getAllocatedDeductionAmount,
                (spoi,allocatedDeductionAmount) -> {
                    SpringContextUtil.addRequestKeyMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG,
                            "basketId[{}]商品分摊三方扣减[{}]",spoi.getBasketId(),allocatedDeductionAmount);
                    spoi.setAllocatedDeductionAmount(allocatedDeductionAmount);
                });
        smallproOrderInfos.forEach(spoi -> {
            BigDecimal originPrice = ObjectUtil.defaultIfNull(spoi.getPriceReturn(), BigDecimal.ZERO);
            BigDecimal allocatedDeductionAmount = ObjectUtil.defaultIfNull(spoi.getAllocatedDeductionAmount(), BigDecimal.ZERO);
            spoi.setPriceReturn(NumberUtil.max(originPrice.subtract(allocatedDeductionAmount),BigDecimal.ZERO));
        });

    }

    /**
     * 处理九机年卡的退款金额
     * @param subId
     * @param overDays
     * @param smallproOrderInfoBO
     * @param ppriceId
     */
    private void jiujiYearCardReturnPrice(Integer subId, Integer overDays, SmallproOrderInfoBO smallproOrderInfoBO, int ppriceId) {
        /**
         * 九机年卡退款处理
         * 1.30天以内（当天算第一天，例如：2023年6月1号 10点购买 那么结束时间是 7月1号0点） 按照优惠码的使用金额进行扣减年卡的退款金额（负数的取0）
         * 2.大于30天的 统一退款金额为0
         */
        if(Objects.equals(ppriceId,197053)
                && !stringRedisTemplate.hasKey(StrUtil.format(RedisKeys.SMALLPRO_NOT_DISCOUNT_BASKET_KEY, smallproOrderInfoBO.getBasketId()))){
            if (ObjectUtil.defaultIfNull(overDays,0) > 30){
                // 超过30天退款金额为0
                SpringContextUtil.addRequestKeyMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG,
                        "九机年卡[{}],距离购买时间{}天,原可退金额: {},超过30天退款金额为0", ppriceId, overDays, smallproOrderInfoBO.getPriceReturn());
                smallproOrderInfoBO.setPriceReturn(BigDecimal.ZERO);
            }else{
                BigDecimal totalUseNcPrices = ObjectUtil.defaultIfNull(smallproMapper.getTotalYearCardUseNcPrices(subId),BigDecimal.ZERO);
                SpringContextUtil.addRequestKeyMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG,
                        "九机年卡[{}],距离购买时间{}天,原可退金额: {},扣除优惠码使用金额{}远", ppriceId, overDays, smallproOrderInfoBO.getPriceReturn(),totalUseNcPrices);
                smallproOrderInfoBO.setPriceReturn(NumberUtil.max(BigDecimal.ZERO, smallproOrderInfoBO.getPriceReturn().subtract(totalUseNcPrices)));
            }
        }
    }

    // endregion

    // region 获取小件商品售后接件要求 getSmallproServiceListInfo

    /**
     * description: <获取小件商品售后接件要求>
     * translation: <Requirements for obtaining aftermarket smallpro>
     *
     * @param smallproOrderInfoBOList 小件接件单商品列表
     * @param writeFlag               写数据源标志位
     * @return java.util.List<com.jiuji.oa.afterservice.smallpro.bo.SmallproServiceConfigBO>
     * <AUTHOR>
     * @date 11:37 2019/12/2
     * @since 1.0.0
     **/
    private List<SmallproServiceConfigBO> getSmallproServiceListInfo(List<SmallproOrderInfoBO> smallproOrderInfoBOList, boolean writeFlag) {
        Set<Integer> productIdList = new HashSet<>(smallproOrderInfoBOList.size() * 3);
        HashMap<SmallproOrderInfoBO, Map<Integer, Integer>> orderProductIdMap = new HashMap<>(smallproOrderInfoBOList.size());
        List<SmallproServiceConfigBO> resultList = new ArrayList<>(smallproOrderInfoBOList.size());
        // 组装查询用数据结构&过滤数据结构
        CategoryService categoryService = SpringUtil.getBean(CategoryService.class);
        smallproOrderInfoBOList.forEach(bo -> {
            Map<Integer, Integer> tempIdKindMap = CollUtil.newHashMap(3);
            categoryService.listAll().stream().filter(c->Objects.equals(c.getId(),bo.getCid())).map(Category::getPath)
                    .flatMap(p->Arrays.stream(StrUtil.split(p, SignConstant.COMMA))).filter(StrUtil::isNotBlank)
                    .map(p->Convert.toInt(p))
                    .forEach(cid -> {
                        tempIdKindMap.put(cid, WxconfigGift.KindEnum.CATEGORY_ID.getCode());
                    });
            tempIdKindMap.put(bo.getCid(), WxconfigGift.KindEnum.CATEGORY_ID.getCode());
            tempIdKindMap.put(bo.getPpriceId(), WxconfigGift.KindEnum.PPID.getCode());
            tempIdKindMap.put(bo.getProductId(), WxconfigGift.KindEnum.PRODUCT_ID.getCode());
            orderProductIdMap.put(bo, tempIdKindMap);
            productIdList.addAll(tempIdKindMap.keySet());
        });
        // 查询符合条件的售后维修服务要求
        QueryWrapper<WxconfigGift> wxconfigGiftQueryWrapper = new QueryWrapper<>();
        wxconfigGiftQueryWrapper.lambda()
                .select(WxconfigGift::getComment, WxconfigGift::getKind, WxconfigGift::getOptinfo)
                .in(WxconfigGift::getProductid, productIdList);
        List<WxconfigGift> wxconfigGiftList;
        wxconfigGiftList = Optional.ofNullable(wxconfigGiftService.listSqlServer(wxconfigGiftQueryWrapper))
                .orElseGet(ArrayList::new);

        // 组装筛选用数据结构
        HashMap<String, WxconfigGift> productIdWxConfigMap = new HashMap<>(wxconfigGiftList.size());
        BiFunction<Integer, Integer, String> configKeyFun = (productId, kind) -> StrUtil.format("{}_{}", productId, kind);
        wxconfigGiftList.forEach(config -> {
            productIdWxConfigMap.put(configKeyFun.apply(config.getProductid(), config.getKind()), config);
        });
        // 筛选组装数据
        smallproOrderInfoBOList.forEach(bo -> {
            SmallproServiceConfigBO smallproWxconfigBO = new SmallproServiceConfigBO();
            AtomicReference<WxconfigGift> wxConfigGift = new AtomicReference<>(null);
            orderProductIdMap.get(bo).forEach((id, kind) -> {
                WxconfigGift tempWxConfigGift = productIdWxConfigMap.get(configKeyFun.apply(id, kind));
                if (wxConfigGift.get() == null) {
                    wxConfigGift.set(tempWxConfigGift);
                } else if (tempWxConfigGift != null) {
                    // kind 2>1>3
                    switch (wxConfigGift.get().getKind()) {
                        case 3:
                            if (!tempWxConfigGift.getKind().equals(3)) {
                                wxConfigGift.set(tempWxConfigGift);
                            }
                            break;
                        case 2:
                            break;
                        case 1:
                            if (tempWxConfigGift.getKind().equals(2)) {
                                wxConfigGift.set(tempWxConfigGift);
                            }
                            break;
                        default:
                            break;
                    }
                }
            });
            StringBuilder smallproName = new StringBuilder("");
            smallproName.append(bo.getProductName());
            smallproName.append(" ");
            smallproName.append((bo.getProductColor() == null) ? "" : bo.getProductColor());
            smallproWxconfigBO.setSmallproName(smallproName.toString());
            if (wxConfigGift.get() != null) {
                smallproWxconfigBO.setServiceComment(wxConfigGift.get().getComment())
                        .setServiceDemand(wxConfigGift.get().getOptinfo());
            }
            resultList.add(smallproWxconfigBO);
        });
        return resultList;
    }

    // endregion

    // region 设置小件接件单地区信息 setAreaInfo

    /**
     * description: <设置小件接件单地区信息>
     * translation: <Set smallpro pick-up order area information>
     *
     * @param smallproOrderInfoBOList 小件接件单商品列表
     * @param smallpro                小件单
     * @return void
     * <AUTHOR>
     * @date 11:38 2019/12/2
     * @since 1.0.0
     **/
    private void setAreaInfo(List<SmallproOrderInfoBO> smallproOrderInfoBOList, Smallpro smallpro) {
        List<Integer> areaIdList = new ArrayList<>(smallproOrderInfoBOList.size() + 2);

        smallproOrderInfoBOList.forEach(bo -> {
            // 购买地区Id
            if (bo.getSubAreaId() != null) {
                areaIdList.add(bo.getSubAreaId());
            }
        });
        // 接件地区Id
        if (smallpro.getAreaId() != null) {
            areaIdList.add(smallpro.getAreaId());
        }
        // 转地区Id
        if (smallpro.getToAreaId() != null) {
            areaIdList.add(smallpro.getToAreaId());
        }
        List<SmallproInfoAreaBO> areaInfoList = Optional.ofNullable(smallproMapper.getSmallproAreaName(areaIdList))
                .orElseGet(ArrayList::new);
        HashMap<Integer, String> areaIdName = new HashMap<>(smallproOrderInfoBOList.size() + 2);
        areaInfoList.forEach(areaInfo -> {
            areaIdName.put(areaInfo.getAreaId(), areaInfo.getArea());
        });
        smallproOrderInfoBOList.forEach(bo -> {
            if (bo.getSubAreaId() != null) {
                bo.setSubAreaName(areaIdName.get(bo.getSubAreaId()));
            }
        });
        smallpro.setToArea(areaIdName.get(smallpro.getToAreaId()));
        smallpro.setArea(areaIdName.get(smallpro.getAreaId()));
    }

    /**
     *
     * @param smallproInfoServiceRecord
     * @return LocalDateTime tradeDate 0,LocalDateTime endDate 1,Integer serviceRecordYear 2,Integer serviceRecordType 3,Integer isServiceRecord 4
     */
    private Tuple buildEndDate(SmallproInfoServiceRecordBO smallproInfoServiceRecord){
        LocalDateTime tradeDate = smallproInfoServiceRecord.getTradeDate();
        tradeDate = tradeDate.minusHours(tradeDate.getHour())
                .minusMinutes(tradeDate.getMinute())
                .minusSeconds(tradeDate.getSecond())
                .minusNanos(tradeDate.getNano());
        LocalDateTime endDate = tradeDate.plusDays(1)
                .minusSeconds(1);
        int serviceRecordYear = 0;
        int serviceRecordType = 0;
        Integer isServiceRecord = 1;
        switch (smallproInfoServiceRecord.getServiceType()) {
            // 只换不修2年
            case 14:
                endDate = endDate.plusYears(2);
                serviceRecordType = 3;
                serviceRecordYear = 2;
                break;
            // 15 意外换新2年
            case 15:
                endDate = endDate.plusYears(2);
                serviceRecordType = 1;
                serviceRecordYear = 2;
                break;
            // 17 意外换新1年
            case 17:
                endDate = endDate.plusYears(1);
                serviceRecordType = 1;
                serviceRecordYear = 1;
                break;
            // 延保1年
            case 16:
                endDate = endDate.plusYears(1);
                serviceRecordType = 2;
                serviceRecordYear = 1;
                break;
            default:
                serviceRecordType = 0;
                serviceRecordYear = 0;
                isServiceRecord = 0;
                break;
        }
        return new Tuple(tradeDate,endDate,serviceRecordYear,serviceRecordType,isServiceRecord);
    }

    // endregion

    // region 获取订单可用九机服务 getSubServiceRecord

    /**
     * description: <获取订单可用九机服务>
     * translation: <Get orders available for JiuJi service>
     *
     * @param smallproOrderInfoBOList 小件接件单商品列表
     * @param dateTimeFormatter       时间转换关系
     * @param filmCardInfo            年包信息
     * @return com.jiuji.oa.afterservice.smallpro.bo.SubServiceRecordBO
     * <AUTHOR>
     * @date 11:42 2019/12/2
     * @since 1.0.0
     **/
    private SubServiceRecordBO getSubServiceRecord(List<SmallproOrderInfoBO> smallproOrderInfoBOList,
                                                   DateTimeFormatter dateTimeFormatter,
                                                   FilmCardInfomationBO filmCardInfo) {
        SubServiceRecordBO subServiceRecordBO = new SubServiceRecordBO();
        // 多件商品的小件单不提供九机服务选项
        if (smallproOrderInfoBOList.size() != 1) {
            return subServiceRecordBO;
        }

        SmallproOrderInfoBO smallproOrderInfoBO = smallproOrderInfoBOList.get(0);
        SmallproInfoServiceRecordBO smallproInfoServiceRecordBO = Optional.ofNullable(
                smallproMapper.getSubServiceRecord(smallproOrderInfoBO.getBasketId()))
                .orElseGet(SmallproInfoServiceRecordBO::new);

        Integer isServiceRecord = 1;
        Integer isServiceRecordUsed = 0;

        if (Optional.ofNullable(smallproInfoServiceRecordBO.getCost()).orElseGet(() -> 0.0) > 0) {
            isServiceRecordUsed = 1;
        }
        if (filmCardInfo != null) {
            isServiceRecordUsed =
                    (filmCardInfo.getIsUseCount() && filmCardInfo.getIsNotExpired() && (!filmCardInfo.getIsFreeExchange()))
                            ? 0 : 1;
        }


        List<Integer> serviceTypeList = new ArrayList<>();
        //todo 待适配
        serviceTypeList.add(14);
        serviceTypeList.add(15);
        serviceTypeList.add(16);
        serviceTypeList.add(17);
        if ((serviceTypeList.contains(smallproInfoServiceRecordBO.getServiceType())
                && smallproInfoServiceRecordBO.getDel() == 0)) {

            Long nowTimeStamp = System.currentTimeMillis();
            Tuple endTuple = buildEndDate(smallproInfoServiceRecordBO);
            LocalDateTime tradeDate = ObjectUtil.defaultIfNull(smallproInfoServiceRecordBO.getStartDate(),endTuple.get(0));
            LocalDateTime endDate = ObjectUtil.defaultIfNull(smallproInfoServiceRecordBO.getEndDate(),endTuple.get(1));
            int serviceRecordYear  = endTuple.get(2);
            int serviceRecordType = endTuple.get(3);
            isServiceRecord = endTuple.get(4);
            //
            String endTimeStr = endDate.format(DateUtil.format_day) + " 23:59:59";

            isServiceRecord = ((isServiceRecord == 1) && (isServiceRecordUsed == 0)) ? 1 : 0;
            subServiceRecordBO.setTradeDate(tradeDate.format(dateTimeFormatter))
//                    .setEndDate(endDate.format(dateTimeFormatter))
                    .setEndDate(endTimeStr)
                    .setWarrantyType(serviceRecordType)
                    .setWarrantyPrice(smallproInfoServiceRecordBO.getCost())
                    .setWarrantyStatus(
                            (endDate.toInstant(ZoneOffset.of("+8")).toEpochMilli() > nowTimeStamp) ? 0 : 1)
                    .setWarrantyYear(serviceRecordYear)
                    .setIsServiceRecord(isServiceRecord)
                    .setIsServiceRecordUsed(isServiceRecordUsed);
            if (filmCardInfo != null) {
                //当已过期 且在过期后六十天内不能使用的才算失效
                subServiceRecordBO.setIsFilmCard(1).setWarrantyType(4).setIsServiceRecord(1).setWarrantyStatus((filmCardInfo.getIsNotExpired()) ? 0 : 1);
            }
        } else if (filmCardInfo != null) {
            subServiceRecordBO.setIsFilmCard(1).setWarrantyType(4).setIsServiceRecord(1).setWarrantyStatus((filmCardInfo.getIsNotExpired()) ? 0 : 1);
        }
        return subServiceRecordBO;
    }

    // endregion

    // region 获取小件接件单退换商品列表 getSmallproSendBackInfoList

    /**
     * description: <获取小件接件单退换商品列表>
     * translation: <Get a list of small items>
     *
     * @param smallproId        小件接件单Id
     * @param dateTimeFormatter 时间转换格式
     * @return java.util.List<com.jiuji.oa.afterservice.smallpro.bo.SmallproSendBackInfoBO>
     * <AUTHOR>
     * @date 11:41 2019/12/2
     * @since 1.0.0
     **/
    private List<SmallproSendBackInfoBO> getSmallproSendBackInfoList(
            Integer smallproId, DateTimeFormatter dateTimeFormatter) {
        QueryWrapper<ShouhouTuihuan> shouhouTuihuanQueryWrapper = new QueryWrapper<>();
        shouhouTuihuanQueryWrapper.lambda().eq(ShouhouTuihuan::getSmallproid, smallproId)
                .in(ShouhouTuihuan::getTuihuanKind, Stream.of(TuihuanKindEnum.TPJ, TuihuanKindEnum.SMALL_PRO_REFUND,
                        TuihuanKindEnum.SMALL_PRO_REFUND_REPAIR_FEE, TuihuanKindEnum.SMALL_PRO_HQTXH).map(TuihuanKindEnum::getCode)
                        .collect(Collectors.toList()))
                .and(wrapper -> wrapper.eq(ShouhouTuihuan::getIsdel, 0).or().isNull(ShouhouTuihuan::getIsdel))
                .orderByDesc(ShouhouTuihuan::getId);
        List<ShouhouTuihuan> shouhouTuihuanList =
                Optional.ofNullable(shouhouTuihuanService.listSqlServer(shouhouTuihuanQueryWrapper))
                        .orElseGet(ArrayList::new);
        List<SmallproSendBackInfoBO> resultList = new ArrayList<>(shouhouTuihuanList.size());
        shouhouTuihuanList.forEach(tuihuanDO -> {
            SmallproSendBackInfoBO smallproSendBackInfoBO = new SmallproSendBackInfoBO();
            smallproSendBackInfoBO.setFirstCheckFlag((tuihuanDO.getCheck1() == null) ? null
                    : (tuihuanDO.getCheck1()) ? 1 : 0)
                    .setFirstCheckUser(tuihuanDO.getCheck1user())
                    .setFirstCheckTime((tuihuanDO.getCheck1dtime() == null) ? null
                            : tuihuanDO.getCheck1dtime().format(dateTimeFormatter))
                    .setSecondCheckFlag((tuihuanDO.getCheck2() == null) ? null :
                            (tuihuanDO.getCheck2()) ? 1 : 0)
                    .setSecondCheckUser(tuihuanDO.getCheck2user())
                    .setSecondCheckTime((tuihuanDO.getCheck2dtime() == null) ? null :
                            tuihuanDO.getCheck2dtime().format(dateTimeFormatter))
                    .setThreeCheckFlag(Convert.toInt(tuihuanDO.getCheck3()))
                    .setThreeCheckUser(tuihuanDO.getCheck3user())
                    .setThreeCheckTime((tuihuanDO.getCheck3dtime() == null) ? null :
                            tuihuanDO.getCheck3dtime().format(dateTimeFormatter))
                    .setInUser(tuihuanDO.getInuser() == null ? null : tuihuanDO.getInuser())
                    .setDTime(tuihuanDO.getDtime() == null ? null : tuihuanDO.getDtime().format(dateTimeFormatter))
                    .setTuikuanM(tuihuanDO.getTuikuanM() == null ? null : tuihuanDO.getTuikuanM().setScale(2,
                            BigDecimal.ROUND_FLOOR).doubleValue())
                    .setTuikuanM1(tuihuanDO.getTuikuanM1() == null ? null : tuihuanDO.getTuikuanM1().setScale(2,
                            BigDecimal.ROUND_FLOOR).doubleValue())
                    .setComment(tuihuanDO.getComment() == null ? null : tuihuanDO.getComment())
                    .setTuiWay(tuihuanDO.getTuiWay() == null ? null : tuihuanDO.getTuiWay())
                    .setShouhouTuihuanId(tuihuanDO.getId())
                    .setPayOpenId(tuihuanDO.getPayOpenId())
                    .setBankfuming(tuihuanDO.getBankfuming())
                    .setBankName(tuihuanDO.getBankname())
                    .setBanknumber(tuihuanDO.getBanknumber())
                    .setKemuTui(tuihuanDO.getKemuTui());
            ;
            resultList.add(smallproSendBackInfoBO);
        });
        return resultList;
    }

    // endregion

    // region 获取小件接件单返厂商品信息列表 getSmallproReturnFactoryInfoList

    /**
     * description: <获取小件接件单返厂商品信息列表>
     * translation: <Get the list of smallpro pick-up of returning goods>
     *
     * @param smallproOrderInfoBOList 小件接件单商品信息列表
     * @param smallproId              小件接件单Id
     * @param dateTimeFormatter       时间转换格式
     * @return java.util.List<com.jiuji.oa.afterservice.smallpro.bo.SmallproReturnFactoryInfoBO>
     * <AUTHOR>
     * @date 11:40 2019/12/2
     * @since 1.0.0
     **/
    private List<SmallproReturnFactoryInfoBO> getSmallproReturnFactoryInfoList(
            List<SmallproOrderInfoBO> smallproOrderInfoBOList, Integer smallproId,
            DateTimeFormatter dateTimeFormatter) {
        List<Long> list = new ArrayList();
        // 存在置换过得
        Map<Integer, SmallproOrderInfoBO> basketIdOrderInfoMap = new HashMap<>(smallproOrderInfoBOList.size());
        Smallpro byIdSqlServer = smallproService.getByIdSqlServer(smallproId);
        // 判断是否二次换货去除之前ppid的商品名称
        // 获取最近一次该basket换货记录
        //Integer smallProId = smallproMapper.getIsChangePre(byIdSqlServer.getId(), byIdSqlServer.getSubId(),
        //        smallproOrderInfoBOList.get(0).getBasketId());

        QueryWrapper<ShouhouFanchang> shouhouFanchangQueryWrapper = new QueryWrapper<>();
        shouhouFanchangQueryWrapper.lambda().eq(ShouhouFanchang::getSmallproid, smallproId);
        List<ShouhouFanchang> shouhouFanchangList =
                Optional.ofNullable(shouhouFanchangService.listSqlServer(shouhouFanchangQueryWrapper))
                        .orElseGet(ArrayList::new);
        if(CollUtil.isNotEmpty(shouhouFanchangList)){
            list.addAll(shouhouFanchangList.stream().map(ShouhouFanchang::getPpid).map(Convert::toLong)
                    .filter(Objects::nonNull).collect(Collectors.toList()));
        }
        if (byIdSqlServer.getChangePpriceid() != null) {
            list.add(Convert.toLong(byIdSqlServer.getChangePpriceid()));
        }
        if (CollectionUtils.isNotEmpty(list)) {
            List<SmallproInfoProductBO> productList =
                    Optional.ofNullable(CommonUtils.bigDataInQuery(list, ids -> smallproMapper.getSmallproShopInfo(ids))).orElseGet(ArrayList::new);
            for (SmallproInfoProductBO smallproInfoProduct : productList) {
                SmallproOrderInfoBO smallproOrderInfo = new SmallproOrderInfoBO();
                smallproOrderInfo.setPpriceId(smallproInfoProduct.getPpriceId());
                smallproOrderInfo.setProductColor(smallproInfoProduct.getProductColor());
                smallproOrderInfo.setProductName(smallproInfoProduct.getProductName());
                basketIdOrderInfoMap.put(smallproOrderInfo.getPpriceId(), smallproOrderInfo);
            }
        }
        smallproOrderInfoBOList.forEach(bo -> {
            basketIdOrderInfoMap.put(bo.getPpriceId(), bo);
        });

        List<SmallproReturnFactoryInfoBO> resultList = new ArrayList<>(shouhouFanchangList.size());
        //添加小件是否良品
        //获取良品小件
        List<Integer> basketIdList = Optional.of(shouhouFanchangList.stream().map(ShouhouFanchang::getBasketId).distinct().collect(Collectors.toList()))
                .orElseGet(ArrayList::new);
        //查询是否是优品小件
        List<Integer> basketTypeById = smallproService.getBasketTypeById(basketIdList);
        //添加良品标识
        shouhouFanchangList.forEach(fanchangDO -> {
            SmallproOrderInfoBO tempOrderInfoBO =
                    Optional.ofNullable(basketIdOrderInfoMap.get(fanchangDO.getPpid()))
                            .orElseGet(SmallproOrderInfoBO::new);
            SmallproReturnFactoryInfoBO smallproReturnFactoryInfoBO = new SmallproReturnFactoryInfoBO();
            smallproReturnFactoryInfoBO.setReturnFactoryId(fanchangDO.getId())
                    .setReturnFactoryInfo(StrUtil.nullToDefault(tempOrderInfoBO.getProductName(),"")
                            + " " + StrUtil.nullToDefault(tempOrderInfoBO.getProductColor(),"") )
                    .setPPriceId(fanchangDO.getPpid().longValue())
                    .setReturnFactoryStats(fanchangDO.getRstats())
                    .setScrappedInuser(fanchangDO.getBfinuser())
                    .setScrappedTime((fanchangDO.getBftime() == null) ? null :
                            fanchangDO.getBftime().format(dateTimeFormatter))
                    .setCashoverInuser(fanchangDO.getZxinuser())
                    .setCashoverTime((fanchangDO.getZxdtime() == null) ? null :
                            fanchangDO.getZxdtime().format(dateTimeFormatter))
                    .setReturnChannel(Optional.ofNullable(fanchangDO.getQudao())
                            .orElseGet(() -> ""))
                    .setPzId(Optional.ofNullable(fanchangDO.getPzid()).orElseGet(() -> null))
                    .setIsYouLangPin(basketTypeById.contains(fanchangDO.getBasketId()))
                    .setCheckStatus(fanchangDO.getCheckStatus()).setCheckTime(fanchangDO.getCheckTime()).setCheckInUser(fanchangDO.getCheckInUser());
            resultList.add(smallproReturnFactoryInfoBO);
        });
        return resultList;
    }

    // endregion

    // region 获取小件接件单退换商品列表 getSmallproShouhouTuihuanList

    /**
     * description: <获取小件接件单退换商品列表>
     * translation: <Get a list of small items>
     *
     * @param smallproId 小件接件单Id
     * @return java.util.List<com.jiuji.oa.afterservice.smallpro.bo.SmallproSendBackInfoBO>
     * <AUTHOR>
     * @date 11:41 2019/12/2
     * @since 1.0.0
     **/
    private List<ShouhouTuihuan> getSmallproShouhouTuihuanList(
            Integer smallproId) {
        QueryWrapper<ShouhouTuihuan> shouhouTuihuanQueryWrapper = new QueryWrapper<>();
        shouhouTuihuanQueryWrapper.lambda().eq(ShouhouTuihuan::getSmallproid, smallproId).in(ShouhouTuihuan::getTuihuanKind, Arrays.asList(7, 9, 10))
                .and(shouhouTuihuanLambdaQueryWrapper -> shouhouTuihuanLambdaQueryWrapper.isNull(ShouhouTuihuan::getIsdel)
                        .or().eq(ShouhouTuihuan::getIsdel, 0))
                .isNotNull(ShouhouTuihuan::getCheck3dtime);
        List<ShouhouTuihuan> shouhouTuihuanList =
                Optional.ofNullable(shouhouTuihuanService.listSqlServer(shouhouTuihuanQueryWrapper))
                        .orElseGet(ArrayList::new);
        return shouhouTuihuanList;
    }
    // endregion

    // region 获取小件接件退款方式 getSmallproReturnWays

    /**
     * description: <获取小件接件退款方式>
     * translation: Get small item refund>
     *
     * @param subId  订单Id/shouhouId
     * @param type   退款方式7/10
     * @param areaId 登录用户门店Id
     * @return java.util.List<com.jiuji.oa.afterservice.smallpro.bo.smallproInfo.SmallproReturnTypeBO>
     * <AUTHOR>
     * @date 17:28 2020/3/31
     * @since 1.0.0
     **/
    private List<SmallProReturnTypeBO> getSmallproReturnWays(Integer subId, Integer type, Integer areaId) {
        if (subId == null || subId <= 0) {
            return null;
        }
        List<SmallProReturnTypeBO> result = new ArrayList<>(7);
        List<String> returnTypeList = new ArrayList<>(7);
        SmallproInfoReturnWayAreaInfoBO areaInfoBO = smallproMapper.getAreaInfoWithReturnWay(areaId);
        // 其他支付数量
        int otherPayCount = 0;
        List<Integer> subIds = new ArrayList<>(2);
        List<String> shouYingTypeList = new ArrayList<>(2);
        List<Shouying> shouyingList = null;
        QueryWrapper<Shouying> queryWrapper = new QueryWrapper<>();
        boolean crashFlag = false;
        boolean bankFlag = false;
        int fenqiId = 0;
        if (type == 7) {
            Integer subPid = smallproMapper.getSubPid(subId);
            if (subPid != null && subPid > 0) {
                subIds.add(subPid);
            }
            subIds.add(subId);
            shouYingTypeList.add("订金");
            shouYingTypeList.add("交易");
            queryWrapper.lambda().in(Shouying::getSubId, subIds).in(Shouying::getShouyingType, shouYingTypeList);
            shouyingList = shouyingService.listSqlServer(queryWrapper);
        } else if (type == 10) {
            shouYingTypeList.add("订金3");
            queryWrapper.lambda().eq(Shouying::getSubId, subId).in(Shouying::getShouyingType, shouYingTypeList);
            shouyingList = shouyingService.listSqlServer(queryWrapper);
        }
        if (shouyingList != null && shouyingList.size() > 0) {
            for (Shouying temp : shouyingList) {
                if (temp.getSubPay03().doubleValue() == 0.0) {
                    otherPayCount++;
                }
                if (temp.getSubPay01().doubleValue() > 0.0) {
                    crashFlag = true;
                }
                if (temp.getSubPay04().doubleValue() > 0.0 ||
                        temp.getSubPay06().doubleValue() > 0.0 ||
                        temp.getSubPay07().doubleValue() > 0.0 ||
                        temp.getSubPay08().doubleValue() > 0.0) {
                    bankFlag = true;
                }
                if (temp.getSubPay04().doubleValue() > 0.0 && fenqiId == 0) {
                    fenqiId = temp.getId();
                }
            }
        }
        // 判断可用退款方式
        if (areaInfoBO.getXTenant().longValue() == (JiujiTenantEnum.JIUJI_TENANT_JIUJI.getCode())
                && areaInfoBO.getAuthorizeId() == 1) {
            returnTypeList.add("余额");
        }
        if (crashFlag || areaInfoBO.getAuthorizeId() != 1) {
            returnTypeList.add("现金");
        }
        if (bankFlag || areaInfoBO.getAuthorizeId() != 1) {
            returnTypeList.add("银行转账");
        }
        // 微信秒退||支付宝秒退 需要判断时间以及当日限额
        LocalDateTime now = LocalDateTime.now();
        // 早上8:30到晚上11点
        boolean timeFlag = now.getHour() < 23 && ((now.getHour() == 8 && now.getMinute() > 30) || now.getHour() >= 9);
        if (timeFlag && type == 7 && subId != null && subId > 0) {
            if (!payWxenterpriseService.checkWxPayDailyLimitAmount(areaInfoBO.getXTenant())) {
                returnTypeList.add("微信秒退");
            }
            if (!alipayToAccountLogService.checkAlipayPayDailyLimitAmount()) {
                returnTypeList.add("支付宝秒退");
            }
        }
        // 建行分期退款方式判断
        List<String> payWayList = new ArrayList<>(2);
        if (type == 7) {
            payWayList = netpayRecordService.getReturnWayInstallmentInfo(type, subIds, null);
        } else if (type == 10) {
            payWayList = netpayRecordService.getReturnWayInstallmentInfo(type, null, subId);
        }
        if (payWayList != null && payWayList.size() == 1 && otherPayCount == 0) {
            String way = EnumUtil.getMessageByCode(PayWayToReturnTypeEnum.class, payWayList.get(0));
            if (StringUtils.isNotEmpty(way)) {
                returnTypeList.add(way);
            }
        }
        if (fenqiId != 0) {
            QueryWrapper<ShouyinOther> shouyinOtherQueryWrapper = new QueryWrapper<>();
            shouyinOtherQueryWrapper.lambda().eq(ShouyinOther::getShouyinid, fenqiId).eq(ShouyinOther::getType, 23);
            List<ShouyinOther> shouyinOtherList = shouyinOtherService.listSqlServer(shouyinOtherQueryWrapper);
            if (shouyinOtherList != null && shouyinOtherList.size() > 0) {
                returnTypeList.add("分期汇返回");
            }
        }

        List<String> effectiveReturnTypeList = smallproRefundExService.checkRefundType(subId, type);
        Map<String, Boolean> effectiveReturnTypeMap = new HashMap<>(effectiveReturnTypeList.size());
        effectiveReturnTypeList.forEach(bo -> {
            effectiveReturnTypeMap.put(bo, true);
        });

        returnTypeList.forEach(bo -> {
            SmallProReturnTypeBO temp = new SmallProReturnTypeBO();
            temp.setReturnName(bo);
            if (effectiveReturnTypeMap.get(bo.replace("返回", "")) != null
                    && effectiveReturnTypeMap.get(bo.replace("返回", ""))) {
                temp.setIsHavePayInfo(true);
            } else {
                temp.setIsHavePayInfo(false);
            }
            result.add(temp);
        });

        return result;
    }

    @Override
    public List<SmallProReturnTypeBO> getSmallProReturnWaysSaas(Integer subId, Integer type, Integer areaId) {
        if (subId == null || subId <= 0) {
            return null;
        }

        R<String> valueR = sysConfigClient.getValueByCode(SysConfigConstant.IN_WCF_HOST);
        String host = valueR.getData();
        String url = host + "/oaApi.svc/rest/getReturnWayJava";
        List<String> returnTypeList = new LinkedList<>();

        HashMap<String, String> params = new HashMap<>(4);
        params.put("id", subId.toString());
        params.put("type", type.toString());
        params.put("areaId", areaId.toString());
        params.put("sub_id", "1");

        List<SmallProReturnTypeBO> result = new LinkedList<>();
        try {
            String json = HttpRequest.post(url)
                    .body(JSON.toJSONString(params))
                    .execute().body();
            R<List<String>> jsonR = JSONObject.parseObject(json, R.class);
            returnTypeList = jsonR.getData();
        } catch (Exception e) {
            log.error("获取退款方式接口异常",e);
        }

        List<String> effectiveReturnTypeList = smallproRefundExService.checkRefundType(subId, type);
        Map<String, Boolean> effectiveReturnTypeMap = new HashMap<>(effectiveReturnTypeList.size());
        effectiveReturnTypeList.forEach(bo -> {
            switch (bo) {
                case "微信APP":
                    effectiveReturnTypeMap.put("微信",true);
                    break;
                case "扫码枪":
                    effectiveReturnTypeMap.put("兴业银行扫码",true);
                    break;
                default:
            }
            effectiveReturnTypeMap.put(bo, true);
        });

        returnTypeList.forEach(bo -> {
            SmallProReturnTypeBO temp = new SmallProReturnTypeBO();
            temp.setReturnName(bo);
            if (effectiveReturnTypeMap.get(bo.replace("返回", "")) != null
                    && effectiveReturnTypeMap.get(bo.replace("返回", ""))) {
                temp.setIsHavePayInfo(true);
            } else {
                temp.setIsHavePayInfo(false);
            }
            if (StringUtils.isNotEmpty(bo) && bo.contains("返回")){
                temp.setIsOriginPath(Boolean.TRUE);
            }
            result.add(temp);
        });

        return result;
    }

    @Override
    public Integer getReturnTypeByBusinessType(Integer businessType) {
        BusinessTypeEnum businessTypeEnum = Optional.ofNullable(EnumUtil.getEnumByCode(BusinessTypeEnum.class, businessType)).orElseThrow(() -> new CustomizeException("业务类型不存在"));
        Integer returnType = TuihuanKindEnum.TDJ.getCode();
        switch (businessTypeEnum) {
            case SALE_ORDER:
                returnType = TuihuanKindEnum.TDJ.getCode();
                break;
            case LP_ORDER:
                returnType = TuihuanKindEnum.TDJ_LP.getCode();
                break;
            default:
                break;
        }
        return returnType;
    }

    /**
     * 获取壳膜分类下所有上架的壳商品
     * @return
     */
    @Override
    public List<FilmAccessoriesV2Item> getSafeShellByPpid(Integer basketId,Integer areaId) {
        //根据cid查询商品信息
        List<Productinfo> productInfoList = productinfoService.list(new LambdaQueryWrapper<Productinfo>()
                .select(Productinfo::getPpriceid, Productinfo::getProductName, Productinfo::getMemberprice, Productinfo::getBarCode,Productinfo::getVipPrice,Productinfo::getCid,Productinfo::getBrandID)
                //品牌排除：苹果（1）、华为（7）、荣耀（1255）、三星（2）、一加（1154）、oppo（11）、vivo（14）、小米（9）、IQOO（3131）
                .eq(Productinfo::getDisplay, Boolean.TRUE).in(Productinfo::getCid, Arrays.asList(217, 218, 464, 43)).notIn(Productinfo::getBrandID, Arrays.asList(1, 7, 1255, 2, 1154, 11, 14, 9, 3131)));
        List<FilmAccessoriesV2Item> filmAccessoriesV2Items = new ArrayList<>();
        for (Productinfo productinfo : productInfoList) {
            FilmAccessoriesV2Item filmAccessoriesV2Item = new FilmAccessoriesV2Item();
            filmAccessoriesV2Item.setPpid(productinfo.getPpriceid());
            filmAccessoriesV2Item.setMakeUpPriceWay(NumberConstant.ONE);
            filmAccessoriesV2Item.setMakeUpPriceWayText("按差价金额补");
            filmAccessoriesV2Item.setMemberPrice(Convert.toBigDecimal(productinfo.getMemberprice()));
            filmAccessoriesV2Item.setIsMakeUpPrice(Boolean.TRUE);
            filmAccessoriesV2Item.setProductName(productinfo.getProductName());
            filmAccessoriesV2Item.setProductColor(productinfo.getProductColor());
            filmAccessoriesV2Item.setBarCode(productinfo.getBarCode());
            filmAccessoriesV2Items.add(filmAccessoriesV2Item);
        }
        List<Integer> ppidList = filmAccessoriesV2Items.stream().map(FilmAccessoriesV2Item::getPpid).filter(Objects::nonNull).collect(Collectors.toList());
        if (CollUtil.isEmpty(ppidList)){
            return filmAccessoriesV2Items;
        }
        //查询当前库存
        List<ProductKc> productKcList = productKcService.list(new LambdaQueryWrapper<ProductKc>().select(ProductKc::getPpriceid, ProductKc::getLeftCount)
                .in(ProductKc::getPpriceid, ppidList).eq(ProductKc::getAreaid, areaId));
        productKcList.forEach(pr -> filmAccessoriesV2Items.forEach(fi -> {
            if (Objects.equals(pr.getPpriceid(),fi.getPpid())){
                fi.setLeftCount(pr.getLeftCount());
            }}));
        //获取服务购买服务时的价格 查询服务价格
        FilmCardInfomationBO filmCardInfoByBasketId = smallproFilmCardService.getFilmCardInfoByBasketId(basketId);
        Basket basket = Optional.ofNullable(basketService.getByIdSqlServer(filmCardInfoByBasketId.getBasketId())).orElseGet(() -> {
            //查询不到从历史库查询
            AtomicReference<Basket> basketRef = new AtomicReference<>();
            new MultipleTransaction().execute(DataSourceConstants.OA_NEW_HIS, () -> basketRef.set(basketService.getByIdSqlServer(filmCardInfoByBasketId.getBasketId())))
                    .commit();
            return basketRef.get();
        });
        BigDecimal canRefundPrice = NumberUtil.toBigDecimal(basket.getPrice());
        //差价 = 商品价格 - 服务价格
        List<FilmAccessoriesV2Item> collect = filmAccessoriesV2Items.stream().filter(fi -> Optional.ofNullable(fi.getLeftCount()).orElse(NumberConstant.ZERO)>NumberConstant.ZERO).collect(Collectors.toList());
        if (CollUtil.isEmpty(collect)) {
            return new ArrayList<>();
        }
        collect.forEach(fi -> {
                    fi.setDifference(DecideUtil.iif(fi.getMemberPrice().subtract(canRefundPrice).compareTo(BigDecimal.ZERO)>0,fi.getMemberPrice().subtract(canRefundPrice),BigDecimal.ZERO));
                    fi.setMassage(String.format("服务价格 %s，商品价格 %s，需补差价 %s",canRefundPrice.setScale(SCALE, RoundingMode.HALF_UP),fi.getMemberPrice().setScale(SCALE, RoundingMode.HALF_UP),fi.getDifference().setScale(SCALE, RoundingMode.HALF_UP)));
                });
        return collect;
    }

    @Override
    public R<Page<SmallproReturnFactoryInfoBO>> getReturnFactoryInfoBOPage(Integer smallproId, Integer returnFactoryId, OaUserBO oaUserBO, Integer size, Integer current) {
        if (CommenUtil.isNullOrZero(smallproId) && CommenUtil.isNotNullZero(returnFactoryId)){
            smallproId = shouhouFanchangService.lambdaQuery().eq(ShouhouFanchang::getId, returnFactoryId)
                    .select(ShouhouFanchang::getSmallproid).list().stream().map(ShouhouFanchang::getSmallproid)
                    .findFirst().orElse(null);
        }
        if (CommenUtil.isNullOrZero(smallproId)) {
            return R.success(new Page<>());
        }
        List<SmallproReturnFactoryInfoBO> smallproReturnFactoryInfoBOList = null;
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<SmallproReturnFactoryInfoBO> smallproReturnFactoryInfoBOPage = new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>();
        Smallpro smallpro = smallproMapper.getByIdSqlServerByWriter(smallproId);
        Boolean authPart = authConfigService.isAuthPart(oaUserBO);
        if (Optional.ofNullable(authPart).orElse(Boolean.FALSE)) {
            //当前登录地区和小件单所属地区不在同一个授权体系下不能进行查看
            Integer currAreaId = CommonUtils.defaultIfNullOrZero(smallpro.getToAreaId(), smallpro.getAreaId());
            Areainfo areaInfo = areainfoService.getById(currAreaId);
            if (!Objects.equals(oaUserBO.getAuthorizeId(), areaInfo.getAuthorizeid())
                    && !Objects.equals(currAreaId, oaUserBO.getAreaId())) {
                return R.success(new Page<>());
            }
        }
        // 结果为null则直接返回null
        if (ObjectUtil.isEmpty(smallpro)) {
            return R.success(new Page<>());
        }
        Integer overDays = getOverDays(smallpro.getBuyDate().toInstant(ZoneOffset.of("+8")).toEpochMilli());
        // 获取小件订单关联ppriceId和basketId
        QueryWrapper<SmallproBill> smallproBillQueryWrapper = new QueryWrapper<>();
        smallproBillQueryWrapper.lambda().eq(SmallproBill::getSmallproID, smallproId);
        List<SmallproBill> smallproBillList =
                smallproBillService.getListWithSqlServer(smallproBillQueryWrapper);
        // 获取额外数据，包括订单和用户
        List<SmallproOrderInfoBO> smallproOrderInfoBOList =
                ((SmallproDetailsExServiceImpl) AopContext.currentProxy()).getSmallproOrderListInfo(smallproBillList,
                        smallpro.getSubId(), overDays);
        // 获取小件返厂件信息
        smallproReturnFactoryInfoBOList =
                getSmallproReturnFactoryInfoList(smallproOrderInfoBOList, smallproId,
                        smallProConstant.getYYYY_MM_DD_HH_MM_SS());
        if(CommenUtil.isNotNullZero(returnFactoryId)){
            smallproReturnFactoryInfoBOList = smallproReturnFactoryInfoBOList.stream()
                    .filter(rf -> ObjectUtil.equal(rf.getReturnFactoryId(),returnFactoryId))
                    .collect(Collectors.toList());
        }
        if (CollUtil.isNotEmpty(smallproReturnFactoryInfoBOList)) {
            RAMPager<SmallproReturnFactoryInfoBO> smallproReturnFactoryInfoBORAMPager = new RAMPager<>(smallproReturnFactoryInfoBOList, size);
            smallproReturnFactoryInfoBOPage.setRecords(smallproReturnFactoryInfoBORAMPager.page(DecideUtil.iif(current <= smallproReturnFactoryInfoBORAMPager.getPageCount(), current, smallproReturnFactoryInfoBORAMPager.getPageCount())));
            smallproReturnFactoryInfoBOPage.setSize(size);
            smallproReturnFactoryInfoBOPage.setCurrent(current);
            smallproReturnFactoryInfoBOPage.setPages(smallproReturnFactoryInfoBORAMPager.getPageCount());
            smallproReturnFactoryInfoBOPage.setTotal(Optional.of(smallproReturnFactoryInfoBOList.size()).orElse(0));
        }
        return R.success(smallproReturnFactoryInfoBOPage);
    }

    @Override
    public RefundSubInfoBo getSmallproMaxRefundPrice(Integer smallproId, TuihuanKindEnum tuihuanKind) {
        return getSmallproSubBuyInfo(smallproId, tuihuanKind).getRefundSubInfoBo();
    }

    @Override
    public SmallproSubBuyInfoBo getSmallproSubBuyInfo(Integer smallproId, TuihuanKindEnum tuihuanKind) {
        return SpringContextUtil.reqCache(() -> invokeGetSmallproSubBuyInfo(smallproId, tuihuanKind), RequestCacheKeys.GET_SMALLPRO_SUB_BUY_INFO, smallproId, tuihuanKind);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void returnAnnualPackage(ReturnAnnualPackageReq req) {
        // 获取小件单信息
        Integer smallProId = req.getId();
        Smallpro smallpro = smallproService.getByIdSqlServer(smallProId);
        LocalDateTime qujianDate = smallpro.getQujianDate();
        Integer serviceType = smallpro.getServiceType();
        OaUserBO userBO = Optional.ofNullable(currentRequestComponent.getCurrentStaffId()).orElseThrow(() -> new CustomizeException("当前登录人失效"));
        List<String> rankList = Optional.ofNullable(userBO.getRank()).orElse(new ArrayList<>());
        if(!rankList.contains("shsq")){
           throw new CustomizeException("当前登录人没有权限");
        }
        if(ObjectUtil.isNotNull(qujianDate) && SmallProServiceTypeEnum.SMALL_PRO_KIND_YEAR_CARD.getCode().equals(serviceType)){
            List<SmallproBill> smallproBillList = smallproBillService.lambdaQuery().eq(SmallproBill::getSmallproID, smallProId).list();
            if(CollectionUtils.isEmpty(smallproBillList) || smallproBillList.size()!=NumberConstant.ONE){
                RRExceptionHandler.logError("返回年包次数但是小件单接件数量异常："+req.getId(), req, null, smsService::sendOaMsgTo9JiMan);
                return;
            }
            TiemoCardUserLogService tiemoCardUserLogService = SpringUtil.getBean(TiemoCardUserLogService.class);
            List<TiemoCardUserLog> logList = tiemoCardUserLogService.lambdaQuery()
                    .eq(TiemoCardUserLog::getSmallProId, smallProId)
                    .orderByDesc(TiemoCardUserLog::getId)
                    .list();
            if(CollectionUtils.isEmpty(logList)){
                throw new CustomizeException("该年包已经返还，请勿重复返还");
            }
            TiemoCardUserLog tiemoCardUserLog = logList.get(NumberConstant.ZERO);
            TiemoCardService tiemoCardService = SpringUtil.getBean(TiemoCardService.class);
            Integer cardId = Optional.ofNullable(tiemoCardUserLog.getCardId()).orElseThrow(() -> new CustomizeException("年包返销cardId查询异常"));
            TiemoCard tiemoCard = Optional.ofNullable(tiemoCardService.getById(cardId)).orElse(new TiemoCard());
            Integer useCount = Optional.ofNullable(tiemoCard.getUseCount()).orElse(NumberConstant.ZERO);
            if(useCount<NumberConstant.ONE){
                throw new CustomizeException("未查询到该订单年包使用记录，请确认是否已返还。");
            }
            boolean update = tiemoCardService.lambdaUpdate()
                    .eq(TiemoCard::getId, tiemoCard.getId())
                    .eq(TiemoCard::getUseCount, useCount)
                    .set(TiemoCard::getUseCount, useCount - NumberConstant.ONE)
                    .update();
            if(!update){
                throw new CustomizeException("tiemoCard表扣减次数失败");
            }
            boolean tiemoCardUserLogUpdate = tiemoCardUserLogService.lambdaUpdate().eq(TiemoCardUserLog::getId, tiemoCardUserLog.getId())
                    .set(TiemoCardUserLog::getCardId, -cardId)
                    .update();
            if(!tiemoCardUserLogUpdate){
                throw new CustomizeException("年包返销日志更新失败");
            }
            //小件单日志记录
            smallproLogService.addLogs(smallProId, "返还本次年包次数，年包出险赠送质保换新服务作废", userBO.getUserName(), 0);
        } else {
            throw new CustomizeException("只有出险年包服务并且取机的才可以返回年包次数");
        }
    }

    /**
     * 查询参数校验
     * @param req
     */
    private void checkReq(SelectResistFilmReq req){
        String selectKeyValue = req.getSelectKeyValue();
        if(StringUtils.isNotEmpty(selectKeyValue)){
            Integer selectKeyType = req.getSelectKeyType();
            //单号 sku 进行正整数校验
            List<Integer> list = Arrays.asList(SelectResistFilmEnum.RECEIVED_GOODS_SKUID.getCode(), SelectResistFilmEnum.ORDER_NUMBER.getCode(), SelectResistFilmEnum.EXCHANGE_GOODS_SKUID.getCode());
            if(list.contains(selectKeyType)){
                if(!(NumberUtil.isNumber(selectKeyValue) && Integer.parseInt(selectKeyValue)>NumberConstant.ZERO)){
                    throw new CustomizeException("单号、SKUID只能输入正整数 范围在1-"+Integer.MAX_VALUE);
                }
            }
        }
        //如果cid为空那就默认为膜分类
        List<Integer> cidList = req.getCidList();
        if(CollectionUtils.isEmpty(cidList)){
            req.setCidList(categoryService.tieMoCids());
        }

    }


    /**
     * 角色时间限制
     * @param req
     */
    private void roleTimeLimit(SelectResistFilmReq req){
        //加入用户角色时间查询
        //角色数据查询
        R dataViewRes = BusinessUtil.checkDataViewScope(CheckDataViewScopeReq.builder().moduleEnum(RoleTermModuleEnum.PROCUREMENT_AND_SALES)
                .getStartTimeFun(req::getOrderTimeStart).getEndTimeFun(req::getOrderTimeEnd)
                .setStartTimeFun(req::setOrderTimeStart)
                .setEndTimeFun(req::setOrderTimeEnd)
                .build(), null);
        if (!dataViewRes.isSuccess()) {
            throw new CustomizeException(dataViewRes.getUserMsg());
        }
    }

    /**
     * 保护膜列表查询
     * @param req
     * @return
     */
    @Override
    public Page<SelectResistFilmRes> selectResistFilm(SelectResistFilmReq req) {
        Page<SelectResistFilmRes> page = new Page<>();
        //门店处理
        OaUserBO oaUserBO = currentRequestComponent.getCurrentStaffId();
        AreaBelongsDcHqD1AreaId backEndInfo = areainfoService.getAreaBelongsDcHqD1AreaId(oaUserBO.getAreaId());
        if (CollUtil.isEmpty(req.getAreaIdList())) {
            if (!Arrays.asList(backEndInfo.getD1AreaId(), backEndInfo.getDcAreaId(), backEndInfo.getH1AreaId()).contains(oaUserBO.getAreaId())
                    && !CollUtil.contains(backEndInfo.getHqAreaIds(), oaUserBO.getAreaId())) {
                req.setAreaIdList(Collections.singletonList(oaUserBO.getAreaId().toString()));
            }
        }
        //角色数据查询
        roleTimeLimit(req);
        //如果过是单号查询  那就过滤所有条件
        if(SelectResistFilmEnum.ORDER_NUMBER.getCode().equals(req.getSelectKeyType()) && StringUtils.isNotEmpty(req.getSelectKeyValue())){
            SelectResistFilmReq reqNew = new SelectResistFilmReq();
            reqNew.setCurrent(req.getCurrent());
            reqNew.setSize(req.getSize());
            reqNew.setSelectKeyValue(req.getSelectKeyValue());
            reqNew.setSelectKeyType(SelectResistFilmEnum.ORDER_NUMBER.getCode());
            req = reqNew;
        }

        page.setCurrent(req.getCurrent());
        page.setSize(req.getSize());
        page.setDesc("s.id");
        //查询参数校验
        checkReq(req);
        //原始数据查询
        page = smallproMapper.selectResistFilmPage(page, req);
        List<SelectResistFilmRes> records = page.getRecords();
        //判断如果数据查询为空  那就进行以下数据封装
        if(CollectionUtils.isEmpty(records)){
            return page;
        }
        Boolean isExport = Optional.ofNullable(req.getIsExport()).orElse(Boolean.FALSE);
        //收集所有的小件单号
        List<Integer> smallProIds = records.stream().map(SelectResistFilmRes::getSmallProId).collect(Collectors.toList());
        //串号大图获取
        Map<Integer, List<Attachments>> attachmentMap = CommonUtils.bigDataInQuery(smallProIds,
                        ids -> attachmentsService.listAttachments(AttachmentsEnum.BIG_IMEI_ATTACHMENTS.getCode(), CollUtil.newHashSet(ids), null))
                .stream().collect(Collectors.groupingBy(Attachments::getLinkedID));
        String data = sysConfigClient.getValueByCode(SysConfigConstant.IMG_URL).getData();
        //如果过不进行损耗数量统计的
        Map<Integer, SelectResistFilmRes> lossMap = new HashMap<>();
        if(ObjectUtil.isNull(req.getLossCountMax()) || ObjectUtil.isNull(req.getLossCountMin())){
            lossMap = CommonUtils.bigDataInQuery(smallProIds,
                            ids -> smallproMapper.selectLossCountSmallProId(ids))
                    .stream().collect(Collectors.toMap(SelectResistFilmRes::getSmallProId, Function.identity(), (n1, n2) -> n2));
        }
        for (SelectResistFilmRes item : records){
            item.setStateValue(SmallProStatsEnum.getMessageByCode(item.getState()));
            item.setServiceTypeValue(SelectResistFilmServiceTypeEnum.getMessageByCode(item.getServiceType()));
            if(ObjectUtil.isNull(req.getLossCountMax()) || ObjectUtil.isNull(req.getLossCountMin())){
                SelectResistFilmRes selectResistFilmRes = lossMap.getOrDefault(item.getSmallProId(), new SelectResistFilmRes());
                item.setLossCount(selectResistFilmRes.getLossCount());
            }
            //如果 不是导出的情况那才进行日志数据的封装
            if(!isExport){
                List<SmallproOperationInfoBO> operationInfoList = smallproService.getSmallproOperationLogs(item.getSmallProId(), 0);
                item.setOperationInfoList(operationInfoList);
                String imageLinkSmall = (StringUtils.isEmpty(item.getFid())) ? "" : StrUtil.indexedFormat("{0}/newstatic/{1}", data, item.getFid());
                ImeiImageInfo imeiImageInfo = new ImeiImageInfo();
                imeiImageInfo.setImageLinkSmall(imageLinkSmall);
                List<Attachments> attachments = attachmentMap.get(item.getSmallProId());
                if(CollectionUtils.isNotEmpty(attachments)){
                    String fid = Optional.ofNullable(attachments.get(NumberConstant.ZERO)).orElse(new Attachments()).getFid();
                    imeiImageInfo.setImageLinkBig(StrUtil.indexedFormat("{0}/newstatic/{1}", data, fid));
                }
                item.setImeiImageInfo(imeiImageInfo);
            }
        }
        return page;
    }

    /**
     * 保护膜接件列表导出
     * @param req
     * @param response
     */
    @Override
    public void exportResistFilm(SelectResistFilmReq req, HttpServletResponse response){
        //设置默认导出数据
        req.setIsExport(Boolean.TRUE);
        req.setSize(SmallproWxServiceImpl.MAX_EXPORT_NUMBER.longValue());
        req.setCurrent(NumberConstant.ONE.longValue());
        //导出数据查询
        Page<SelectResistFilmRes> page = selectResistFilm(req);
        List<SelectResistFilmRes> records = page.getRecords();
        if(CollectionUtils.isEmpty(records)){
            throw new CustomizeException("导出数据为空");
        }
        //导出格式转换
        records.forEach(item->{
            item.setCreateTimeValue(item.getCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        });

        //response为HttpServletResponse对象
        response.setContentType("application/vnd.ms-excel;charset=utf-8");
        //test.xls是弹出下载对话框的文件名，不能为中文，中文请自行编码
        response.setHeader("Content-Disposition", String.format("attachment;filename=%s_%s.xls", URLUtil.encode("贴膜查询")
                , LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmm"))));
        log.warn("保护膜接件列表导出: {}", JSON.toJSONString(req));
        // 通过工具类创建writer，默认创建xls格式
        ExcelWriter writer = ExcelUtil.getWriter();
        //自定义标题别名
        writer.addHeaderAlias("smallProId","单号");
        writer.addHeaderAlias("area","接件地区");
        writer.addHeaderAlias("receivedGoods","接件商品");
        writer.addHeaderAlias("exchangeGoods","置换商品");
        writer.addHeaderAlias("serviceTypeValue","服务类型");
        writer.addHeaderAlias("supplementAmount","补差金额");
        writer.addHeaderAlias("lossCount","损耗数量");
        writer.addHeaderAlias("recipient","接件人");
        writer.addHeaderAlias("createTimeValue","下单时间");
        writer.addHeaderAlias("stateValue","状态");
        try {
            writer.setOnlyAlias(true);
            // 一次性写出内容，使用默认样式，强制输出标题
            writer.write(records, true);
            //out为OutputStream，需要写出到的目标流
            try {
                writer.flush(response.getOutputStream(), false);
            } catch (IOException e) {
                log.error("贴膜查询导出IO异常",e);
                throw new CustomizeException("贴膜查询导出IO异常");
            }
        } finally {
            // 关闭writer，释放内存
            writer.close();
        }
    }

    @Override
    public LossDetailsRes selectLossDetails(LossDetailsReq req) {
        LossDetailsRes lossDetailsRes = new LossDetailsRes();
        List<LossDetailsInfo> lossDetailsInfos = smallproMapper.selectLossDetails(req);
        if(CollectionUtils.isEmpty(lossDetailsInfos)){
            return lossDetailsRes;
        }
        //状态封装
        lossDetailsInfos.forEach(item->{
            item.setStateValue(SmallProStatsEnum.getMessageByCode(item.getState()));
        });
        lossDetailsRes.setDetailsInfoList(lossDetailsInfos);
        return lossDetailsRes;
    }

    public SmallproSubBuyInfoBo invokeGetSmallproSubBuyInfo(Integer smallproId, TuihuanKindEnum tuihuanKind) {
        SmallproSubBuyInfoBo smallproSubBuyInfoBo = new SmallproSubBuyInfoBo();
        RefundSubInfoBo refundSubInfoBo = new RefundSubInfoBo().setMaxRefundPrice(BigDecimal.ZERO)
                .setBusinessType(BusinessTypeEnum.SALE_ORDER.getCode()).setSubCheck(SubCheckStatusEnum.FINISHED.getCode());
        smallproSubBuyInfoBo.setRefundSubInfoBo(refundSubInfoBo);
        // 获取小件单信息
        Smallpro smallpro = smallproService.getByIdSqlServer(smallproId);
        if(smallpro == null){
            throw new CustomizeException(StrUtil.format("小件单[{}]不存在",smallproId));
        }
        smallproSubBuyInfoBo.setSmallpro(smallpro);
        refundSubInfoBo.setOrderId(smallpro.getSubId())
                .setShouhouAreaId(CommenUtil.currAreaId(smallpro.getToAreaId(),smallpro.getAreaId()))
                .setUserId(smallpro.getUserId()).setIsNotNeedValid(StrUtil.isNotBlank(smallpro.getCodeMsg()));
        //设置已付金额
        Optional<Sub> subOpt = CommenUtil.autoQueryHist(() -> subService.lambdaQuery().eq(Sub::getSubId, smallpro.getSubId()).list(), MTableInfoEnum.SUB, smallpro.getSubId())
                .stream().findFirst();
        TuihuanKindEnum tuihuanKindEnum;
        if(SmallProKindEnum.SMALL_PRO_KIND_EXCHANGE.getCode().equals(smallpro.getKind())){
            tuihuanKindEnum = TuihuanKindEnum.SMALL_PRO_HQTXH;
        }else{
            tuihuanKindEnum = TuihuanKindEnum.TPJ;
        }
        subOpt.ifPresent(sub ->{
            refundSubInfoBo.setYifuM(sub.getYifuM());
            refundSubInfoBo.setAllYifuM(RefundMoneyUtil.getAllYifuM(sub.getSubId(), sub.getSubCheck(), sub.getYifuM(), tuihuanKindEnum));
            refundSubInfoBo.setAreaId(sub.getAreaId());
            // 设置交易完成时间, 三方获取交易完成之后的退订
            SpringContextUtil.getRequest().ifPresent(req -> req.setAttribute(RequestAttrKeys.GROUP_REFUND_ORDER_TRADEDATE, sub.getTradeDate1()));
        });
        smallproSubBuyInfoBo.setSubOpt(subOpt);

        // 获取小件订单关联ppriceId和basketId
        List<SmallproBill> smallproBillList = smallproBillService.lambdaQuery().eq(SmallproBill::getSmallproID, smallproId).list();
        smallproSubBuyInfoBo.setSmallproBillList(smallproBillList);
        // 查询商品信息、订单信息逻辑
        int overDays = Convert.toInt(ChronoUnit.DAYS.between(smallpro.getBuyDate(),smallpro.getInDate()));
        List<SmallproOrderInfoBO> smallproOrderListInfos = smallproDetailsExService.getSmallproOrderListInfo(smallproBillList, smallpro.getSubId(), overDays);
        smallproOrderListInfos.stream().map(soli -> ObjectUtil.defaultIfNull(soli.getPriceReturn(), BigDecimal.ZERO)
                .multiply(new BigDecimal(ObjectUtil.defaultIfNull(soli.getProductCount(), 0))))
                .filter(Objects::nonNull).reduce(BigDecimal::add)
                .ifPresent(refundSubInfoBo::setTotalPrice);
        for(SmallproOrderInfoBO soi: smallproOrderListInfos){
            Integer targetPpriceId = soi.getTargetPpriceId();
            Integer smallproBillBasketId = soi.getBasketId();
            LocalDateTime buyTime = smallpro.getBuyDate();
            BigDecimal productCount = new BigDecimal(ObjectUtil.defaultIfNull(soi.getProductCount(), 0));
            BigDecimal linePrice = ObjectUtil.defaultIfNull(soi.getPriceReturn(), BigDecimal.ZERO).multiply(productCount);
            boolean isServiceProduct = isServiceProduct(smallproBillBasketId, targetPpriceId);
            //服务商品进行折价处理
            if (isServiceProduct) {
                BigDecimal customerMaxRefundPrice = Optional.ofNullable(XtenantEnum.isJiujiXtenant()).filter(Boolean::booleanValue)
                .map(isJiuji -> RefundMoneyUtil.getCustomerMaxRefundPrice(refundSubInfoBo, tuihuanKindEnum, linePrice, BigDecimal.ZERO)).orElse(linePrice);
                refundSubInfoBo.setMaxRefundPrice(refundSubInfoBo.getMaxRefundPrice()
                        .add(smallProAdapterService.calculateDiscountAmount(targetPpriceId, buyTime, customerMaxRefundPrice,soi.getBasketId()).multiply(productCount)
                        .add(NumberUtil.max(linePrice.subtract(customerMaxRefundPrice),BigDecimal.ZERO))));
            } else if (isYearCardProduct(targetPpriceId)) {
                FilmCardInfomationBO filmCardInfomation = smallproFilmCardService.getFilmCardInfoByBasketIdByWrite(smallproBillBasketId);
                Boolean isCanRefund = Optional.ofNullable(filmCardInfomation).map(FilmCardInfomationBO::getIsCanRefund).orElse(false);
                if (isCanRefund && filmCardInfomation.getIsCanRefund()) {
                    refundSubInfoBo.setMaxRefundPrice(refundSubInfoBo.getMaxRefundPrice()
                            .add(BigDecimal.valueOf(filmCardInfomation.getRefundAmount()).multiply(productCount)));
                }
            }else{
                refundSubInfoBo.setMaxRefundPrice(refundSubInfoBo.getMaxRefundPrice().add(linePrice));
            }
        }
        //计算成本
        List<Integer> basketIdList = smallproBillList.stream().map(SmallproBill::getBasketId).collect(Collectors.toList());
        refundSubInfoBo.setBasketIds(basketIdList);
        List<SmallproRefundPriceInfoBO> priceInfoBOList = smallproMapper.getRefundPrice(smallpro.getSubId(), basketIdList);
        //获取上一次换货的出库成本
        Map<Integer, LastExchangeSmallproBo> lastExchangeSmallproMap = SpringUtil.getBean(SmallproExchangePurchaseService.class).listLastChangeSmallpro(smallproId, smallpro.getSubId(),
                basketIdList.stream().collect(Collectors.toSet())).stream().collect(Collectors
                .toMap(LastExchangeSmallproBo::getBasketId, Function.identity(), (v1, v2) -> v1));

        //成本只算单个,不乘以数量
        priceInfoBOList.stream()
                //如果存在换货,取上一次换货的出库成本
                .map(sprpi -> Optional.ofNullable(lastExchangeSmallproMap.get(sprpi.getBasketId()))
                            .map(LastExchangeSmallproBo::getInprice).orElseGet(() -> Convert.toBigDecimal(sprpi.getInPrice(),BigDecimal.ZERO))
                ).reduce(BigDecimal::add).ifPresent(refundSubInfoBo::setInPrice);
        return smallproSubBuyInfoBo;
    }

    // endregion

    // region 更新小件接件单情况选项 updateSmallproKindType

    /**
     * description: <更新小件接件单情况选项>
     * translation: <Update smallpro pick up case options>
     *
     * @param smallproId 小件接件单ID
     * @param kindType   情况选项
     * @return void
     * <AUTHOR>
     * @date 16:19 2019/12/16
     * @since 1.0.0
     **/
    @DS("")
    private boolean updateSmallproKindType(Integer smallproId, Integer kindType) {
        QueryWrapper<SmallproKindtype> smallproKindtypeQueryWrapper = new QueryWrapper<>();
        smallproKindtypeQueryWrapper.lambda().eq(SmallproKindtype::getSmallproid, smallproId);
        SmallproKindtype smallproKindtype = smallproKindtypeService.getOneSqlServer(smallproKindtypeQueryWrapper);
        boolean flag = true;
        if (smallproKindtype == null) {
            smallproKindtype = new SmallproKindtype();
            smallproKindtype.setSmallproid(smallproId)
                    .setKind(kindType);
            flag = smallproKindtypeService.save(smallproKindtype);
        } else {
            smallproKindtype.setKind(kindType);
            flag = smallproKindtypeService.updateById(smallproKindtype);
        }
        return flag;
    }

    // endregion

    // region 通过Ppid获取对应的商品名称 getProductNameByPpriceId

    /**
     * description: <通过Ppid获取对应的商品名称>
     * translation: <Get the corresponding product name through Ppid>
     *
     * @param ppriceId Ppid
     * @return com.jiuji.oa.afterservice.smallpro.bo.SmallproProductInfoBO
     * <AUTHOR>
     * @date 9:24 2020/4/28
     * @since 1.0.0
     **/
    private SmallproProductInfoBO getProductNameByPpriceId(Integer ppriceId) {
        SmallproProductInfoBO smallproProductInfoBO = smallproMapper.getProductInfoByPpriceId(ppriceId);
        if (smallproProductInfoBO == null) {
            return null;
        }
        StringBuilder resultBuilder = new StringBuilder("商品名称：");
        resultBuilder.append(smallproProductInfoBO.getProductName()).append(" ")
                .append(smallproProductInfoBO.getProductColor())
                .append("，条码：").append(smallproProductInfoBO.getBarCode());
        smallproProductInfoBO.setDisplay(resultBuilder.toString());
        return smallproProductInfoBO;
    }

    // endregion

    // region 补偿操作-如果当前订单接件有未完成但是实际已完成的订单，默认改为已完成并不跳转链接 checkOldSmallpro

    /**
     * description: <补偿操作-如果当前订单接件有未完成但是实际已完成的订单，默认改为已完成并不跳转链接>
     * translation: <Compensation operation-If the current order has uncompleted but actually completed orders, the
     * default is completed and does not jump links>
     *
     * @param smallproList 小件列表
     * @return java.util.List<com.jiuji.oa.afterservice.smallpro.po.Smallpro>
     * <AUTHOR>
     * @date 9:36 2020/4/30
     * @since 1.0.0
     **/
    public List<Smallpro> checkOldSmallpro(List<Smallpro> smallproList) {
        if (smallproList == null || smallproList.size() <= 0) {
            return new ArrayList<>();
        }
        List<Smallpro> result = new ArrayList<>(smallproList.size());
        for (Smallpro smallpro : smallproList) {
            QueryWrapper<ShouhouFanchang> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(ShouhouFanchang::getSmallproid, smallpro.getId());
            List<ShouhouFanchang> tempList = shouhouFanchangService.listSqlServer(queryWrapper);
            if (tempList != null && tempList.size() > 0) {
                smallpro.setStats(SmallProStatsEnum.SMALL_PRO_STATS_PROCESS_COMPLETED.getCode());
                smallproService.updateById(smallpro);
            } else {
                result.add(smallpro);
                return result;
            }
        }
        return result;
    }

    private void buildWebConfigTime(WebTimeConfig webTimeConfig, SmallproOrderInfoBO orderInfo) {
        if (orderInfo.getFilmCardInfo() == null || CommenUtil.isNullOrZero(orderInfo.getFilmCardInfo().getSubId()) || !Objects.equals(orderInfo.getIsServiceProduct(), Boolean.TRUE)) {
            return;
        }
        webTimeConfig.setIsSupportReturn(orderInfo.getFilmCardInfo().getIsCanRefund())
                .setIsSupportExchange(orderInfo.getFilmCardInfo().getIsNotExpired());
        if (orderInfo.getFilmCardInfo().getIsCanRefund() != null && orderInfo.getFilmCardInfo().getIsCanRefund()) {
            webTimeConfig.setReturnEndDate(orderInfo.getFilmCardInfo().getEndTime());
        }
        if (orderInfo.getFilmCardInfo().getIsNotExpired() != null && orderInfo.getFilmCardInfo().getIsNotExpired()) {
            webTimeConfig.setExchangeEndTime(orderInfo.getFilmCardInfo().getEndTime());
        }
    }

    /**
     * 查询小件单是否是服务商品
     * @param smallproId
     * @return
     */
    @Override
    public boolean getSmallproIsServiceProduct(Integer smallproId) {
        if (smallproId == null) {
            return false;
        }
        QueryWrapper<SmallproBill> smallproBillQueryWrapper = new QueryWrapper<>();
        smallproBillQueryWrapper.lambda().eq(SmallproBill::getSmallproID, smallproId);
        List<SmallproBill> smallproBillList = smallproBillService.getListWithSqlServer(smallproBillQueryWrapper);
        if (CollUtil.isEmpty(smallproBillList)) {
            return false;
        }
        return smallproBillList.stream().anyMatch(smallproBill -> isServiceProduct(smallproBill.getBasketId(), Convert.toInt(smallproBill.getPpriceid())) || isYearCardProduct(Convert.toInt(smallproBill.getPpriceid())));
    }
}

