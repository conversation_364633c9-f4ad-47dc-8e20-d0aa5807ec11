package com.jiuji.oa.afterservice.delayQueuePush.appointmentFormStrategy.impl;

import cn.hutool.json.JSONUtil;
import com.jiuji.oa.afterservice.common.enums.SmsReceiverClassfyEnum;
import com.jiuji.oa.afterservice.delayQueuePush.appointmentFormStrategy.AppointmentFormPushStrategy;
import com.jiuji.oa.afterservice.delayQueuePush.service.impl.AppointmentFormConsumerServiceImpl;
import com.jiuji.oa.afterservice.delayQueuePush.vo.AppointmentFormPush;
import com.jiuji.tc.utils.constants.NumberConstant;
import com.meitu.platform.lmstfy.client.LmstfyClient;
import com.meitu.platform.lmstfy.exception.LmstfyException;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.LocalDateTime;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;


/**
 * 处理方式为预约到店且订单类型为大件预约单
 * <AUTHOR>
 */
@Slf4j
@Service(value = "LargeSizedObjectQueueStrategy")
public class LargeSizedObjectQueueStrategy extends CommonStrategy implements AppointmentFormPushStrategy {


    @Value("${lmstfy.mult.first-lmstfy-client.largeSizedObjectQueue}")
    private String largeSizedObjectQueue;
    @Value("${lmstfy.mult.first-lmstfy-client.largeSizedObjectAccumulationQueue}")
    private String largeSizedObjectAccumulationQueue;
    @Resource(name = "firstLmstfyClient")
    private LmstfyClient firstLmstfyClient;

    /**
     * 超时未进行业务确认的预约单，超30分钟未业务确认给预约单门店对应区域的“售后主管”推送超时信息，
     * 超60分钟未确认给预约单门店对应区域的“售后主管”和“售后经理”推送超时信息。
     * 后续若还未确认每隔1小时推送一次超时信息 5次封顶
     * @param appointmentFormPush
     */
    @Override
    public void pushDataDelayDetection(AppointmentFormPush appointmentFormPush) {
        try {
            //计算延迟队列时间和统计推送次数
            Integer calculationDelaySecond = this.calculationDelaySecond(appointmentFormPush);
            String publish = firstLmstfyClient.publish(largeSizedObjectQueue, JSONUtil.toJsonStr(appointmentFormPush).getBytes(), 0, (short) 1, calculationDelaySecond);
            log.warn("约到店且订单类型为大件预约单延迟队列推送成功，队列名称{}，推送参数{}，返回结果{}",largeSizedObjectQueue,JSONUtil.toJsonStr(appointmentFormPush),publish);
        } catch (LmstfyException e){
            appointmentFormPush.setPushNumber(appointmentFormPush.getPushNumber()-NumberConstant.ONE);
            log.error("约到店且订单类型为大件预约单延迟队列推送异常，队列名称{}推送参数{}",largeSizedObjectQueue,JSONUtil.toJsonStr(appointmentFormPush),e);
        }
    }

    @Override
    public void pushData(AppointmentFormPush appointmentFormPush) {
        //判断当前是否为0点到9点30
        Boolean accumulationTime = super.isAccumulationTime();
        log.warn("当前时间为：{}，比较开始时间：{}，比较结束时间：{},订单内容：{}", LocalDateTime.now().toString(),
                AppointmentFormConsumerServiceImpl.getTimeInfo().getStartTime().toString(),
                AppointmentFormConsumerServiceImpl.getTimeInfo().getEndTime().toString(),
                appointmentFormPush.toString());
        if(accumulationTime){
            //在晚上0点到9点30的订单把订单推送到积压队列
            try {
                //计算延迟队列时间和统计推送次数
                Integer calculationDelaySecond = super.calculationDelaySecond(appointmentFormPush);
                String publish = firstLmstfyClient.publish(largeSizedObjectAccumulationQueue, JSONUtil.toJsonStr(appointmentFormPush).getBytes(), 0, (short) 1, calculationDelaySecond);
                log.warn("约到店且订单类型为大件预约单延迟队列（堆积）推送成功，队列名称{}推送参数{}，返回结果{}",largeSizedObjectAccumulationQueue,JSONUtil.toJsonStr(appointmentFormPush),publish);
            } catch (LmstfyException e){
                appointmentFormPush.setPushNumber(appointmentFormPush.getPushNumber()- NumberConstant.ONE);
                log.error("约到店且订单类型为大件预约单延迟队列（堆积）推送异常，队列名称{}推送参数{}",largeSizedObjectAccumulationQueue,JSONUtil.toJsonStr(appointmentFormPush),e);
            }
        } else {
            //直接推送OA信息
            pushDirectMsg(appointmentFormPush, SmsReceiverClassfyEnum.MOBILE_YYDD_QR_SEND);
            //消息推送之后进行消息延迟监控
            pushDataDelayDetection(appointmentFormPush);
        }

    }
}
