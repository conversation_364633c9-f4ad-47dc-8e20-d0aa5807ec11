package com.jiuji.oa.afterservice.smallpro.po.refund;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 小件换货商品配置
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("small_refund_product_config")
public class SmallRefundProductConfigPo {

    /**
     * 自增长
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    /**
     * 关联small_refund_config表
     *
     * @see SmallRefundConfigPo
     */
    private Integer fkConfigId;
    /**
     * 配置类型(1 品牌 2分类 4 SPU 5 SKU)
     */
    private Integer configType;
    /**
     * 配置值
     */
    private Integer configValue;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 创建人
     */
    private String createUser;
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    /**
     * 是否删除
     */
    private Boolean isDel;

    /**
     * 可换货商品配置方式(1 品牌 2分类 3 品牌+分类 4 SPU 5 SKU)
     */
    @Getter
    @AllArgsConstructor
    public enum ConfigTypeEnum implements CodeMessageEnumInterface {
        BRAND(1,"品牌"),
        CATEGORY(2,"分类"),
        SPU(4,"SPU"),
        SKU(5,"SKU")
        ;

        /**
         * 编码
         */
        private Integer code;

        /**
         * 编码对应信息
         */
        private String message;
    }
}