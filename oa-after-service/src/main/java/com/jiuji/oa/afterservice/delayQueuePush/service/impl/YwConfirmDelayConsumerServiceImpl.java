package com.jiuji.oa.afterservice.delayQueuePush.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.jiuji.infra.lmstfy.anotation.LmstfyConsume;
import com.jiuji.oa.afterservice.bigpro.enums.OaMesTypeEnum;
import com.jiuji.oa.afterservice.bigpro.po.ShouhouYuyue;
import com.jiuji.oa.afterservice.bigpro.service.ShouhouYuyueService;
import com.jiuji.oa.afterservice.bigpro.service.SmsService;
import com.jiuji.oa.afterservice.common.exception.CustomizeException;
import com.jiuji.oa.afterservice.common.exception.RRExceptionHandler;
import com.jiuji.oa.afterservice.delayQueuePush.vo.YwConfirmDelayPush;

import com.jiuji.oa.afterservice.other.bo.LogisticsRecipientBO;
import com.jiuji.oa.afterservice.other.dao.ReceivePersonConfigMapper;
import com.meitu.platform.lmstfy.Job;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 业务确认延迟队列消费者
 * <AUTHOR>
 */
@Slf4j
@Service
public class YwConfirmDelayConsumerServiceImpl {

    private static final String YW_CONFIRM_DELAY_QUEUE = "${lmstfy.mult.first-lmstfy-client.ywConfirmDelayQueue}";

    @Resource
    private ShouhouYuyueService yuyueService;
    @Resource
    private SmsService smsService;
    @Resource
    private ReceivePersonConfigMapper receivePersonConfigMapper;

    /**
     * 延迟队列监听消费
     * 业务确认延迟队列消费
     * @param job
     */
    @LmstfyConsume(queues = YW_CONFIRM_DELAY_QUEUE, clientBeanName = "firstLmstfyClient")
    public void consumeYwConfirmDelayQueue(Job job) {
        try {
            log.warn("YW_CONFIRM_DELAY_QUEUE消费到数据：{}", JSONUtil.toJsonStr(job));
            String data = Optional.ofNullable(job).orElse(new Job()).getData();
            if (StrUtil.isEmpty(data)) {
                throw new CustomizeException("YW_CONFIRM_DELAY_QUEUE消费到空数据");
            }
            YwConfirmDelayPush ywConfirmDelayPush = JSONUtil.toBean(data, YwConfirmDelayPush.class);
            log.warn("YW_CONFIRM_DELAY_QUEUE消费到数据详情：{}", JSONUtil.toJsonStr(ywConfirmDelayPush));
            Integer yuyueId = ywConfirmDelayPush.getYuyueId();
            if (ObjectUtil.isNull(yuyueId)) {
                throw new CustomizeException("YW_CONFIRM_DELAY_QUEUE消费到数据，预约单ID为空");
            }
            ShouhouYuyue shouhouYuyue = Optional.ofNullable(yuyueService.getById(yuyueId)).orElseThrow(() -> new CustomizeException("查询预约单为空"));
            String checkUser = shouhouYuyue.getCheckUser();
            if(StrUtil.isEmpty(checkUser)){
                throw new CustomizeException("预约单无业务确认人");
            }
            //todo 判断业务确认人当前时间 没有考情或者暂离

            // 格式化时间为10:00的形式
            DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("HH:mm");
            String stime = shouhouYuyue.getStime().format(timeFormatter);
            String etime = shouhouYuyue.getEtime().format(timeFormatter);
            String msg = String.format("您负责的门店存在即将履约的售后预约单，单号%s，到店时间：%s-%s，业务负责人：%s（未在岗/暂离）（注;未在岗和暂离看打卡情况进行区分显示），请及时介入处理！", shouhouYuyue.getId(), stime, etime, checkUser);
            Integer areaId = Optional.ofNullable(shouhouYuyue.getAreaid()).orElseThrow(() -> new CustomizeException("门店ID为空"));
            List<LogisticsRecipientBO> logisticsRecipientBOS = receivePersonConfigMapper.getExecutiveDirectorAndShopowner(areaId);
            if(CollUtil.isEmpty(logisticsRecipientBOS)){
                throw new CustomizeException("门店无店长主管");
            }
            String ch999ids = logisticsRecipientBOS.stream().map(e -> Convert.toStr(e.getReceiveUserId())).collect(Collectors.joining(","));
            smsService.sendOaMsg(msg, "", ch999ids, OaMesTypeEnum.DDTZ);
            String ch999UserName = logisticsRecipientBOS.stream().map(e -> Convert.toStr(e.getReceiveUserName())).collect(Collectors.joining(","));
            log.warn("业务确认延迟队列消费成功，预约单ID：{},推送内容：{},推送人：{}", yuyueId, msg,ch999UserName);
        } catch (Exception e) {
            RRExceptionHandler.logError("YW_CONFIRM_DELAY_QUEUE消费异常", job, e, smsService::sendOaMsgTo9JiMan);
        }
    }
}
