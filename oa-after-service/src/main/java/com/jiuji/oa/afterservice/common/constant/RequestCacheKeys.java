package com.jiuji.oa.afterservice.common.constant;

/**
 * 单次请求级别的缓存
 * <AUTHOR>
 * @since 2023/9/27 16:13
 */
public class RequestCacheKeys {
    /**
     * 根据订单查询运营商配置信息
     */
    public static final String SELECT_OPERATOR_BUSINESS = "selectOperatorBusiness:{}";
    /**
     * 获取抖音团购最后一次使用记录 启用线程级别的缓存
     */
    public static final String GET_LAST_DOU_YIN_COUPON_LOG = "getLastDouYinCouponLog:{}_{}";
    /**
     * 用户最近N天打款次数、打款总额（微信/支付宝）
     */
    public static final String GET_LAST_DAYS_PAY_COUNT = "getLastDaysPayCount:{}_{}_{}";
    /**
     * 用户最近N天打款次数、打款总额（微信/支付宝）
     */
    public static final String GET_CARD_PAY_INFO = "getCardPayInfo:{}";
    /**
     * 获取购买价格 oa999DAL\ShouhouTuihuan.cs::GetTuihuanBuyPrice
     */
    public static final String GET_BUY_PRICE_AND_SUB_INFO = "getBuyPriceAndSubInfo:{}:{}";
    /**
     * 通过售后ID获取售后信息
     */
    public static final String REFUND_MACHINE_SERVICE_GET_SHOUHOU_BY_ID = "RefundMachineService:getShouhouById:{}";
    /**
     * 获取网络支付信息
     */
    public static final String GET_ALIPAY_PAY_INFO = "NetPayOriginWayService:getAlipayPayInfo:{}";
    /**
     * 获取订单下所有三方支付信息(只在线程内缓)
     */
    public static final String THIRD_ORIGIN_WAY_SERVICE_LIST_ALL = "ThirdOriginWayService.listAll:{}_{}_{}";
    /**
     * 秒退金额的限制
     */
    public static final String SECONDS_REFUND_LIMIT = "secondsRefundLimit:{}";
    /**
     * 获取贴膜信息的方法
     */
    public static final String GET_FILM_CARD_INFOMATION = "SmallproFilmCardService.getFilmCardInfomation:{}_{}";
    /**
     *获取售后时间配置, 只开启线程级别的缓存
     */
    public static final String GET_AFTER_TIME_CFG_MAP = "getAfterTimeCfgMap:{}";
    /**
     *获取不可退金额，开启线程级别的缓存
     */
    public static final String GET_NOT_REFUND_MONEY = "GET_NOT_REFUND_MONEY:{}:{}:{}";
    public static final String LIST_LP_BASKETS = "listLpBaskets: {}";
    public static final String LIST_SUB_BASKETS = "listSubBaskets: {}";
    /**
     *获取父子订单，开启线程级别的缓存
     */
    public static final String PARENT_CHILD_ORDER_IDS = "parentChildOrderIds:{}:{}";

    /**
     * 获取订单下所有其他支付信息(只在线程内缓)
     */
    public static final String OTHER_REFUND_SERVICE_LIST_ALL = "OtherRefundService.listAll:{}_{}";
    /**
     *获取最近为完成的退款信息，开启线程级别的缓存
     */
    public static final String GET_LAST_NOT_COMPLETE_REFUND = "getLastNotCompleteRefund:{}:{}:{}";
    /**
     *获取小件单信息，开启线程级别的缓存
     */
    public static final String SMALLPRO_GET_BY_ID_SQL_SERVER = "smallpro.getByIdSqlServer:{}";
    /**
     *获取小件单购买信息，开启线程级别的缓存
     */
    public static final String GET_SMALLPRO_SUB_BUY_INFO = "getSmallproSubBuyInfo:{}:{}";
    /**
     *获取门店信息缓存，开启线程级别的缓存
     */
    public static final String AREA_INFO_CLIENT_GET_AREA_INFO_BY_ID = "AreaInfoClient.getAreaInfoById:{}";

    /**
     * 获取退换消息线程缓存key
     */
    public static final String SHOUHOU_REFUND_MONEY_MAPPER_GET_REFUND = "shouhouRefundMoneyMapper.getRefund: {}";
    /**
     * 用户是否在税务模式
     */
    public static final String  USER_TAX_MODEL = "taxModel_userId_{}_areaId_{}";
    /**
     * 获取系统配置 请求级别的缓存
     */
    public static final String  SYS_CONFIG_CLIENT_GETVALUEBYCODE = "sys_config_client_getvaluebycode_{}";

    public static final String ROLEINFO_CLOUD_GET_ROLE_DATA_VIEW_SCOPE = "roleinfoCloud.getRoleDataViewScope({},{})";
    public static final String SUB_SERVICE_GET_BY_ID_SQL_SERVER = "SubService.getByIdSqlServer({})";

    public static final String SHELL_SERVICE_TRY_HANDLE_FILM_CARD_INFO = "SmallproSafeShellService.tryHandleFilmCardInfo({})";

    public static final String SMALLPRO_SERVICE_IS_HQTXH = "SmallproService.isHqtxh({})";

    public static final String PRODUCTINFO_SERVICE_GET_PRODUCTINFO_BY_PPID = "ProductinfoService.getProductinfoByPpid({})";

    public static final String SMALLPRO_SERVICE_GET_FILM_CARD_BASKET = "SmallproService.getFilmCardBasket({})";

    public static final String BASKET_SERVICE_GET_BY_ID_SQL_SERVER = "BasketService.getByIdSqlServer({})";

    public static final String SHOUHOU_TUIHUAN_BY_SHOUHOU_ID = "RefundMachineService.getShouhouTuihuanByShouhouId({})";

    public static final String ORIGIN_WAY_SERVICE_IS_GUO_BU_ORDER = "ThirdOriginWayService.isGuoBuOrder({})";

    public static final String ORIGIN_WAY_SERVICE_SELECT_GUO_BU_OTHER_TYPE = "ThirdOriginWayService.selectGuoBuOtherType()";
    public static final String CONFIG_SERVICE_LIST_PPID_BY_BAR_CODE = "SmallProConfigService.listPpidByBarCode({}, {})";
    public static final String YEARPACKAGE_TRANSFER_DETAIL = "IYearPackageTransferService.getTransferDetail({})";
    public static final String SHOUHOU_SERVICE_GET_ORDER_TYPE = "ShouhouService.getOrderType({})";
    public static final String OA_STOCK_CLOUD_IS_VIRTUAL_PRODUCT = "OaStockCloud.isVirtualProduct({})";

}
