package com.jiuji.oa.afterservice.apollo;

import cn.hutool.core.convert.Convert;
import com.jiuji.oa.afterservice.delayQueuePush.appointmentFormStrategy.impl.CommonStrategy;
import com.jiuji.oa.afterservice.delayQueuePush.vo.TimeInfo;
import com.jiuji.oa.afterservice.rabbitmq.ShouhouQujiRabblitMq;
import com.jiuji.tc.common.vo.R;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.core.MessageProperties;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.nio.charset.Charset;

/**
 * <AUTHOR>
 */

@RestController
@RequestMapping("api/apollo")
public class TestController {

    @Resource
    private ApolloEntity apolloEntity;
    @Resource
    private ShouhouQujiRabblitMq shouhouQujiRabblitMq;

    @GetMapping("/test/v1")
    public R<String> getReturnInfo() {
        TimeInfo timeInfo = CommonStrategy.getTimeInfo();
        return R.success(timeInfo.toString());
    }



    @GetMapping("/test/RabblitMq")
    public R<String> testRabblitMq() {
        String testBody = "4446179"; // 示例售后单号
        byte[] body = testBody.getBytes(Charset.defaultCharset());
        MessageProperties properties = new MessageProperties();
        properties.setContentType(MessageProperties.CONTENT_TYPE_JSON);
        Message message = new Message(body, properties);
        shouhouQujiRabblitMq.shouhouQujiLosswarning(message);
        return R.success("");
    }


}
