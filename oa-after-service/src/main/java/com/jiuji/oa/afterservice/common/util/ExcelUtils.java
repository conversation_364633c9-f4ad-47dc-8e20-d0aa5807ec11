package com.jiuji.oa.afterservice.common.util;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.BigExcelWriter;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.streaming.SXSSFCell;
import org.apache.poi.xssf.streaming.SXSSFRow;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicReference;

/**
 * @Auther: qiweiqing
 * @Date: 2020/07/16/11:00
 * @Description:
 */
public class ExcelUtils {
    /**
     * 导出Excel
     *
     * @param sheetName sheet名称
     * @param title     标题
     * @param values    内容
     * @param wb        HSSFWorkbook对象
     * @return
     */
    public static HSSFWorkbook getHSSFWorkbook(String sheetName, String[] title, String[][] values, HSSFWorkbook wb) {

        // 第一步，创建一个HSSFWorkbook，对应一个Excel文件
        if (wb == null) {
            wb = new HSSFWorkbook();
        }

        // 第二步，在workbook中添加一个sheet,对应Excel文件中的sheet
        HSSFSheet sheet = wb.createSheet(sheetName);

        // 第三步，在sheet中添加表头第0行,注意老版本poi对Excel的行数列数有限制
        HSSFRow row = sheet.createRow(0);
        // 第四步，创建单元格，并设置值表头 设置表头居中
        HSSFCellStyle style = wb.createCellStyle();
        style.setAlignment(HSSFCellStyle.ALIGN_CENTER); // 创建一个居中格式

        //声明列对象
        HSSFCell cell = null;

        //创建标题
        for (int i = 0; i < title.length; i++) {
            cell = row.createCell(i);
            cell.setCellValue(title[i]);
            cell.setCellStyle(style);
        }
        //创建内容
        if (!Objects.equals(null, values)) {
            for (int i = 0; i < values.length; i++) {
                row = sheet.createRow(i + 1);
                for (int j = 0; j < values[i].length; j++) {
                    //将内容按顺序赋给对应的列对象
                    HSSFCell cells = row.createCell(j);
                    cells.setCellValue(values[i][j]);
                    cells.setCellStyle(style);
                    cells.setCellType(HSSFCell.ENCODING_UTF_16);
                }
            }
        }
        sheet.setColumnWidth(0, 20 * 256);
        sheet.setColumnWidth(1, 20 * 256);
        return wb;
    }

    /**
     * 导出xlsx ,xls容易超长
     *
     * @param sheetName
     * @param title
     * @param values
     * @return
     */
    public static SXSSFWorkbook getSXSSFWorkbook(String sheetName, String[] title, String[][] values) {
        SXSSFWorkbook wb = new SXSSFWorkbook();
        // 第二步，在workbook中添加一个sheet,对应Excel文件中的sheet
        SXSSFSheet sheet = wb.createSheet(sheetName);

        // 第三步，在sheet中添加表头第0行,注意老版本poi对Excel的行数列数有限制
        SXSSFRow row = sheet.createRow(0);
        // 第四步，创建单元格，并设置值表头 设置表头居中
        CellStyle style = wb.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER); // 创建一个居中格式

        //声明列对象
        SXSSFCell cell = null;

        //创建标题
        for (int i = 0; i < title.length; i++) {
            cell = row.createCell(i);
            cell.setCellValue(title[i]);
            cell.setCellStyle(style);
        }
        //创建内容
        if (!Objects.equals(null, values)) {
            for (int i = 0; i < values.length; i++) {
                row = sheet.createRow(i + 1);
                for (int j = 0; j < values[i].length; j++) {
                    //将内容按顺序赋给对应的列对象
                    SXSSFCell cells = row.createCell(j);
                    cells.setCellValue(values[i][j]);
                    cells.setCellStyle(style);
                    cells.setCellType(CellType.STRING);
                }
            }
        }
        sheet.setColumnWidth(0, 20 * 256);
        sheet.setColumnWidth(1, 20 * 256);
        return wb;
    }

    /**
     * 导出Excel
     *
     * @param sheetName sheet名称
     * @param title     标题
     * @param values    内容
     * @param wb        HSSFWorkbook对象
     * @return
     */
    public static HSSFWorkbook getHSSFWorkbookMedal(String sheetName, String[] title, String[][] values,
                                                    HSSFWorkbook wb) {

        // 第一步，创建一个HSSFWorkbook，对应一个Excel文件
        if (wb == null) {
            wb = new HSSFWorkbook();
        }

        // 第二步，在workbook中添加一个sheet,对应Excel文件中的sheet
        HSSFSheet sheet = wb.createSheet(sheetName);

        // 第三步，在sheet中添加表头第0行,注意老版本poi对Excel的行数列数有限制
        HSSFRow row = sheet.createRow(0);
        // 第四步，创建单元格，并设置值表头 设置表头居中
        HSSFCellStyle style = wb.createCellStyle();
        style.setAlignment(HSSFCellStyle.ALIGN_CENTER); // 创建一个居中格式

        //声明列对象
        HSSFCell cell = null;

        //创建标题
        for (int i = 0; i < title.length; i++) {
            cell = row.createCell(i);
            cell.setCellValue(title[i]);
            cell.setCellStyle(style);
        }
        //创建内容
        for (int i = 0; i < values.length; i++) {
            row = sheet.createRow(i + 1);
            for (int j = 0; j < values[i].length; j++) {
                //将内容按顺序赋给对应的列对象
                HSSFCell cells = row.createCell(j);
                cells.setCellValue(values[i][j]);
                cells.setCellStyle(style);
                cells.setCellType(HSSFCell.ENCODING_UTF_16);
            }
        }
        sheet.setColumnWidth(0, 30 * 256);
        sheet.setColumnWidth(1, 30 * 256);
        sheet.setColumnWidth(2, 40 * 256);
        sheet.setColumnWidth(3, 60 * 256);
        return wb;
    }

    /**
     * 创建标题样式
     *
     * @param wb
     * @return
     */
    private static HSSFCellStyle createTitleCellStyle(HSSFWorkbook wb) {
        HSSFCellStyle cellStyle = wb.createCellStyle();
        cellStyle.setAlignment(HorizontalAlignment.CENTER);//水平居中
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);//垂直对齐
        cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        // cellStyle.setFillForegroundColor(IndexedColors.GREY_40_PERCENT.getIndex());//背景颜色

        HSSFFont headerFont1 = (HSSFFont) wb.createFont(); // 创建字体样式
        headerFont1.setBold(true); //字体加粗
        headerFont1.setFontName("黑体"); // 设置字体类型
        headerFont1.setFontHeightInPoints((short) 10); // 设置字体大小
        cellStyle.setFont(headerFont1); // 为标题样式设置字体样式

        return cellStyle;
    }

    public static HSSFWorkbook getHSSFWorkbookSalary(String sheetName, String[] title, String[][] values,
                                                     HSSFWorkbook wb) {
        // 第一步，创建一个HSSFWorkbook，对应一个Excel文件
        if (wb == null) {
            wb = new HSSFWorkbook();
        }

        // 第二步，在workbook中添加一个sheet,对应Excel文件中的sheet
        HSSFSheet sheet = wb.createSheet(sheetName);
        HSSFCellStyle style = wb.createCellStyle();
        style.setBorderBottom(HSSFCellStyle.BORDER_THIN);//下边框
        style.setBorderLeft(HSSFCellStyle.BORDER_THIN);//左边框
        style.setBorderTop(HSSFCellStyle.BORDER_THIN);//上边框
        style.setBorderRight(HSSFCellStyle.BORDER_THIN);//右边框
        style.setAlignment(HSSFCellStyle.ALIGN_CENTER); // 创建一个居中格式
        // 创建第一页的第一行，索引从0开始
        HSSFRow row0 = sheet.createRow(0);
        row0.setHeight((short) 800);// 设置行高
        HSSFCellStyle titleStyle = createTitleCellStyle(wb);
        HSSFCell c00 = row0.createCell(0);
        c00.setCellValue(sheetName);
        c00.setCellStyle(titleStyle);
        // 合并单元格，参数依次为起始行，结束行，起始列，结束列 （索引0开始）
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 18));//标题合并单元格操作，6为总列数

        // 创建第一页的第二行
        HSSFRow row1 = sheet.createRow(1);
        String[] titleSecond = {"基础信息", "销售提成", "售后", "回收"};
        for (int i = 0; i < 19; i++) {
            HSSFCell cell = row1.createCell(i);
            if (i == 0) {
                cell.setCellValue(titleSecond[0]);
            } else if (i == 4) {
                cell.setCellValue(titleSecond[1]);
            } else if (i == 12) {
                cell.setCellValue(titleSecond[2]);
            } else if (i == 17) {
                cell.setCellValue(titleSecond[3]);
            } else {
                cell.setCellValue(new HSSFRichTextString(""));
            }
            cell.setCellStyle(style);
            cell.setCellType(HSSFCell.ENCODING_UTF_16);
        }

        // 合并单元格，参数依次为起始行，结束行，起始列，结束列 （索引0开始）
        sheet.addMergedRegion(new CellRangeAddress(1, 1, 0, 3));
        sheet.addMergedRegion(new CellRangeAddress(1, 1, 4, 11));
        sheet.addMergedRegion(new CellRangeAddress(1, 1, 12, 16));
        sheet.addMergedRegion(new CellRangeAddress(1, 1, 17, 18));
        // todo
        // 第三步，在sheet中添加表头第0行,注意老版本poi对Excel的行数列数有限制
        HSSFRow row2 = sheet.createRow(2);

        for (int i = 0; i < title.length; i++) {
            HSSFCell cell = row2.createCell(i);
            cell.setCellValue(title[i]);
            cell.setCellStyle(style);
            cell.setCellType(HSSFCell.ENCODING_UTF_16);
        }
        //声明列对象
        HSSFCell cell = null;
        //创建内容
        HSSFRow row = null;
        for (int i = 0; i < values.length; i++) {
            row = sheet.createRow(i + 3);
            for (int j = 0; j < values[i].length; j++) {
                //将内容按顺序赋给对应的列对象
                HSSFCell cells = row.createCell(j);
                cells.setCellValue(values[i][j]);
                cells.setCellStyle(style);
                cells.setCellType(HSSFCell.ENCODING_UTF_16);
            }
        }
        sheet.setColumnWidth(0, 30 * 256);
        sheet.setColumnWidth(1, 30 * 256);
        sheet.setColumnWidth(2, 40 * 256);
        sheet.setColumnWidth(3, 60 * 256);
        return wb;
    }


    /**
     * 页面数据导出通用方法 支持表头的合并 （没有任何合并信息的列会自动帮忙处理 不需要定义 不然每一个列都需要定义合并信息）
     *
     * @param response   HttpServletResponse
     * @param data       数据
     * @param header     如果有多层 就是最后一层表头的名称
     * @param mergeInfos 合并信息
     * @param fileName   导出的文件名称
     * @param maxLevel   表头的层级
     * @throws IOException Exception
     */
    public static void export(HttpServletResponse response, List<List<Object>> data, List<String> header, List<HeaderMergeInfo> mergeInfos, String fileName, int maxLevel) throws IOException {
        export(response,data,header,mergeInfos,fileName,maxLevel,ExcelUtil.getWriter(fileName.contains(".xlsx")));
    }

    /**
     * 页面数据导出通用方法 支持表头的合并 （没有任何合并信息的列会自动帮忙处理 不需要定义 不然每一个列都需要定义合并信息）
     *
     * @param response   HttpServletResponse
     * @param data       数据
     * @param header     如果有多层 就是最后一层表头的名称
     * @param mergeInfos 合并信息
     * @param fileName   导出的文件名称
     * @param maxLevel   表头的层级
     * @throws IOException Exception
     */
    public static void bigExport(HttpServletResponse response, List<List<Object>> data, List<String> header, List<HeaderMergeInfo> mergeInfos, String fileName, int maxLevel) throws IOException {
        export(response,data,header,mergeInfos,fileName,maxLevel,ExcelUtil.getBigWriter());
    }
    /**
     * 页面数据导出通用方法 支持表头的合并 （没有任何合并信息的列会自动帮忙处理 不需要定义 不然每一个列都需要定义合并信息）
     *
     * @param response   HttpServletResponse
     * @param data       数据
     * @param header     如果有多层 就是最后一层表头的名称
     * @param mergeInfos 合并信息
     * @param fileName   导出的文件名称
     * @param maxLevel   表头的层级
     * @throws IOException Exception
     */
    public static void export(HttpServletResponse response, List<List<Object>> data, List<String> header, List<HeaderMergeInfo> mergeInfos, String fileName, int maxLevel, ExcelWriter writer) throws IOException {
        if (data == null || maxLevel < 0) {
            throw new IllegalArgumentException();
        }

        for (int i = 0; i < maxLevel; i++) {
            writer.writeHeadRow(header);
        }
        if (mergeInfos != null && mergeInfos.size() > 0) {
            for (HeaderMergeInfo mergeInfo : mergeInfos) {
                writer.merge(mergeInfo.getFirstRow(), mergeInfo.getLastRow(), mergeInfo.getFirstColumn(), mergeInfo.getLastColumn(), mergeInfo.getTitle(), true);
            }
            //处理没有任何合并信息的列
            for (int j = 0; j < header.size(); j++) {
                int finalJ = j;
                if (mergeInfos.parallelStream().noneMatch(p -> finalJ >= p.getFirstColumn() && finalJ <= p.getLastColumn())) {
                    writer.merge(0, maxLevel - 1, j, j, header.get(j), true);
                }
            }
        }
        writer.write(data);
        AtomicReference<File> cachedFileRef = new AtomicReference<>();
        ExcelWriterUtil.writeFileByUserCache(file -> {
            writer.setDestFile(file);
            writer.flush();
            cachedFileRef.set(file);
        });
        response.setContentType("application/vnd.ms-excel;charset=utf-8");
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName.endsWith(".xls") || fileName.endsWith(".xlsx") ? fileName : fileName.concat(".xls"), "UTF-8"));
        ServletOutputStream out = response.getOutputStream();
        if(cachedFileRef.get() != null && writer instanceof BigExcelWriter){
            FileUtil.writeToStream(cachedFileRef.get(),out);
            out.flush();
        }else{
            writer.flush(out, true);
        }
        writer.close();
        IoUtil.close(out);
    }

    /**
     * 导出文件名称
     * @param businessName
     * @return
     */
    public static String getExportFileName(String businessName) {
        return StrUtil.format("{}-{}.xlsx", businessName, DateUtil.format(LocalDateTime.now(), DatePattern.PURE_DATETIME_PATTERN));
    }

    @Data
    @AllArgsConstructor
    class HeaderMergeInfo {
        int firstRow;
        int lastRow;
        int firstColumn;
        int lastColumn;
        String title;
    }
}
