package com.jiuji.oa.afterservice.bigpro.vo.res;

import com.jiuji.oa.afterservice.bigpro.po.DaiyongjiYajin;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @description 代用机详情响应数据
 * 
 * <AUTHOR> quan
 * @date 2020-05-15 16:44:33
 */
@Data
public class DaiYongJiInfoRes implements Serializable {
	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "代用机id")
	private Integer id;

	@ApiModelProperty(value = "门店id")
	private Integer areaId;

	@ApiModelProperty(value = "门店")
	private String area;

	@ApiModelProperty(value = "转地区id")
	private Integer toareaId;

	@ApiModelProperty(value = "转地区")
	private Integer toArea;


	private String waiGuan;

	private String productColor;

	private String name;

	public Integer grade;

	public String gradeName;

	@ApiModelProperty(value = "是否可以继续使用")
	private Boolean isContinueUse;

	@ApiModelProperty(value = "是否有保护壳")
	private Boolean isProtectiveShell;

	@ApiModelProperty(value = "是否有保护膜")
	private Boolean isProtectiveFilm;

	@ApiModelProperty(value = "测试结果")
	private String testResult;

	@ApiModelProperty(value = "串号")
	private String imei;

	@ApiModelProperty(value = "价格")
	private BigDecimal price;

	private BigDecimal inprice;

	private Integer applyCount;

	private LocalDateTime applyDate;

	@ApiModelProperty(value = "归还日期")
	private LocalDateTime guiHuanDate;

	@ApiModelProperty(value = "机框")
	private String jiKuang;

	private Boolean isDel;

	private Integer pos;

	private Integer stats;

	private Integer toAreaStats;

	@ApiModelProperty(value = "是否盘点")
	private Boolean isPanDian;

	@ApiModelProperty(value = "盘点时间")
	private LocalDateTime panDianDate;

	private String kcCheck;

	private Integer leavel;

	@ApiModelProperty(value = "借出人")
	private String jieChuRen;

	@ApiModelProperty(value = "借出时间")
	private LocalDateTime jieChuTime;

	@ApiModelProperty(value = "预约id")
	private Integer yuYueId;

	@ApiModelProperty(value = "代用机押金")
	private BigDecimal yajin;

	@ApiModelProperty(value = "支付状态 未支付=1，已支付=2，已退款=3，已扣押金=4")
	private Integer paystate;

	@ApiModelProperty(value = "代用机押金详情")
	private DaiyongjiYajin dyjYajin;

	@ApiModelProperty(value = "代用机签署协议图片地址")
	private String signImgsrc;

}
