package com.jiuji.oa.afterservice.refund.service.way.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.jiuji.oa.afterservice.bigpro.bo.OpenIdInfoBo;
import com.jiuji.oa.afterservice.bigpro.bo.OpenValidInfoBo;
import com.jiuji.oa.afterservice.bigpro.service.ShouhouRefundService;
import com.jiuji.oa.afterservice.bigpro.service.SmsService;
import com.jiuji.oa.afterservice.bigpro.service.TuiHuanOpenIdService;
import com.jiuji.oa.afterservice.common.bo.OaUserBO;
import com.jiuji.oa.afterservice.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.afterservice.common.constant.RequestAttrKeys;
import com.jiuji.oa.afterservice.common.constant.RequestCacheKeys;
import com.jiuji.oa.afterservice.common.enums.RankEnum;
import com.jiuji.oa.afterservice.common.enums.XtenantEnum;
import com.jiuji.oa.afterservice.common.exception.CustomizeException;
import com.jiuji.oa.afterservice.common.exception.RRExceptionHandler;
import com.jiuji.oa.afterservice.common.util.SpringContextUtil;
import com.jiuji.oa.afterservice.refund.bo.DetailParamBo;
import com.jiuji.oa.afterservice.refund.bo.PayCountBo;
import com.jiuji.oa.afterservice.refund.bo.RefundSubInfoBo;
import com.jiuji.oa.afterservice.refund.dao.way.WechatAlipaySecondsRefundMapper;
import com.jiuji.oa.afterservice.refund.enums.TuiGroupEnum;
import com.jiuji.oa.afterservice.refund.po.ShouhouTuihuanDetailPo;
import com.jiuji.oa.afterservice.refund.service.BaseRefundService;
import com.jiuji.oa.afterservice.refund.service.impl.BaseRefundServiceImpl;
import com.jiuji.oa.afterservice.refund.service.way.OtherRefundService;
import com.jiuji.oa.afterservice.refund.service.way.WechatAlipaySecondsRefundService;
import com.jiuji.oa.afterservice.refund.vo.req.GroupTuihuanFormVo;
import com.jiuji.oa.afterservice.refund.vo.res.RefundMoneyDetailVo;
import com.jiuji.oa.afterservice.refund.vo.res.RefundWayDetailVo;
import com.jiuji.oa.afterservice.refund.vo.res.TuiHuanCheckVo;
import com.jiuji.oa.afterservice.refund.vo.res.way.OtherRefundVo;
import com.jiuji.oa.afterservice.refund.vo.res.way.WechatAlipaySecondsRefundVo;
import com.jiuji.oa.afterservice.sys.service.SysConfigService;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.utils.common.DecideUtil;
import com.jiuji.tc.utils.common.build.LambdaBuild;
import com.jiuji.tc.utils.constants.SysConfigConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 微信支付宝秒退
 *
 * <AUTHOR>
 * @since 2022/7/19 15:31
 */
@Service
@Slf4j
public class WechatAlipaySecondsRefundServiceImpl extends BaseRefundServiceImpl<WechatAlipaySecondsRefundVo> implements WechatAlipaySecondsRefundService {

    private static final Pattern PATH_MATCH_NAME = Pattern.compile("^(([\u4e00-\u9fa5]{2,8})|([a-zA-Z]{2,16}))$");

    @Resource
    @Lazy
    private WechatAlipaySecondsRefundService wechatAlipaySecondsRefundService;
    @Resource
    private WechatAlipaySecondsRefundMapper secondsRefundMapper;
    @Resource
    private TuiHuanOpenIdService tuiHuanOpenIdService;
    @Resource
    private SmsService smsService;

    @Resource
    private OtherRefundService otherRefundService;

    private final static BigDecimal CASH_RECEIPT= new BigDecimal("300.00");

    private final static List<String>  RETUR_WAY_LIST = Arrays.asList(ShouhouRefundService.WECHAT_REFUND_WAY,ShouhouRefundService.ALIPAY_REFUND_WAY);

    @Override
    protected void setGroupDataByRefundWays(DetailParamBo detailParamBo, RefundWayDetailVo<WechatAlipaySecondsRefundVo> refundWayDetailVo, R<RefundMoneyDetailVo> result) {
        RefundMoneyDetailVo detailVo = result.getData();
        refundWayDetailVo.setDataTemplate(new WechatAlipaySecondsRefundVo().setGroupCode(getMyGroup().getCode()));
        //九机特殊做处理
        List<RefundMoneyDetailVo.RefundWayVo> refundWayVoList = detailVo.getRefundWays();
        if(XtenantEnum.isJiujiXtenant() && CollUtil.isNotEmpty(refundWayVoList)){
            //过了条件：当退款日期和收银日期不是同一天，且现金收银金额≥300时 过滤不能进行支付宝秒退和微信秒退
            refundWayVoList.removeIf(item->{
                //获取当前时间
                LocalDate now = LocalDate.now();
                //获取收银日期
                List<OtherRefundVo> refundVoList = otherRefundService.listAll(detailParamBo.getRefundSubInfo().getOrderId(), detailParamBo.getTuihuanKindEnum());
                if(CollUtil.isNotEmpty(refundVoList)){
                    //现金收银 退款总金额
                    BigDecimal refundPrice = refundVoList.stream()
                            .filter(obj-> OtherRefundService.XIAN_JIN.equals(obj.getReturnWayName()))
                            .map(OtherRefundVo::getRefundPrice)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    OtherRefundVo otherRefundVo = refundVoList.stream()
                            .filter(obj->OtherRefundService.XIAN_JIN.equals(obj.getReturnWayName()))
                            .min(Comparator.comparing(OtherRefundVo::getDtime))
                            .orElse(new OtherRefundVo());
                    LocalDateTime firstDtime = Optional.ofNullable(otherRefundVo.getDtime()).orElse(LocalDateTime.MIN);
                    if(refundPrice.compareTo(CASH_RECEIPT) > 0  && now.compareTo(firstDtime.toLocalDate()) !=0 && RETUR_WAY_LIST.contains(item.getName())){
                        try {
                            //获取收银id
                            StringJoiner joiner = new StringJoiner(",");
                            refundVoList.stream()
                                    .filter(obj->OtherRefundService.XIAN_JIN.equals(obj.getReturnWayName()))
                                    .map(obj->obj.getShouyingId()+"")
                                    .forEach(joiner::add);
                            SpringContextUtil.addRequestKeyMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG, "当退款日期和收银日期不是同一天，且现金收银金额≥300时 过滤不能进行支付宝秒退和微信秒退,退款方式：[{}],现金收银:[{}],收银id:[{}]"
                                    ,item.getName(),refundPrice,joiner.toString());
                        }catch (Exception e){
                            RRExceptionHandler.logError("微信支付宝秒退日志记录收银id异常", refundVoList, e, smsService:: sendOaMsgTo9JiMan);
                        }
                        return Boolean.TRUE;
                    }
                }
                return Boolean.FALSE;
            });
        }

    }

    @Override
    protected List<WechatAlipaySecondsRefundVo> toGroupDataByDetail(DetailParamBo detailParamBo, List<ShouhouTuihuanDetailPo> myTuihuanDetailPos) {
        ShouhouRefundService shouhouRefundService = SpringUtil.getBean(ShouhouRefundService.class);
        return myTuihuanDetailPos.stream()
                .map(
                        thd -> {
                            LambdaBuild<WechatAlipaySecondsRefundVo> refundBulid = LambdaBuild.create(new WechatAlipaySecondsRefundVo()).set(WechatAlipaySecondsRefundVo::setGroupCode, thd.getTuiGroup())
                                    .set(WechatAlipaySecondsRefundVo::setRefundPrice, thd.getRefundPrice()).set(WechatAlipaySecondsRefundVo::setId, thd.getId())
                                    .set(WechatAlipaySecondsRefundVo::setReturnWayName, thd.getTuiWay())
                                    .set(WechatAlipaySecondsRefundVo::setRefundBusinessType, thd.getRefundBusinessType())
                                    .set(WechatAlipaySecondsRefundVo::setBankFuming,thd.getBankFuming())
                                    .set(WechatAlipaySecondsRefundVo::setPayOpenType,thd.getPayOpenType())
                                    .set(WechatAlipaySecondsRefundVo::setValidtTime,thd.getValidtTime());
                            shouhouRefundService.setRefundWayValid((url,info)->{
                                refundBulid.set(WechatAlipaySecondsRefundVo::setOpenIdUrl,url).set(WechatAlipaySecondsRefundVo::setOpenIdInfo,info);
                            },getOpenValidInfo(thd).setUserId(Optional.ofNullable(detailParamBo.getRefundSubInfo()).map(RefundSubInfoBo::getUserId).orElse(null))
                            .setOrderBusinessTypeEnum(detailParamBo.getTuihuanKindEnum().getOrderBusinessTypeEnum()).setSubId(detailParamBo.getRefundSubInfo().getOrderId()));
                            return refundBulid.build();
                        }
                )
                .collect(Collectors.toList());
    }

    private OpenValidInfoBo getOpenValidInfo(ShouhouTuihuanDetailPo thd) {
        OpenValidInfoBo openValidInfo = new OpenValidInfoBo();
        Arrays.stream(OpenIdInfoBo.OpenType.values())
                .filter(ot -> Objects.equals(ot.getCode(),thd.getPayOpenType()))
                .findFirst().ifPresent(openValidInfo::setOpenType);
        if (ObjectUtil.equal(OpenIdInfoBo.OpenType.ALIPAY.getCode(), thd.getPayOpenType())) {
            openValidInfo.setId(thd.getId()).setIdTypeEnum(OpenValidInfoBo.IdTypeEnum.TUIHUAN_DETAIL_ID);
        } else {
            openValidInfo.setId(thd.getFkTuihuanId()).setIdTypeEnum(OpenValidInfoBo.IdTypeEnum.TUIHUAN_ID);
        }
        return openValidInfo.setTuiWay(thd.getTuiWay()).setPayOpenId(thd.getPayOpenId());
    }

    @Override
    protected RefundWayDetailVo<WechatAlipaySecondsRefundVo> initRefundWayDetailVo(DetailParamBo detailParamBo) {
        boolean isEdit = detailParamBo.getTuiHuanPo() == null;
        List<ShouhouTuihuanDetailPo> tuihuanDetailPos = Optional.ofNullable(detailParamBo.getTuihuanDetailPos()).orElse(Collections.emptyList());
        String inputOrText = DecideUtil.iif(isEdit, RefundWayDetailVo.GroupColumnTypeEnum.INPUT.getCode(), RefundWayDetailVo.GroupColumnTypeEnum.TEXT.getCode());
        LambdaBuild<LinkedList<RefundWayDetailVo.GroupColumns>> columnsBuild = LambdaBuild.create(new LinkedList<RefundWayDetailVo.GroupColumns>())
                .set(List::add, new RefundWayDetailVo.GroupColumns().setDataIndex("returnWayName").setTitle(TuiGroupEnum.WECHAT_ALIPAY_SECONDS_REFUND.getMessage())
                        .setType(RefundWayDetailVo.GroupColumnTypeEnum.TEXT.getCode()))
                .set(List::add, new RefundWayDetailVo.GroupColumns().setDataIndex("bankFuming").setTitle("收款人姓名").setType(inputOrText))
                .set(List::add, new RefundWayDetailVo.GroupColumns().setDataIndex("openIdInfo").setTitle("验证情况").setType(RefundWayDetailVo.GroupColumnTypeEnum.LINK.getCode()))
                ;
        //动态设置验证时间显示
        if(tuihuanDetailPos.stream().anyMatch(td -> Objects.nonNull(td.getValidtTime()))){
            columnsBuild.set(List::add, new RefundWayDetailVo.GroupColumns().setDataIndex("validtTime").setTitle("验证时间").setType(RefundWayDetailVo.GroupColumnTypeEnum.TEXT.getCode()));
        }
        columnsBuild.set(List::add, new RefundWayDetailVo.GroupColumns().setDataIndex("refundPrice").setTitle("本次退款金额").setType(inputOrText));

        return new RefundWayDetailVo<WechatAlipaySecondsRefundVo>().setGroupColumns(columnsBuild.build());
    }

    @Override
    public boolean isMyGroup(Integer tuiGroup) {
        return Objects.equals(tuiGroup,getMyGroup().getCode());
    }

    @Override
    public TuiGroupEnum getMyGroup() {
        return TuiGroupEnum.WECHAT_ALIPAY_SECONDS_REFUND;
    }

    @Override
    protected BaseRefundService<WechatAlipaySecondsRefundVo> getMyService() {
        return wechatAlipaySecondsRefundService;
    }

    @Override
    protected void doAssertCheckSave(GroupTuihuanFormVo tuihuanForm, List<WechatAlipaySecondsRefundVo> tuiWayDetails) {
        Assert.isFalse(tuiWayDetails.stream().anyMatch(tw -> tw.getRefundPrice().compareTo(BigDecimal.ONE) < 0),
                "支付宝微信秒退金额必须大于等于1");
        Assert.isFalse(tuiWayDetails.stream().anyMatch(tw -> ObjectUtil.equal(tw.getReturnWayName(), ShouhouRefundService.WECHAT_REFUND_WAY)
                && StrUtil.isBlank(tw.getBankFuming())), "微信限制，需录入客户真实姓名，以方便退款！");
        tuiWayDetails.forEach(x -> {
            if (ObjectUtil.equal(x.getReturnWayName(), ShouhouRefundService.WECHAT_REFUND_WAY)
                    && StrUtil.isNotBlank(x.getBankFuming())) {
                boolean isCorrectName = PATH_MATCH_NAME.matcher(x.getBankFuming()).matches();
                Assert.isFalse(!isCorrectName, "收款姓名录入有误，请检查");
            }
        });

        //设置payOpenType
        tuiWayDetails.forEach(twd -> Optional.ofNullable(tuiHuanOpenIdService.getOpenType(twd.getReturnWayName())).
                map(OpenIdInfoBo.OpenType::getCode).ifPresent(twd::setPayOpenType));

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void doSave(GroupTuihuanFormVo tuihuanForm, List<WechatAlipaySecondsRefundVo> myTuiWayDetails) {
        secondsRefundMapper.batchInsert(tuihuanForm, myTuiWayDetails);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateCheck(TuiHuanCheckVo tuiHuanCheckVo) {
        List<ShouhouTuihuanDetailPo> myTuihuanDetails = getMyTuihuanDetails(tuiHuanCheckVo.getTuihuanDetailPos());
        if(myTuihuanDetails.isEmpty()){
            return;
        }
        ShouhouRefundService shouhouRefundService = SpringUtil.getBean(ShouhouRefundService.class);
        switch (tuiHuanCheckVo.getProcessStatusEnum()){
            case CHECK2:
                //校验是否已经验证
                String notValidTuiWay = myTuihuanDetails.stream().filter(thd -> {
                    if(thd.getPayOpenType() == null){
                        throw new CustomizeException(StrUtil.format("{}验证平台类型不能为空",thd.getTuiWay()));
                    }
                    OpenValidInfoBo openValidInfo = getOpenValidInfo(thd);
                    return shouhouRefundService.isNotValid(openValidInfo, openValidInfo.getOpenType());
                }).map(ShouhouTuihuanDetailPo::getTuiWay).collect(Collectors.joining("/"));
                if(StrUtil.isNotBlank(notValidTuiWay)){
                    throw new CustomizeException(StrUtil.format("{}未进行验证",notValidTuiWay));
                }

                break;
            case CHECK3:
                //校验秒退限制 兜底校验,各业务线会单独校验
                R<Boolean> srlCheck = wechatAlipaySecondsRefundService.secondsRefundLimit(tuiHuanCheckVo);
                if(!srlCheck.isSuccess()){
                    throw new CustomizeException(srlCheck.getUserMsg());
                }
                break;
        }



    }

    /**
     * 秒退金额的限制
     * @return
     */
    @Override
    public R<Boolean> secondsRefundLimit(TuiHuanCheckVo tuiHuanCheckVo) {
        return SpringContextUtil.reqCache(()-> invokeSecondsRefundLimit(tuiHuanCheckVo), RequestCacheKeys.SECONDS_REFUND_LIMIT, JSON.toJSONString(tuiHuanCheckVo));
    }

    /**
     * 秒退金额的限制
     * @return
     */
    private R<Boolean> invokeSecondsRefundLimit(TuiHuanCheckVo tuiHuanCheckVo) {
        List<ShouhouTuihuanDetailPo> myTuihuanDetails = getMyTuihuanDetails(tuiHuanCheckVo.getTuihuanDetailPos());
        if(myTuihuanDetails.isEmpty()){
            return R.success(Boolean.TRUE);
        }
        BigDecimal wechatSecondPrice = myTuihuanDetails.stream().filter(td -> Objects.equals(td.getTuiWay(), ShouhouRefundService.WECHAT_REFUND_WAY))
                .map(ShouhouTuihuanDetailPo::getRefundPrice).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal alipaySecondPrice = myTuihuanDetails.stream().filter(td -> Objects.equals(td.getTuiWay(), ShouhouRefundService.ALIPAY_REFUND_WAY))
                .map(ShouhouTuihuanDetailPo::getRefundPrice).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
        SysConfigService sysConfigService = SpringUtil.getBean(SysConfigService.class);
        List<String> ranks = Optional.ofNullable(SpringUtil.getBean(AbstractCurrentRequestComponent.class).getCurrentStaffId())
                .map(OaUserBO::getRank).orElse(Collections.emptyList());
        Optional<R<Boolean>> rOpt;
        //订单单笔限额
        rOpt = Optional.ofNullable(sysConfigService.getValueByCodeNoCache(SysConfigConstant.SECOND_PAYMENT_ENUM_ORDERSINGLEAMOUNT))
                .filter(StrUtil::isNotBlank)
                .map(Convert::toBigDecimal)
                //金额超过限制且没有秒付的控制权限
                .filter(maxSubPrice -> {
                    BigDecimal secondPrice = wechatSecondPrice.add(alipaySecondPrice);
                    SpringContextUtil.addRequestKeyMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG,"订单单笔限额: {}, 当前订单秒退总额: {}",maxSubPrice,secondPrice);
                    return secondPrice.compareTo(maxSubPrice) > 0 && RankEnum.MFKZ.noHasAuthority(ranks);
                })
                .map(maxSubPrice -> R.error("支付失败，该订单秒付金额大于限制金额，请对接有秒付控制（mfkz）权限的同事进行支付。"));

        if(!rOpt.isPresent() && wechatSecondPrice.compareTo(BigDecimal.ZERO) > 0){
            //微信秒退
            String payopenid = myTuihuanDetails.stream().filter(td -> Objects.equals(td.getTuiWay(), ShouhouRefundService.WECHAT_REFUND_WAY))
                    .map(ShouhouTuihuanDetailPo::getPayOpenId).filter(StrUtil::isNotBlank).findFirst()
                    .orElseThrow(()-> new CustomizeException("秒退验证微信用户id不能为空"));
            rOpt = wechatCheckLimit(wechatSecondPrice, ranks, payopenid);
        }

        if(!rOpt.isPresent() && alipaySecondPrice.compareTo(BigDecimal.ZERO) > 0){
            //支付宝秒退
            String payopenid = myTuihuanDetails.stream().filter(td -> Objects.equals(td.getTuiWay(), ShouhouRefundService.ALIPAY_REFUND_WAY))
                    .map(ShouhouTuihuanDetailPo::getPayOpenId).filter(StrUtil::isNotBlank).findFirst()
                    .orElseThrow(()-> new CustomizeException("秒退验证支付宝用户id不能为空"));
            rOpt = alipayCheckLimit(alipaySecondPrice, ranks, payopenid);
        }

        return rOpt.orElseGet(()->R.success(Boolean.TRUE));
    }

    @Override
    public Optional<R<Boolean>> alipayCheckLimit(BigDecimal alipaySecondPrice, List<String> ranks, String payopenid) {
        SysConfigService sysConfigService = SpringUtil.getBean(SysConfigService.class);
        Optional<R<Boolean>> rOpt;
        //支付宝日限额
        rOpt = Optional.ofNullable(sysConfigService.getValueByCodeNoCache(SysConfigConstant.SECOND_PAYMENT_ENUM_ALIPAYRESTRICTIONS))
                .filter(StrUtil::isNotBlank)
                .map(Convert::toBigDecimal)
                //金额超过限制且没有秒付的控制权限
                .filter(maxSubPrice -> {
                    BigDecimal payDaysTotal = ObjectUtil.defaultIfNull(secondsRefundMapper.getAliPayDayTotal(), BigDecimal.ZERO);
                    SpringContextUtil.addRequestKeyMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG,"支付宝日限额: {}, 支付宝当天支付总额: {}",maxSubPrice,payDaysTotal);
                    return alipaySecondPrice.add(payDaysTotal).compareTo(maxSubPrice) > 0 && RankEnum.MFKZ.noHasAuthority(ranks);
                })
                .map(maxSubPrice -> R.error("支付失败，支付宝秒付金额已超过日限额，请对接有秒付控制（mfkz）权限的同事进行支付。"));
        //同用户近30天累计秒付笔数(含本次)
        if (!rOpt.isPresent()){
            rOpt = Optional.ofNullable(sysConfigService.getValueByCodeNoCache(SysConfigConstant.SECOND_PAYMENT_ENUM_LEIJIBISHU))
                    .filter(StrUtil::isNotBlank)
                    .map(Convert::toBigDecimal)
                    //金额超过限制且没有秒付的控制权限
                    .filter(maxNum -> {
                        BigDecimal lastDaysCount = Optional.ofNullable(getLastDaysPayCount(2, -30, payopenid))
                                .map(PayCountBo::getPayCount).orElse(BigDecimal.ZERO);
                        SpringContextUtil.addRequestKeyMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG,"支付宝30天累计秒付限制笔数: {}, 同用户近30天累计支付宝秒付笔数: {}",maxNum,lastDaysCount);
                        return lastDaysCount.add(BigDecimal.ONE).compareTo(maxNum) > 0 && RankEnum.MFKZ.noHasAuthority(ranks);
                    })
                    .map(maxSubPrice -> R.error("支付失败，该支付宝用户近30天累计秒付笔数大于限制数量，请对接有秒付控制（mfkz）权限的同事进行支付。"));
        }
        //同用户近30天累计秒付金额(含本次)
        if (!rOpt.isPresent()){
            rOpt = Optional.ofNullable(sysConfigService.getValueByCodeNoCache(SysConfigConstant.SECOND_PAYMENT_ENUM_LEIJIJINE))
                    .filter(StrUtil::isNotBlank)
                    .map(Convert::toBigDecimal)
                    //金额超过限制且没有秒付的控制权限
                    .filter(maxNum -> {
                        BigDecimal lastDaysTotal = Optional.ofNullable(getLastDaysPayCount(2, -30, payopenid))
                                .map(PayCountBo::getPayTotal).orElse(BigDecimal.ZERO);
                        SpringContextUtil.addRequestKeyMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG,"支付宝30天累计秒付限制金额: {}, 同用户近30天累计支付宝秒付金额: {}",maxNum,lastDaysTotal);
                        return lastDaysTotal.add(alipaySecondPrice).compareTo(maxNum) > 0 && RankEnum.MFKZ.noHasAuthority(ranks);
                    })
                    .map(maxSubPrice -> R.error("支付失败，该支付宝用户近30天累计秒付金额大于限制金额，请对接有秒付控制（mfkz）权限的同事进行支付。"));
        }
        return rOpt;
    }

    @Override
    public Optional<R<Boolean>> wechatCheckLimit(BigDecimal wechatSecondPrice, List<String> ranks, String payopenid) {
        SysConfigService sysConfigService = SpringUtil.getBean(SysConfigService.class);
        Optional<R<Boolean>> rOpt;
        //微信日限额
        rOpt = Optional.ofNullable(sysConfigService.getValueByCodeNoCache(SysConfigConstant.SECOND_PAYMENT_ENUM_WECHATRESTRICTIONS))
                .filter(StrUtil::isNotBlank)
                .map(Convert::toBigDecimal)
                //金额超过限制且没有秒付的控制权限
                .filter(maxSubPrice -> {
                    BigDecimal wechatPayDayTotal = ObjectUtil.defaultIfNull(secondsRefundMapper.getWeixinPayDayTotal(XtenantEnum.getXtenant()), BigDecimal.ZERO);
                    SpringContextUtil.addRequestKeyMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG,"微信日限额: {}, 微信当天支付总额: {}",maxSubPrice,wechatPayDayTotal);
                    return wechatSecondPrice.add(wechatPayDayTotal).compareTo(maxSubPrice) > 0 && RankEnum.MFKZ.noHasAuthority(ranks);
                })
                .map(maxSubPrice -> R.error("支付失败，微信秒付金额已超过日限额，请对接有秒付控制（mfkz）权限的同事进行支付。"));
        //同用户近30天累计秒付笔数(含本次)
        if (!rOpt.isPresent()){
            rOpt = Optional.ofNullable(sysConfigService.getValueByCodeNoCache(SysConfigConstant.SECOND_PAYMENT_ENUM_LEIJIBISHU))
                    .filter(StrUtil::isNotBlank)
                    .map(Convert::toBigDecimal)
                    //金额超过限制且没有秒付的控制权限
                    .filter(maxNum -> {
                        BigDecimal lastDayPayCount = Optional.ofNullable(getLastDaysPayCount(1, -30, payopenid))
                                .map(PayCountBo::getPayCount).orElse(BigDecimal.ZERO);
                        SpringContextUtil.addRequestKeyMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG,"微信30天累计秒付限制笔数: {}, 同用户近30天累计微信秒付笔数: {}",maxNum,lastDayPayCount);
                        return lastDayPayCount.add(BigDecimal.ONE).compareTo(maxNum) > 0 && RankEnum.MFKZ.noHasAuthority(ranks);
                    })
                    .map(maxSubPrice -> R.error("支付失败，该微信用户近30天累计秒付笔数大于限制数量，请对接有秒付控制（mfkz）权限的同事进行支付。"));
        }
        //同用户近30天累计秒付金额(含本次)
        if (!rOpt.isPresent()){
            rOpt = Optional.ofNullable(sysConfigService.getValueByCodeNoCache(SysConfigConstant.SECOND_PAYMENT_ENUM_LEIJIJINE))
                    .filter(StrUtil::isNotBlank)
                    .map(Convert::toBigDecimal)
                    //金额超过限制且没有秒付的控制权限
                    .filter(maxNum -> {
                        BigDecimal lastDaysTotal = Optional.ofNullable(getLastDaysPayCount(1, -30, payopenid))
                                .map(PayCountBo::getPayTotal).orElse(BigDecimal.ZERO);
                        SpringContextUtil.addRequestKeyMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG,"微信30天累计秒付限制金额: {}, 同用户近30天累计微信秒付金额: {}",maxNum,lastDaysTotal);
                        return lastDaysTotal.add(wechatSecondPrice).compareTo(maxNum) > 0 && RankEnum.MFKZ.noHasAuthority(ranks);
                    })
                    .map(maxSubPrice -> R.error("支付失败，该微信用户近30天累计秒付金额大于限制金额，请对接有秒付控制（mfkz）权限的同事进行支付。"));
        }
        return rOpt;
    }


    /**
     * 用户最近N天打款次数、打款总额（微信/支付宝）
     * @param type 类型：1微信，2支付宝
     * @param days
     * @param payOpenId
     * @return
     */
    private PayCountBo getLastDaysPayCount(int type, int days, String payOpenId){
        return SpringContextUtil.reqCache(()-> secondsRefundMapper.getLastDaysPayCount(type, days, payOpenId), RequestCacheKeys.GET_LAST_DAYS_PAY_COUNT, type, days, payOpenId);
    }
}
