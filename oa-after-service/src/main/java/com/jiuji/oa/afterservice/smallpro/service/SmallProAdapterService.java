package com.jiuji.oa.afterservice.smallpro.service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 小件模式输出适配相关接口
 */
public interface SmallProAdapterService {

    /**
     * 获取 新/老年包卡时间节点
     * @return
     */
    LocalDateTime getFrameTime();

    /**
     * 根据 code值 获取小件相关配置信息
     * @param code
     * @return
     */
    List<Integer> getSmallRelativeConfigList(Integer code);

    /**
     * 计算折价金额
     * @param ppid
     * @param buyTime
     * @param price
     * @param smallproOrderInfoBO
     * @return
     */
    BigDecimal calculateDiscountAmount(Integer ppid, LocalDateTime buyTime, BigDecimal price,Integer basketId);

    /**
     * 根据code值获取配置信息
     * @param code
     * @return
     */
    String getValueByCode(Integer code);

    /**
     * 获取所有支付方式
     * @return
     */
    List<String> getAllOnlinePayWayList();

}
