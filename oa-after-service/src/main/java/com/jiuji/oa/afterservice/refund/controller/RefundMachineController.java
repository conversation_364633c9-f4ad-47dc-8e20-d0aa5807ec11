package com.jiuji.oa.afterservice.refund.controller;

import cn.hutool.extra.spring.SpringUtil;
import com.jiuji.oa.afterservice.bigpro.service.SmsService;
import com.jiuji.oa.afterservice.common.aspect.RepeatSubmitCheck;
import com.jiuji.oa.afterservice.common.exception.RRExceptionHandler;
import com.jiuji.oa.afterservice.other.service.ShouhouTuihuanService;
import com.jiuji.oa.afterservice.refund.service.RefundMachineService;
import com.jiuji.oa.afterservice.refund.service.RefundMoneyService;
import com.jiuji.oa.afterservice.refund.vo.req.OperatorByBasketIdReq;
import com.jiuji.oa.afterservice.refund.vo.req.machine.HuanInfoReq;
import com.jiuji.oa.afterservice.refund.vo.req.machine.RefundMachineFormVo;
import com.jiuji.oa.afterservice.refund.vo.req.machine.ZheJiaMachineFormVo;
import com.jiuji.oa.afterservice.refund.vo.res.OperatorByBasketIdRes;
import com.jiuji.oa.afterservice.refund.vo.res.TuiHuanCheckVo;
import com.jiuji.oa.afterservice.refund.vo.res.machine.*;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.common.vo.ResultCode;
import com.jiuji.tc.utils.common.annotation.LogRecordAround;
import com.jiuji.tc.utils.common.build.LambdaBuild;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2022/11/16 16:20
 */
@RestController
@Api(tags = "组合退换机")
@RequestMapping("/api/groupRefundMachine")
public class RefundMachineController {

    @Resource
    private RefundMachineService refundMachineService;
    @Resource
    private ShouhouTuihuanService shouhouTuihuanService;

    /**
     * @see https://test01.oa.saas.ch999.cn/shouhou/huan/10156?is_iframe=1
     * @param shouhouId
     * @return
     */
    @GetMapping("/detail")
    @ApiOperation(value = "组合退款详情", notes = "组合退款详情")
    public R<RefundMachineDetailVo> detail(@ApiParam("售后单号") @RequestParam(value = "shouhouId") Integer shouhouId,
                                          @ApiParam("商品明细id") @RequestParam(value = "basketId", required = false) Integer basketId) {
        // 传递basketId参数到服务层
        return refundMachineService.detail(shouhouId, basketId);
    }

    /**
     * oa999UI\Controllers\ShouhouController.cs::Tuihuan
     * @param machineFormVo
     * @return
     */
    @PostMapping("/save")
    @ApiOperation(value = "组合退换机提交", notes = "组合退换机提交")
    public R<Integer> save(@RequestBody @Valid RefundMachineFormVo machineFormVo){
        R<Integer> save = refundMachineService.save(machineFormVo);
        try {
            refundMachineService.autoMobileHttpTuikuan(machineFormVo);
        } catch (Exception e){
            RRExceptionHandler.logError("良品退款自动办理异常",machineFormVo,e,SpringUtil.getBean(SmsService.class)::sendOaMsgTo9JiMan);
        }

        return save;
    }


    /**
     * 判断主商品是否存在 运营商返现
     * @param req
     * @return
     */
    @PostMapping("/getOperatorByBasketId")
    public R<OperatorByBasketIdRes> getOperatorByBasketId(@RequestBody @Valid OperatorByBasketIdReq req){
        return R.success(refundMachineService.getOperatorByBasketId(req));
    }

    /**
     * 退换机管理折价计算接口
     * @param zheJiaFormVo
     * @return
     */
    @PostMapping("/zhejia")
    @ApiOperation(value = "折价计算接口", notes = "折价计算接口")
    public R<ZheJiaMachineVo> zheJia(@RequestBody ZheJiaMachineFormVo zheJiaFormVo){
        return refundMachineService.zheJia(zheJiaFormVo);
    }

    /**
     * 获取换货详情信息
     * @see c# /shouhou/huaninfo  oa999UI\Controllers\ShouhouController.cs::HuanInfo ljg
     * @return
     */
    @PostMapping("/huanInfo")
    @ApiOperation(value = "获取换货详情信息", notes = "获取换货详情信息")
    public R<HuanInfoVo> huanInfo(@RequestBody HuanInfoReq huanInfo){

        return refundMachineService.huanInfo(huanInfo);
    }


    @PostMapping("/cancelRefund")
    @ApiOperation(value = "撤销退款申请", notes = "撤销退款申请")
    public R<Boolean> cancelRefund(@ApiParam("审批退款id") @RequestParam("tuihuanId") Integer tuihuanId,@RequestParam("mark") String mark){
        return refundMachineService.cancelRefund(tuihuanId, mark);
    }

    @PostMapping("/submitCheck")
    @ApiOperation(value = "退款审核", notes = "退款审核")
    public R<Integer> submitCheck(@RequestBody @Valid TuiHuanCheckVo tuiHuanCheckVo){
        R<Integer> r = refundMachineService.submitCheck(tuiHuanCheckVo);
        if(r.isSuccess()){
            r = Optional.ofNullable(SpringUtil.getBean(RefundMoneyService.class)
                            .tryAutoCheck3(tuiHuanCheckVo, refundMachineService::submitCheck))
                    // 自动退款办理不影响主流程
                    .map(rr -> LambdaBuild.create(rr).set(R::setCode, ResultCode.SUCCESS).build())
                    .orElse(r);
        }
        return r;
    }

    @PostMapping("/updatePiaoPrice")
    @ApiOperation(value = "更新发票折价", notes = "更新发票折价")
    public R<Boolean> updatePiaoPrice(@RequestBody @Valid UpdatePiaoPriceFormVo formVo){
        return refundMachineService.updatePiaoPrice(formVo);
    }

    @PostMapping("/saveZheJiaPay")
    @ApiOperation(value = "保存折价支付费用信息", notes = "保存折价支付费用信息")
    @RepeatSubmitCheck(expression = "#{packageFullName}:#{methodSignName}:#{form.shouhouId}")
    public R<Integer> saveZheJiaPay(@RequestBody @Valid ZheJiaPayFormVo form){
        return refundMachineService.saveZheJiaPay(form);
    }
}
