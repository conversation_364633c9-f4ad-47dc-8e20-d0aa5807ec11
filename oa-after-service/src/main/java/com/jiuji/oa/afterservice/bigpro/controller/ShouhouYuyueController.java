package com.jiuji.oa.afterservice.bigpro.controller;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.format.FastDateFormat;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ch999.common.util.vo.Result;
import com.jiuji.cloud.after.enums.ShouhouyuyueFromSourceEnum;
import com.jiuji.oa.afterservice.bigpro.bo.HandleYuYueData;
import com.jiuji.oa.afterservice.bigpro.bo.yuyue.YjsxAddress;
import com.jiuji.oa.afterservice.bigpro.dao.ShouhouYuyueMapper;
import com.jiuji.oa.afterservice.bigpro.enums.*;
import com.jiuji.oa.afterservice.bigpro.po.ShouhouYuyue;
import com.jiuji.oa.afterservice.bigpro.service.ShouhouYuyueExService;
import com.jiuji.oa.afterservice.bigpro.service.ShouhouYuyueService;
import com.jiuji.oa.afterservice.bigpro.service.SmsService;
import com.jiuji.oa.afterservice.bigpro.vo.OrderPartsVo;
import com.jiuji.oa.afterservice.bigpro.vo.RecoveryHandleDataVo;
import com.jiuji.oa.afterservice.bigpro.vo.req.ShouhouYuYueAlterImeiReq;
import com.jiuji.oa.afterservice.bigpro.vo.req.ShouhouYuyueListReq;
import com.jiuji.oa.afterservice.bigpro.vo.req.ShouhouYuyueReq;
import com.jiuji.oa.afterservice.bigpro.vo.res.*;
import com.jiuji.oa.afterservice.cloud.service.WebCloud;
import com.jiuji.oa.afterservice.cloud.vo.SelectsVo;
import com.jiuji.oa.afterservice.common.aspect.RepeatSubmitCheck;
import com.jiuji.oa.afterservice.common.bo.OaUserBO;
import com.jiuji.oa.afterservice.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.afterservice.common.enums.AuthorizeIdEnum;
import com.jiuji.oa.afterservice.common.enums.XtenantEnum;
import com.jiuji.oa.afterservice.common.exception.RRExceptionHandler;
import com.jiuji.oa.afterservice.common.vo.PageVo;
import com.jiuji.oa.afterservice.sys.service.SysConfigService;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.common.vo.ResultCode;
import com.jiuji.tc.utils.common.annotation.LogRecordAround;
import com.jiuji.tc.utils.constants.NumberConstant;
import com.jiuji.tc.utils.constants.SysConfigConstant;
import com.jiuji.tc.utils.enums.EnumUtil;
import com.jiuji.tc.utils.enums.EnumVO;
import com.jiuji.oa.afterservice.bigpro.vo.req.TuiProductInfoReq;
import com.jiuji.oa.afterservice.bigpro.vo.res.TuiProductInfoRes;
import com.jiuji.oa.afterservice.bigpro.vo.res.YuyueProductInfoRes;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-11
 */
@Api(tags = "大件：售后预约")
@RestController
@RequestMapping("/api/bigpro/shouhouYuyue")
public class ShouhouYuyueController {

    @Autowired
    private ShouhouYuyueService shouhouYuyueService;
    @Resource
    private AbstractCurrentRequestComponent currentRequestComponent;

    @Resource
    private ShouhouYuyueMapper shouhouYuyueMapper;
    @Autowired
    private ShouhouYuyueExService shouhouYuyueExService;
    @Autowired
    private SysConfigService sysConfigService;

    @GetMapping("/updateYuyueLog")
    public R<String> updateYuyueLog() {
        return R.success("更新成功", shouhouYuyueService.updateYuyueLog());
    }

    @ApiOperation(value = "获取大件售后预约查询列表", notes = "param传相应的json数据", httpMethod = "POST", response = ShouhouYuyueListRes.class)
    @PostMapping("/getShouhouYuyuePage")
    public R<PageVo> getShouhouYuyuePage(@RequestBody ShouhouYuyueListReq param) {
        param = param.uniqueClearOtherVariable(param);
        return R.success("获取成功", shouhouYuyueService.getShouhouYuyuePage(param));
    }


    @GetMapping("/isUseNewUI")
    public R<Boolean> isUseNewUI() {
        return R.success( shouhouYuyueService.isUseNewUI());
    }


    @GetMapping("/getShouhouYuyueInfo")
    @ApiOperation(value = "获取售后预约详情", notes = "id为售后预约id", response = ShouhouYuyueInfoRes.class)
    public R<ShouhouYuyueInfoRes> getShouhouYuyueInfo(@RequestParam(value = "id") Integer id) {
        return R.success("获取成功", shouhouYuyueService.getShouhouYuyueInfo(id));
    }

    @GetMapping("/getWxServiceInfo")
    @ApiOperation(value = "保修状态查询", response = WxServiceInfoRes.class)
    public R<WxServiceInfoRes> getWxServiceInfo(@RequestParam(value = "imei") String imei) {
        return shouhouYuyueService.getServiceInfo(imei, true);
    }

    @GetMapping("/getYuyueConfirmUsers")
    @ApiOperation(value = "获取客服确认人", response = UserSimpleInfoRes.class)
    public R<List<UserSimpleInfoRes>> getYuyueConfirmUsers(@RequestParam("stype") Integer stype, @RequestParam("yyid") Integer yyid) {
        return R.success("请求成功", shouhouYuyueService.getYuyueConfirmUsers(stype, yyid));
    }

    @GetMapping("/yuyueConfirm")
    @ApiOperation(value = "客服确认接口")
    @RepeatSubmitCheck(argIndexs = {1})
    public R<Boolean> yuyueConfirm(@RequestParam("smUserId") Integer smUserId, @RequestParam("yyid") Integer yyid) {
        return shouhouYuyueService.yuyueConfirm(yyid, smUserId);
    }

    @GetMapping("/delYuyue")
    @ApiOperation(value = "取消预约单")
    public R<Boolean> delYuyue(@RequestParam("yyid") Integer yyid, @RequestParam("cancelType") String cancelType, @RequestParam("remark") String remark) {
        OaUserBO oaUserBO = currentRequestComponent.getCurrentStaffId();
        return shouhouYuyueService.delYuyue(yyid, cancelType, remark,oaUserBO.getUserName(), null);
    }

    @PostMapping("/saveOrUpdateYuyue")
    @ApiOperation(value = "保存或修改接口")
    public R<Integer> saveOrUpdateYuyue(@RequestBody ShouhouYuyueReq param) {
        R<Integer> result = shouhouYuyueService.saveOrUpdateYuyue(param);
        if(shouhouYuyueService.isUseNewYuYue() && result.isSuccess()){
            //判断是否新加情况
           if(ObjectUtil.isNull(param.getId()) || NumberConstant.ZERO.equals(param.getId())){
               try {
                   shouhouYuyueService.yuyueconfirmYwEnter(result.getData());
               } catch (Exception e){
                   RRExceptionHandler.logError("预约单保存转已确认异常", param, e, SpringUtil.getBean(SmsService.class)::sendOaMsgTo9JiMan);
               }
           }
        }
        return result;

    }


    @GetMapping("/hasYuyueSubByMobileAndImei")
    @ApiOperation(value = "查询是否存在其他预约单")
    public R<Integer> hasYuyueSubByMobileAndImei(String imei, String mobile) {
        return shouhouYuyueService.hasYuyueSubByMobileAndImei(mobile, imei);
    }

    @GetMapping("/getYuyueProduct")
    @ApiOperation(value = "小件可退换商品")
    public R<List<YuyueProductInfoRes>> getYuyueProduct(@RequestParam(value = "subId", required = true) String subIdStr) {
        Integer subId = Convert.toInt(subIdStr);
        return R.success(shouhouYuyueService.getYuyueProduct(subId));
    }

    @GetMapping("/getTuiProductInfoBySubId")
    @ApiOperation(value = "根据订单号获取可接件商品信息",notes = "isMobile 大小件区别 0小件、1大件 (如果是大件接件，获取返回的第一条数据，如果是小件，显示所有的信息)")
    public R<List<YuyueProductInfoRes>> getTuiProductInfoBySubId(@RequestParam(value = "subId") String subIdStr,
                                                           @RequestParam(value = "isMobile") Integer isMobile) {
        Integer subId = Convert.toInt(subIdStr);
        return R.success(shouhouYuyueService.getTuiProductInfoBySubId(subId,isMobile));
    }
    @LogRecordAround(value = "新版预约单根据单号查询信息")
    @PostMapping("/getTuiProductInfoBySubId/v2")
    public R<TuiProductInfoRes> getTuiProductInfoBySubIdV2(@RequestBody @Validated TuiProductInfoReq req) {
        TuiProductInfoRes result = Optional.ofNullable(shouhouYuyueService.getTuiProductInfoBySubIdV2(req)).orElse(new TuiProductInfoRes());
        if(CollUtil.isEmpty(result.getProductInfoList())){
            return R.error("订单不存在或不属于当前用户或没有符合的商品");
        }
        return R.success(result);
    }

    @GetMapping("/yuyueconfirmSmDdEnter")
    @ApiOperation(value = "设置为 已上门 或 已到店 （生成售后单）")
    @RepeatSubmitCheck(argIndexs = {0})
    public R<Boolean> yuyueconfirmSmDdEnter(Integer id, Integer smddType, Boolean wuliuFlag) {
        R<Boolean> booleanR = shouhouYuyueService.yuyueconfirmSmDdEnter(id, smddType, wuliuFlag);
        //.客户提交预约单申请，生成维修单后会自动提交订购申请，若客户所预约的配件与实际实际配件不符会存在订购错误的情况，导致无效的人工成本 （功能下架）
//        Map<String, Object> map = Optional.ofNullable(booleanR.getExData()).orElse(new HashMap<>());
//        Object object = map.get(ShouhouYuyueServiceImpl.ORDER_PARTS);
//        if(ObjectUtil.isNotNull(object)){
//            List<OrderPartsVo> list = JSONUtil.toList(JSONUtil.toJsonStr(object), OrderPartsVo.class);
//            for (OrderPartsVo orderPartsVo : list) {
//                shouhouYuyueService.createOrderParts(orderPartsVo);
//            }
//        }
        return booleanR;
    }


    /**
     * 维修订购
     * @param id
     * @param smddType
     * @param wuliuFlag
     * @return
     */
    @GetMapping("/orderingAccessories")
    @RepeatSubmitCheck(argIndexs = {0})
    public R<Boolean> orderingAccessories(Integer id, Integer smddType, Boolean wuliuFlag) {
        R<List<OrderPartsVo>> result = shouhouYuyueService.orderingAccessories(id, smddType, wuliuFlag);
        if(result.isSuccess()){
//            //事物分离所以抽出来处理  不然会出现死锁
//            List<OrderPartsVo> data = result.getData();
//            for (OrderPartsVo orderPartsVo : data) {
//                shouhouYuyueService.createOrderParts(orderPartsVo);
//            }
            return R.success("操作成功");
        }
        return R.error(result.getUserMsg());
    }

    @GetMapping("/yuyueconfirmYwEnter")
    @ApiOperation(value = "业务确认接口")
    @RepeatSubmitCheck(argIndexs = {0})
    public R<Boolean> yuyueconfirmYwEnter(Integer yuyueId) {
        return shouhouYuyueService.yuyueconfirmYwEnter(yuyueId);
    }

    /**
     * 预约单流程优化 处理历史数据
     * @return
     */

    @GetMapping("/handleData")
    public R<String> handleData() {
        List<HandleYuYueData> handleYuYueDataList = shouhouYuyueService.handleData();
        return R.success(JSONUtil.toJsonStr(handleYuYueDataList));
    }


    /**
     * 预约单流程优化 处理历史数据
     * @return
     */
    @PostMapping("/recoveryHandleData")
    public R<String> recoveryHandleData(@RequestBody RecoveryHandleDataVo recoveryHandleDataVo) {
        List<HandleYuYueData> handleYuYueDataList = shouhouYuyueService.recoveryHandleData(recoveryHandleDataVo.getJsonData());
        return R.success(JSONUtil.toJsonStr(handleYuYueDataList));
    }
    /**
     * 处理删除数据
     * @return
     */
    @GetMapping("/handleDataDel")
    public R<String> handleDataDel() {
        return R.success(shouhouYuyueService.handleDataDel());
    }


    @GetMapping("/checkDiamondsMember/v1")
    @ApiOperation(value = "校验钻石会员")
    public R<String> checkDiamondsMember(@RequestParam(value = "yuyueId") Integer yuyueId) {
        return shouhouYuyueService.checkDiamondsMember(yuyueId);
    }

    @PostMapping("/sendYuyueSms")
    @ApiOperation(value = " 发送预约确认短信，发送已接件短信(发送接件短信)")
    public R<Boolean> sendYuyueSms(@RequestParam(value = "type", required = true) Integer type, @RequestParam(value = "mobile", required = true) String mobile, @RequestParam(value = "id", required = true) Integer id, @RequestParam(value = "userId", required = true) Integer userId) {
        return shouhouYuyueService.sendYuyueSms(type, mobile, id, userId);
    }

    @GetMapping("/getYuyueSmsContent")
    @ApiOperation(value = "获取接件短信内容")
    public R<String> getYuyueSmsContent(@RequestParam Integer shouhouId, @RequestParam Integer userId) {
        return shouhouYuyueService.getYuyueSmsContent(shouhouId,userId);
    }

    @PostMapping("/zyYishoujian")
    @ApiOperation(value = " 中邮已接件")
    public R<Boolean> zyYishoujian(@RequestParam(value = "yuyueId", required = true) Integer yuyueId) {
        return shouhouYuyueService.zyYishoujian(yuyueId);
    }

    @GetMapping("/getPaiduiPring")
    @ApiOperation(value = " 获取排队叫号打印")
    public R<List<YuyuePaiduiPrintRes>> getPaiduiPring(String area) {
        return shouhouYuyueService.getPaiduiPring(area);
    }

    @GetMapping("/getEnums")
    @ApiOperation("获取售后预约查询列表枚举选项")
    public R<Map<String, List<?>>> listAllEnum(@RequestParam(value = "yuyueId", required = false) Integer yuyueId) {
        OaUserBO oaUserBO = currentRequestComponent.getCurrentStaffId();
        if (oaUserBO == null) {
            return R.error("请登陆");
        }
        Integer authorizedId = oaUserBO.getAuthorizeId();
        Map<String, List<?>> enumMap = new HashMap<>();
        //中邮特殊处理
        boolean isJiujiXtenant = XtenantEnum.isJiujiXtenant();
        if (AuthorizeIdEnum.CHINA_POST_NO_AIYIN_AREA.getCode().equals(authorizedId)) {
            List<EnumVO> statusEnum = EnumUtil.toEnumVOList(ZyYuyueStatusEnum.class);
            enumMap.put("status", statusEnum);
        } else {
            List<EnumVO> statusEnum = EnumUtil.toEnumVOList(YuyueStatusEnum.class);
            if (isJiujiXtenant) {
                statusEnum.removeIf(item -> Objects.equals(item.getValue(), YuyueStatusEnum.KFQR.getCode()));
            }
            if(shouhouYuyueService.isUseNewYuYue()){
                List<Integer> list = Arrays.asList(YuyueStatusEnum.YSM.getCode(), YuyueStatusEnum.YDD.getCode(), YuyueStatusEnum.YQX.getCode(), YuyueStatusEnum.WQJ.getCode());
                statusEnum.stream().forEach(item->{
                    if(item.getValue().equals(YuyueStatusEnum.YWQR.getCode())){
                        item.setLabel(YuyueStatusEnum.getNewMessageByCode(YuyueStatusEnum.YWQR.getCode()));
                    }
                });
                statusEnum.removeIf(item -> list.contains(item.getValue()));
            }
            enumMap.put("status", statusEnum);
        }
        //todo 这里服务方式要适配 输出系统要
        List<EnumVO> serviceWayEnum = EnumUtil.toEnumVOList(ServicesWayEnum.class);
        String serviceValues = sysConfigService.getValueByCode(SysConfigConstant.YU_YUE_SERVICE_WAY);
        serviceWayEnum.removeIf(e -> !serviceValues.contains(String.valueOf(e.getValue())));
        List<Integer> shangmenEnumList = Arrays.asList(ServicesWayEnum.SMQJ.getCode(),
                ServicesWayEnum.SMWX.getCode(), ServicesWayEnum.SMAZ.getCode());
        if (XtenantEnum.isJiujiXtenant()) {
            if (ObjectUtil.defaultIfNull(yuyueId,0) >0) {
                ShouhouYuyue shouhouYuyueInfo = shouhouYuyueMapper.selectById(yuyueId);
                if (Objects.isNull(shouhouYuyueInfo) || !shangmenEnumList.contains(shouhouYuyueInfo.getStype())) {
                    //售后“上门取件”业务下架
                    serviceWayEnum.removeIf(e -> shangmenEnumList.contains(e.getValue()));
                }else if(shangmenEnumList.contains(shouhouYuyueInfo.getStype())){
                    serviceWayEnum.removeIf(e -> !e.getValue().equals(shouhouYuyueInfo.getStype()) && shangmenEnumList.contains(e.getValue()));
                }
            } else if(ObjectUtil.isNotNull(yuyueId) && yuyueId == 0){
                //售后“上门取件”业务下架
                serviceWayEnum.removeIf(e -> shangmenEnumList.contains(e.getValue()));
            }
        }
        enumMap.put("serviceWay", serviceWayEnum);
        List<EnumVO> overtimeEnum = EnumUtil.toEnumVOList(OvertimeTypeEnum.class);
        enumMap.put("overTimeType", overtimeEnum);
        List<EnumVO> yuyueQueryKeyEnum = EnumUtil.toEnumVOList(YuyueQueryKeyEnum.class);
        enumMap.put("yuyueQueryKey", yuyueQueryKeyEnum);
        List<EnumVO> yuyueStype = EnumUtil.toEnumVOList(YuYueSTypeEnum.class);
        yuyueStype.removeIf(e-> !serviceValues.contains(String.valueOf(e.getValue())));
        //这里九机需要移除店面预约、将上门快修改为上门维修
        List<EnumVO> dealWayEnum = EnumUtil.toEnumVOList(DealWayEnum.class);
        if (isJiujiXtenant) {
            yuyueStype.removeIf(item -> Convert.toInt(item.getValue()).equals(YuYueSTypeEnum.DMYY.getCode()));
            yuyueStype.forEach(item -> {
                if (Convert.toInt(item.getValue()).equals(YuYueSTypeEnum.SMKX.getCode())){
                    item.setLabel("上门维修");
                }
            });
            dealWayEnum.removeIf(item -> Convert.toInt(item.getValue()).equals(DealWayEnum.KX.getCode()));
            if (ObjectUtil.defaultIfNull(yuyueId,0) >0) {
                ShouhouYuyue shouhouYuyueInfo = shouhouYuyueMapper.selectById(yuyueId);
                if (Objects.isNull(shouhouYuyueInfo) || !shangmenEnumList.contains(shouhouYuyueInfo.getStype())) {
                    //售后“上门取件”业务下架
                    yuyueStype.removeIf(e -> shangmenEnumList.contains(e.getValue()));
                }else if(shangmenEnumList.contains(shouhouYuyueInfo.getStype())){
                    yuyueStype.removeIf(e -> !e.getValue().equals(shouhouYuyueInfo.getStype()) && shangmenEnumList.contains(e.getValue()));
                }
            } else if(ObjectUtil.isNotNull(yuyueId) && yuyueId == 0){
                //售后“上门取件”业务下架
                yuyueStype.removeIf(e -> shangmenEnumList.contains(e.getValue()));
            }

        }
        enumMap.put("yuyueStype", yuyueStype);
        enumMap.put("dealWay", dealWayEnum);
        List<EnumVO> timeType = EnumUtil.toEnumVOList(YuyueTimeTypeEnum.class);
        enumMap.put("timeType", timeType);
        List<EnumVO> cancelType = EnumUtil.toEnumVOList(YuyueCancelTypeEnum.class);
        enumMap.put("cancelType", cancelType);
        enumMap.put("repairType",shouhouYuyueService.getRepairTypes());
        enumMap.put("fromSource",EnumUtil.toEnumVOList(ShouhouyuyueFromSourceEnum.class));
        return R.success(enumMap);
    }

    @GetMapping("/shouhouYuyueTimeOutMsgSend")
    @ApiOperation(value = "预约单超时推送（任务逻辑）")
    public void shouhouYuyueTimeOutMsgSend() {
        shouhouYuyueExService.shouHouYuyueTimeOutMsgSend();
    }

    @GetMapping("/shouhouYuyueTimeOutMsgSendNew")
    @ApiOperation(value = "预约单超时推送，新逻辑（任务逻辑）")
    public void shouhouYuyueTimeOutMsgSendNew() {
        shouhouYuyueExService.shouHouYuyueTimeOutMsgSendNew();
    }

    /**
     * 预约记录 按手机号码查询进行中的预约单
     *
     * @param mobile 手机号码
     * @return R
     * @see oa999UI.Controllers.ShouhouController.searchYuyueByMobile
     */
    @GetMapping("/searchYuyueByMobile")
    @ApiOperation(value = "预约记录 按手机号码查询进行中的预约单")
    public R<List<ShouhouYuyue>> searchYuyueByMobile(@RequestParam String mobile) {
        return R.success(shouhouYuyueService.list(new LambdaQueryWrapper<ShouhouYuyue>()
                .select(ShouhouYuyue::getId, ShouhouYuyue::getName, ShouhouYuyue::getImei, ShouhouYuyue::getProblem, ShouhouYuyue::getDtime)
                .eq(ShouhouYuyue::getMobile, mobile).eq(ShouhouYuyue::getIsdel, 0)
                .in(ShouhouYuyue::getStats, CollUtil.newArrayList(1, 2, 4, 6, 7))
        ));
    }

    @GetMapping("/shouhouyuyueExport")
    @ApiOperation(value = "按条件筛选导出预约列表",httpMethod = "GET")
    public void shouhouyuyueExport(@RequestParam(required = false) String key,
                                   @RequestParam(required = false) String keyVal,
                                   @RequestParam(required = false)  String isMobile,
                                   @RequestParam(required = false)  List<String> areaIds,
                                   @RequestParam(required = false) String dealWay,
                                   @RequestParam(required = false) String overtimeType,
                                   @RequestParam(required = false) String status,
                                   @RequestParam(required = false) String gaoji,
                                   @RequestParam(required = false) String servicesWay,
                                   @RequestParam(required = false) String timeType,
                                   @RequestParam(required = false)  @DateTimeFormat(pattern = "yyyy-MM-dd") String startTime,
                                   @DateTimeFormat(pattern = "yyyy-MM-dd") @RequestParam(required = false) String endTime,
                                   @RequestParam(required = false) String timeRange,
                                   @RequestParam(required = false) String cancelReason,
                                   @RequestParam(required = false)  String cancelRemark,
                                   HttpServletResponse response){
        if((StrUtil.isBlank(startTime) || StrUtil.isBlank(endTime)) && StrUtil.isNotBlank(timeRange)){
            String[] timeRanges = timeRange.split(",");
            FastDateFormat dateFormat = FastDateFormat.getInstance("EEE MMM dd yyyy HH:mm:ss' GMT 0800'", Locale.US);
            if(StrUtil.isBlank(startTime) && timeRanges.length >0){
                startTime = DateUtil.beginOfDay(DateUtil.parse(timeRanges[0], dateFormat)).toString(DatePattern.NORM_DATETIME_FORMAT);
            }
            if(StrUtil.isBlank(endTime) && timeRanges.length >1){
                endTime = DateUtil.endOfDay(DateUtil.parse(timeRanges[1], dateFormat)).toString(DatePattern.NORM_DATETIME_FORMAT);
            }
        }
        shouhouYuyueExService.shouHouYuyueExport(key,keyVal,Convert.toBool(isMobile),areaIds,Convert.toInt(dealWay),
                Convert.toInt(overtimeType),Convert.toInt(status), Convert.toInt(gaoji),Convert.toInt(servicesWay),
                Convert.toInt(timeType),startTime,endTime,cancelReason,cancelRemark, null, response);
    }

    @ApiOperation(value = "按条件筛选导出预约列表v2", notes = "param传相应的json数据", httpMethod = "POST", response = ShouhouYuyueListRes.class)
    @PostMapping("/shouhouyuyueExport/v2")
    public void shouhouyuyueExportV2(@RequestBody ShouhouYuyueListReq param,HttpServletResponse response) {
        shouhouYuyueExService.shouHouYuyueExport(param.getKey(), param.getKeyVal(),param.getIsMobile(),param.getAreaIds(),
                param.getDealWay(), param.getOvertimeType(), param.getStatus(), param.getGaoji(), param.getServicesWay(),
                param.getTimeType(), param.getStartTime(),param.getEndTime(), param.getCancelReason(), param.getCancelRemark(),
                param.getFromSource(),
                response);
    }

    @PostMapping("/updateImeiById")
    @ApiOperation(value = "根据单号修改串号信息")
    public R<Boolean> updateImeiById(@RequestBody @Validated ShouhouYuYueAlterImeiReq req) {
        return shouhouYuyueService.updateImeiById(req);
    }

    @GetMapping("getYjsxAddress")
    @ApiOperation(value = "获取邮寄送修地址")
    public R<List<YjsxAddress>> getYjsxAddress() {
        return shouhouYuyueService.getYjsxAddress();
    }

    /**
     * 获取售后预约的时间
     * @param shopId
     * @return
     */
    @GetMapping("/getShopTime/v1")
    public R<List<SelectsVo.SelectVo>> getShopTime(@RequestParam("shopId") Integer shopId, @RequestParam("date") String date){
        Result<List<SelectsVo.SelectVo>> shopTimeR = SpringUtil.getBean(WebCloud.class).getAfterServiceShopTime(shopId, date, XtenantEnum.getXtenant());
        if(shopTimeR == null){
            return R.error("getShopTime接口返回错误的结构");
        }
        if (shopTimeR.getCode() == ResultCode.SUCCESS) {
            return R.success(shopTimeR.getData());
        }
        return R.error(shopTimeR.getUserMsg());
    }

    /**
     * 通过售后预约id得到来源类型
     * @param yuyueId
     * @return
     */
    @GetMapping("/getFromSourceDescById")
    public R<String> getFromSourceDescById(@RequestParam(value = "yuyueId", required = true) Integer yuyueId){
        return R.success(shouhouYuyueService.getFromSourceDescById(yuyueId));
    }
}

