package com.jiuji.oa.afterservice.api.service;

import com.alicp.jetcache.anno.Cached;
import com.jiuji.oa.afterservice.api.bo.Ch999SimpleUserRoleBo;
import com.jiuji.oa.afterservice.api.bo.Ch999UserBasicBO;
import com.jiuji.oa.afterservice.common.constant.RedisKeys;
import com.jiuji.oa.afterservice.common.enums.BusinessTypeEnum;
import com.jiuji.oa.afterservice.shouhou.vo.*;
import com.jiuji.oa.afterservice.shouhou.vo.res.AfterSaleResultRes;
import com.jiuji.oa.afterservice.shouhou.vo.res.ShouHouDetailRes;
import com.jiuji.oa.afterservice.yuyue.vo.ShouhouAddressInfoVo;
import com.jiuji.oa.afterservice.yuyue.vo.ShouhouYuyueproductinfoVo;
import com.jiuji.oa.afterservice.yuyue.vo.req.ShouhouYuyueReq;
import com.jiuji.oa.afterservice.yuyue.vo.req.ShouhouYuyueReqVo;
import com.jiuji.oa.afterservice.yuyue.vo.res.ShouHouYuyueRes;
import com.jiuji.tc.common.vo.R;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * @Description: 售后WCF接口
 * @author: Li quan
 * @date: 2020/6/28 14:17
 */

public interface OaApiService {

    /**
     * 售后详情
     *
     * @param id
     * @param userId
     * @param imei
     * @return
     */
    R<ShouHouDetailRes> getShouHouDetail(Integer id, Integer userId, String imei);

    /**
     * 获取预约详情
     *
     * @param id
     * @param userid
     * @return
     */
    R<ShouHouYuyueRes> getYuYueDetail(Integer id, Integer userid);

    /**
     * 查询售后基本信息、会员信息
     *
     * @param id
     * @param userId
     * @param imei
     * @param isMobile 是否是通过手机号查询
     * @return
     */
    ShouHouDetailVo getShouhouInfoByIdAndUserIdAndImei(Integer id, Integer userId,
                                                       String imei, Boolean isMobile);

    /**
     * 维修库存查询
     *
     * @param shouhouId
     * @return
     */
    List<WxkcInfoVo> getWxkcOutputList(Integer shouhouId);

    /**
     * 获取中邮预约机型集合
     *
     * @param yuyueId
     * @return
     */
    List<ShouhouYuyueproductinfoVo> getYuyueProducts(Integer yuyueId);

    /**
     * 获取中邮预约图片
     *
     * @param yuyueId
     * @return
     */
    List<AttachmentsVo> getYuyuePics(Integer yuyueId);

    /**
     * 获取
     *
     * @param linkid
     * @param kind
     * @return
     */
    ShouhouAddressInfoVo getShouhouAddressInfo(Integer linkid, Integer kind);

    /**
     * 售后预约添加
     *
     * @param yuyueReqVo
     * @return
     */
    R<Integer> addShouhouYuyue(ShouhouYuyueReq yuyueReqVo);

    /**
     * 获取最新维修信息 维修专区调用
     *
     * @param rows 记录行数
     * @return R
     */
    AfterSaleResultRes getAfterSale(Integer rows);

    /**
     * 根据店面获取店长
     *
     * @param areaId    门店代码
     * @param type      1 姓名 2 工号（默认姓名）
     * @param zhiwuName
     * @param takeCount
     * @return
     */
    List<String> getShopkeeperByAreaNew(Integer areaId, Integer type, List<String> zhiwuName, Integer takeCount);

    /**
     * 拉取所有用户信息
     *
     * @return
     */
    @Cached(name = "listAllUserBasicInfo", expire = 13, timeUnit = TimeUnit.MINUTES)
    List<Ch999UserBasicBO> listAllUserBasicInfo();

    @Cached(name = RedisKeys.IMEI_QUERY_API, key = "#q", expire = 1400)
    String imeiQueryApi(String q);

    /**
     * 根据会员ID校验用户是否修过手机
     *
     * @param userId
     * @return
     */
    R<List<ShouhouRepaireUserVo>> getShouhouRepaireUserInfo(List<Long> userId);

    /**
     * 查询商品可用库存信息
     *
     * @param ppids ppid
     * @param type  1大件、2小件
     * @return
     */
    List<ProductKcInfo> queryProductStockByPpid(List<Integer> ppids, Integer type);

    /**
     * 查询商品可用库存信息
     *
     * @param ppids ppid
     * @param type  1大件、2小件
     * @return
     */
    List<ProductKcInfo> queryProductStockByPpidV3(List<Integer> ppids, Integer type);

    /**
     * 推送微信或短信通知给客户
     * @param yuyue
     * @param yuyueAreaId
     * @param isTryWechat 是否尝试推送微信
     */
    void sendWechatAndSmsNoticeToCustomer(ShouhouYuyueReqVo yuyue, Integer yuyueAreaId, boolean isTryWechat);

    /**
     * 获取用户壳膜订单
     * @param userId 会员id
     * @return 订单数量
     */
    R<Integer> getOrderClassByUserId(Integer userId);

    // mapper的getRoleNameByMobile
    Ch999SimpleUserRoleBo getRoleNameByMobile(String mobile);

    /**
     * 判断是否是用户订单
     * @param orderId
     * @param userId
     * @param orderType
     * @see BusinessTypeEnum
     * @return
     */
    R<Boolean> isUserOrder(Integer orderId, Integer userId, Integer orderType);
}
