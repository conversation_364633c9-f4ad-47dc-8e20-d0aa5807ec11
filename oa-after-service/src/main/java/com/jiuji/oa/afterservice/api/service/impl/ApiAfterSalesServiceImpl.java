package com.jiuji.oa.afterservice.api.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.jiuji.cloud.after.vo.req.AfterCountReq;
import com.jiuji.cloud.after.vo.req.QueryAfterSalesCouponVo;
import com.jiuji.cloud.after.vo.req.UseCouponVO;
import com.jiuji.cloud.after.vo.req.XiaojianSubReqVO;
import com.jiuji.cloud.after.vo.res.*;
import com.jiuji.oa.afterservice.api.service.ApiAfterSalesService;
import com.jiuji.oa.afterservice.bigpro.bo.YuyueTuiDataBo;
import com.jiuji.oa.afterservice.bigpro.dao.ShouhouMapper;
import com.jiuji.oa.afterservice.bigpro.enums.YuyueStatusEnum;
import com.jiuji.oa.afterservice.bigpro.po.Shouhou;
import com.jiuji.oa.afterservice.bigpro.po.ShouhouYuyue;
import com.jiuji.oa.afterservice.bigpro.po.UseYouhuiMaInfo;
import com.jiuji.oa.afterservice.bigpro.service.NumberCardService;
import com.jiuji.oa.afterservice.bigpro.service.ShouhouExService;
import com.jiuji.oa.afterservice.bigpro.service.ShouhouYuyueService;
import com.jiuji.oa.afterservice.bigpro.vo.res.NumberCardRes;
import com.jiuji.oa.afterservice.common.bo.OaUserBO;
import com.jiuji.oa.afterservice.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.afterservice.common.enums.XtenantEnum;
import com.jiuji.oa.afterservice.common.exception.CustomizeException;
import com.jiuji.oa.afterservice.other.document.SmallproLogDocument;
import com.jiuji.oa.afterservice.other.po.Areainfo;
import com.jiuji.oa.afterservice.other.service.AreainfoService;
import com.jiuji.oa.afterservice.rabbitmq.SmallproRabblitMq;
import com.jiuji.oa.afterservice.shouhou.vo.req.MaintenanceReq;
import com.jiuji.oa.afterservice.shouhou.vo.res.MaintenanceRes;
import com.jiuji.oa.afterservice.shouhou.vo.res.ShouhouInfo;
import com.jiuji.oa.afterservice.smallpro.dao.SmallproMapper;
import com.jiuji.oa.afterservice.smallpro.enums.SmallProStatsEnum;
import com.jiuji.oa.afterservice.smallpro.po.Smallpro;
import com.jiuji.oa.afterservice.smallpro.service.SmallproLogService;
import com.jiuji.oa.afterservice.smallpro.service.SmallproService;
import com.jiuji.oa.afterservice.smallpro.yearcardtransfer.entity.YearPackageTransferPo;
import com.jiuji.oa.afterservice.smallpro.yearcardtransfer.service.IYearPackageTransferService;
import com.jiuji.oa.afterservice.sub.service.CategoryService;
import com.jiuji.oa.orginfo.areainfo.vo.AreaInfo;
import com.jiuji.oa.orginfo.sysconfig.client.SysConfigClient;
import com.jiuji.oa.orginfo.userinfo.client.UserInfoClient;
import com.jiuji.oa.orginfo.userinfo.vo.Ch999UserVo;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.common.vo.ResultCode;
import com.jiuji.tc.utils.common.CommonUtils;
import com.jiuji.tc.utils.common.build.LambdaBuild;
import com.jiuji.tc.utils.constants.NumberConstant;
import com.jiuji.tc.utils.constants.SysConfigConstant;
import jodd.typeconverter.Convert;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class ApiAfterSalesServiceImpl implements ApiAfterSalesService {


    @Resource
    private  NumberCardService numberCardService;
    @Resource
    private  ShouhouExService shouhouExService;
    @Resource
    private SmallproRabblitMq smallproRabblitMq;
    @Resource
    private AreainfoService areainfoService;
    @Resource
    private AbstractCurrentRequestComponent currentRequestComponent;
    @Resource
    private ShouhouMapper shouhouMapper;
    @Resource
    private SmallproMapper smallproMapper;
    @Resource
    private UserInfoClient userInfoClient;
    @Resource
    private SmallproLogService smallproLogService;
    @Resource
    private SysConfigClient sysConfigClient;
    @Resource
    private CategoryService categoryService;
    @Resource
    private ShouhouYuyueService yuyueService;
    @Resource
    private SmallproService smallproService;



    @Override
    public AfterCountVO selectAfterCount(AfterCountReq afterCountReq) {
        Integer subId = Optional.ofNullable(afterCountReq.getSubId()).orElseThrow(() -> new CustomizeException("订单号不能为空"));

        //查询没有被删除的维修单
        List<ShouhouYuyue> list = yuyueService.lambdaQuery().eq(ShouhouYuyue::getSubId, subId)
                .ne(ShouhouYuyue::getStats, YuyueStatusEnum.YSC.getCode())
                .list();
        AfterCountVO afterCountVO = new AfterCountVO();
        List<AfterCountInfoVO> infoVOArrayList = new ArrayList<>();
        if(CollectionUtils.isEmpty(list)){
            return afterCountVO;
        }
        //过滤出来已经完成的预约单
        List <ShouhouYuyue> completedYuYueList = list.stream().filter(item -> YuyueStatusEnum.YWC.getCode().equals(item.getStats())).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(completedYuYueList)){
            //收集小件单id
            List<Integer> smallProIdList = completedYuYueList.stream().map(ShouhouYuyue::getSmallproid).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(smallProIdList)){
                //查询处理中的小件单
                List<Integer> processingIdList = Optional.ofNullable(CommonUtils.bigDataInQuery(smallProIdList, ids ->
                        smallproService.lambdaQuery().in(Smallpro::getId, ids)
                                .eq(Smallpro::getStats, SmallProStatsEnum.SMALL_PRO_STATS_PROCESSING.getCode())
                                .list()
                ).stream().map(Smallpro::getId).collect(Collectors.toList())).orElse(new ArrayList<>());
                completedYuYueList.forEach(item->{
                    if(processingIdList.contains(item.getSmallproid())){
                        countYuYueList(item,infoVOArrayList);
                    }
                });
            }
        }
        //过滤出来未完成完成的预约单
        List <ShouhouYuyue> unCompletedYuYueList = list.stream().filter(item -> !YuyueStatusEnum.YWC.getCode().equals(item.getStats())).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(unCompletedYuYueList)){
            unCompletedYuYueList.forEach(item->{
                countYuYueList(item,infoVOArrayList);
            });
        }
        //数据根据basketId进行聚合
        if(CollectionUtils.isNotEmpty(infoVOArrayList)){
            List<AfterCountInfoVO> polymericList = infoVOArrayList.stream()
                    .collect(Collectors.groupingBy(AfterCountInfoVO::getBasketId))
                    .entrySet().stream()
                    .map(entry -> {
                        AfterCountInfoVO afterCountInfoVO = new AfterCountInfoVO();
                        afterCountInfoVO.setBasketId(entry.getKey());
                        afterCountInfoVO.setAfterCount(entry.getValue().stream()
                                .map(AfterCountInfoVO::getAfterCount)
                                .filter(ObjectUtil::isNotNull)
                                .mapToInt(Integer::intValue)
                                .sum());
                        return afterCountInfoVO;
                    })
                    .collect(Collectors.toList());
            afterCountVO.setAfterCountInfoVOList(polymericList);
        } else {
            afterCountVO.setAfterCountInfoVOList(infoVOArrayList);
        }
        return afterCountVO;
    }

    /**
     * 解析统计
     * @param item
     * @param infoVOArrayList
     */
    private void countYuYueList(ShouhouYuyue item,List<AfterCountInfoVO> infoVOArrayList){
        String tuidata = item.getTuidata();
        if(StringUtils.isNotEmpty(tuidata)){
            YuyueTuiDataBo yuyueTuiDataBo =Optional.ofNullable(JSON.parseObject(tuidata, YuyueTuiDataBo.class)).orElse(new YuyueTuiDataBo()) ;
            List<YuyueTuiDataBo.Basket> basketList = yuyueTuiDataBo.getBasket();
            if(CollectionUtils.isNotEmpty(basketList)){
                basketList.forEach(obj->{
                    AfterCountInfoVO afterCountInfoVO = new AfterCountInfoVO();
                    afterCountInfoVO.setAfterCount(obj.getCount());
                    afterCountInfoVO.setBasketId(obj.getId());
                    infoVOArrayList.add(afterCountInfoVO);
                });
            }
        }
    }

    @Override
    public MaintenanceRes selectMaintenanceOrder(MaintenanceReq req) {
        Integer userId = req.getUserId();
        if(ObjectUtil.isNull(userId)){
            throw new CustomizeException("用户id不能为空");
        }

        MaintenanceRes maintenanceRes = new MaintenanceRes();
        List<Shouhou> list = shouhouMapper.selectMaintenanceOrder(req);
        if (CollectionUtils.isNotEmpty(list)){
            List<ShouhouInfo> collect = list.stream().map(item -> {
                ShouhouInfo shouhouBasicInfo = new ShouhouInfo();
                BeanUtils.copyProperties(item, shouhouBasicInfo);
                return shouhouBasicInfo;
            }).collect(Collectors.toList());
            maintenanceRes.setShouhouList(collect);
        }
        return maintenanceRes;
    }

    /**
     * 查询小件维修单
     *
     * @param req
     * @return
     */
    @Override
    public XiaojianSubResVO getXiaojianSub(XiaojianSubReqVO req) {
        XiaojianSubResVO xiaojianSub = smallproMapper.getXiaojianSub(req);
        if (xiaojianSub == null) {
            throw new CustomizeException("该维修单不是当前账号的维修单，请使用送修账号查看！");
        }
        List<XiaojianProductVO> xiaojianProducts = smallproMapper.getXiaojianProductList(req);
        if (CollectionUtils.isNotEmpty(xiaojianProducts)) {
            xiaojianProducts.forEach(x -> x.setProductUrl(Optional.ofNullable(x.getProductUrl())
                    .map(v -> {
                        String picUrl = v;
                        if (StringUtils.isEmpty(picUrl)) {
                            return null;
                        }
                        picUrl = StringUtils.stripEnd(picUrl, "/");
                        String pa = "^\\d{1,9}/.*";
                        Pattern pattern = Pattern.compile(pa);
                        String url = sysConfigClient.getValueByCodeAndXtenant(SysConfigConstant.IMG_URL, XtenantEnum.getXtenant()).getData();
                        if (pattern.matcher(picUrl).matches()) {
                            url += "/newstatic/" + picUrl;
                        } else {
                            url += "/pic/product/440x440/" + picUrl;
                        }
                        return url;
                    }).orElse("")));
            List<Integer> cidList = xiaojianProducts.stream().map(XiaojianProductVO::getCid).collect(Collectors.toList());
            xiaojianSub.setPjKinds(getPjKings(cidList));
        }
        xiaojianSub.setProducts(xiaojianProducts);

        if (xiaojianSub.getBuySubId() != null && xiaojianSub.getBuySubId() > 0) {
            //历史维修次数
            Integer wcount = smallproMapper.getTuihuanHistCount(xiaojianSub.getBuySubId());
            xiaojianSub.setWcount(wcount);
        }

        if (StringUtils.isNotBlank(xiaojianSub.getInuser())) {
            //接件人信息
            R<Ch999UserVo> r = userInfoClient.getCh999UserByUserName(xiaojianSub.getInuser());
            if (r.getCode() == ResultCode.SUCCESS && r.getData() != null) {
                xiaojianSub.setInuserCh999Id(r.getData().getCh999Id());
                /*String headUrl = Optional.ofNullable(userInfoClient.getImgByCh999Id(r.getData().getCh999Id()))
                        .filter(v -> v.getCode() == ResultCode.SUCCESS)
                        .map(v -> v.getData().stream().findFirst().map(AppHeadImgVO::getUrl).orElse("")).orElse("");*/
                xiaojianSub.setInuserHeaderImage(r.getData().getAvatar());
            }
        }
        //日志
        List<SmallproLogDocument> logList = smallproLogService.findBySmallproId(req.getId());
        if (CollectionUtils.isNotEmpty(logList)) {
            List<SmallproLogModel> logs = logList.stream().filter(v -> v.getShowType() == 1)
                    .map(v -> LambdaBuild.create(SmallproLogModel.class)
                            .set(SmallproLogModel::setSmallproId, v.getSmallproId())
                            .set(SmallproLogModel::setInuser, v.getInUser())
                            .set(SmallproLogModel::setIndate, v.getInDate())
                            .set(SmallproLogModel::setComment, v.getComment())
                            .build())
                    .collect(Collectors.toList());
            xiaojianSub.setLogs(logs);
        }
        List<Integer> cids = categoryService.getProductChildCidList(Collections.singletonList(43));
        if (xiaojianSub.getProducts().stream().allMatch(p -> cids.contains(p.getCid()))) {
            xiaojianSub.setShowBaoxiuStatus(false);
        }
//        if (ObjectUtil.isNotNull(xiaojianSub.getOldId()) && ObjectUtil.isNotNull(xiaojianSub.getOldIdType())) {
//            if (OldIdTypeEnum.SALE_TYPE.getCode().equals(xiaojianSub.getOldIdType())) {
//                String orderUrl = String.format("<a href= /staticpc/#/small-refund/%s>%s</a>", xiaojianSub.getOldId(), xiaojianSub.getOldId());
//                xiaojianSub.setOldIdUrl(orderUrl);
//            } else if (OldIdTypeEnum.SMALL_PRO_TYPE.getCode().equals(xiaojianSub.getOldIdType())) {
//                //在原来的小件单上面添加生成现货单日志
//                String orderUrl = String.format("<a href= /staticpc/#/small-refund/%s>%s</a>", xiaojianSub.getOldId(), xiaojianSub.getOldId());
//                xiaojianSub.setOldIdUrl(orderUrl);
//            }
//        }
        // 赠送年包的小件单, 直接抹去订单号
        if(SpringUtil.getBean(IYearPackageTransferService.class).lambdaQuery().eq(YearPackageTransferPo::getSmallId, req.getId()).count() >0){
            xiaojianSub.setBuySubId(null);
        }
        return xiaojianSub;
    }

    private String getPjKings(List<Integer> cids) {
        List<Integer> tieMoCids = categoryService.tieMoCids();
        if (CollectionUtils.isNotEmpty(cids) && CollectionUtils.isNotEmpty(tieMoCids)) {
            if (cids.stream().allMatch(v -> tieMoCids.contains(v))) {
                return "KT";
            }
        }
        return "KX";
    }

    @Override
    public R<List<AfterSalesCouponVO>> selectAfterSalesCoupon(QueryAfterSalesCouponVo queryAfterSalesCouponVo) {
        Integer shouhouId = queryAfterSalesCouponVo.getShouhouId();
        Long userId = queryAfterSalesCouponVo.getUserId();
        Boolean isSoft = Optional.ofNullable(queryAfterSalesCouponVo.getIsSoft()).orElse(Boolean.FALSE);
        Areainfo areaInfo = areainfoService.getById(queryAfterSalesCouponVo.getAreaId());
        AreaInfo area = new AreaInfo();
        BeanUtils.copyProperties(areaInfo,area);
        //oa用户信息处理,需要模拟用户信息
        OaUserBO oaUser = smallproRabblitMq.simulateUser(area,queryAfterSalesCouponVo.getCurrUser());
        R<List<NumberCardRes>> result = currentRequestComponent.invokeWithUser(Convert.toLong(oaUser.getXTenant()), oaUser, user -> numberCardService.getKxYouhuimas(userId, isSoft, shouhouId));
        int code = result.getCode();
        List<NumberCardRes> data = result.getData();
        //判断查询成功并且数据不为空
        if(NumberConstant.ZERO==code && CollectionUtils.isNotEmpty(data)){
            List<AfterSalesCouponVO> numberCardResVOSList = new ArrayList<>();
            data.forEach(item->{
                AfterSalesCouponVO afterSalesCouponVO = new AfterSalesCouponVO();
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                afterSalesCouponVO.setEndTime(formatter.format(item.getEndTime()));
                afterSalesCouponVO.setStartTime(formatter.format(item.getStartTime()));
                afterSalesCouponVO.setCardID(item.getCardID());
                afterSalesCouponVO.setGName(item.getGName());
                afterSalesCouponVO.setLimitprice(item.getLimitprice());
                afterSalesCouponVO.setTotal(item.getTotal());
                afterSalesCouponVO.setLimit1name(item.getLimit1name());
                numberCardResVOSList.add(afterSalesCouponVO);
            });
            numberCardResVOSList.forEach(item->{
                String cardID = item.getCardID();
                R<UseYouhuiMaInfo> useYouhuiMaInfoR = currentRequestComponent.invokeWithUser(Convert.toLong(oaUser.getXTenant()), oaUser, user -> shouhouExService.checkYouhuiMa(shouhouId, cardID));
                if(useYouhuiMaInfoR.getCode()!=NumberConstant.ZERO){
                    item.setIsUse(NumberConstant.ZERO);
                    item.setIsNotUseReasons(Optional.ofNullable(useYouhuiMaInfoR.getUserMsg()).orElse(useYouhuiMaInfoR.getMsg()));
                } else {
                    item.setIsUse(NumberConstant.ONE);
                }
            });
            return R.success(numberCardResVOSList);
        }
        return R.error(Optional.ofNullable(result.getUserMsg()).orElse(result.getMsg()));
    }

    @Override
    public R<Boolean> useAfterSalesCoupon(UseCouponVO useCouponVO) {
        Integer shouhouId = useCouponVO.getShouhouId();
        String operateUser = useCouponVO.getOperateUser();
        String code = useCouponVO.getCode();
        Areainfo areaInfo = areainfoService.getById(useCouponVO.getAreaId());
        AreaInfo area = new AreaInfo();
        BeanUtils.copyProperties(areaInfo,area);
        //oa用户信息处理,需要模拟用户信息
        OaUserBO oaUser = smallproRabblitMq.simulateUser(area,useCouponVO.getOperateUser());
        return currentRequestComponent.invokeWithUser(Convert.toLong(oaUser.getXTenant()), oaUser, user -> shouhouExService.useYouhuiMa(shouhouId, code, operateUser));
    }

}
