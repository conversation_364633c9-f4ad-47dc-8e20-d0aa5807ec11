package com.jiuji.oa.afterservice.delayQueuePush.controller;


import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.jiuji.oa.afterservice.bigpro.po.WeixinUser;
import com.jiuji.oa.afterservice.bigpro.service.SmsService;
import com.jiuji.oa.afterservice.bigpro.service.WeixinUserService;
import com.jiuji.oa.afterservice.cloud.service.IMCloud;
import com.jiuji.oa.afterservice.common.bo.OaUserBO;
import com.jiuji.oa.afterservice.common.constant.RedisKeys;
import com.jiuji.oa.afterservice.common.enums.BusinessTypeV1Enum;
import com.jiuji.oa.afterservice.common.enums.ESmsChannelTypeEnum;
import com.jiuji.oa.afterservice.common.enums.XtenantEnum;
import com.jiuji.oa.afterservice.common.util.DateUtil;
import com.jiuji.oa.afterservice.delayQueuePush.service.AppointmentFormPushService;
import com.jiuji.oa.afterservice.delayQueuePush.vo.AppointmentFormPush;
import com.jiuji.oa.afterservice.refund.vo.req.CommonValidVo;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.common.vo.ResultCode;
import com.jiuji.tc.utils.common.CommonUtils;
import com.jiuji.tc.utils.constants.NumberConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Optional;


/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/LmstfyController")
public class LmstfyController {


    @Resource
    private AppointmentFormPushService appointmentFormPushService;
    @Resource
    private IMCloud imCloud;
    @Resource
    private WeixinUserService weixinUserService;
    @Resource
    private SmsService smsService;

    /**
     * 延迟队列测试
     * @param appointmentFormPush
     * @return
     */
    @PostMapping("/test")
    public R<String> pushMsgTest(@RequestBody AppointmentFormPush appointmentFormPush){
        appointmentFormPushService.pushMsg(appointmentFormPush);
        return R.success("推送成功");
    }



    @GetMapping("/pushWeiXin")
    public R<String> pushWeiXin(){
        BusinessTypeV1Enum businessType =BusinessTypeV1Enum.COMMON;
        int code = CommonUtils.getRandom4Code();

        SmsService smsService = SpringUtil.getBean(SmsService.class);
        String msg = StrUtil.format("您正在进行[{}]操作，验证码{}，5分钟内有效，售后单号：{}。如非本人操作请忽略。", businessType.getMessage(),code, 123456);
        R<Boolean> result = smsService.sendSms("***********",msg
                , DateUtil.localDateTime2LocalDate(LocalDateTime.now().minusMinutes(NumberConstant.TWO))
                , "刘昊楠", smsService.getSmsChannelByTenant(949, ESmsChannelTypeEnum.VERIFICATION_CODE));

        return R.success("推送成功");
    }



}
