package com.jiuji.oa.afterservice.rabbitmq;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.jiuji.oa.afterservice.bigpro.po.Shouhou;
import com.jiuji.oa.afterservice.bigpro.service.ShouhouQujiLosswarnService;
import com.jiuji.oa.afterservice.bigpro.service.ShouhouService;
import com.jiuji.oa.afterservice.bigpro.service.SmsService;
import com.jiuji.oa.afterservice.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.afterservice.common.config.rabbimq.RabbitMqConfig;
import com.jiuji.oa.afterservice.common.enums.MTableInfoEnum;
import com.jiuji.oa.afterservice.common.enums.XtenantEnum;
import com.jiuji.oa.afterservice.common.exception.RRExceptionHandler;
import com.jiuji.oa.afterservice.common.util.CommenUtil;
import com.jiuji.oa.orginfo.areainfo.client.AreaInfoClient;
import com.jiuji.oa.orginfo.areainfo.vo.AreaInfo;
import com.jiuji.tc.utils.common.TraceIdUtil;
import com.jiuji.tc.utils.xtenant.Namespaces;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.*;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.charset.Charset;

/**
 * 售后取机的消息队列管理
 * <AUTHOR>
 * @since 2022/6/15 15:27
 */
@Component
@Slf4j
public class ShouhouQujiRabblitMq {

    @Resource
    private SmsService smsService;
    @Resource
    private AreaInfoClient areaInfoClient;
    /**
     * 获取用户信息
     * 这里必须用 invokeWithUser
     */
    @Resource
    private ShouhouService shouhouService;
    /**
     * 售后取机
     */
    @Resource
    private ShouhouQujiLosswarnService shouhouQujiLosswarnService;

    /**
     *
     * @param message
     */
    @RabbitHandler
    @RabbitListener(bindings = {
            @QueueBinding(                  //exchange与队列的绑定
                    value = @Queue(RabbitMqConfig.SHOUHOU_QUJI_LOSSWARNING),         //绑定队列，如果没有指定队列名称，系统会给一个生成一个临时的队列名
                    exchange = @Exchange(value = RabbitMqConfig.AFTER_SHOUHOU_QU_JI,type = "fanout") //指定exchange的名称和类型
            )}, containerFactory = "oaListenerContainerFactory")
    @SneakyThrows
    public void shouhouQujiLosswarning(Message message){

        String body = StrUtil.str(message.getBody(), Charset.defaultCharset());
        try{
            MDC.put(TraceIdUtil.TRACE_ID_KEY,TraceIdUtil.getTraceId());
            log.warn("售后单亏损推送单号: {}", body);
            if(StrUtil.isBlank(body) || !NumberUtil.isInteger(body)){
                log.error("售后单号错误");
                return;
            }
            Integer shouhouId = Convert.toInt(body);
           // Shouhou shouhou = shouhouService.getById(shouhouId);
            Shouhou shouhou = CommenUtil.autoQueryHist(() ->shouhouService.getById(shouhouId), MTableInfoEnum.SHOUHOU,shouhouId);
            if(shouhou == null){
                log.error("维修单没有记录",body);
                return;
            }
            AreaInfo currAreaInfo = areaInfoClient.getAreaInfoById(ObjectUtil.defaultIfNull(shouhou.getToareaid(),shouhou.getAreaid())).getData();
            if (currAreaInfo == null){
                log.error("售后取机亏损预警,单号[{}],门店信息为空",body);
                return;
            }
            Namespaces.set(currAreaInfo.getXtenant());
            if(!XtenantEnum.isJiujiXtenant()){
                return;
            }
            //售后异常标注
            SpringUtil.getBean(AbstractCurrentRequestComponent.class).invokeWithUser(Convert.toLong(currAreaInfo.getXtenant()),
                    SpringUtil.getBean(SmallproRabblitMq.class).simulateUser(currAreaInfo,"系统"),
                    oaUser-> {
                        shouhouQujiLosswarnService.qujiLosswarn(shouhou,currAreaInfo);
                        return null;
                    });
        }catch (Exception e) {
            RRExceptionHandler.logError("售后单亏损推送消费", body, e, smsService::sendOaMsgTo9JiMan);
        }finally {
            MDC.remove(TraceIdUtil.TRACE_ID_KEY);
        }

    }
}
