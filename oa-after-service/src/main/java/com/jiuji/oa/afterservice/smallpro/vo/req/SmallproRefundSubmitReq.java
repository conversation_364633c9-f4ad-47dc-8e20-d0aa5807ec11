package com.jiuji.oa.afterservice.smallpro.vo.req;

import com.jiuji.oa.afterservice.smallpro.bo.SmallproBasketTinyInfoBO;
import com.jiuji.oa.afterservice.smallpro.bo.smallproExchange.SmallproRefundNetPayBO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * description: <小件接件提交退款单Req>
 * translation: <Small Refund Submit Submission Req>
 *
 * <AUTHOR>
 * @date 2020/3/13
 * @since 1.0.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class SmallproRefundSubmitReq implements Serializable {
    private static final long serialVersionUID = 8369210499153407667L;
    /**
     * 原订单Id
     */
    @ApiModelProperty("原订单Id")
    private Integer subId;
    /**
     * 退款金额
     */
    @ApiModelProperty("退款金额")
    private BigDecimal tuikuanM;

    /**
     *
     */
    @ApiModelProperty
    private BigDecimal tuikuanM1;
    /**
     * 折价金额
     */
    @ApiModelProperty("折价金额")
    private BigDecimal zhejiaM;
    /**
     * 退换类型
     * @see com.jiuji.oa.afterservice.common.enums.TuihuanKindEnum
     */
    @ApiModelProperty("退换类型")
    private Integer tuihuanKind;
    /**
     * 退款方式
     */
    @ApiModelProperty("退款方式")
    private String tuiWay;
    /**
     * 银行名称
     */
    @ApiModelProperty("银行名称")
    private String bankName;
    /**
     * 银行户名
     */
    @ApiModelProperty("银行户名")
    private String bankfuming;
    /**
     * 银行账户
     */
    @ApiModelProperty("银行账户")
    private String banknumber;
    /**
     * shouhou_tuihuanId
     */
    @ApiModelProperty("售后退换Id,shouhou_tuihuanId")
    private Long shouhouId;
    /**
     * 是否退手续费[0不退|1退]
     */
    @ApiModelProperty("是否退手续费[0不退|1退]")
    private Boolean isRefundHandlingFee;
    /**
     * 网络支付信息列表
     */
    private List<SmallproRefundNetPayBO> netPay;
    /**
     * 是否退运费[0不退|1退]
     */
    @ApiModelProperty("是否退运费[0不退|1退]")
    private Boolean isRefundFreight;
    /**
     *
     */
    @ApiModelProperty
    private BigDecimal coinM;
    /**
     *
     */
    @ApiModelProperty
    private BigDecimal kuBaitiaoM;
    /**
     *
     */
    @ApiModelProperty
    private BigDecimal baitiaoM;
    /**
     * 退款商品信息，basketId-basketCount ,List
     */
    @ApiModelProperty("退款商品信息，basketId-basketCount ,List")
    private List<SmallproBasketTinyInfoBO> basketInfoList;
    /**
     *
     */
    @ApiModelProperty("")
    private String comment;
    /**
     *
     */
    @ApiModelProperty("")
    private String basketIds;
    /**
     * 是否需要验证码验证
     */
    @ApiModelProperty("是否需要验证码验证")
    private String isValidt;
}
