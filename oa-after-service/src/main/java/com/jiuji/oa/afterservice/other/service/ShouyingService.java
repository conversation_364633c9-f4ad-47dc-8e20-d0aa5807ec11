package com.jiuji.oa.afterservice.other.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.oa.afterservice.other.po.Shouying;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-25
 */
public interface ShouyingService extends IService<Shouying> {


    /**
     * description: <SqlServer-list>
     * translation: <SqlServer-list>
     *
     * @param wrapper 筛选条件
     * @return java.util.List<com.jiuji.oa.afterservice.other.po.Shouying>
     * <AUTHOR>
     * @date 9:37 2020/3/25
     * @since 1.0.0
     **/
    List<Shouying> listSqlServer(@Param(Constants.WRAPPER) Wrapper wrapper);


    /**
     * description: <SqlServer-getById>
     * translation: <SqlServer-getById>
     *
     * @param id 主键
     * @return com.jiuji.oa.afterservice.other.po.Shouying
     * <AUTHOR>
     * @date 9:37 2020/3/25
     * @since 1.0.0
     **/
    Shouying getByIdSqlServer(Integer id);

}
