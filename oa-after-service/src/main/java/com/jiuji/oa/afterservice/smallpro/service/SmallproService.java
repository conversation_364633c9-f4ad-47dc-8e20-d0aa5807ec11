package com.jiuji.oa.afterservice.smallpro.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.cloud.after.vo.req.CutScreenReq;
import com.jiuji.cloud.after.vo.req.SmallExchangeReq;
import com.jiuji.oa.afterservice.bigpro.po.Productinfo;
import com.jiuji.oa.afterservice.bigpro.po.ShouhouMsgconfig;
import com.jiuji.oa.afterservice.common.bo.OaUserBO;
import com.jiuji.oa.afterservice.common.vo.PageVo;
import com.jiuji.oa.afterservice.common.vo.res.CommonDataRes;
import com.jiuji.oa.afterservice.common.vo.res.CommonTitleRes;
import com.jiuji.oa.afterservice.other.bo.LogisticsRecipientBO;
import com.jiuji.oa.afterservice.other.po.Areainfo;
import com.jiuji.oa.afterservice.small.ExchangeProductListVO;
import com.jiuji.oa.afterservice.small.SmallYuyueOrderOnGoingVO;
import com.jiuji.oa.afterservice.smallpro.bo.SmallproOperationInfoBO;
import com.jiuji.oa.afterservice.smallpro.bo.filmCard.FilmAccessoriesBo;
import com.jiuji.oa.afterservice.smallpro.bo.filmCard.FilmAccessoriesV2Item;
import com.jiuji.oa.afterservice.smallpro.bo.smallproInfo.NotSoldBackBO;
import com.jiuji.oa.afterservice.smallpro.enums.SmallProServiceTypeEnum;
import com.jiuji.oa.afterservice.smallpro.po.Smallpro;
import com.jiuji.oa.afterservice.smallpro.po.SmallproBill;
import com.jiuji.oa.afterservice.smallpro.po.exchange.SmallExchangeConfigPo;
import com.jiuji.oa.afterservice.smallpro.vo.req.*;
import com.jiuji.oa.afterservice.smallpro.vo.res.*;
import com.jiuji.oa.afterservice.statistics.vo.req.SmallproAddLogBatchReq;
import com.jiuji.oa.loginfo.smallprobill.client.vo.SmallproBillLogVo;
import com.jiuji.oa.oacore.oaorder.req.SubWLModelReq;
import com.jiuji.oa.oacore.oaorder.res.MiniFileRes;
import com.jiuji.tc.common.vo.R;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Optional;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-11-13
 */
public interface SmallproService extends IService<Smallpro> {

    /**
     * 串号查询
     * @param imei
     * @return
     */
    ProductInfoListByImeiReq selectProductInfoListByImei(String imei);
    /**
     * 取机流程修改校验
     * @param pickUpCheckReq
     * @return
     */
    PickUpCheckRes pickUpCheck(PickUpCheckReq pickUpCheckReq);

    /**
     * 是否使用新版小件单流程
     * @return
     */
    Boolean isNewSmallPro(Integer areaId);

    void fixOldUserId ();


    /**
     *
     * @param basketId
     * @return
     */
    Integer handleProductBindType(Integer basketId);
    /**
     *
     * @param automaticBindImeiReq
     */
    void automaticBindImei(AutomaticBindImeiReq automaticBindImeiReq);
    /**
     * 获取损耗数量
     * @return
     */
    Integer selectQuantityOfLoss(Integer oldIdType,Integer oldId);

    /**
     * 小件单生成现货单
     * @param req
     * @return
     */
    AutGeneratedCashRes autGeneratedCash(AutGeneratedCashReq req );


    /**
     * 销售单生成现货单
     * @param req
     * @return
     */
    AutGeneratedCashRes autGeneratedCashByBasketId(AutGeneratedCashByBasketIdReq req );


    /**
     * 小件库存查询
     * @param req
     * @return
     */
    SelectKcCountRes selectKcCount(SelectKcCountReq req );


    /**
     * 损耗详情查询你
     * @param req
     * @return
     */
    SelectKcCountRes selectLossInfo( SelectLossInfoReq req );

    /**
     *
     * @param req
     * @return
     */
    BindImeiRes selectBindImei(BindImeiReq req);

    /**
     * 取机并且自动完成现货单
     * @param req
     * @return
     */
    R<Boolean> autGeneratedCashAndPickUp(@RequestBody @Validated AutGeneratedCashReq req );

    /**
     * 小件单提交二次确认
     * @param req
     * @return
     */
    String secondaryConfirmation(SmallproReq req);

    /**
     * 历史数据查询
     * @param req
     * @return
     */
    List<HistoricalProcessingRes> selectHistoricalProcessing(HistoricalProcessingReq req);
    /**
     * 是否为换其他型号
     * @param id
     * @return
     */
    boolean isHqtxh(Integer id);


    /**
     *   返回false  那就是要不生成销售单 走原始逻辑
     *   返回true   那就是生成销售单
     * @param smallpro
     * @return
     */
    boolean isHqtxh(Smallpro smallpro);


    /**
     * 是否为换其他型号
     * @param req
     * @return
     */
    HqtxhRes generateSalesOrder(HqtxhReq req);

    // region 逻辑方法

    // region 删除小件接件单

    /**
     * description: <删除小件接件单>
     * translation: <Delete small order>
     *
     * @param id 小件接件单号
     * @return com.jiuji.oa.afterservice.smallpro.vo.res.SmallproNormalCodeMessageRes
     * <AUTHOR>
     * @date 15:45 2020/4/10
     * @since 1.0.0
     **/
    SmallproNormalCodeMessageRes delSmallpro(Integer id);

    // endregion

    // region 获取小件接件查询筛选列表

    /**
     * description: <获取小件接件查询筛选列表>
     * translation: <Get the smallpro pickup filter list>
     *
     * @return com.jiuji.oa.afterservice.smallpro.vo.res.SmallproQueryConditionRes
     * <AUTHOR>
     * @date 16:50 2019/11/19
     * @since 1.0.0
     **/
    SmallproQueryConditionRes getSmallproQueryCondition();
    // endregion

    // region 获取小件接件详情页面选择列表

    /**
     * description: <获取小件接件详情页面选择列表>
     * translation: <Get smallpro details selection list>
     *
     * @return com.jiuji.oa.afterservice.smallpro.vo.res.SmallproInfoSelectionRes
     * <AUTHOR>
     * @date 14:58 2019/11/25
     * @since 1.0.0
     **/
    SmallproInfoSelectionRes getSmallproInfoSelection();

    // endregion

    // region 获取小件接件单列表

    /**
     * description: <获取小件接件单列表>
     * translation: <Get a list of smallpro>
     *
     * @param query       筛选条件
     * @param pageSize    页面尺寸
     * @param currentPage 当前页
     * @param isWithOperateLog
     * @return com.jiuji.oa.afterservice.common.vo.PageVo
     * <AUTHOR>
     * @date 16:57 2020/4/10
     * @since 1.0.0
     **/
    R<PageVo> getSmallproPage(SmallproReturnGoodsReq query, Integer pageSize, Integer currentPage, boolean isWithOperateLog);

    // endregion

    // region 获取小件接件日志列表

    /**
     * description: <获取小件接件日志列表>
     * translation: <Get the list of small parts log>
     *
     * @param smallproId 小件接件单Id
     * @param showType   显示类别
     * @return java.util.List<com.jiuji.oa.afterservice.smallpro.bo.SmallproOperationInfoBO>
     * <AUTHOR>
     * @date 17:06 2020/4/10
     * @since 1.0.0
     **/
    List<SmallproOperationInfoBO> getSmallproOperationLogs(Integer smallproId, Integer showType);

    // endregion

    // region 添加小件接件进程日志

    /**
     * description: <添加小件接件进程日志>
     * translation: <Add smallpro process log>
     *
     * @param smallproAddLogReq 小件添加进程日志Req
     * @param userName          操作用户名称
     * @return com.jiuji.oa.afterservice.smallpro.vo.res.SmallproLogRes
     * <AUTHOR>
     * @date 9:39 2019/12/5
     * @since 1.0.0
     **/
    SmallproLogRes addSmallproLogWithPush(SmallproAddLogReq smallproAddLogReq, String userName);

    // endregion

    // region 渠道送修

    /**
     * description: <渠道送修>
     * translation: <Repair by Channel>
     *
     * @param smallproId      小件接件Id
     * @param maintainChannel 送修渠道
     * @return com.jiuji.oa.afterservice.smallpro.vo.res.SmallproNormalCodeMessageRes
     * <AUTHOR>
     * @date 17:48 2020/1/10
     * @since 1.0.0
     **/
    SmallproNormalCodeMessageRes repair(Integer smallproId, String maintainChannel);

    // endregion

    // region 更新小件接件维修状态

    /**
     * description: <更新小件接件维修状态>
     * translation: <Update smallpro repair status>
     *
     * @param smallproId    小件接件单Id
     * @param username      维修人名称
     * @param maintainState 维修状态
     * @return com.jiuji.oa.afterservice.smallpro.vo.res.SmallproNormalCodeMessageRes
     * <AUTHOR>
     * @date 15:42 2020/1/3
     * @since 1.0.0
     **/
    SmallproNormalCodeMessageRes updateSmallproRepairStatus(Integer smallproId, String username, Integer maintainState);

    // endregion

    // region 小件接件预约确认

    /**
     * description: <小件接件预约确认>
     * translation: <Confirmation of Small Piece Pickup Reservation>
     *
     * @param smallproId 小件接件Id
     * @param oaUserBO   操作用户信息
     * @return com.jiuji.oa.afterservice.smallpro.vo.res.SmallproNormalCodeMessageRes
     * <AUTHOR>
     * @date 21:35 2020/4/13
     * @since 1.0.0
     **/
    SmallproNormalCodeMessageRes checkReserve(Integer smallproId, OaUserBO oaUserBO);

    // endregion

    // endregion

    // region 数据库方法

    /**
     * description: <SqlServer-getById>
     * translation: <SqlServer-getById>
     *
     * @param id 主键
     * @return com.jiuji.oa.afterservice.smallpro.po.Smallpro
     * <AUTHOR>
     * @date 14:27 2019/11/29
     * @since 1.0.0
     **/
    Smallpro getByIdSqlServer(Integer id);

    /**
     * description: <SqlServer-list>
     * translation: <SqlServer-list>
     *
     * @param wrapper 筛选条件
     * @return java.util.List<com.jiuji.oa.afterservice.smallpro.po.Smallpro>
     * <AUTHOR>
     * @date 14:27 2019/11/29
     * @since 1.0.0
     **/
    List<Smallpro> listSqlServer(Wrapper wrapper);

    // region 转地区操作

    /**
     * description: <转地区操作>
     * translation: <Transit operation>
     *
     * @param smallproId  小件接件Id
     * @param areaId      当前地区Id
     * @param toAreaId    转到地区Id
     * @param type        操作类型
     * @param oaUserBO    操作用户
     * @param createWuliu 是否生成物流单
     * @param smallpro
     * @return com.jiuji.oa.afterservice.smallpro.vo.res.SmallproNormalCodeMessageRes
     * <AUTHOR>
     * @date 10:56 2020/4/2
     * @since 1.0.0
     **/
    SmallproNormalCodeMessageRes toArea(Integer smallproId, Integer areaId, Integer toAreaId, Integer type,
                                        OaUserBO oaUserBO, Boolean createWuliu, Smallpro smallpro);

    // endregion

    // region 根据小件单号列表批量转地区

    /**
     * description: <根据小件单号列表批量转地区>
     * translation: <Batch transfer regions based on the list of small items>
     *
     * @param smallproIdList 小件单号列表
     * @param toAreaId       转到地区Id
     * @param type           转地区操作类别
     * @param oaUserBO       操作用户
     * @return com.jiuji.oa.afterservice.smallpro.vo.res.SmallproNormalCodeMessageRes
     * <AUTHOR>
     * @date 20:07 2020/4/24
     * @since 1.0.0
     **/
    SmallproNormalCodeMessageRes toArea(List<Integer> smallproIdList, Integer toAreaId, Integer type,
                                        OaUserBO oaUserBO);

    // endregion

    // region 添加接件信息

    /**
     * description: <添加接件信息>
     * translation: <Add connection information>
     *
     * @param smallpro 接件信息
     * @param areaId   地区Id
     * @param oaUserBO 操作用户信息
     * @return com.jiuji.tc.common.vo.R
     * <AUTHOR>
     * @date 11:08 2020/4/9
     * @since 1.0.0
     **/
    R saveSmallpro(SmallproReq smallpro, Integer areaId, OaUserBO oaUserBO);

    // endregion

    // region 根据返厂旧件单号获取小件接件单号

    /**
     * 校验年包退款
     * @param billBasketIds
     */
    void assertCheckFilmCard(List<Integer> billBasketIds);

    void sendSmallProWeiXinMsg(Integer userId, Integer areaId, ShouhouMsgconfig shouhouMsgconfig,
                               String format, String mobile, Integer smallproId);

    /**
     * description: <根据返厂旧件单号获取小件接件单号>
     * translation: <Obtain the small piece receipt number according to the old part number returned to the factory>
     *
     * @param returnFactoryId 旧件返厂Id
     * @return java.lang.Integer
     * <AUTHOR>
     * @date 12:04 2020/4/23
     * @since 1.0.0
     **/
    Integer getSmallproIdByReturnFactoryId(Integer returnFactoryId);

    // endregion

    // region 一键备货

    /**
     * description: <一键备货>
     * translation: <One-click stocking>
     *
     * @param smallproId 小件接件Id
     * @param oaUserBO   当前登录操作用户
     * @return com.jiuji.oa.afterservice.smallpro.vo.res.SmallproNormalCodeMessageRes
     * <AUTHOR>
     * @date 17:57 2020/4/26
     * @since 1.0.0
     **/
    SmallproNormalCodeMessageRes oneClickStocking(Integer smallproId, OaUserBO oaUserBO);

    // endregion

    // region

    /**
     * description: <根据条形码获取对应的PpriceId>
     * translation: <Obtain the corresponding PpriceId according to the barcode>
     *
     * @param barCode 条形码
     * @return java.lang.Integer
     * <AUTHOR>
     * @date 14:18 2020/5/11
     * @since 1.0.0
     **/
    Integer getPpriceIdByBarcodeWithSmallpro(String barCode);

    // endregion

    // region 验证钢化膜是否绑定串号 checkTemperedFilm

    /**
     * description: <验证钢化膜是否绑定串号>
     * translation: <Verify whether the tempered film is bound to the serial number>
     *
     * @param basketId 订单详情Id
     * @return com.jiuji.oa.afterservice.smallpro.vo.res.SmallproNormalCodeMessageRes
     * <AUTHOR>
     * @date 11:28 2020/5/18
     * @since 1.0.0
     **/
    SmallproNormalCodeMessageRes checkTemperedFilm(Integer basketId);

    // endregion

    // region App钢化膜扫码验证

    /**
     * description: <App钢化膜扫码验证>
     * translation: <App steel code scanning code verification>
     *
     * @param imei       串号参数1
     * @param smallproId 小件单号
     * @param basketId   商品详情Id
     * @param fid        串号参数2
     * @param kind       售后操作类型
     * @return com.jiuji.oa.afterservice.smallpro.vo.res.SmallproNormalCodeMessageRes
     * <AUTHOR>
     * @date 16:02 2020/5/18
     * @since 1.0.0
     **/
    SmallproNormalCodeMessageRes appTemperedFilmCode(String imei, Integer smallproId, Integer basketId, String fid,
                                                     Integer kind);

    /**
     * 判断原始订单是否已提交并且未处理的
     *
     * @param basketId
     * @return
     */
    List<Smallpro> checkOrginSubCommitAndUnDeal(List<Integer> basketId);

    /**
     * 根据订单id查询小件商品id
     *
     * @param subId   订单id
     * @param comment 备注信息
     * @return
     */
    Integer getSmallProIdBySubId(Integer subId, String comment);

    SXSSFWorkbook getWorkBook(SmallproReturnGoodsReq query, String fileName);

    void buildCommonSmallProWuLiu(Areainfo sendInfo, Areainfo receiveInfo, OaUserBO oaUserBO,
                                  LogisticsRecipientBO receiver, SubWLModelReq req, String comment);

    Boolean addSmallproLogBatch(SmallproAddLogBatchReq req, String userName);

    Integer getSpotSubData(Integer subId, List<Long> ppIds);

    void saveSmallproAttachments(List<MiniFileRes> filesList, OaUserBO oaUserBO, Integer smallproId);

    /**
     * 小件微信推送消息
     *  @param wxMsg 维修消息
     * @param pushType
     * @param mobile
     */
    boolean sendWeixinMsg(String wxMsg, String url, String handleType,
                          String status, Integer areaId, Integer userId, String logs, Integer pushType, String mobile);

    SmallReceiptPrintingRes receiptPrinting(Integer smallProId);

    R<CheckSmallProWuLiuRes> checkSmallProWuLiu(Integer smallProId, OaUserBO oaUserBO);

    /**
     * 小件品类统计查询参数下拉数据
     *
     * @return CommonTitleRes
     */
    CommonTitleRes getTitle();

    CommonDataRes data(SmallCategoryReq req);

    Boolean batchPush(SmallBatchPushReq req, OaUserBO staffId);

    R<SmallProYuyueInfo> getYuyueInfoByMobile(String mobile,Integer yuYueId);

    /**
     * 测试无故障
     * @param checkStatus
     * @return
     */
    R<Boolean> testWithoutProblem(Integer checkStatus,Integer fcId);

    R<List<FilmAccessoriesBo>> getFilmAccessories(Integer ppid,HttpServletRequest request);

    /**
     * 质保换新新接口
     * @param ppid
     * @return
     */
    R<List<FilmAccessoriesV2Item>> getFilmAccessoriesV2(Integer ppid,Integer type,Integer basketId);

    /**
     * 获取
     * @return
     */
    R<List<SmallProOldGoodsWaitingForSelectRes>> getSmallProOldGoodsWaitingForSelect(String area);


    /**
     * 根据basket_id获取运营商业务的状态
     * @return
     */
    List<Integer> getOperatorBasketByStatus(List<Integer> basketIdList);

    /**
     * 根据basket_id获取商品是否类型（优品）
     * @return
     */
    List<Integer> getBasketTypeById(List<Integer> basketIdList);

    /**
     * 小件换货v3版本
     *
     * @param ppid
     * @param type
     * @param basketId
     * @param key
     * @param imei
     * @return
     */
    R<List<FilmAccessoriesV2Item>> getFilmAccessoriesV3(Integer ppid, Integer type, Integer basketId, String key,Integer mobileExchangeFlag,String imei);

    /**
     * 小件换货web版本
     * @param req
     * @return
     */
    R<ExchangeProductListVO> getExchangeProductList(SmallExchangeReq req);

    /**
     * 获取配置信息
     * @param serviceType 服务类型
     * @param key 置换的商品ppid
     * @param productinfo 源商品信息
     * @param searchSamePpid 是否为同ppid查询
     * @param basketId
     * @see SmallProServiceTypeEnum
     * @return
     */
    Optional<SmallExchangeConfigPo> getConfigOpt(Integer serviceType, String key, Productinfo productinfo, boolean searchSamePpid, Integer basketId);

    /**
     * 获取配置信息
     * @param serviceType 服务类型
     * @param productinfo 源商品信息
     * @param webKeyword 来自web的搜索关键词
     * @param searchSamePpid 是否为同ppid查询
     * @see SmallProServiceTypeEnum
     * @return
     */
    Optional<SmallExchangeConfigPo> getConfigOpt(Integer serviceType, Productinfo productinfo,
                                                 String webKeyword, boolean searchSamePpid);

    /**
     * 批量返销接口
     * @param notSoldBackList notSoldBackList
     * @return
     */
    R<Boolean> batchReturnOperatorBasket(List<NotSoldBackBO> notSoldBackList);

    /**
     * 获取运营商未返现的商品
     * @param basketIdList
     * @return
     */
    List<NotSoldBackBO> getProductIsResale(List<Integer> basketIdList);

    /**
     * 校验是否需要返销
     * @param smallproBillList 小件接件信息
     * @return
     */
    R<List<NotSoldBackBO>> checkResultOperatorBasket(List<SmallproBill> smallproBillList);

    R cancelSmallTuifeiDiscount(String smallproID);

    /**
     * 进行中的预约单和小件单
     *
     * @param basketIds 原订单的商品basketId（壳膜商品basketId）
     * @param userId
     * @return {@link SmallYuyueOrderOnGoingVO}
     */
    R<List<SmallYuyueOrderOnGoingVO>> listSmallYuyueOrderOnGoing(List<Integer> basketIds, Integer userId);


    /**
     * 新增切膜日志
     * @param req
     * @return
     */
    Boolean addCutScreenReqLog(CutScreenReq req);

    /**
     * 获取分销订单批量生成小件单退货列表
     * @param subId
     * @return
     */
    SmallproBasketRes getSmallproBasketList(String subId);

    /**
     * 小件备货接口
     * @param req
     * @return
     */
    R stockUp(SmallStockUpReq req);

    /**
     * 添加日志
     * @param req
     * @return
     */
    R addStockLog(SmallproBillLogVo req);

    /**
     * 获取日志列表
     * @param billId
     * @param type
     * @return
     */
    R<List<SmallproBillLogVo>> listStockLog(Integer billId, Integer type);

    /**
     * 处理保护壳半价复购提醒推送
     */
    void handlePhoneCasePurchaseBuy();

    /**
     * 不折价退款申请
     * @param req
     * @return
     */
    R<Boolean> submitNoDiscount(SmallproNoDiscountReqVo req);

    /**
     * 不折价退款审核
     * @param req
     * @return
     */
    R<Boolean> checkNoDiscount(SmallproNoDiscountCheckReqVo req);

    /**
     * 获取不折价退款申请单详情
     * @param smallProId
     * @return
     */
    R<SmallproNoDiscountResVo> getSubmitNoDiscount(Integer smallProId);
}
