package com.jiuji.oa.afterservice.refund.bo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/8/22 8:33
 */
@Data
@Accessors(chain = true)
@ApiModel("获取退款验证实体Bo")
public class RefundSubInfoBo {
    /**
     * 单号(交易单号)
     */
    @ApiModelProperty(value = "单号")
    private Integer orderId;
    /**
     * 订单类型
     * @see com.jiuji.tc.utils.enums.coupon.BusinessTypeEnum
     */
    @ApiModelProperty(value = "业务类型")
    private Integer businessType;
    /**
     * 订单门店id
     */
    @ApiModelProperty(value = "订单门店id")
    private Integer areaId;
    /**
     * 售后的门店id
     */
    @ApiModelProperty(value = "售后的门店id")
    private Integer shouhouAreaId;
    /**
     * 已付金额
     */
    @ApiModelProperty(value = "已付金额")
    private BigDecimal yifuM;
    /**
     * 已付金额(包含父子订单的yifum)
     */
    @ApiModelProperty(value = "已付金额(包含父子订单的yifum)")
    private BigDecimal allYifuM;
    /**
     * 应付金额
     */
    @ApiModelProperty(value = "应付金额")
    private BigDecimal yingfuM;
    /**
     * 当前退商品总金额(包含折价金额)
     * 排除了未折价的不可退金额
     */
    @ApiModelProperty(value = "当前退商品总金额")
    private BigDecimal totalPrice;
    /**
     * 售前单会员编号
     */
    @ApiModelProperty(value = "售前单会员编号")
    private Integer userId;
    /**
     * 成本
     */
    @ApiModelProperty(value = "成本")
    private BigDecimal inPrice;
    /**
     * 折价金额
     */
    @ApiModelProperty(value = "折价金额")
    private BigDecimal zheJiaM;
    /**
     * 不可退退金额
     */
    @ApiModelProperty(value = "不可退退金额")
    private BigDecimal notRefundPrice;
    /**
     * 客户实付金额
     */
    @ApiModelProperty(value = "客户实付金额")
    private BigDecimal customerMaxRefundPrice;
    /**
     * 当前订单最大可退金额
     */
    @ApiModelProperty(value = "当前退商品最大可退金额")
    private BigDecimal subMaxRefundPrice;
    /**
     * 当前退商品最大可退金额
     */
    @ApiModelProperty(value = "当前退商品最大可退金额")
    private BigDecimal maxRefundPrice;
    /**
     * 不同订单类型,状态不一样
     * @see com.jiuji.tc.utils.constants.SubCheckStatusEnum
     */
    @ApiModelProperty(value = "订单状态")
    private Integer subCheck;
    /**
     * 是否不需要验证了(单之前已经验证过)
     */
    @ApiModelProperty(value = "是否不需要验证了")
    private Boolean isNotNeedValid;

    /**
     * 退款的商品ids(小件, 大件售后才有该值)
     */
    @ApiModelProperty(value = "退款的商品ids")
    private List<Integer> basketIds;

    // 生成默认构造函数, 并设置basketIds为空集合
    public RefundSubInfoBo() {
        this.basketIds = Collections.emptyList();
    }
}
