package com.jiuji.oa.afterservice.other.bo;

import com.alibaba.fastjson.annotation.JSONType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * description: <操作余额接口参数模型>
 * translation: <Operating balance interface parameter model>
 *
 * <AUTHOR>
 * @date 2020/3/24
 * @since 1.0.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@JSONType(orders = {"userid", "amount", "subid", "memo", "ekind", "areaid"})
public class SaveMoneyInfoBO {
    /**
     * 客户Id BBSXP_USER_ID
     */
    private Integer userid;
    /**
     * 金额
     */
    private Double amount;
    /**
     * 订单Id
     */
    private Integer subid;
    /**
     * 备注信息
     */
    private String memo;
    /**
     * 操作类别
     *
     * @see com.jiuji.oa.afterservice.other.enums.SaveMoneyEkindEnum
     */
    private Integer ekind;

    /**
     * 所属门店：华腾等包含财务系统的租户，需要传入。进行做账处理
     */
    private Integer areaid;
}
