package com.jiuji.oa.afterservice.bigpro.dao;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jiuji.oa.afterservice.api.bo.Ch999UserBasicBO;
import com.jiuji.oa.afterservice.bigpro.bo.*;
import com.jiuji.oa.afterservice.bigpro.bo.yuyue.ShouhouYuYueBasicInfo;
import com.jiuji.oa.afterservice.bigpro.m.bo.ProductKcStatusInfo;
import com.jiuji.oa.afterservice.bigpro.m.bo.RepairRecords;
import com.jiuji.oa.afterservice.bigpro.po.OfficialInsurance;
import com.jiuji.oa.afterservice.bigpro.po.Shouhou;
import com.jiuji.oa.afterservice.bigpro.vo.req.ChaoshiZengpinListReq;
import com.jiuji.oa.afterservice.bigpro.vo.req.LastRepairReq;
import com.jiuji.oa.afterservice.bigpro.vo.req.PageRepairImeiReq;
import com.jiuji.oa.afterservice.bigpro.vo.req.ShouhouIndexReq;
import com.jiuji.oa.afterservice.bigpro.vo.res.*;
import com.jiuji.oa.afterservice.other.bo.BBSXPUsersMoneyBo;
import com.jiuji.oa.afterservice.smallpro.bo.smallproInfo.AfterServiceTimeConfigBo;
import com.jiuji.tc.foundation.db.annotation.DS;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-16
 */
@Mapper
public interface ShouhouExMapper extends BaseMapper<Shouhou> {

    @Select("select * from shouhou with(nolock) ${ew.customSqlSegment}")
    List<Shouhou> listSqlServer(@Param(Constants.WRAPPER) Wrapper wrapper);

    @Select("select * from shouhou with(nolock) where id=${id}")
    Shouhou getByIdSqlServer(@Param("id") Integer id);

    @Select("select id from dbo.wuliu with(nolock) where nu=#{nu} and com in ('meituan','meituanFastest') ")
    Long getDeliveryId(@Param("nu") String nu);

    /**
     * 获取售后赠品信息
     *
     * @param shouhouId
     * @param type
     * @return
     */
    List<ShouHouZengpinBo> getZengpinByShouhouid(@Param("shouhouId") Integer shouhouId, @Param("type") Integer type);

    /**
     * 服务出险记录查询
     *
     * @param imei
     * @param wxId
     * @return
     */
    List<ShouhouServiceOutBo> getServersList(@Param("imei") String imei, @Param("wxId") Integer wxId);

    /**
     * 查询ppid
     *
     * @param ppriceid
     * @return
     */
    Integer getPpidBy(@Param("ppriceid") Integer ppriceid);

    /**
     * 查询价格和交易时间
     *
     * @param basketId
     * @return
     */
    List<ShouHouPriceAndTradeDateBo> getPriceAndTradeDate(@Param("basketId") Integer basketId);

    /**
     * 取第一条价格
     *
     * @param subId
     * @param ppriceid
     * @return
     */
    BigDecimal getPriceBySubIdAndPpid(@Param("subId") Integer subId, @Param("ppriceid") Integer ppriceid);

    /**
     * 获取所有故障类型
     *
     * @return
     */
    List<ShouhouTroubleListRes> getTroubleList();

    /**
     * 获取售后表单相关故障
     *
     * @param shouhouId
     * @return
     */
    List<Integer> getShouhouTroubles(Integer shouhouId);

    /**
     * 根据订单信息表内容更新购买地区
     *
     * @param subId
     * @param shouhouId
     * @return
     */
    Integer updateBuyAreaIdBySubInfo(@Param("subId") Integer subId, @Param("shouhouId") Integer shouhouId);

    /**
     * 根据RecoverMarketInfo表内容更新购买地区
     *
     * @param subId
     * @param shouhouId
     * @return
     */
    Integer updateBuyAreaIdByRecoverMarketInfo(@Param("subId") Integer subId, @Param("shouhouId") Integer shouhouId);

    /**
     * 硬件历史记录
     *
     * @param imei
     * @return
     */
    List<ShouhouHardwareHistoryRes> getHardwareHistory(@Param("imei") String imei);

    /**
     * 客户维修记录
     *
     * @param userId
     * @return
     */
    List<ShouhouUserHistoryRes> getUserHistory(@Param("userId") Integer userId,
                                               @Param("isAuthPart") Boolean isAuthPart,
                                               @Param("xtenant") Integer xtenant,
                                               @Param("authorizeId") Integer authorizeId);

    /**
     * 查询支付宝优惠码对应订单id
     *
     * @param shouhouId
     * @param aliPayYouhuiPpids
     * @return
     */
    Integer queryAlipayYouhuiSubId(@Param("shouhouId") Integer shouhouId, @Param("aliPayYouhuiPpids") List<Integer> aliPayYouhuiPpids);

    /**
     * 查询串号信息
     *
     * @param id
     * @return
     */
    ShouhouImeiInfoBo getImeiInfo(@Param("id") Integer id);

    /**
     * 查询串号信息
     *
     * @param imei2
     * @return
     */
    List<String> getImei1(@Param("imei2") String imei2);

    /**
     * 查询串号改变次数
     *
     * @param id
     * @return
     */
    Integer getImeiChangeCount(@Param("id") Integer id);

    /**
     * 根据售后编号 获取对应订单信息
     *
     * @param shouhouId
     * @return
     */
    ShouhouSubDetailBo loadSubInfoByShouhouid(@Param("shouhouId") Integer shouhouId);

    /**
     * @param userId
     * @return
     */
    List<BBSXPUsersMoneyBo> queryBBsXpUserMoneyInfo(@Param("userId") Integer userId);

    /**
     * 查询fuedu参数信息
     *
     * @param userId
     * @return
     */
    BigDecimal getFueduByUserId(@Param("userId") Integer userId);

    /**
     * 更新维修费用
     *
     * @param shouhouId
     */
    void updateCostPriceById(@Param("shouhouId") Integer shouhouId);

    /**
     * 查询收银时对应门店id
     *
     * @param shouhouId
     * @return
     */
    Integer getShouyinAreaId(@Param("shouhouId") Integer shouhouId);

    /**
     * 更改收银锁状态
     *
     * @param shouhouId
     * @return
     */
    Integer updateShouyingLock(@Param("shouhouId") Integer shouhouId);

    /**
     * 超时赠品查询
     *
     * @param page
     * @param req
     * @return
     */
    Page<ChaoshiZengPinRes> getZengpingPageList(Page<ChaoshiZengPinRes> page, @Param("req") ChaoshiZengpinListReq req);

    /**
     * 售后评价
     *
     * @param shouhouId
     * @param pingjia
     * @param pjkind
     * @return
     */
    Integer pingJia(@Param("shouhouId") Integer shouhouId, @Param("pingjia") String pingjia, @Param("pjkind") Integer pjkind);

    /**
     * 售后列表页面数据
     *
     * @param shouhou      前端发送的请求体
     * @param startIndex   分页起始行
     * @param endIndex     分页结束行
     * @param orderBy      排序
     * @return 售后分页列表
     */
    List<ShouhouIndexRes> getList(@Param("shouhou") ShouhouIndexReq shouhou,
                                  @Param("startIndex") long startIndex, @Param("pageSize") long pageSize);

    /**
     * 售后列表记录行数
     *
     * @param shouhou
     * @return
     */
    Long getListCount(@Param("shouhou") ShouhouIndexReq shouhou);

    /**
     * 获取采购发货列表
     *
     * @param shouhouId 售后id
     * @return
     */
    List<BasketsQueryBO> listBaskets(@Param("shouhouId") Integer shouhouId);

    /**
     * @param imei   imei
     * @param userId 会员id
     * @param mobile 会员电话
     * @param isLp
     * @return 维修历史列表
     * @see .net:oa999DAL.ShouHou.WeixiuLishi
     */
    List<RepairHistoryRes> repairHistories(@Param("imei") String imei, @Param("userId") Long userId, @Param("mobile") String mobile,
                                           @Param("isLp") Boolean isLp,@Param("tradingHours")String tradingHours);

    /**
     * @param imei   imei
     * @param userId 会员id
     * @param mobile 会员电话
     * @param isLp
     * @return 维修历史列表
     * @see .net:oa999DAL.ShouHou.WeixiuLishi
     */
    Integer repairHistoriesCount(@Param("imei") String imei, @Param("userId") Long userId, @Param("mobile") String mobile,
                                 @Param("isLp") Boolean isLp,@Param("tradingHours")String tradingHours);

    /**
     * 查询上一次维修单号
     * @param lastRepairReq
     * @return
     */
    LastRepairRes selectLastRepair(@Param("lastRepairReq") LastRepairReq lastRepairReq);

    /**
     * 分页查询维修单使用记录
     * @param pageRepairImeiReq
     * @return
     */
    Page<PageRepairImeiHisVo> selectRepairImeiHis(@Param("page") Page<PageRepairImeiHisVo> page,@Param("req") PageRepairImeiReq pageRepairImeiReq);

    /**
     * 用户从开始日期到目前的退换次数
     *
     * @param userId    用户id
     * @param startDate 开始日期
     * @return 退换次数
     */
    Integer exchangeCount(@Param("userId") Long userId, @Param("startDate") LocalDateTime startDate);


    /**
     * 根据imei或者mkcId查询维修记录id
     *
     * @param mkcID mkcID
     * @param imei  imei
     * @return 售后id 没查到返回null
     */
    Integer hasShouhou(@Param("mkcId") Integer mkcId, @Param("imei") String imei);

    Integer hasRecoveryShouhou(@Param("mkcId") Integer mkcId, @Param("imei") String imei);

    /**
     * 根据imei或者mkcId查询维修记录id
     *
     * @param mkcID mkcID
     * @param imei  imei
     * @return 售后id 没查到返回null
     */
    Integer has4GjiaTaocan(@Param("imei") String imei);

    /**
     * 售后外修软件接件
     *
     * @param imei imei
     * @return 售后id 没查到返回null
     * @see .net:oa999DAL.ShouHou.GetShohouIstichengSoft
     */
    Shouhou getShohouIstichengSoft(@Param("imei") String imei);

    /**
     * 串号硬件维修次数统计
     *
     * @param imei imei
     * @return 记录数
     * @see .net:oa999DAL.ShouHou.GetWxCount
     */
    Integer getWxCount(@Param("imei") String imei);


    Integer getBuyAreaIdBySubId(@Param("subId") Integer subId);

    Integer getBuyAreaIdByRecoverMarketInfo(@Param("subId") Integer subId);

    Integer insertShouHou(Shouhou shouhou);

    /**
     * 判断是否为返修
     * @param imei imei
     * @return Boolean
     */
    Integer checkFanxiu(String imei);

    /**
     * 根据良品订单号获取会员黑名单标识
     * @param subId
     * @return
     */
    Integer getUserBlackListTypeByRecoverId(@Param("subId") Integer subId);

    /**
     * 获取小件单号
     *
     * @param shouhouId
     * @return
     */
    List<Integer> getSmallProId(@Param("shouhouId") Integer shouhouId);

    Integer getBigProId(@Param("basketId") Integer basketId);


    /**
     * 官方延保
     *
     * @param subId subId
     * @return List<OfficialInsurance>
     */
    List<OfficialInsurance> getOfficialInsurance(@Param("subId") Integer subId);

    /**
     * 根据手机号码查询正在进行中的大件预约单信息
     * @param mobile
     * @return
     */
    List<ShouhouYuYueBasicInfo> getYuYueListByMobile(@Param("mobile") String mobile);

    /**
     * 查询库存状态信息
     * @param imei
     * @return
     */
    ProductKcStatusInfo getProductKcStatusInfoByImei(@Param("imei") String imei);

    /**
     * 根据保修状态信息获取维修单号
     * @param serviceType
     * @return
     */
    Integer getShouHouIdByServiceType(@Param("serviceType") Integer serviceType,@Param("imei") String imei);

    /**
     * 根据手机号或串号查询维修记录
     * @param imei
     * @param mobile
     * @return
     */
    List<RepairRecords> getRepairRecordsByImeiOrMobile(@Param("imei") String imei, @Param("mobile") String mobile);

    /**
     * 查询所有生效的配置信息
     * @return
     */
    @DS("web999")
    List<AfterServiceTimeConfigBo> getAfterServiceTimeConfigList();


    /**
     * 根据售后id List查询配件分类
     *
     * @param shouhouIds shouhouIds
     * @return
     */
    List<ShouhouCategoryRes> getCategoryByShouIdList(@Param("shouhouIds") Collection<Integer> shouhouIds);

    /**
     * 通过售后id获取特价配件列表
     * @param shouhouId
     * @return
     */
    List<Integer> listBargainPprice(@Param("shouhouId") Integer shouhouId);

    Integer getPpriceIdByShouhouInfo(@Param("shouhou") Shouhou shouhou);


    List<Ch999UserBasicBO> selectUserByMobile(@Param("mobile") String mobile);

    /**
     * 根据会员id查询员工信息
     * @param userId
     * @return
     */
    List<Ch999UserBasicBO> selectUserById(@Param("userId") Long userId);

    /**
     * 根据会员id查询员工信息
     * @param userId
     * @return
     */
    Ch999UserBasicBO selectUserByUserName(@Param("userName") String userName);

    /**
     * 查询直属上级
     * @param userId
     * @return
     */
    List<Ch999UserBasicBO> selectDirectSuperiorLeaders(@Param("userId") Integer userId);

    /**
     * 处理维修配件默认标签
     * @param maxPpirceId
     * @return
     */
    Boolean handleUpdateDeFaultLabel(@Param("maxPpirceId") Integer maxPpirceId);

    /**
     * 处理维修配件默认标签
     * @param maxPpirceId
     * @return
     */
    Boolean handleInsertDeFaultLabel(@Param("maxPpirceId") Integer maxPpirceId);
}
