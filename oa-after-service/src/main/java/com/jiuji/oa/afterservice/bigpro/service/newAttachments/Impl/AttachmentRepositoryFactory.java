package com.jiuji.oa.afterservice.bigpro.service.newAttachments.Impl;


import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ch999.common.util.utils.CommonUtils;
import com.jiuji.oa.afterservice.bigpro.dao.AttachmentsMapper;
import com.jiuji.oa.afterservice.bigpro.enums.AttachmentsEnum;
import com.jiuji.oa.afterservice.bigpro.po.Attachments;
import com.jiuji.oa.afterservice.bigpro.service.ShouhouService;
import com.jiuji.oa.afterservice.bigpro.service.newAttachments.AttachmentRepository;
import com.jiuji.oa.afterservice.bigpro.service.newAttachments.po.AttachmentsSeparateConfig;
import com.jiuji.oa.afterservice.bigpro.service.newAttachments.res.BusinessTimeRes;
import com.jiuji.oa.afterservice.common.constant.DataSourceConstants;
import com.jiuji.oa.afterservice.common.enums.XtenantEnum;
import com.jiuji.oa.afterservice.common.util.CommenUtil;
import com.jiuji.tc.utils.transaction.MultipleTransaction;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/9/14 11:30
 * @Description 附件仓库工厂类
 */
@Slf4j
@Component
public class AttachmentRepositoryFactory {

    @Resource
    private AttachmentsMapper attachmentsMapper;


    /**
     * 判断从历史还是从实时库进行
     * @param linkIds ID列表
     * @param attachmentsType 附件类型
     * @param creationTime 创建时间
     * @return AttachmentRepository 实例
     */
    private AttachmentRepository determineRepository(List<Integer> linkIds, Integer attachmentsType, LocalDateTime creationTime) {
        // 如果附件类型为空或为零，直接返回实时库的实例
        if (CommenUtil.isNullOrZero(attachmentsType) || !XtenantEnum.isJiujiXtenant()) {
            return new RealTimeAttachmentRepository(attachmentsMapper);
        }
        // 获取附件分隔配置
        AttachmentsSeparateConfig config = attachmentsMapper.getAttachmentsSeparateConfig(attachmentsType);

        // 如果创建时间为空，处理特殊附件类型
        if (Objects.isNull(creationTime)) {
            if (Objects.equals(attachmentsType, AttachmentsEnum.SHOUHOU.getCode())) {
                // 获取与链接ID相关的业务时间列表 也是双库查询(shouhou 没得历史库)
                List<BusinessTimeRes> businessTimeResList = Optional.ofNullable(CommenUtil.autoQueryMergeHist(()->attachmentsMapper.getSendTimeByShouhouId(linkIds))).orElse(new ArrayList<>()).stream().collect(Collectors.toList());
                // 根据业务时间列表和配置判断使用哪个库
                return getAttachmentRepository(businessTimeResList, config);
            }
        }

        // 如果创建时间不为空，直接判断使用实时库
        return getAttachmentRepository(creationTime, config);
    }

    /**
     * 根据业务时间列表和附件分隔配置判断使用哪个库
     * @param businessTimeResList 业务时间列表
     * @param config 附件分隔配置
     * @return AttachmentRepository 实例
     */
    private AttachmentRepository getAttachmentRepository(List<BusinessTimeRes> businessTimeResList, AttachmentsSeparateConfig config) {
        // 如果业务时间列表为空或配置为空，返回实时库的实例
        if (CollUtil.isEmpty(businessTimeResList) || Objects.isNull(config)) {
            return new RealTimeAttachmentRepository(attachmentsMapper);
        }

        // 判断是否存在任何业务创建时间晚于分隔时间
        boolean hasRecentBusinessTime = businessTimeResList.stream()
                .anyMatch(businessTimeRes -> config.getSeparateTime().isAfter(businessTimeRes.getBusinessCreationTime()));

        // 根据判断结果返回相应的库
        if (hasRecentBusinessTime) {
            // 查询历史库逻辑
            RealTimeAttachmentRepository realTimeRepository = new RealTimeAttachmentRepository(attachmentsMapper);
            return new HistoryAttachmentRepository(attachmentsMapper, realTimeRepository);
        }

        return new RealTimeAttachmentRepository(attachmentsMapper);
    }

    /**
     * 根据创建时间和附件分隔配置判断使用哪个库
     * @param creationTime 创建时间
     * @param config 附件分隔配置
     * @return AttachmentRepository 实例
     */
    private AttachmentRepository getAttachmentRepository(LocalDateTime creationTime, AttachmentsSeparateConfig config) {
        // 根据分隔时间判断使用历史库还是实时库
        if (Objects.nonNull(config) && config.getSeparateTime().isAfter(creationTime)) {
            // 创建实时库的实例
            RealTimeAttachmentRepository realTimeRepository = new RealTimeAttachmentRepository(attachmentsMapper);
            return new HistoryAttachmentRepository(attachmentsMapper, realTimeRepository);
        } else {
            return new RealTimeAttachmentRepository(attachmentsMapper);
        }
    }

    // 重载方法，支持单个链接ID
    public AttachmentRepository getRepository(Integer linkId, Integer attachmentsType, LocalDateTime creationTime) {
        return determineRepository(Collections.singletonList(linkId), attachmentsType, creationTime);
    }

    // 重载方法，支持多个链接ID
    public AttachmentRepository getRepository(List<Integer> linkIds, Integer attachmentsType, LocalDateTime creationTime) {
        return determineRepository(linkIds, attachmentsType, creationTime);
    }

    // 重载方法，支持链接ID集合
    public AttachmentRepository getRepository(Set<Integer> linkIds, Integer attachmentsType, LocalDateTime creationTime) {
        return determineRepository(new ArrayList<>(linkIds), attachmentsType, creationTime);
    }

    /**
     * 直接从实时库查询
     * @return AttachmentRepository 实例
     */
    public AttachmentRepository getRepository() {
        return new RealTimeAttachmentRepository(attachmentsMapper);
    }

}