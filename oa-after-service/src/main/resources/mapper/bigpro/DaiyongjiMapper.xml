<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jiuji.oa.afterservice.bigpro.dao.DaiyongjiMapper">

    <resultMap type="com.jiuji.oa.afterservice.bigpro.po.Daiyongji" id="daiyongjiMap">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="imei" column="imei"/>
        <result property="jikuang" column="jikuang"/>
        <result property="price" column="price"/>
        <result property="pos" column="pos"/>
        <result property="beizhu" column="beizhu"/>
        <result property="isdel" column="isdel"/>
        <result property="area" column="area"/>
        <result property="dtime" column="dtime"/>
        <result property="stats" column="stats"/>
        <result property="pandian" column="pandian"/>
        <result property="pandiandate" column="pandiandate"/>
        <result property="panUser" column="pan_user"/>
        <result property="kcCheck" column="kc_check"/>
        <result property="areaid" column="areaid"/>
        <result property="leavel" column="leavel"/>
        <result property="peizhi" column="peizhi"/>
        <result property="applycount" column="applycount"/>
        <result property="applydate" column="applyDate"/>
        <result property="inprice" column="inprice"/>
        <result property="pricetime" column="priceTime"/>
        <result property="jiechuren" column="jiechuren"/>
        <result property="jiechutime" column="jiechutime"/>
        <result property="yuyueid" column="yuyueid"/>
        <result property="guihuandate" column="guihuanDate"/>
        <result property="ppriceid" column="ppriceid"/>
        <result property="grade" column="grade"/>
        <result property="ppriceid" column="ppriceid"/>
        <result property="productColor" column="product_color"/>
        <result property="waiGuan" column="wai_guan"/>
    </resultMap>

    <select id="getDaiYongJiPage" resultType="com.jiuji.oa.afterservice.bigpro.vo.res.DaiYongJiListRes">
        SELECT d.id,ISNULL(d.pos,0) AS wxId,a.area,d.name,d.imei,d.price,d.jikuang,ISNULL(d.applycount,0) applyCount,d.applyDate,d.wai_guan,d.product_color,d.grade
        FROM dbo.daiyongji d with(nolock)  LEFT JOIN dbo.areainfo a with(nolock) ON d.areaid=a.id
        WHERE 1=1
        <if test="req != null">
            <if test="req.areaId != null">
              and d.areaid= #{req.areaId}
            </if>
            <if test="req.stats != null">
                and d.stats= #{req.stats}
            </if>
        </if>
        AND ISNULL(d.isdel,0)=0
        AND d.id not in (SELECT daiyongjiId FROM daiyongji_toarea with(nolock) where stats != 3 )
    </select>

    <select id="checkDyjUse" resultType="Integer">
        SELECT COUNT(1) FROM daiyongji_yajin with(nolock) WHERE wxid=#{wxId} AND cancel=0
    </select>
    <update id="updateDaiYongJiByIdAndStats">
        UPDATE daiyongji SET [stats]=2, pos=#{wxId}, applydate=#{applydate},applycount=ISNULL(applycount,0)+1 WHERE id=#{dyjId} AND [stats] IN(1,7)
    </update>
    <select id="getDaiYongJiInfoBy" resultType="com.jiuji.oa.afterservice.bigpro.po.Daiyongji">
        select d.*,
        (SELECT TOP 1 isnull(dt.stats,0) FROM dbo.daiyongji_toarea dt with(nolock) WHERE dt.daiyongjiId=d.id  ORDER BY dt.id DESC) AS toareaStats
        from daiyongji d with(nolock) where d.id=#{dyjId} and d.pos=#{wxId};

    </select>

    <update id="cancelDaiYongJiYajin">
        UPDATE daiyongji_yajin SET cancel=1, paystate=#{paystate}, cancelUser=#{userName}, cancelDate=GETDATE() WHERE id=#{dyjId} AND cancel=0
    </update>

    <select id="getDaiYongJiAreaId" resultType="java.lang.Integer">
        SELECT areaid FROM dbo.daiyongji with(nolock) WHERE id= #{dyjId}
    </select>

    <update id="updateDaiYongJiStatsAndAreaId">
        UPDATE daiyongji SET [stats]=#{dyjStats}, pos=NULL, areaid = #{updateDyjArea},guihuanDate=GETDATE() WHERE ID=#{dyjId}
    </update>

    <update id="updateShouhouDyjId">
        UPDATE shouhou SET dyjid=NULL WHERE id=#{wxId}
    </update>

    <insert id="saveDaiYongJiLogs">
        insert into daiyongjilogs(dyjid,comment,inuser,dtime) values(#{dyjId},#{comment},#{inuser},GETDATE())
    </insert>

    <select id="getShouhouAreaId" resultType="java.lang.Integer">
        SELECT ISNULL(toareaid,areaid) FROM dbo.shouhou  with(nolock)  WHERE id=#{wxId}
    </select>

    <select id="getDyjNowAreaId" resultType="java.lang.Integer">
        SELECT top 1 s.areaid FROM dbo.shouhou s WITH(NOLOCK) INNER JOIN dbo.daiyongji_yajin yj WITH(NOLOCK) ON s.id=yj.wxid WHERE yj.id=#{yaJinId}
    </select>

    <select id="getList" resultType="com.jiuji.oa.afterservice.bigpro.statistics.vo.res.DaiYongJiListResVo">
        SELECT d.id,name,imei,jikuang as jiKuang,price,inprice as inPrice,pos,beizhu,d.dtime,isnull(d.stats,0) status,
        pandiandate as panDianDate,pan_user as panUser,ISNULL(d.applyCount,0) as applyCount,d.applyDate as applyDate,
        d.guihuanDate as guiHuanDate,ISNULL(isdel,0) as isDel,ISNULL(pandian,0) as isPanDian,kc_check,d.leavel as level,
        ISNULL(dt.areaid,d.areaid) as areaId,dt.toareaid as toAreaId,ISNULL(dt.status,0) AS toAreaStatus,priceTime,wxid,
        d.grade,d.wai_guan,d.product_color,ISNULL(yjtab.total,0) AS yaJin,payStatus FROM daiyongji d WITH(NOLOCK)
        LEFT JOIN (
        SELECT id,daiyongjiId,areaId,toAreaId,stats as status, ROW_NUMBER() OVER(PARTITION BY daiyongjiId ORDER BY id DESC) rindex
        FROM dbo.daiyongji_toarea  WITH(NOLOCK) WHERE STATS != 3
        ) dt ON d.id=dt.daiyongjiId AND dt.rindex = 1
        LEFT JOIN(
        SELECT dyjid,wxid,yaJin,total,payStatus FROM (SELECT dyjid,wxid,yaJin,total,paystate as payStatus ,ROW_NUMBER() OVER(PARTITION BY dyjid ORDER BY id DESC) rnum FROM daiyongji_yajin WITH(NOLOCK)) AS yj WHERE yj.rnum=1
        ) yjtab ON d.id=yjtab.dyjid
        LEFT JOIN areainfo a WITH(NOLOCK) ON d.areaid=a.id
        WHERE 1=1
        <if test="req.queryKind != null and req.queryKind != 0">
            <if test="req.queryKind == 1">
                and d.imei like concat('%',#{req.keyWord},'%')
            </if>
            <if test="req.queryKind == 2">
                and d.id = #{req.keyWord}
            </if>
            <if test="req.queryKind == 3">
                and d.name like concat('%',#{req.keyWord},'%')
            </if>
            <if test="req.queryKind == 4">
                and yjtab.wxid = #{req.keyWord}
            </if>
        </if>

        <if test="req.yaJinkind != null and req.yaJinkind != 0 ">
            <if test="req.yaJinkind == 1">
                and yjtab.paystate = 2 and ISNULL(yjtab.total,0) > 0
            </if>
            <if test="req.yaJinkind == 2">
                and yjtab.paystate=2 and ISNULL(yjtab.total,0)=0
            </if>
                <if test="req.yaJinkind == 3">
                and yjtab.paystate=3 and ISNULL(yjtab.total,0)>0
            </if>
        </if>

        <choose>
            <when test="req.isDel != null and req.isDel == true">
                and isnull(d.isdel,0)=1
            </when>
            <otherwise>
                and isnull(d.isdel,0)=0
                <choose>
                    <when test="req.status != null and req.status != 0">
                        and d.[stats] = #{req.status}
                    </when>
                    <otherwise>
                        and d.[stats] in (1,2,3,7)
                    </otherwise>
                </choose>
            </otherwise>
        </choose>

        <if test="req.price30day != null and req.price30day == 1">
            AND (priceTime IS NULL OR DATEDIFF(DAY,priceTime,GETDATE())>=30)
        </if>

        <if test="req.panStatus != null">
            and d.pandian= #{req.panStatus}
        </if>

        <choose>
            <when test="req.curAreaId == 22 or req.curAreaId == 1">
                <if test="req.areaIds != null and req.areaIds.size > 0">
                    and d.areaid in
                    <foreach collection="req.areaIds" item="areaId" open="(" close=")" separator=",">
                        #{areaId}
                    </foreach>
                </if>
            </when>
            <otherwise>
                <if test="req.curAreaId != null and req.curAreaId != 0">
                    and d.areaid = #{req.curAreaId}
                </if>
            </otherwise>
        </choose>

        <if test="req.areaKind != null and areaKind != 0">
            <choose>
                <when test="req.areaKind == 1">
                    AND a.kind1 = 1
                </when>
                <otherwise>
                    AND a.kind1 != 1
                </otherwise>
            </choose>
        </if>

        <choose>
            <when test="req.sortKind != null and req.sortKind != 0 and req.sortDes != null and req.sortDes != 0">
                <if test="req.sortkind == 1">
                    order by applyCount
                </if>
                <if test="req.sortkind == 2">
                    order by applyDate
                </if>
                <if test="req.sortkind == 3">
                    order by guihuandate
                </if>
                <if test="req.sortkind == 4">
                    order by price
                </if>
                <if test="req.sortkind == 5">
                    order by inprice
                </if>
                <choose>
                    <when test="req.sortDes ==1">
                        ASC
                    </when>
                    <otherwise>
                        DESC
                    </otherwise>
                </choose>
            </when>
            <otherwise>
                order by id desc
            </otherwise>
        </choose>
    </select>

    <select id="getDaiYongjiListByIds" resultType="com.jiuji.oa.afterservice.bigpro.statistics.bo.DaiYongJiBo">
        SELECT d.id,d.name,d.areaid,d.grade,d.wai_guan,d.product_color,a.area FROM dbo.daiyongji d WITH(NOLOCK)
        LEFT JOIN daiyongji_toarea t WITH(NOLOCK) ON d.id=t.daiyongjiId  AND t.stats!=3
        LEFT JOIN dbo.areainfo a WITH(NOLOCK) ON a.id=d.areaid
        WHERE t.id IS NULL AND d.id IN
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
        AND ISNULL(d.stats,0)=1
    </select>

</mapper>