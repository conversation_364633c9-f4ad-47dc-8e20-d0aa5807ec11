<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.bigpro.dao.KaoqinMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.afterservice.bigpro.po.Kaoqin">
        <id column="id" property="id" />
        <result column="ch999_id" property="ch999Id" />
        <result column="dtime" property="dtime" />
        <result column="kind1" property="kind1" />
        <result column="kind2" property="kind2" />
        <result column="bkind" property="bkind" />
        <result column="dsc" property="dsc" />
        <result column="sslid" property="sslid" />
        <result column="scheduleInfoId" property="scheduleInfoId" />
        <result column="unusual" property="unusual" />
        <result column="ischeck" property="ischeck" />
        <result column="appidentifier" property="appidentifier" />
        <result column="phoneName" property="phoneName" />
        <result column="dkgps" property="dkgps" />
    </resultMap>

    <select id="getCurrentWork" resultType="java.lang.Integer">
        SELECT ch999_id FROM dbo.kaoqin with(nolock) WHERE 1=1
        <if test="ch999Ids != null and ch999Ids.size > 0">
            and ch999_id in
            <foreach collection="ch999Ids" index="index" item="ch99Id" open="(" separator="," close=")">
                #{ch99Id}
            </foreach>
        </if>
         and datediff(day,dtime,getdate())=0  and kind1=1 order by id desc
    </select>
    <select id="getCurrentWorkByAreaIdAndCh999Ids" resultType="java.lang.Integer">
        <include refid="currentWorkCh999IdByAreaIdSqlPart"></include>
              and cu.ch999_id in
              <foreach collection="ch999Ids" separator="," item="ch999Id" open="(" close=")">
                  #{ch999Id}
              </foreach>
    </select>
    <select id="getCurrentWorkByAreaIdAndRoleIds" resultType="java.lang.Integer">
        <include refid="currentWorkCh999IdByAreaIdSqlPart"></include>
        and cu.mainRole in
        <foreach collection="roleIds" separator="," item="roleId" open="(" close=")">
            #{roleId}
        </foreach>
    </select>
    <select id="getCurrentWorkByAreaIdAndZhiWuIds" resultType="java.lang.Integer">
        <include refid="currentWorkCh999IdByAreaIdSqlPart"></include>
        and cu.zhiwuid in
        <foreach collection="zhiWuIds" separator="," item="zhiWuId" open="(" close=")">
            #{zhiWuId}
        </foreach>
    </select>
    <sql id="currentWorkCh999IdByAreaIdSqlPart">
        SELECT cu.ch999_id from ch999_user cu with(nolock)
                                    inner join areainfo a with(nolock) on a.id = #{areaId} and a.id = cu.area1id
        where exists(select 1 from kaoqin k with(nolock) where k.ch999_id = cu.ch999_id and datediff(day,dtime,getdate())=0)
    </sql>
</mapper>
