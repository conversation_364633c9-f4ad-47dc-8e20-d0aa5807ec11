<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jiuji.oa.afterservice.smallpro.dao.SmallProConfigMapper">
    <sql id="andKeyLikeSqlPart">
        and <choose>
               <when test="ppids != null and ppids.size() != 1">
                     pi.ppriceid in
                     <foreach collection="ppids" item="ppid" open="(" close=")" separator=",">
                         #{ppid}
                     </foreach>
               </when>
                <otherwise>
                    pi.ppriceid = #{wordKey}
                </otherwise>
            </choose>
    </sql>
    <sql id="andWebKeywordSqlPart">
        and (pi.ppriceid like CONCAT ( '',#{webKeyword}, '%' ) or pi.product_name like CONCAT ( '%', #{webKeyword}, '%' ) or pi.barCode like CONCAT ( '%', #{webKeyword}, '%' ))
    </sql>
    <select id="getConfig" resultType="com.jiuji.oa.afterservice.smallpro.po.exchange.SmallExchangeConfigPo">
        SELECT top 1 sec.id, sec.title, sec.service_type, sec.is_different_price, sec.is_service_different_price, sec.different_price_type
             , sec.different_price_percent, sec.product_config_type,sec.policy_content,sec.create_time, sec.create_user, sec.update_time, sec.is_del
             , sec.is_same_machine
        FROM dbo.small_exchange_config sec with(nolock)
        <where>
            isnull(sec.is_del,0)=0 and isnull(is_enabled,0)=1 and sec.product_config_type = #{configType}
            <foreach collection="configTypeAndValues" open="and" separator="and" item="configTypeAndValue">
                exists(
                    select 1 from small_exchange_product_config sepc with(nolock)
                    where isnull(sepc.is_del,0) = 0 and sepc.fk_config_id = sec.id
                    and sepc.kind = 1
                    and sepc.config_type = #{configTypeAndValue.configType.code} and sepc.config_value = #{configTypeAndValue.configValue}
                )
                <if test="wordKey != null and wordKey>0 and configTypeAndValue.configType.columnName != null">
                   and exists(
                        select 1 from small_exchange_product_config sepc with(nolock)
                        where isnull(sepc.is_del,0) = 0 and sepc.fk_config_id = sec.id
                        and sepc.kind = 1
                        and sepc.config_type = #{configTypeAndValue.configType.code}
                        and exists(select 1 from dbo.productinfo pi with(nolock)
                        where ISNULL(pi.ismobile1,0)=0 and pi.${configTypeAndValue.configType.columnName} = sepc.config_value <include refid="andKeyLikeSqlPart"></include>)
                    )
                </if>
                <if test="wordKey == null and webKeyword != null and webKeyword != '' and configTypeAndValue.configType.columnName != null">
                    and exists(
                        select 1 from small_exchange_product_config sepc with(nolock)
                        where isnull(sepc.is_del,0) = 0 and sepc.fk_config_id = sec.id
                        and sepc.kind = 1
                        and sepc.config_type = #{configTypeAndValue.configType.code}
                        and exists(select 1 from dbo.productinfo pi with(nolock)
                        where ISNULL(pi.ismobile1,0)=0 and pi.${configTypeAndValue.configType.columnName} = sepc.config_value
                        <include refid="andWebKeywordSqlPart"></include>)
                    )
                </if>
            </foreach>
            and service_type = #{serviceType}
        </where>
        order by sec.update_time desc,sec.id desc

    </select>
    <select id="listConfigProduct"
            resultType="com.jiuji.oa.afterservice.smallpro.po.exchange.SmallExchangeProductConfigPo">
        SELECT sepc.id, sepc.fk_config_id, sepc.config_type, sepc.config_value, sepc.create_time, sepc.create_user, sepc.update_time, sepc.is_del
        , sepc.kind
        FROM dbo.small_exchange_product_config sepc with(nolock) where isnull(sepc.is_del,0)=0 and sepc.fk_config_id = #{configId}
    </select>
    <select id="listSmallProductInfo" resultType="com.jiuji.oa.afterservice.smallpro.po.exchange.ProductExchangePo">

        SELECT  pi.productid productId,a.ppriceid ppriceid,pi.brandID brandID,a.cid, pi.product_name productName,pi.product_color productColor,a.barCode
        ,a.memberprice,isnull(a.leftCount,0) leftCount,pi.display,pi.que
        FROM (
                SELECT pi.ppriceid,pi.cid,pi.barCode,pi.memberprice,pk.leftCount FROM dbo.productinfo pi with(nolock)
                left join product_kc pk with(nolock) on pk.ppriceid = pi.ppriceid and pk.areaid = #{smallConfigProductInfo.kcAreaId}
                <where>
                    <if test="wordKey != null and wordKey>0">
                        <include refid="andKeyLikeSqlPart"></include>
                    </if>
                    <if test="smallConfigProductInfo.productIds != null">
                        <foreach collection="smallConfigProductInfo.productIds" item="productId" open="and pi.productid in (" close=")" separator=",">
                            #{productId}
                        </foreach>
                    </if>
                    <if test="smallConfigProductInfo.ppids != null">
                        <foreach collection="smallConfigProductInfo.ppids" item="ppid" open="and pi.ppriceid in (" close=")" separator=",">
                            #{ppid}
                        </foreach>
                    </if>
                    <if test="smallConfigProductInfo.retainPpids != null">
                        <foreach collection="smallConfigProductInfo.retainPpids" item="ppid" open="and pi.ppriceid in (" close=")" separator=",">
                            #{ppid}
                        </foreach>
                    </if>
                    <if test="smallConfigProductInfo.brandIds != null">
                        <foreach collection="smallConfigProductInfo.brandIds" item="brandId" open="and pi.brandID in (" close=")" separator=",">
                            #{brandId}
                        </foreach>
                    </if>
                    <if test="smallConfigProductInfo.cids != null">
                        <foreach collection="smallConfigProductInfo.cids" item="cid" open="and pi.cid in (" close=")" separator=",">
                            #{cid}
                        </foreach>
                    </if>
                    AND ISNULL(pi.ismobile1,0)=0
                </where>
                ORDER BY leftCount desc,isnull(pi.display,0) desc,pi.memberprice ASC,pi.viewsWeek DESC
                OFFSET 0 ROWS FETCH NEXT 50 ROWS ONLY
        ) a
        inner join dbo.productinfo pi with(nolock) on a.ppriceid = pi.ppriceid
        ORDER BY leftCount desc,memberprice ASC

    </select>

    <!-- SmallProductInfoForWebWhere -->
    <sql id="SmallProductInfoForWebWhere">
        <where>
            <if test="req.webPpids != null and req.webPpids.size() > 0">
                <choose>
                    <when test="req.webPpidsNotIn != null and req.webPpidsNotIn == true">
                        and (pi.ppriceid NOT IN
                    </when>
                    <otherwise>
                        and (pi.ppriceid IN
                    </otherwise>
                </choose>
                <foreach collection="req.webPpids" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="req.keywords != null and req.keywords != ''">
                and (pi.product_name like CONCAT ( '%', #{req.keywords}, '%' ))
            </if>
            <if test="req.categoryIds != null and req.categoryIds.size() > 0">
                <foreach collection="req.categoryIds" item="cid" open="and pi.cid in (" close=")" separator=",">
                    #{cid}
                </foreach>
            </if>
            <if test="req.brandIds != null and req.brandIds.size() > 0">
                <foreach collection="req.brandIds" item="brandId" open="and pi.brandID in (" close=")" separator=",">
                    #{brandId}
                </foreach>
            </if>
            <if test="config.productIds != null">
                <foreach collection="config.productIds" item="productId" open="and pi.productid in (" close=")" separator=",">
                    #{productId}
                </foreach>
            </if>
            <if test="config.ppids != null">
                <foreach collection="config.ppids" item="ppid" open="and pi.ppriceid in (" close=")" separator=",">
                    #{ppid}
                </foreach>
            </if>
            <if test="config.brandIds != null and config.brandIds.size() > 0">
                <foreach collection="config.brandIds" item="brandId" open="and pi.brandID in (" close=")" separator=",">
                    #{brandId}
                </foreach>
            </if>
            <if test="config.cids != null and config.cids.size() > 0">
                <foreach collection="config.cids" item="cid" open="and pi.cid in (" close=")" separator=",">
                    #{cid}
                </foreach>
            </if>
            AND ISNULL(pi.ismobile1, 0) = 0
            AND pi.display = 1
        </where>
    </sql>

    <!-- listSmallProductInfoForWeb -->
    <select id="listSmallProductInfoForWeb" resultType="com.jiuji.oa.afterservice.smallpro.po.exchange.ProductExchangePo">
        SELECT pi.productid productId,pi.ppriceid ppriceid,pi.brandID brandID,pi.cid, pi.product_name productName,
            pi.product_color productColor,pi.barCode, pi.memberprice
        FROM dbo.productinfo pi with(nolock)
        <include refid="SmallProductInfoForWebWhere"></include>
        ORDER BY isnull(pi.display,0) desc, pi.memberprice ASC, pi.viewsWeek DESC
        OFFSET #{req.offset} ROWS FETCH NEXT #{req.size} ROWS ONLY
    </select>

    <!-- countSmallProductInfoForWeb -->
    <select id="countSmallProductInfoForWeb" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM dbo.productinfo pi with(nolock)
        <include refid="SmallProductInfoForWebWhere"></include>
    </select>

    <select id="getConfigList" resultType="com.jiuji.oa.afterservice.smallpro.vo.res.ExchangeGoodsConfigRes">
        SELECT sec.id, sec.title, sec.service_type, sec.is_different_price, sec.is_service_different_price, sec.different_price_type
        , sec.different_price_percent, sec.product_config_type,sec.create_time, sec.create_user, sec.update_time, sec.is_del,
        sec.policy_content,sec.is_same_machine
        FROM dbo.small_exchange_config sec with(nolock) WHERE isnull(sec.is_del,0)=0 and isnull(is_enabled,0)=1
        <if test="req.brandIdList != null and req.brandIdList.size() != 0">
            and exists(select 1 from small_exchange_product_config sepc with(nolock)
            where isnull(sepc.is_del,0) = 0 and sepc.fk_config_id = sec.id
            and sepc.config_type = 1 and sepc.config_value in
            <foreach collection="req.brandIdList" open="(" separator="," close=")" item="brandId">
                #{brandId}
            </foreach>
            )
        </if>
        <if test="req.cidList != null and req.cidList.size() != 0">
            and exists(select 1 from small_exchange_product_config sepc with(nolock)
            where isnull(sepc.is_del,0) = 0 and sepc.fk_config_id = sec.id
            and sepc.config_type = 2 and sepc.config_value in
            <foreach collection="req.cidList" open="(" separator="," close=")" item="cid">
                #{cid}
            </foreach>
            )
        </if>
        <if test="req.differentPriceType != null and req.differentPriceType != 0">
            and sec.different_price_type = #{req.differentPriceType}
        </if>
        <if test="req.serviceType != null">
            and sec.service_type = #{req.serviceType}
        </if>
        <if test="req.serviceTypeList != null and req.serviceTypeList.size > 0">
            and sec.service_type in
            <foreach collection="req.serviceTypeList" index="index" item="serviceType" open="(" close=")" separator=",">
                #{serviceType}
            </foreach>
        </if>
        <if test="req.title != '' and req.title != null">
            and sec.title like concat('%',#{req.title},'%')
        </if>
        <if test="req.productConfigType != null and req.productConfigType.size() != 0">
            and sec.product_config_type in
            <foreach collection="req.productConfigType" open="(" separator="," close=")" item="productConfigTypes">
                #{productConfigTypes}
            </foreach>
        </if>
        <if test="req.searchValue != null and req.searchValue != ''">
            <choose>
                <!--sku-->
                <when test="req.searchType != null and req.searchType == 1">
                    and exists(select 1 from small_exchange_product_config sepc with(nolock)
                    where isnull(sepc.is_del,0) = 0 and sepc.fk_config_id = sec.id
                    and sepc.config_type = 5 and sepc.config_value = #{req.searchValue}
                    )
                </when>
                <!--spu-->
                <when test="req.searchType != null and req.searchType == 2">
                    and exists(select 1 from small_exchange_product_config sepc with(nolock)
                    where isnull(sepc.is_del,0) = 0 and sepc.fk_config_id = sec.id
                    and sepc.config_type = 4 and sepc.config_value = #{req.searchValue}
                    )
                </when>
            </choose>
        </if>
    </select>

    <select id="listConfigProductByIds"
            resultType="com.jiuji.oa.afterservice.smallpro.po.exchange.SmallExchangeProductConfigPo">
        SELECT sepc.id, sepc.fk_config_id, sepc.config_type, sepc.config_value, sepc.create_time, sepc.create_user, sepc.update_time, sepc.is_del
        FROM dbo.small_exchange_product_config sepc with(nolock) where isnull(sepc.is_del,0)=0 and sepc.fk_config_id in
        <foreach collection="configIds" open="(" separator="," close=")" item="configId">
            #{configId}
        </foreach>
    </select>

    <update id="delExchangeGoodsConfig">
        update small_exchange_config set is_del = 1 where id = #{id} and isnull(is_del,0) = 0
    </update>

    <update id="delExchangeProductConfigByConfigId">
        update small_exchange_product_config set is_del = 1 where fk_config_id = #{configId} and isnull(is_del,0) = 0
    </update>

    <insert id="addExchangeConfig" useGeneratedKeys="true" keyProperty="req.id" keyColumn="id">
        insert into dbo.small_exchange_config
        (title, service_type, is_different_price, is_service_different_price, different_price_type,different_price_percent, product_config_type,create_time, create_user, update_time,is_enabled, is_del, policy_content, is_same_machine)
        values(#{req.title},#{req.serviceType},#{req.isDifferentPrice},#{req.isServiceDifferentPrice},#{req.differentPriceType},#{req.differentPricePercent},#{req.productConfigType},GETDATE() ,#{userName},GETDATE(),1,0,#{req.policyContent}, #{req.isSameMachine})
    </insert>

    <insert id="addExchangeProductConfig">
        insert into small_exchange_product_config
        (fk_config_id, config_type, config_value, create_time, create_user, update_time, is_del)
        values
        <foreach collection="configTypeAndValues" item="item" separator=",">
            (#{id}, #{item.configType.code}, #{item.configValue},GETDATE(),#{userName},GETDATE(),0)
        </foreach>
    </insert>
    <insert id="saveExchangeConfig" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert into dbo.small_exchange_config
        (title, service_type, is_different_price, is_service_different_price, different_price_type,
         different_price_percent, product_config_type,create_time, create_user, update_time,is_enabled,
         is_del, policy_content, is_same_machine, addition_product_config_type)
        values(#{req.title},#{req.serviceType},#{req.isDifferentPrice},#{req.isServiceDifferentPrice},#{req.differentPriceType},
               #{req.differentPricePercent},#{req.productConfigType},GETDATE() ,#{req.createUser},GETDATE(),1,
               0,#{req.policyContent}, #{req.isSameMachine}, #{req.additionProductConfigType})
    </insert>
    <insert id="saveExchangeProductConfigBatch">
        insert into small_exchange_product_config
        (fk_config_id, config_type, config_value, create_time, create_user, update_time, is_del,kind)
        values
        <foreach collection="req" item="item" separator=",">
            (#{item.fkConfigId}, #{item.configType}, #{item.configValue},GETDATE(),#{item.createUser},GETDATE(),0,#{item.kind})
        </foreach>
    </insert>

    <update id="updateExchangeConfig">
        UPDATE small_exchange_config
        SET title = #{req.title}
        ,service_type = #{req.serviceType}
        ,is_different_price = #{req.isDifferentPrice}
        ,is_service_different_price = #{req.isServiceDifferentPrice}
        ,different_price_type = #{req.differentPriceType}
        ,different_price_percent = #{req.differentPricePercent}
        ,product_config_type = #{req.productConfigType}
        ,create_user = #{userName}
        ,policy_content = #{req.policyContent}
        ,is_same_machine = #{req.isSameMachine}
        ,update_time = GETDATE()
        WHERE
        id = #{req.id}
    </update>

    <update id="updateExchangeProductConfig">
        insert into small_exchange_product_config
        (fk_config_id, config_type, config_value, create_time, create_user, update_time, is_del)
        values
        <foreach collection="smallExchangeProductConfigPoList" item="item" separator=",">
            (#{configId}, #{item.configType}, #{item.configValue},GETDATE(),#{userName},GETDATE(),0)
        </foreach>
    </update>

    <!--配置删除-->
    <update id="delExchangeProductConfig">
        UPDATE small_exchange_product_config
        SET is_del = 1
        WHERE isnull(is_del,0) = 0 and fk_config_id = #{configId}
        and config_type in
        <foreach collection="smallExchangeProductConfigPoList" item="data" open="(" close=")" separator=",">
            #{data.configType}
        </foreach>
        and config_value in
        <foreach collection="smallExchangeProductConfigPoList" item="data" open="(" close=")" separator=",">
            #{data.configValue}
        </foreach>
    </update>
    <update id="updateExchangeProductConfigById">
        UPDATE small_exchange_config
        SET title = #{req.title}
          ,service_type = #{req.serviceType}
          ,is_different_price = #{req.isDifferentPrice}
          ,is_service_different_price = #{req.isServiceDifferentPrice}
          ,different_price_type = #{req.differentPriceType}
          ,different_price_percent = #{req.differentPricePercent}
          ,product_config_type = #{req.productConfigType}
          ,policy_content = #{req.policyContent}
          ,is_same_machine = #{req.isSameMachine}
          ,update_time = GETDATE()
          ,addition_product_config_type = #{req.additionProductConfigType}
        WHERE
            id = #{req.id}
    </update>
    <update id="delExchangeProductConfigByIds">
        UPDATE small_exchange_product_config
        SET is_del = 1
        WHERE id in
        <foreach collection="ids" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </update>

    <select id="getConfigById" resultType="com.jiuji.oa.afterservice.smallpro.po.exchange.SmallExchangeConfigPo">
        SELECT sec.id, sec.title, sec.service_type, sec.is_different_price, sec.is_service_different_price, sec.different_price_type
             , sec.different_price_percent, sec.product_config_type,sec.create_time, sec.create_user, sec.update_time, sec.is_del,sec.policy_content
             , sec.is_same_machine, sec.addition_product_config_type
        FROM dbo.small_exchange_config sec with(nolock) where sec.id = #{id}
    </select>
    <select id="selectConfigToWeb" resultType="com.jiuji.oa.afterservice.shouhou.vo.res.SmallExchangeConfigRes">
        select * from dbo.small_exchange_config s with(nolock)
        <where>
            isnull(s.is_del,0)=0
            <if test="req.serviceType != null and req.serviceType.size > 0">
                and s.service_type in
                <foreach collection="req.serviceType" item="serviceType" open="(" close=")" separator=",">
                    #{serviceType}
                </foreach>
            </if>
        </where>
    </select>
    <select id="selectConfigProductByIds" resultType="com.jiuji.oa.afterservice.shouhou.vo.res.SmallExchangeProductConfigRes">
        select * from dbo.small_exchange_product_config c with(nolock )
        <where>
            isnull(c.is_del,0)=0
            <if test="ids != null and ids.size > 0">
                and c.fk_config_id in
                <foreach collection="ids" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
        </where>
    </select>


</mapper>
