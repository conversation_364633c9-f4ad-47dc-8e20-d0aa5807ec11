<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.smallpro.dao.SmallproMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.afterservice.smallpro.po.Smallpro">
        <id column="id" property="id"/>
        <result column="Name" property="name"/>
        <result column="Area" property="area"/>
        <result column="userid" property="userId"/>
        <result column="sub_id" property="subId"/>
        <result column="Buydate" property="buyDate"/>
        <result column="Outward" property="outward"/>
        <result column="IsBaoxiu" property="isBaoxiu"/>
        <result column="Groupid" property="groupId"/>
        <result column="Inuser" property="inUser"/>
        <result column="Indate" property="inDate"/>
        <result column="Kind" property="kind"/>
        <result column="Stats" property="stats"/>
        <result column="Username" property="userName"/>
        <result column="Mobile" property="mobile"/>
        <result column="Problem" property="problem"/>
        <result column="Comment" property="comment"/>
        <result column="toarea" property="toArea"/>
        <result column="istoarea" property="isToArea"/>
        <result column="yuyueCheck" property="yuyueCheck"/>
        <result column="yuyueid" property="yuyueId"/>
        <result column="areaid" property="areaId"/>
        <result column="toareaid" property="toAreaId"/>
        <result column="oldid" property="oldId"/>
        <result column="codeMsg" property="codeMsg"/>
        <result column="owenStats" property="owenStats"/>
        <result column="isshouyinglock" property="isShouyingLock"/>
        <result column="feiyong" property="feiyong"/>
        <result column="costprice" property="costPrice"/>
        <result column="shouyinuser" property="shouyinUser"/>
        <result column="shouyindate" property="shouyinDate"/>
        <result column="istui" property="isTui"/>
        <result column="qujiandate" property="qujianDate"/>
        <result column="wxqudao" property="wxQudao"/>
        <result column="wxState" property="wxState"/>
        <result column="wxuser" property="wxUser"/>
        <result column="isCheckmsg" property="isCheckMsg"/>
        <result column="isdel" property="isdel"/>
        <result column="isth" property="isTh"/>
        <result column="tongzhitime" property="tongzhiTime"/>
        <result column="wuliuid" property="wuliuId"/>
        <result column="fcWuliuId" property="fcWuliuId"/>
        <result column="intertime" property="interTime"/>
        <result column="toareatime" property="toAreaTime"/>
        <result column="config" property="config"/>
        <result column="dataRelease" property="dataRelease"/>
        <result column="imei" property="imei"/>
        <result column="fid" property="fid"/>
        <result column="hhSubId" property="hhSubId"/>
        <result column="changePpriceid" property="changePpriceid"/>
        <result column="ServiceType" property="serviceType"/>
    </resultMap>

    <select id="selectLogByBasketId" resultType= "com.jiuji.oa.afterservice.smallpro.po.Smallpro">
     select * from dbo.Smallpro s with (nolock)
        left join dbo.SmallproBill b with (nolock) on b.smallproID = s.id
        where isnull(s.isdel,0)=0 and s.ServiceType = 5 and s.Stats &lt;&gt; 2
        and b.basket_id =#{basketId}
    </select>

    <!--如果筛选项为已删除，isdel判断不需要，已删除根据isdel做判断-->
    <sql id="queryConditionSql">
        <if test="query != null">
            <choose>
                <when test="query.keyKind != null and query.key != null and query.key.trim() != '' and query.keyKind=='id'">
                    and sp.id = #{query.key}
                </when>
                <otherwise>
                    <if test="query.keyKind=='basketId' and query.smallProIds!=null and query.smallProIds.size()>0">
                        and  sp.id in
                        <foreach collection="query.smallProIds" index="index" item="item" separator="," open="(" close=")">
                            #{item}
                        </foreach>
                    </if>
                    <if test="query.serviceType != null and query.serviceType !=0">
                        and ServiceType = #{query.serviceType}
                    </if>
                    <!-- 赠送年包服务 : 1  年包赠送出险 2  年包赠送质保换新-->
                    <if test="query.giveServiceTypeList != null and query.giveServiceTypeList.size() > 0">
                        and (
                        <foreach collection="query.giveServiceTypeList" item="item" index="index" open="" separator=" or " close="">
                            <if test="item == 1">
                                 EXISTS (SELECT 1
                                from year_package_transfer ypt with(nolock)
                                WHERE 1=1 and ypt.small_id = sp.id
                                )
                            </if>
                            <if test="item == 2">
                                 EXISTS (
                                SELECT 1
                                from year_package_transfer ypt with(nolock)
                                WHERE 1=1 and ypt.transfer_code = sp.imei  and sp.ServiceType = 5
                                )
                            </if>

                        </foreach>
                        )
                    </if>

                    <if test="query.abnormalType != null and query.abnormalType !=0">
                        and abnormal_type = #{query.abnormalType}
                    </if>
                    <if test="query.kind != null and query.kind>0">
                        and kind = #{query.kind}
                    </if>
                    <if test="query.startExcelTime != null and query.startExcelTime != ''">
                        and indate <![CDATA[ > ]]> #{query.startExcelTime}
                    </if>
                    <if test="query.status != null">
                        <choose>
                            <when test="query.status == 0">
                                and stats=0 and isnull(wxState,0) = 0 and isnull(sp.isdel,0) = 0 and stats!=2
                            </when>
                            <when test="query.status == 2">
                                and isnull(sp.isdel,0) = 1
                            </when>
                            <when test="query.status == 4">
                                AND EXISTS(SELECT 1 FROM shouhou_fanchang with(nolock) WHERE smallproid=sp.id AND
                                rstats=1) and isnull(sp.isdel,0) = 0 and stats!=2
                            </when>
                            <when test="query.status == 5">
                                and isnull(wxState,0) = 1 and isnull(stats,0) &lt;> 1 and isnull(sp.isdel,0) = 0 and stats!=2
                            </when>
                            <when test="query.status == 6">
                                and isnull(wxState,0) = 2 and isnull(stats,0) &lt;> 1 and isnull(sp.isdel,0) = 0 and stats!=2
                            </when>
                            <otherwise>
                                and stats = #{query.status}
                            </otherwise>
                        </choose>
                    </if>
                    <if test="query.kind != null and query.kind == 2 and query.exchangeCheckState != null">
                        <choose>
                            <when test="query.exchangeCheckState == 0">
                                and exists(select 1 from dbo.shouhou_tuihuan t with(nolock) where t.tuihuan_kind=9 and
                                t.check1user is null
                                and t.smallproid=sp.id and isnull(t.isdel,0)=0)
                            </when>
                            <when test="query.exchangeCheckState == 1">
                                and exists(select 1 from dbo.shouhou_tuihuan t with(nolock) where t.tuihuan_kind=9 and
                                t.check1user is not
                                null and t.smallproid=sp.id and isnull(t.isdel,0)=0)
                            </when>
                        </choose>
                    </if>
                    <if test="query.kind != null and query.kind == 3 and query.returnCheckState != null">
                        <choose>
                            <when test="query.returnCheckState == 0">
                                and exists(select 1 from dbo.shouhou_tuihuan t with(nolock) where t.tuihuan_kind=7 and
                                t.check2user is null
                                and t.smallproid=sp.id and isnull(t.isdel,0)=0)
                                and not exists(select 1 from dbo.shouhou_tuihuan t with(nolock) where t.tuihuan_kind=7 and
                                t.check2user is not null
                                and t.smallproid=sp.id and isnull(t.isdel,0)=0)
                            </when>
                            <when test="query.returnCheckState == 1">
                                and exists(select 1 from dbo.shouhou_tuihuan t with(nolock) where t.tuihuan_kind=7 and
                                t.check1user is not
                                null and t.check2user is null and t.smallproid=sp.id and isnull(t.isdel,0)=0)
                            </when>
                            <when test="query.returnCheckState == 2">
                                and exists(select 1 from dbo.shouhou_tuihuan t with(nolock) where t.tuihuan_kind=7 and
                                t.check2user is not
                                null and t.check3user is null and t.smallproid=sp.id and isnull(t.isdel,0)=0)
                            </when>
                            <when test="query.returnCheckState == 3">
                                and exists(select 1 from dbo.shouhou_tuihuan t with(nolock) where t.tuihuan_kind=7 and
                                t.check3user is not
                                null and t.smallproid=sp.id and isnull(t.isdel,0)=0)
                            </when>
                        </choose>
                    </if>
                    <if test="query.status == null || query.status != 2">
                        and isnull(sp.isdel,0) = 0
                    </if>
                    <if test="query.isSenior != null and query.isSenior == 1 and query.startTime != null and query.startTime != '' and query.endTime != null and query.endTime != ''">
                        <if test="query.timeKind != null and query.timeKind == 1">
                            and indate between #{query.startTime} and #{query.endTime}
                        </if>
                        <if test="query.timeKind != null and query.timeKind == 2">
                            and wx_time between #{query.startTime} and #{query.endTime}
                        </if>
                    </if>

                    <if test="query.keyKind != null and query.key != null and query.key != ''">
                        <choose>
                            <when test="query.keyKind=='productName'">
                                and name like CONCAT('%',#{query.key},'%')
                            </when>
                            <when test="query.keyKind=='inUser'">
                                and inuser = #{query.key}
                            </when>
                            <when test="query.keyKind=='subId'">
                                and sub_id = #{query.key}
                            </when>
                            <when test="query.keyKind=='mobile'">
                                and mobile like CONCAT('%',#{query.key},'%')
                            </when>
                            <when test="query.keyKind=='userName'">
                                and username like CONCAT('%',#{query.key},'%')
                            </when>
                            <when test="query.keyKind=='ppid'">
                                and EXISTS (select 1 from SmallproBill sb with(nolock) where sb.smallproID=sp.id and sb.ppriceid
                                =#{query.key})
                            </when>
                            <when test="query.keyKind=='barCode'">
                                AND sp.id in (
                                SELECT
                                sp.id
                                FROM
                                SmallproBill a with(nolock)
                                LEFT JOIN productinfo p with(nolock) ON a.ppriceid= p.ppriceid
                                LEFT JOIN Smallpro sp with(nolock) on a.smallproID=sp.id
                                WHERE
                                p.barCode LIKE CONCAT ( '%',#{query.key}, '%' )
                                )
                            </when>
                            <when test="query.keyKind=='wxUser'">
                                and wxuser LIKE CONCAT ( '%',#{query.key}, '%' )
                            </when>
                        </choose>
                    </if>
                    <if test="(query.toAreaIds != null and query.toAreaIds.size>0) and (query.areaIds !=null and query.areaIds.size>0)">
                        AND (
                        (( sp.istoarea= 1 AND sp.toareaid IN
                        <foreach collection="query.toAreaIds" index="index" item="areaId" separator="," open="(" close=")">
                            #{areaId}
                        </foreach>
                        )
                        OR ( sp.istoarea= 0 AND ISNULL( sp.toareaid, sp.areaid ) IN
                        <foreach collection="query.toAreaIds" index="index" item="areaId" separator="," open="(" close=")">
                            #{areaId}
                        </foreach>
                        ))
                        and sp.areaid IN
                        <foreach collection="query.areaIds" index="index" item="areaId" separator="," open="(" close=")">
                            #{areaId}
                        </foreach>
                        )
                    </if>
                    <if test="((query.toAreaIds != null and query.toAreaIds.size > 0) and (query.areaIds ==null or query.areaIds.size &lt;= 0))
                    or ((query.areaIds !=null and query.areaIds.size > 0) and (query.toAreaIds == null or query.toAreaIds.size &lt;= 0))">
                        <if test="query.toAreaIds != null and query.toAreaIds.size>0">
                            AND (
                            ( sp.istoarea= 1 AND sp.toareaid IN
                            <foreach collection="query.toAreaIds" index="index" item="areaId" separator="," open="(" close=")">
                                #{areaId}
                            </foreach>
                            )
                            OR ( sp.istoarea= 0 AND ISNULL( sp.toareaid, sp.areaid ) IN
                            <foreach collection="query.toAreaIds" index="index" item="areaId" separator="," open="(" close=")">
                                #{areaId}
                            </foreach>
                            )
                            )
                        </if>
                        <if test="query.areaIds !=null and query.areaIds.size>0">
                            AND sp.areaid IN
                            <foreach collection="query.areaIds" index="index" item="areaId" separator="," open="(" close=")">
                                #{areaId}
                            </foreach>
                        </if>
                    </if>
                    <if test="query.toAreaStatus != null and query.toAreaStatus >0">
                        <choose>
                            <when test="query.toAreaStatus == 1">
                                and isnull(isToArea,0) = 1
                            </when>
                            <otherwise>
                                and isnull(isToArea,0) = 0
                            </otherwise>
                        </choose>
                    </if>
                    <if test="query.toCollectionStatus != null and query.toCollectionStatus >0">
                        <choose>
                            <when test="query.toCollectionStatus == 1">
                                and isnull(isshouyinglock,0) = 1
                            </when>
                            <otherwise>
                                and isnull(isshouyinglock,0) = 0
                            </otherwise>
                        </choose>
                    </if>
                    <if test="query.isSenior != null and query.isSenior == 1 and query.check3dtimeHour != null and query.check3dtimeHour>0">
                        and DATEDIFF(HOUR, Indate, GETDATE()) >= #{query.check3dtimeHour} AND toareatime IS NULL
                    </if>
                    <if test="query.isSenior != null and query.isSenior == 1 and query.toAreaTimeHour != null and query.toAreaTimeHour>0">
                        and Kind in (2,3) and DATEDIFF(HOUR,Indate,GETDATE()) >= #{query.toAreaTimeHour} AND NOT EXISTS
                        (SELECT 1 FROM shouhou_tuihuan st with(nolock) WHERE st.smallproid = sp.id AND st.check3dtime IS
                        NOT NULL )
                    </if>
                </otherwise>
            </choose>
            <!-- 授权隔离 -->
            <if test="query.isAuthPart != null and query.isAuthPart == true">
                AND EXISTS(SELECT 1 FROM dbo.areainfo area WITH(NOLOCK) WHERE authorizeid= #{query.authorizeId} AND area.id=ISNULL(sp.areaid,sp.toareaid))
            </if>
            <!-- 是否大件换货 -->
            <if test="query.mobileExchangeFlag != null and query.mobileExchangeFlag == 0">
                AND EXISTS(SELECT 1 FROM dbo.SmallproBill sb WITH(NOLOCK) WHERE sb.smallproID= sp.id and ISNULL(mobile_exchange_flag,0) = 0)
            </if>
            <if test="query.mobileExchangeFlag != null and query.mobileExchangeFlag == 1">
                AND EXISTS(SELECT 1 FROM dbo.SmallproBill sb WITH(NOLOCK) WHERE sb.smallproID= sp.id and ISNULL(mobile_exchange_flag,0) = 1)
            </if>
        </if>
    </sql>

    <select id="getSmallproReturnGoodsNum" resultType="java.lang.Integer">
        select count(*) from smallpro sp with(nolock)
        LEFT JOIN productinfo p with(nolock) on sp.changePpriceid=p.ppriceid
        where 1=1
        <include refid="queryConditionSql">
            <property name="query" value="#{query}"/>
        </include>
        <if test="query.brandIds != null and query.brandIds.size() > 0">
            and p.brandid in
            <foreach collection="query.brandIds" index="index" item="it" separator="," open="(" close=")">
                #{it}
            </foreach>
        </if>
        <if test="query.categoryCharSeq != null and query.categoryCharSeq != '' ">
            and p.cid in (select id from f_category_children (#{query.categoryCharSeq}))
        </if>
    </select>

    <select id="getSmallproReturnGoodsList"
            resultType="com.jiuji.oa.afterservice.smallpro.vo.res.SmallproReturnGoodsRes">
        SELECT
        sp.id,
        sp.changePpriceid as changePpriceid,
        (p.product_name + p.product_color)  as changePpriceidName,
        sp.sub_id as subId,
        sp.ServiceType as serviceType,
        sp.userid AS userId,
        sp.name AS productName,
        CONVERT(varchar(100), sp.indate, 20) AS inDate,
        sp.qujiandate AS quJiAnDate,
        CASE
        WHEN sp.istoarea= 1 THEN
        sp.toareaid ELSE ISNULL( sp.toareaid, sp.areaid )
        END AS toAreaId,
        sp.areaid AS areaId,
        sp.IsBaoxiu AS warrantyStatus,
        sp.Kind,
        sp.Groupid AS groupId,
        sp.Problem AS problem,
        sp.Inuser AS inUser,
        sp.Stats AS status,
        sp.costPrice AS costPrice,
        sp.feiyong AS maintainPrice,
        a.area AS toArea,
        a2.area AS area,
        sp.fid
        FROM
        smallpro sp with(nolock)
        LEFT JOIN areainfo a with(nolock) ON isnull( sp.toareaid, sp.areaid ) = a.id
        LEFT JOIN areainfo a2 WITH(nolock) ON sp.areaid=a2.id
        LEFT JOIN productinfo p with(nolock) on sp.changePpriceid=p.ppriceid
        WHERE
        1 = 1
        <include refid="queryConditionSql">
            <property name="query" value="#{query}"/>
        </include>
        <if test="query.brandIds != null and query.brandIds.size() > 0">
            and p.brandid in
            <foreach collection="query.brandIds" index="index" item="it" separator="," open="(" close=")">
                #{it}
            </foreach>
        </if>
        <if test="query.categoryCharSeq != null and query.categoryCharSeq != '' ">
            and p.cid in (select id from f_category_children (#{query.categoryCharSeq}))
        </if>
        order by id desc
        OFFSET #{startRow} ROWS FETCH NEXT #{pageSize} ROWS ONLY
    </select>

    <select id="getSmallproOrderInfo"
            resultType="com.jiuji.oa.afterservice.smallpro.bo.smallproInfo.SmallproInfoOrderBO">
        SELECT
        bsk.basket_id AS basketId,
        bsk.price as price,
        bsk.basket_date as basketDate,
        <!--(赠品全部用price其余用price2) + giftPrice 赠品四位小数 四舍五入进行计算 与price2的计算逻辑一致-->
        isnull(case bsk.type when 1 then bsk.price else isnull(bsk.price_shouhou, bsk.price2) end,0) + round(isnull(bsk.giftPrice,0),2) priceReturn,
        p.memberprice AS memberPrice,
        s.areaid AS areaId
        FROM dbo.sub s with(nolock)
        inner join dbo.basket as bsk with(nolock) on s.sub_id = bsk.sub_id and isnull(bsk.isdel, 0) = 0
        inner join dbo.productinfo as pro with(nolock) on pro.ppriceid = bsk.ppriceid
        LEFT JOIN productinfo p with(nolock) ON bsk.ppriceid= p.ppriceid
        WHERE s.sub_check &lt;&gt; 4
        and basket_id in
        <foreach collection="basketIdList" index="index" item="basketId" separator="," open="(" close=")">
            #{basketId}
        </foreach>
    </select>

    <select id="getSmallproShopInfo"
            resultType="com.jiuji.oa.afterservice.smallpro.bo.smallproInfo.SmallproInfoProductBO">
        SELECT
        ppriceId,
        ppriceid1 as targetPpriceId,
        barCode,
        product_name as productName,
        product_color as productColor,
        productid as productId,
        cid,
        isSn
        FROM
        productinfo with(nolock)
        WHERE
        ppriceid in
        <foreach collection="ppriceIdList" index="index" item="ppriceId" separator="," open="(" close=")">
            #{ppriceId}
        </foreach>
    </select>

    <select id="getSmallproUserInfo" resultType="com.jiuji.oa.afterservice.smallpro.bo.smallproInfo.SmallproInfoUserBO">
        SELECT id userId,mobile,
        UserName,
        userclass
        FROM
        view_BBSXP_Users with(nolock)
        WHERE
        id = #{userId}
    </select>

    <select id="getSmallproInUserInfo"
            resultType="com.jiuji.oa.afterservice.smallpro.bo.smallproInfo.SmallproInfoInuserInfoBO">
        SELECT
        ch999_id AS inUserId,
        iszaizhi as isZaiZhi,
        mobile,
        ch999_name as inUserName
        FROM
        ch999_user with(nolock)
        WHERE
        ch999_name in
        <foreach collection="inUserNameList" index="index" item="inUserName" separator="," open="(" close=")">
            #{inUserName}
        </foreach>
    </select>

    <select id="getSmallproInUserInfoByWrite"
            resultType="com.jiuji.oa.afterservice.smallpro.bo.smallproInfo.SmallproInfoInuserInfoBO">
        SELECT
        ch999_id AS inUserId,
        iszaizhi as isZaiZhi,
        mobile,
        ch999_name as inUserName
        FROM
        ch999_user with(nolock)
        WHERE
        ch999_name in
        <foreach collection="inUserNameList" index="index" item="inUserName" separator="," open="(" close=")">
            #{inUserName}
        </foreach>
    </select>

    <!--and (s.areaid in
                <foreach collection="query.areaIds" index="index" item="areaId" separator="," open="(" close=")">
                    #{areaId}
                </foreach>
                or s.toareaid in
                <foreach collection="query.areaIds" index="index" item="areaId" separator="," open="(" close=")">
                    #{areaId}
                </foreach>
                )-->

    <sql id="querySmallproOldConditionSql">
        <if test="query != null">
            <!-- 是否大件换货 -->
            <if test="query.mobileExchangeFlag != null and query.mobileExchangeFlag == 0">
                AND EXISTS(SELECT 1 FROM dbo.SmallproBill sb WITH(NOLOCK) WHERE sb.smallproID= s.id and ISNULL(mobile_exchange_flag,0) = 0)
            </if>
            <if test="query.mobileExchangeFlag != null and query.mobileExchangeFlag == 1">
                AND EXISTS(SELECT 1 FROM dbo.SmallproBill sb WITH(NOLOCK) WHERE sb.smallproID= s.id and ISNULL(mobile_exchange_flag,0) = 1)
            </if>
            <if test="query.startExcelTime != null and query.startExcelTime != ''">
                and sf.dtime <![CDATA[ > ]]> #{query.startExcelTime}
            </if>
            <if test="query.kind != null and query.kind>0">
                and s.kind = #{query.kind}
            </if>
<!--            查询异常信息条件-->
            <if test="query.abnormalType != null and query.abnormalType !=0">
                and s.abnormal_type = #{query.abnormalType}
            </if>
            <choose>
                <when test="query.status != null and query.status == 0">
                    and isnull(sf.rstats,0) in(0,1)
                </when>
                <otherwise>
                    <if test="query.status2 != null and query.status2 == 0">
                        and isnull(sf.rstats,0) in(2,3)
                    </if>
                    <if test="query.status2 != null and query.status2 == 1">
                        and isnull(sf.rstats,0) = 2
                    </if>
                    <if test="query.status2 != null and query.status2 == 2">
                        and isnull(sf.rstats,0) = 3
                    </if>
                </otherwise>
            </choose>
            <if test="query.key != null and query.key != '' and query.keyKind != null and query.keyKind != ''">
                <choose>
                    <when test="query.keyKind == 'ppid'">
                        and p.ppriceid=#{query.key}
                    </when>
                    <when test="query.keyKind == 'productName'">
                        and p.product_name like CONCAT('%',#{query.key},'%')
                    </when>
                    <when test="query.keyKind=='smallproId'">
                        and sf.smallproid = #{query.key}
                    </when>
                    <when test="query.keyKind == 'channelName'">
                        AND EXISTS(SELECT 1 FROM shouhou_fanchang with(nolock) WHERE smallproid=s.id AND
                        qudao=#{query.key})
                    </when>
                    <when test="query.keyKind == 'barCode'">
                        and p.barCode like CONCAT('%',#{query.key},'%')
                    </when>
                    <when test="query.keyKind == 'id'">
                        and s.id = #{query.key}
                    </when>
                    <when test="query.keyKind == 'mobile'">
                        and s.Mobile = #{query.key}
                    </when>
                    <when test="query.keyKind == 'subId'">
                        and s.sub_id = #{query.key}
                    </when>
                    <when test="query.keyKind == 'inUser'">
                        and s.Inuser = #{query.key}
                    </when>
                    <when test="query.keyKind == 'userName'">
                        and s.Username = #{query.key}
                    </when>
                    <when test="query.keyKind == 'wxUser'">
                        and s.wxuser = #{query.key}
                    </when>
                </choose>
            </if>
            <if test="query.startTime != null and query.startTime != '' and query.endTime != null and query.endTime != ''">
                <choose>
                    <when test="query.timeType == 1">
                        and sf.dtime between #{query.startTime} and #{query.endTime}
                    </when>
                    <when test="query.timeType == 2">
                        and sf.zxdtime between #{query.startTime} and #{query.endTime}
                    </when>
                </choose>
            </if>
            <if test="(query.toAreaIds != null and query.toAreaIds.size>0) and (query.areaIds !=null and query.areaIds.size>0)">
                AND (
                (( s.istoarea= 1 AND s.toareaid IN
                <foreach collection="query.toAreaIds" index="index" item="areaId" separator="," open="(" close=")">
                    #{areaId}
                </foreach>
                )
                OR ( s.istoarea= 0 AND ISNULL( s.toareaid, s.areaid ) IN
                <foreach collection="query.toAreaIds" index="index" item="areaId" separator="," open="(" close=")">
                    #{areaId}
                </foreach>
                ))
                and s.areaid IN
                <foreach collection="query.areaIds" index="index" item="areaId" separator="," open="(" close=")">
                    #{areaId}
                </foreach>
                )
            </if>
            <if test="((query.toAreaIds != null and query.toAreaIds.size > 0) and (query.areaIds ==null or query.areaIds.size &lt;= 0)) or ((query.areaIds !=null and query.areaIds.size > 0) and (query.toAreaIds == null or query.toAreaIds.size &lt;= 0))">
                <if test="query.toAreaIds != null and query.toAreaIds.size>0">
                    AND (
                    ( s.istoarea= 1 AND s.toareaid IN
                    <foreach collection="query.toAreaIds" index="index" item="areaId" separator="," open="(" close=")">
                        #{areaId}
                    </foreach>
                    )
                    OR ( s.istoarea= 0 AND ISNULL( s.toareaid, s.areaid ) IN
                    <foreach collection="query.toAreaIds" index="index" item="areaId" separator="," open="(" close=")">
                        #{areaId}
                    </foreach>
                    )
                    )
                </if>
                <if test="query.areaIds !=null and query.areaIds.size>0">
                    AND s.areaid IN
                    <foreach collection="query.areaIds" index="index" item="areaId" separator="," open="(" close=")">
                        #{areaId}
                    </foreach>
                </if>
            </if>
            <if test="query.productTypes != null and query.productTypes.size>0">
                and p.cid in
                <foreach collection="query.productTypes" index="index" item="pType" separator="," open="(" close=")">
                    #{pType}
                </foreach>
            </if>
            <if test="query.brands != null and query.brands.size>0">
                and p.brandID in
                <foreach collection="query.brands" index="index" item="brand" separator="," open="(" close=")">
                    #{brand}
                </foreach>
            </if>
            <if test="query.toAreaStatus != null and query.toAreaStatus != 0">
                <if test="query.toAreaStatus == 1 ">
                    and w.stats in (1,2,3)
                </if>
                <if test="query.toAreaStatus == 2 ">
                    and s.istoarea=0
                    and w.stats in (4,6)
                    <foreach collection="query.toAreaIds" index="index" item="areaId" separator="," open="and s.toareaid in(" close=")">
                        #{areaId}
                    </foreach>
                </if>
                <if test="query.toAreaStatus == 3 ">
                    and s.wuliuid is null
                </if>
            </if>

            <if test="query.toAreaStartTime != null and query.toAreaStartTime != ''">
                and s.toareatime <![CDATA[ >= ]]> #{query.toAreaStartTime}
            </if>
            <if test="query.toAreaEndTime != null and query.toAreaEndTime != ''">
                and s.toareatime <![CDATA[ <= ]]> #{query.toAreaEndTime}
            </if>

            <if test="query.checkStatus != null and query.checkStatus != 0">
                <if test="query.checkStatus == 1">
                    AND EXISTS(SELECT 1 FROM shouhou_fanchang with(nolock) WHERE smallproid=s.id AND
                    check_status=0)
                </if>
                <if test="query.checkStatus == 2">
                    AND EXISTS(SELECT 1 FROM shouhou_fanchang with(nolock) WHERE smallproid=s.id AND
                    check_status=1)
                </if>
            </if>
        </if>
        <if test="query.authPart != null and query.authPart == true">
            and a.authorizeid = #{query.authorizeId}
        </if>

    </sql>

    <select id="getSmallproOldPartTotalNum" resultType="java.lang.Integer">
        select count(*)
        from shouhou_fanchang sf with(nolock)
        left join productinfo p with(nolock) on sf.ppid = p.ppriceid
        left join smallpro s with(nolock) on sf.smallproid = s.id
        left join wuliu w with(nolock) on s.wuliuid = w.id
        left join areainfo a with(nolock) on isnull(s.toareaid,s.areaid)=a.id
        where 1=1
        and s.kind in (2,3,4)
        and isnull( s.isdel, 0 ) = 0
        <include refid="querySmallproOldConditionSql">
            <property name="query" value="#{query}"/>
        </include>
    </select>

    <!--s.istoarea AS isToArea,-->
    <select id="getSmallproOldPartList" resultType="com.jiuji.oa.afterservice.smallpro.bo.SmallproOldPartBO">
        <include refid="selectDateInfo"/>
        OFFSET #{startRow} ROWS FETCH NEXT #{pageSize} ROWS ONLY
    </select>

    <select id="getSmallproOldPartListAll" resultType="com.jiuji.oa.afterservice.smallpro.bo.SmallproOldPartBO">
        <include refid="selectDateInfo"/>
    </select>
    <select id="getSmallproOldPartAllCost" resultType="java.math.BigDecimal">
        select sum(round(case when sb.inprice is not null then sb.inprice when sf.inprice is not null then sf.inprice else b.inprice end, 2))
        FROM <include refid="selectDateInfo_table_where"></include>
    </select>
    <sql id="selectDateInfo_table_where">
        shouhou_fanchang sf with(nolock)
        left join smallproBill sb with(nolock) on sb.smallproID = sf.smallproid and sb.basket_id = sf.basket_id
        left join basket b with(nolock) on b.basket_id = sf.basket_id
        LEFT JOIN productinfo p with(nolock) ON sf.ppid = p.ppriceid
        LEFT JOIN smallpro s with(nolock) ON sf.smallproid = s.id
        LEFT JOIN wuliu w with(nolock) ON s.wuliuid = w.id
        LEFT JOIN areainfo a with(nolock) ON isnull( s.toareaid, s.areaid ) = a.id
        LEFT JOIN areainfo a2 WITH(nolock) on s.areaid=a2.id
        LEFT JOIN sub with(nolock) on sub.sub_id = s.sub_id
        LEFT JOIN sub ss with(nolock)  on ss.sub_id = s.oldid
        WHERE
        1 =1
        and s.kind in (2,3,4)
        and isnull( s.isdel, 0 ) = 0
        <include refid="querySmallproOldConditionSql">
            <property name="query" value="#{query}"/>
        </include>
    </sql>
    <sql id = "selectDateInfo">
        SELECT sf.id,
        a.area as toArea,
        a2.area,
        CASE
        WHEN s.istoarea= 1 THEN
        s.toareaid ELSE ISNULL( s.toareaid, s.areaid )
        END AS toAreaId,
        s.areaid AS areaId,
        0 AS isToArea,
        s.Inuser AS inUser,
        sf.smallproid AS smallproId,
        sf.ppid,
        sf.dtime AS submitTime,
        sf.zxdtime AS processTime,
        sf.rstats AS status,
        sf.inprice AS inPrice,
        sf.basket_id AS basketId,
        p.product_name AS productName,
        p.product_color AS productColor,
        p.barCode,
        s.fid,
        (case when s.Kind=4 THEN ss.sub_check else sub.sub_check end ) sub_check,
        (case when s.Kind=4 THEN ss.sub_id else sub.sub_id end ) sub_id,
        s.Problem as problem,
        s.Kind as kind,
        round(case when sb.inprice is not null then sb.inprice when sf.inprice is not null then sf.inprice else b.inprice end, 2) cost
        FROM <include refid="selectDateInfo_table_where"></include>
        order by sf.id desc
    </sql>

    <select id="getAssessCostAndAmount" resultType="com.jiuji.oa.afterservice.smallpro.vo.res.SmallproOldPartRes">
        SELECT SUM(round(bi.inprice,2)) AS assessCost,SUM(p.memberprice) AS assessAmount
        from shouhou_fanchang sf with(nolock)
        left join productinfo p with(nolock) on sf.ppid = p.ppriceid
        left join smallpro s with(nolock) on sf.smallproid = s.id
        left join areainfo a with(nolock) on s.areaid=a.id
        left join (
        SELECT * FROM (
        select *,row_number() over(partition by smallproID order by id desc) rn from SmallproBill with(nolock)
        ) aa
        ) bi on (bi.smallproID = s.id)
        left join wuliu w with(nolock) on s.wuliuid = w.id
        where 1=1
        <include refid="querySmallproOldConditionSql">
            <property name="query" value="#{query}"/>
        </include>
    </select>

    <select id="getSmallproAreaName" resultType="com.jiuji.oa.afterservice.smallpro.bo.smallproInfo.SmallproInfoAreaBO">
        SELECT
        id as areaId,
        area
        FROM
        view_areainfo with(nolock)
        WHERE
        id IN
        <foreach collection="areaIdList" item="areaId" index="index" open="(" close=")" separator=",">
            #{areaId}
        </foreach>
    </select>

    <select id="getSmallproFileInfo" resultType="com.jiuji.oa.afterservice.smallpro.bo.smallproInfo.SmallproInfoFileBO">
        SELECT
        id,
        filename as fileName,
        fid,
        Extension as extension
        FROM
        attachments with(nolock)
        WHERE
        linkedID = #{smallproId}
        AND type = 38
    </select>

    <select id="getSubServiceRecord"
            resultType="com.jiuji.oa.afterservice.smallpro.bo.smallproInfo.SmallproInfoServiceRecordBO">
        SELECT top 1
        tradedate AS tradeDate,
        ServiceType AS serviceType,
        startTime startTime,
        endTime endTime,
        isnull( feiyong, 0 ) AS cost,
        isnull( s.isdel, 0 ) AS del
        FROM
        dbo.ServiceRecord s WITH(nolock)
        WHERE s.basket_idBind=#{basketId}
    </select>


    <select id="getSubServiceRecordByServiceProduct"
            resultType="com.jiuji.oa.afterservice.smallpro.bo.smallproInfo.SmallproInfoServiceRecordBO">
        SELECT
        tradedate AS tradeDate,
        ServiceType AS serviceType,
        isnull( feiyong, 0 ) AS cost,
        isnull( s.isdel, 0 ) AS del
        FROM
        dbo.ServiceRecord s WITH(nolock)
        WHERE s.basket_id=#{basketId}
        AND servicesTypeBindId is null
    </select>

    <select id="getProductServiceTime"
            resultType="com.jiuji.oa.afterservice.smallpro.bo.smallproReceivable.SmallproProductServiceTimeBO">
        SELECT
        productid as productId,
        csname as serviceName,
        csvalue as serviceDate1
        FROM
        VIEW_product_param with(nolock)
        WHERE
        productid IN
        <foreach collection="productIdList" item="productId" index="index" open="(" close=")" separator=",">
            #{productId}
        </foreach>
        AND ( csname = '包换时间' OR csname = '保修时间' )
        AND isdel =0
    </select>

    <select id="getSmallproReceivableProduct"
            resultType="com.jiuji.oa.afterservice.smallpro.bo.smallproReceivable.SmallproReceivableProductBO">
        SELECT
        0 AS basketId,
        k.ppriceid AS ppriceId,
        76783 AS userId,
        GETDATE( ) AS tradeDate,
        '现货' AS userName,
        '00000000000' AS mobile,
        '现货' AS realName,
        0 AS isMobile,
        p.productid AS productId,
        p.ppriceid1 as targetPpriceId,
        p.product_name AS productName,
        p.product_color AS productColor,
        k.lcount AS basketCount,
        k.inprice AS price,
        p.cid,
        p.barCode,
        p.isSn
        FROM
        product_kc k with(nolock)
        LEFT JOIN productinfo p with(nolock) ON k.ppriceid= p.ppriceid
        WHERE
        k.areaid = #{areaId}
        AND
        <if test="type==2">
            k.ppriceid IN
            <foreach collection="keyList" item="key" index="index" open="(" close=")" separator=",">
                #{key}
            </foreach>
        </if>
        <if test="type==3">
            right(p.barCode,6) IN
            <foreach collection="keyList" item="key" index="index" open="(" close=")" separator=",">
                #{key}
            </foreach>
        </if>
    </select>

    <select id="getSmallproReceivableProductBySubId"
            resultType="com.jiuji.oa.afterservice.smallpro.bo.smallproReceivable.SmallproReceivableProductBO">
        SELECT
        basket_id as basketId,
        b.ppriceid as ppriceId,
        s.userid as userId,
        s.tradedate1 as tradeDate,
        u.username as userName,
        ISNULL(NULLIF(LTRIM(RTRIM(u.mobile)), ''), s.sub_mobile) AS mobile,
        u.realname as realName,
        b.ismobile as isMobile,
        p.productid as productId,
        ppriceid1 as targetPpriceId,
        p.product_name as productName,
        p.product_color as productColor,
        basket_count as basketCount,
        p.memberprice as memberprice,
        b.price as price,
        b.type as basketType,
        tc.id as tieMoId,
        tc.useCount as useCount,
        p.cid,
        p.barCode,
        p.isSn
        FROM
        basket b with(nolock)
        INNER JOIN sub s with(nolock) ON b.sub_id= s.sub_id
        LEFT JOIN productinfo p with(nolock) ON b.ppriceid= p.ppriceid
        LEFT JOIN BBSXP_Users u with(nolock) ON u.id= s.userid
        LEFT JOIN dbo.tiemoCard tc with(nolock) ON tc.basketid = b.basket_id
        LEFT JOIN areainfo a with(nolock) on s.areaid = a.id
        WHERE
        s.sub_id= #{subId}
        AND ismobile = 0
        AND s.sub_check= 3
        AND isnull( b.isdel, 0 ) = 0
        AND b.ischu= 1
        <if test="authPart != null and authPart == true">
            and a.authorizeid = #{authorizeId}
        </if>
        <if test="basketId != null and basketId != 0">
            and b.basket_id = #{basketId}
        </if>
        AND NOT EXISTS (
        SELECT
        smallpro.STATS,
        smallpro.id
        FROM
        smallprobill with(nolock)
        LEFT JOIN smallpro with(nolock) ON smallprobill.smallproid = smallpro.id
        WHERE
        smallpro.stats in (0,3,5,6)
        AND smallpro.kind in (1,2,3)
        AND isnull(smallpro.isDel,0) = 0
        AND smallpro.sub_id = s.sub_id
        )
        <if test="isJiujiXtenant != null and isJiujiXtenant == false">
        AND (
        tc.id IS NULL
        OR (
        ( tc.dtime &gt;= #{frameTime} AND isnull( tc.useCount, 0 ) = 0 )
        OR ( tc.dtime &lt; #{frameTime} AND isnull( tc.useCount, 0 ) &lt;= 2 )
        )
        )
        </if>
    </select>


    <select id="getSmallproReceivableProductBySubIdV3"
            resultType="com.jiuji.oa.afterservice.smallpro.bo.smallproReceivable.SmallproReceivableProductBO">
        SELECT
        basket_id as basketId,
        b.ppriceid as ppriceId,
        s.userid as userId,
        s.tradedate1 as tradeDate,
        u.username as userName,
        u.mobile as mobile,
        u.realname as realName,
        b.ismobile as isMobile,
        p.productid as productId,
        ppriceid1 as targetPpriceId,
        p.product_name as productName,
        p.product_color as productColor,
        basket_count as basketCount,
        p.memberprice as memberprice,
        b.price as price,
        b.type as basketType,
        tc.id as tieMoId,
        p.cid,
        p.barCode,
        p.isSn
        FROM
        basket b with(nolock)
        INNER JOIN sub s with(nolock) ON b.sub_id= s.sub_id
        LEFT JOIN productinfo p with(nolock) ON b.ppriceid= p.ppriceid
        LEFT JOIN BBSXP_Users u with(nolock) ON u.id= s.userid
        LEFT JOIN dbo.tiemoCard tc with(nolock) ON tc.basketid = b.basket_id
        LEFT JOIN areainfo a with(nolock) on s.areaid = a.id
        WHERE
        s.sub_id= #{subId}
        AND ismobile = 0
        AND s.sub_check= 3
        AND isnull( b.isdel, 0 ) = 0
        AND b.ischu= 1
        <if test="authPart != null and authPart == true">
            and a.authorizeid = #{authorizeId}
        </if>
        <if test="isJiujiXtenant != null and  isJiujiXtenant == false">
        AND (
        tc.id IS NULL
        OR (
        ( tc.dtime &gt;= #{frameTime} AND isnull( tc.useCount, 0 ) = 0 )
        OR ( tc.dtime &lt; #{frameTime} AND isnull( tc.useCount, 0 ) &lt;= 2 )
        )
        )
        </if>
    </select>

    <select id="getMobileReceivableProductBySubIdV2"
            resultType="com.jiuji.oa.afterservice.smallpro.bo.smallproReceivable.SmallproReceivableProductBO">
        SELECT
        basket_id as basketId,
        b.ppriceid as ppriceId,
        s.userid as userId,
        s.tradedate1 as tradeDate,
        u.username as userName,
        u.mobile as mobile,
        u.realname as realName,
        b.ismobile as isMobile,
        p.productid as productId,
        ppriceid1 as targetPpriceId,
        p.product_name as productName,
        p.product_color as productColor,
        basket_count as basketCount,
        p.memberprice as memberprice,
        b.price as price,
        p.cid,
        p.barCode,
        p.isSn
        FROM
        basket b with(nolock)
        INNER JOIN sub s with(nolock) ON b.sub_id= s.sub_id
        LEFT JOIN productinfo p with(nolock) ON b.ppriceid= p.ppriceid
        LEFT JOIN BBSXP_Users u with(nolock) ON u.id= s.userid
        LEFT JOIN dbo.tiemoCard tc with(nolock) ON tc.basketid = b.basket_id
        LEFT JOIN areainfo a with(nolock) on s.areaid = a.id
        WHERE
        s.sub_id= #{subId}
        AND ismobile = 1
        AND s.sub_check= 3
        AND isnull( b.isdel, 0 ) = 0
        AND b.ischu= 1
        <if test="authPart != null and authPart == true">
            and a.authorizeid = #{authorizeId}
        </if>
        AND NOT EXISTS (
        SELECT
        smallpro.STATS,
        smallpro.id
        FROM
        smallprobill with(nolock)
        LEFT JOIN smallpro with(nolock) ON smallprobill.smallproid = smallpro.id
        WHERE
        smallpro.stats in (0,3,5,6)
        AND smallpro.kind in (1,2,3)
        AND isnull(smallpro.isDel,0) = 0
        AND smallpro.sub_id = s.sub_id
        )
        <if test="isJiujiXtenant != null and  isJiujiXtenant == false">
        AND (
        tc.id IS NULL
        OR (
        ( tc.dtime &gt;= #{frameTime} AND isnull( tc.useCount, 0 ) = 0 )
        OR ( tc.dtime &lt; #{frameTime} AND isnull( tc.useCount, 0 ) &lt;= 2 )
        )
        )
        </if>
    </select>

    <select id="getSmallproPickUpInfo"
            resultType="com.jiuji.oa.afterservice.smallpro.bo.smallproExchange.SmallproPickUpInfoBO">
        SELECT
        smallprobill.*,
        productinfo.ppriceid1,
        smallpro.sub_id AS subId
        FROM
        smallprobill with(nolock)
        LEFT JOIN smallpro with(nolock) ON smallpro.id = smallprobill.smallproid
        LEFT JOIN productinfo with(nolock) ON smallprobill.ppriceid = productinfo.ppriceid
        WHERE
        ISNULL( stats, 0 ) = 0
        AND smallproid = #{smallproId}
    </select>

    <select id="getSmallproExchangeStockInfo"
            resultType="com.jiuji.oa.afterservice.smallpro.bo.smallproExchange.SmallproExchangeStockInfoBO">
        SELECT
        b.id as smallproBillId,
        b.smallproID as smallproId,
        b.basket_id as basketId,
        isnull( s.changePpriceid, b.ppriceid ) as ppriceId,
        b.count as count,
        s.areaid as areaId,
        p.ppriceid1 as ppriceId1
        FROM
        smallprobill b with(nolock)
        LEFT JOIN smallpro s with(nolock) ON s.id = b.smallproid
        LEFT JOIN productinfo p with(nolock) ON isnull( s.changePpriceid, b.ppriceid ) = p.ppriceid
        WHERE
        ISNULL( stats, 0 ) = 0
        AND b.smallproid= #{smallproId}
    </select>

    <select id="getSmallproPickupOrderInfo"
            resultType="com.jiuji.oa.afterservice.smallpro.bo.smallproExchange.SmallproPickupOrderInfoBO">
        SELECT
        sb.id as smallproBillId,
        sb.smallproID as smallproId,
        sb.basket_id as basketId,
        sb.ppriceid as ppriceId,
        s.changePpriceid changePpriceid,
        sb.count as count,
        p.ppriceid1 as ppriceId1,
        s.sub_id as subId,
        k.inprice
        FROM
        smallprobill sb with(nolock)
        LEFT JOIN smallpro s with(nolock) ON s.id = sb.smallproid
        LEFT JOIN productinfo p with(nolock) ON sb.ppriceid = p.ppriceid
        LEFT JOIN product_kc k with(nolock) on k.ppriceid = p.ppriceid1 and k.areaid = s.areaid
        WHERE
        ISNULL( stats, 0 ) = 0
        AND smallproid = #{smallproId}
        order by sb.id desc
    </select>

    <select id="getMsgCodeSendSubInfo"
            resultType="com.jiuji.oa.afterservice.smallpro.bo.smallproSendMsgCode.SmallproSendMsgCodeSubInfoBO">
        SELECT
        tradeDate1 as tradeDate,
        sub_mobile as subMobile
        FROM
        sub WITH(nolock)
        WHERE
        sub_id =#{subId}
    </select>

    <select id="getMsgCodeSendAreaInfo"
            resultType="com.jiuji.oa.afterservice.smallpro.bo.smallproSendMsgCode.SmallproSendMsgCodeAreaInfoBO">
        SELECT
        area_name as areaName,
        printName,
        isSend,
        xtenant as xTenant
        FROM
        areainfo with(nolock)
        WHERE
        id = #{areaId}
    </select>

    <select id="getMsgCodeSaveInfo"
            resultType="com.jiuji.oa.afterservice.smallpro.bo.smallproSendMsgCode.SmallproMsgCodeSaveInfo">
        SELECT top 1
        s.areaid as areaId,
        w.openid as openId,
        s.kind,
        s.userid as userId,
        s.name,
        ss.sub_mobile as subMobile,
        w.wxid wechatId
        FROM
        smallpro s WITH(nolock)
        LEFT JOIN WeixinUser w with(nolock) ON w.userid = s.userid
        AND w.follow= 1 and w.type = 1 and w.kinds = 1
        LEFT JOIN sub ss with(nolock) ON s.sub_id = ss.sub_id
        WHERE
        s.id = #{smallproId}
        order by w.dtime desc
    </select>

    <select id="getOutboundInfo"
            resultType="com.jiuji.oa.afterservice.smallpro.bo.smallproExchange.SmallproPickUpOutboundInfoBO">
        SELECT
        b.id as smallproBillId,
        b.smallproID as smallproId,
        b.basket_id as basketId,
        iif(isnull(s.changePpriceid,0)=0, b.ppriceid,s.changePpriceid ) ppriceId,
        b.count,
        s.areaid as areaId,
        p.ppriceid1 as ppriceId1
        FROM
        smallprobill b with(nolock)
        LEFT JOIN smallpro s with(nolock) ON s.id = b.smallproid
        LEFT JOIN productinfo p with(nolock) ON iif(isnull(s.changePpriceid,0)=0, b.ppriceid,s.changePpriceid ) = p.ppriceid
        WHERE
        ISNULL( stats, 0 ) = 0
        AND b.smallproid= #{smallproId}
    </select>

    <select id="getJiujiServiceInPriceInfo"
            resultType="com.jiuji.oa.afterservice.smallpro.bo.smallproExchange.SmallproJiujiServiceInpriceInfoBO">
        SELECT
        k.inprice as inPrice,
        bsk.price,
        b.basket_id basketId
        FROM
        smallprobill b with(nolock)
        JOIN smallpro s with(nolock) ON s.id = b.smallproid
        JOIN productinfo p with(nolock) ON isnull( s.changePpriceid, b.ppriceid ) = p.ppriceid
        JOIN dbo.product_kc k with(nolock) ON ( k.areaid= s.areaid AND k.ppriceid= p.ppriceid1 )
        LEFT JOIN dbo.basket bsk with(nolock) ON bsk.basket_id= b.basket_id
        WHERE
        b.smallproid=#{smallproId}
    </select>

    <update id="updateJiujiService">
        UPDATE ServiceRecord
        SET feiyong = #{costPrice}
        WHERE
        isnull( isdel, 0 ) = 0
        AND isnull( feiyong, 0 ) = 0
        AND basket_idBind = #{basketId}
        <if test="serviceType ==1 ">
            AND ServiceType in
            <foreach collection="serviceTypeList" index="index" item="serviceType" separator="," open="(" close=")">
                #{serviceType}
            </foreach>
        </if>
        <if test="serviceType==2 || serviceType==3">
            AND ServiceType = #{serviceTypeResult}
        </if>
    </update>

    <select id="getSendReviewInfo"
            resultType="com.jiuji.oa.afterservice.smallpro.bo.smallproExchange.SmallproSendReviewInfoBO">
        SELECT
        p.cid as cId,
        c.Path as path,
        s.Inuser as inUser,
        s.Mobile as mobile,
        cu.ch999_id as inUserCh999Id,
        isnull( s.toareaid, s.areaid ) areaId,
        s.userid as userId,
        s.id as num,
        dbo.[getUserNickName] ( s.userid ) nickName
        FROM
        smallprobill b with(nolock)
        JOIN smallpro s with(nolock) ON s.id = b.smallproid
        JOIN productinfo p with(nolock) ON isnull( s.changePpriceid, b.ppriceid ) = p.ppriceid
        LEFT JOIN category c with(nolock) ON c.ID=p.cid
        LEFT JOIN ch999_user cu with(nolock) on cu.ch999_name=s.Inuser
        WHERE
        b.smallproid=#{smallproId}
        AND [Stats] = 1
    </select>

    <select id="getInUserInfoByMobile"
            resultType="com.jiuji.oa.afterservice.smallpro.bo.smallproInfo.SmallproInfoInuserInfoBO">
        SELECT top 1
        ch999_id AS inUserId,
        iszaizhi AS isZaiZhi,
        mobile,
        ch999_name AS inUserName
        FROM
        dbo.ch999_user with(nolock)
        WHERE
        iszaizhi = 1
        AND mobile = #{mobile}
        order by isnull(islogin,0) asc
    </select>

    <select id="getUserWechatInfo"
            resultType="com.jiuji.oa.afterservice.smallpro.bo.smallproExchange.SmallproSendReviewUserWechatInfoBO">
        SELECT top 1
        openid as openId,
        wxid as wechatId
        FROM
        WeixinUser with(nolock)
        WHERE
        userid =#{userId}
        AND type = 1
        AND kinds = 1
        and follow =1
    </select>

    <select id="getLogisticsReceiverUserInfo"
            resultType="com.jiuji.oa.afterservice.smallpro.bo.SmallproLogisticsReceiverUserInfoBO">
        select
        ch999_name as ch999Name,
        leve1 as level,
        ch999_id as ch999Id
        from ch999_user with(nolock)
        where area1id=#{areaId}
        and zhiwu in ('门店主管','主管', '店长', '副店长', '职员', '专员')
        and (isshixi!=4 or isshixi is null)
        and (islogin!=1 or islogin is null)
        order by leve1 asc
    </select>

    <select id="getRelatedAreaInfoByArea" resultType="com.jiuji.oa.afterservice.smallpro.bo.SmallproRelatedAreaInfoBO">
        SELECT
        id AS areaId,
        dcArea,
        dcAreaID AS dcAreaId
        FROM
        areainfo with(nolock)
        WHERE
        id = #{areaId}
    </select>

    <select id="isReturnAlipay" resultType="java.lang.Integer">
        SELECT
        id
        FROM
        netPayRefundInfo with(nolock)
        WHERE
        returnid=#{returnFactoryId}
    </select>

    <select id="getFilmCardInfomation" resultType="com.jiuji.oa.afterservice.smallpro.bo.filmCard.FilmCardInfomationBO">
        SELECT TOP
        1 tc.sub_id AS subId,
        tc.areaid AS areaId,
        tc.basketid AS basketId,
        b.basket_id cardBasketTbId,
        tc.basket_idBind AS basketBindId,
        tc.price tcPrice,
        CASE
        WHEN tc.basketid=#{basketId} then b.price
        WHEN tc.basket_idBind=#{basketId} then tc.price
        END price,
        isnull(b.price_shouhou, b.price2) as canRefundPrice,
        tc.useCount ,
        tc.allCount,
        tc.dtime AS buyTime,
        tc.etime AS endTime,
        a.area AS areaCode,
        a.area_name AS areaName,
        tcl.dtime AS lastUseTime,
        tcl.inuser AS lastInuser,
        isnull(s.tradeDate1,tc.dtime) AS tradeDate,
        <!--新增字段startTime-->
        tc.stime startTime,
        <!--新增字段lastCount 年包过期后的使用次数-->
        tc.last_count lastCount,
        tc.type_ type,
        b.ppriceid
        FROM
        tiemoCard tc with(nolock)
        LEFT JOIN areainfo a with(nolock) ON tc.areaid = a.id
        LEFT JOIN tiemoCardUserLog tcl with(nolock) ON tc.id= tcl.cardId
        LEFT JOIN sub s with(nolock) ON s.sub_id= tc.sub_id
        LEFT JOIN basket b with(nolock) ON tc.basketid= b.basket_id
        WHERE (basket_idBind = #{basketId} OR basketid = #{basketId})
          <if test="isNotDel">
              and isnull(tc.isdel,0)=0
          </if>

        ORDER BY
                 <!--优先取有使用次数的-->
                 case when isnull(tc.allCount,0) - isnull(tc.useCount,0) >0 then 0 else 1 end asc,
                 <!--优先取没有超时使用的-->
                 isnull(tc.last_count,0) asc,
                 tc.id desc,lastUseTime DESC
    </select>

    <select id="getSmallproStockingInfoBo"
            resultType="com.jiuji.oa.afterservice.smallpro.bo.smallproExchange.SmallproStockingInfoBO">
        SELECT
        basket_id as basketId
        FROM
        basket b WITH(nolock)
        LEFT JOIN sub s WITH(nolock) ON s.sub_id = b.sub_id
        WHERE
        s.sub_check = 1
        AND b.sub_id = #{hhSubId}
    </select>

    <select id="getScrapSmallproInfo"
            resultType="com.jiuji.oa.afterservice.smallpro.bo.smallproExchange.SmallproScrapInfoBO">
        SELECT
        s.id AS smallproId,
        s.sub_id AS subId,
        s.wuliuid AS wuliuId,
        b.inprice AS inPrice,
        ISNULL( s.areaid, s.toareaid ) areaId,
        s.istoarea AS isToArea,
        k.inprice AS kcInprice,
        s.userid AS userId ,
        a.kind1 ,
        s.toareaid as toAreaId,
        a.area as areaCode,
        s.ServiceType serviceType
        FROM
        dbo.shouhou_fanchang sf with(nolock)
        LEFT JOIN dbo.basket b with(nolock) ON b.basket_id = sf.basket_id
        LEFT JOIN dbo.Smallpro s with(nolock) ON s.id = sf.smallproid
        LEFT JOIN dbo.product_kc k with(nolock) ON ( k.ppriceid = sf.ppid AND k.areaid = ISNULL( s.areaid, s.toareaid
        ) )
        LEFT JOIN dbo.areainfo a with(nolock) ON a.id = ISNULL( s.areaid, s.toareaid )
        WHERE
        sf.id = #{returnFactoryId}
        AND sf.rstats IN ( 0, 1 )
    </select>

    <select id="getIncomingSmallproInfo"
            resultType="com.jiuji.oa.afterservice.smallpro.bo.smallproExchange.SmallproIncomingInfoBO">
        SELECT
        sf.id as shouhouFanchangId,
        sf.smallproid as smallproId,
        sf.basket_id as basketId,
        sf.ppid,
        sf.ppid1,
        dbo.getPjType ( p.cid ) pjType,
        s.areaid as areaId,
        s.istoarea as isToArea,
        s.toareaid as toAreaId,
        s.wuliuid as wuliuId,
        s.Inuser as inUser,
        case when isnull(bi.inprice,0)&lt;&gt;0 then bi.inprice when b.ismobile = 1 then sf.inprice else isnull(b.inprice,sf.inprice) end tuikuanM
        FROM
        shouhou_fanchang sf with(nolock)
        LEFT JOIN smallpro s with(nolock) ON sf.smallproid = s.id
        left join dbo.SmallproBill bi with(nolock) on bi.id = sf.billId
        LEFT JOIN dbo.basket b with(nolock) ON sf.basket_id = b.basket_id
        LEFT JOIN dbo.productinfo p with(nolock) ON p.ppriceid = sf.ppid1
        WHERE
        sf.zxinuser IS NULL
        AND sf.id = #{returnFactoryId}
    </select>


    <select id="getRefundMoneyInfo"
            resultType="com.jiuji.oa.afterservice.smallpro.bo.smallproExchange.SmallproRefundMoneyInfoBO">
        SELECT
        *
        FROM
        (
        SELECT
        y.sub_pay04 as whiteBarMoney
        FROM
        dbo.shouying y with(nolock),
        dbo.shouyin_other o with(nolock)
        WHERE
        y.id = o.shouyinid
        AND o.type_= 10
        AND y.sub_id= #{subId}
        ) a,
        (
        SELECT SUM
        ( money - isnull( refundPrice, 0 ) ) leftPrice
        FROM
        dbo.netpay_record with(nolock)
        WHERE
        sub_number = #{subId}
        AND type = 1
        AND payWay = '库分期'
        ) b
    </select>

    <select id="getRefundSubInfo"
            resultType="com.jiuji.oa.afterservice.smallpro.bo.smallproExchange.SmallproRefundSubInfoBO">
        SELECT
        *
        FROM
        (
        SELECT
        b.ppriceid as ppriceId,
        basket_id as basketId,
        b.sub_id as subId,
        basket_count - ISNULL( re.count_, 0 ) basketCount,
        CASE
        WHEN b.[type] = 1 THEN
        0 ELSE price2
        END price,
        p.product_name as productName,
        p.product_color as productColor,
        s.areaid as areaId,
        s.shouxuM,
        s.feeM,
        s.yifuM,
        s.coinM
        FROM
        dbo.basket b with(nolock)
        LEFT JOIN dbo.sub s with(nolock) ON b.sub_id= s.sub_id
        LEFT JOIN dbo.productinfo p with(nolock) ON b.ppriceid= p.ppriceid
        LEFT JOIN ( SELECT SUM ( BasketCount ) count_, BasketID FROM dbo.ReturnsDetail with(nolock) GROUP BY BasketID ) re ON
        b.basket_id= re.BasketID
        WHERE
        b.sub_id=#{subId}
        AND b.ismobile= 0
        AND s.sub_check= 3
        AND isnull( b.isdel, 0 ) = 0
        AND b.ischu= 1
        AND isnull( b.isdel, 0 ) = 0
        ) aa
        WHERE
        aa.basketCount>0
    </select>

    <select id="getRefundSubInfoExisting"
            resultType="com.jiuji.oa.afterservice.smallpro.bo.smallproExchange.SmallproRefundSubInfoBO">
        SELECT
        basket_id as basketId,
        b.ppriceid as ppriceId,
        b.sub_id as subId,
        re.BasketCount as basketCount,
        price2 price,
        p.product_name as productName,
        p.product_color as productColor,
        s.areaid as areaId,
        s.shouxuM,
        s.feeM,
        s.yifuM,
        s.coinM
        FROM
        dbo.ReturnsDetail re with(nolock)
        LEFT JOIN dbo.basket b with(nolock) ON re.BasketID = b.basket_id
        LEFT JOIN dbo.sub s with(nolock) ON b.sub_id = s.sub_id
        LEFT JOIN dbo.productinfo p with(nolock) ON b.ppriceid = p.ppriceid
        WHERE
        b.sub_id = #{subId}
        AND isnull( b.isdel, 0 ) = 0
        AND b.ismobile = 0
        AND re.SHTHID =#{shouhouId}
    </select>

    <select id="checkUserPwd2" resultType="java.lang.Integer">
        SELECT COUNT(ch999_id)
        FROM
        ch999_user with(nolock)
        WHERE
        iszaizhi = 1
        AND ch999_id = #{userId}
        AND pwd2 = #{pwd2}
    </select>

    <select id="getRefundAreaInfo"
            resultType="com.jiuji.oa.afterservice.smallpro.bo.smallproExchange.SmallproRefundAreaInfoBO">
        SELECT
        id as areaId,
        kind1 as areaKind1
        FROM
        areainfo with(nolock)
        WHERE
        id=#{areaId}
    </select>

    <select id="checkZitidianType" resultType="java.lang.Integer">
        SELECT COUNT
        ( 1 )
        FROM
        BBSXP_Users u with(nolock)
        LEFT JOIN zitidian z with(nolock) ON u.id= z.userid
        LEFT JOIN sub s with(nolock) ON s.zitidianid= z.id
        WHERE
        z.id> 0
        AND s.delivery= 3
        AND s.sub_id=#{subId}
    </select>

    <select id="refundServiceCheck1" resultType="java.lang.Integer">
        SELECT COUNT
        ( 1 )
        FROM
        dbo.basket b with(nolock)
        WHERE
        basket_id IN
        <foreach collection="basketIdList" index="index" item="basketId" separator="," open="(" close=")">
            #{basketId}
        </foreach>
        AND (
        (
        b.product_peizhi &lt;&gt; ''
        AND EXISTS ( SELECT 1 FROM dbo.productinfo p with(nolock) WHERE p.ppriceid= b.ppriceid AND p.cid= 164 )
        )
        OR ppriceid IN ( 21073, 21074, 21075, 31642, 32342, 52447, 52475, 52476 )
        )
    </select>

    <select id="getRefundPrice"
            resultType="com.jiuji.oa.afterservice.smallpro.bo.smallproExchange.SmallproRefundPriceInfoBO">
        SELECT
        isnull( inprice, price ) AS inPrice,
        s.areaid AS areaId,
        s.userid AS userId,b.basket_id basketId
        FROM
        dbo.basket b with(nolock)
        LEFT JOIN dbo.sub s with(nolock) ON s.sub_id = b.sub_id
        WHERE
        s.sub_check IN ( 3 )
        AND b.ischu= 1
        AND isnull( b.isdel, 0 ) = 0
        AND b.sub_id= #{subId}
        AND basket_id IN
        <foreach collection="basketIdList" index="index" item="basketId" separator="," open="(" close=")">
            #{basketId}
        </foreach>
    </select>

    <select id="getNetworkMoneyPayout" resultType="java.lang.Double">
        SELECT SUM
        ( isnull( sub_pay03, 0 ) ) as subPay3
        FROM
        shouying with(nolock)
        WHERE
        sub_id = #{subId}
        AND shouying_type IN ( '订金', '交易' )
        AND isnull( sub_pay03, 0 ) &lt;&gt; 0
    </select>

    <select id="getWechatMoneyPayout" resultType="java.lang.Double">
        SELECT SUM
        ( tuikuanM ) as tuikuanMSum
        FROM
        dbo.shouhou_tuihuan with(nolock)
        WHERE
        tuihuan_kind = 7
        AND tui_way = '微信秒退'
        AND isnull( isdel, 0 ) = 0
        AND sub_id = #{subId}
    </select>

    <select id="getRefundKuBaiTiaoM"
            resultType="com.jiuji.oa.afterservice.smallpro.bo.smallproExchange.SmallproRefundKuBaiTiaoMBO">
        SELECT
        id,
        money - isnull( refundPrice, 0 ) leftPrice
        FROM
        dbo.netpay_record with(nolock)
        WHERE
        sub_number = #{subId}
        AND type = 1
        AND money - isnull( refundPrice, 0 ) > 0
        AND payWay = '库分期'
    </select>

    <select id="getRefundNetPayInfo"
            resultType="com.jiuji.oa.afterservice.smallpro.bo.smallproExchange.SmallproRefundNetPayInfoBO">
        SELECT
        id,
        money,
        isnull(refundPrice,0.0) as refundPrice,
        payWay,
        trade_no AS tradeNo,
        type
        FROM
        dbo.netpay_record with(nolock)
        WHERE
        type = #{type}
        AND id IN
        <foreach collection="idList" index="index" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </select>

    <update id="updateNetPayRecordBySmallproSubmitRefund">
        UPDATE dbo.netpay_record
        SET refundPrice = isnull( refundPrice, 0 ) + #{price}
        WHERE
        id = #{shouhouTuihuanId}
        AND money >= isnull( refundPrice, 0 ) + #{price}
    </update>

    <update id="deleteNetPayRecordByShouhouTuihuanId">
        UPDATE netpay_record
        SET batch_no = NULL
        WHERE len(batch_no)>=14 and RIGHT (batch_no,len( batch_no ) - 14) =#{shouhouTuihuanId}
    </update>

    <update id="updateNetPayRefundInfoWhenCancelRefundCheck">
        UPDATE n
        SET n.refundPrice= n.refundPrice- f.price
        FROM
        netPayRefundInfo f with(nolock)
        JOIN dbo.netpay_record n ON f.netRecordId = n.id
        WHERE
        f.returnid = #{shouhouTuihuanId}
    </update>

    <select id="getSubPid" resultType="java.lang.Integer">
        SELECT
        subPID as subPid
        FROM
        dbo.sub with(nolock)
        WHERE
        sub_id = #{subId}
    </select>

    <select id="getAreaInfoWithReturnWay"
            resultType="com.jiuji.oa.afterservice.smallpro.bo.smallproInfo.SmallproInfoReturnWayAreaInfoBO">
        SELECT
        id AS areaId,
        authorizeid AS authorizeId,
        xtenant AS xTenant
        FROM
        areainfo with(nolock)
        WHERE
        id =#{areaId}
    </select>

    <select id="getSmallproRefundProcessingInfo"
            resultType="com.jiuji.oa.afterservice.smallpro.bo.smallproExchange.SmallproRefundProcessingInfoBO">
        SELECT
        basket_id as basketId,
        re.BasketCount basketCount,
        price,
        p.product_name as productName ,
        p.product_color as productColor,
        b.ismobile as isMobile,
        p.ppriceid as ppriceId,
        p.OEMPrice as oemPrice,
        s.areaid as areaId,
        s.userid as userId,
        p.ppriceid1 as ppriceId1,
        p.cid as cId,
        b.basket_count basketCount1,
        s.sub_id as subId,
        b.inprice as inPrice,
        s.shouxuM as shouxuM,
        s.feeM as feeM,
        s.sub_mobile as subMobile,
        s.coinM as coinM,
        b.type as type,
        dbo.getPjType ( p.cid ) as pjType
        FROM
        dbo.ReturnsDetail re with(nolock)
        LEFT JOIN dbo.basket b with(nolock) ON re.BasketID = b.basket_id
        LEFT JOIN dbo.sub s with(nolock) ON b.sub_id = s.sub_id
        LEFT JOIN dbo.productinfo p with(nolock) ON b.ppriceid = p.ppriceid
        WHERE
        b.sub_id =#{subId}
        AND isnull( b.isdel, 0 ) = 0
        AND b.ismobile = 0
        AND s.sub_check= 3
        AND re.SHTHID =#{shouhouTuihuanId}
    </select>

    <select id="getSmallproRefundProcessingInfoWithNoOne"
            resultType="com.jiuji.oa.afterservice.smallpro.bo.smallproExchange.SmallproRefundProcessingInfoBO">
        SELECT
        basket_id as basketId,
        price as price,
        p.product_name as productName ,
        p.product_color as productColor,
        b.ismobile as isMobile,
        p.ppriceid as ppriceId,
        p.OEMPrice as oemPrice,
        s.areaid as areaId,
        s.userid as userId,
        p.ppriceid1 as ppriceId1,
        p.cid as cId,
        b.basket_count as basketCount1,
        s.sub_id as subId,
        b.inprice as inPrice,
        s.shouxuM as shouxuM,
        s.feeM as feeM,
        s.sub_mobile as subMobile,
        s.coinM as coinM,
        b.type as type,
        dbo.getPjType ( p.cid ) as pjType
        FROM
        dbo.basket b with(nolock)
        LEFT JOIN dbo.sub s with(nolock) ON b.sub_id = s.sub_id
        LEFT JOIN dbo.productinfo p with(nolock) ON b.ppriceid = p.ppriceid
        WHERE
        b.sub_id =#{subId}
        AND isnull( b.isdel, 0 ) = 0
        AND b.ismobile = 0
        AND s.sub_check=3
    </select>

    <select id="getRefundSubCountInfo" resultType="java.lang.Integer">
        SELECT COUNT
        ( 1 )
        FROM
        dbo.basket with(nolock)
        WHERE
        sub_id =#{subId}
        AND isnull( isdel, 0 ) = 0
        AND basket_id NOT IN (
        SELECT
        basket_id
        FROM
        dbo.basket b with(nolock),
        ReturnsDetail r with(nolock)
        WHERE
        b.sub_id=#{subId}
        AND b.basket_id= r.BasketID
        AND b.basket_count= r.BasketCount
        AND r.SHTHID=#{shouhouTuihuanId}
        AND isnull( b.isdel, 0 ) = 0
        )
        <if test="tuiMobile_basketID!=0">
            AND basket_id &lt;&gt; #{tuiMobile_basketID}
        </if>
    </select>

    <select id="getCh999UserIdForJifen" resultType="java.lang.Integer">
        SELECT
        s.ch999userid
        FROM
        dbo.SubAddress s with(nolock)
        WHERE
        s.sub_id = #{subId}
        AND EXISTS (
        SELECT
        1
        FROM
        dbo.basket b with(nolock)
        WHERE
        s.sub_id = b.sub_id
        AND isnull( b.type, 0 ) = 29
        )
    </select>

    <update id="updateTaocanByRefund1">
        UPDATE c
        SET c.flag= 0
        FROM
        taocan t with(nolock),
        taocan_card c with(nolock)
        WHERE
        t.mobi_id= c.id
        AND c.flag IN ( 1, 2 )
        AND t.basket_id=#{basketId}
    </update>

    <update id="updateTaocanByRefund2">
        UPDATE taocan
        SET flag = 3
        WHERE
        flag = 1
        AND basket_id=#{basketId}
    </update>

    <update id="updateTaocanByRefund3">
        UPDATE taocan
        SET flag = 0
        WHERE
        basket_id = #{basketId}
        AND flag IN ( 1, 2 )
    </update>

    <update id="updateHaomaByRefund">
        UPDATE haoma
        SET basket_id = NULL,
        flag = 0
        WHERE
        basket_id=#{basketId}
    </update>

    <update id="updateBasketByRefund1">
        UPDATE basket
        SET sub_id = #{subId}
        WHERE
        basket_id =#{basketId}
    </update>

    <update id="updateShouhouTuihuanByRefund1">
        UPDATE shouhou_tuihuan
        SET sub_id = #{subId}
        WHERE
        check3 IS NULL
        AND id = #{shouhouTuihuanId}
    </update>

    <update id="updateBasketByRefund2">
        UPDATE basket
        SET basket_count = basket_count - #{basketCount}
        WHERE
        basket_id = #{basketId}
    </update>

    <update id="updateReturnsDetailByRefund1">
        UPDATE ReturnsDetail
        SET BasketID = #{newBasketId}
        WHERE
        BasketID = #{oldBasketId}
    </update>

    <update id="deleteBskHalfBuyRecordByRefund1">
        UPDATE dbo.bskHalfBuyRecord
        SET isdel = 1
        WHERE
        basket_id = #{basketId}
    </update>

    <update id="updateBskHalfBuyRecordByRefund2">
        UPDATE dbo.bskHalfBuyRecord
        SET buyCount = buyCount - #{basketCount}
        WHERE
        basket_id= #{basketId}
    </update>

    <update id="deleteServiceRecordByRefund1">
        UPDATE ServiceRecord
        SET isdel = 1
        WHERE
        basket_id IN
        <foreach collection="basketIdList" index="index" item="basketId" separator="," open="(" close=")">
            #{basketId}
        </foreach>
    </update>

    <update id="deleteInstallServicesRecordByRefund1">
        UPDATE dbo.installServicesRecord
        SET stats_ = 2,
        useDate = getdate(),
        userName = #{userName}
        WHERE
        stats_ = 0
        AND basket_id IN
        <foreach collection="basketIdList" index="index" item="basketId" separator="," open="(" close=")">
            #{basketId}
        </foreach>
    </update>

    <update id="deleteTiemoCardByRefund1">
        UPDATE dbo.tiemoCard
        SET isdel = 1
        WHERE
        ( basketid IN
        <foreach collection="basketIdList" index="index" item="basketId" separator="," open="(" close=")">
            #{basketId}
        </foreach>
        OR basket_idBind IN
        <foreach collection="basketIdList" index="index" item="basketId" separator="," open="(" close=")">
            #{basketId}
        </foreach>
        )
    </update>

    <update id="deleteProductSnByRefund1">
        UPDATE dbo.product_sn
        SET basket_id = NULL,
        basketType = NULL
        WHERE
        basket_id IN
        <foreach collection="basketIdList" index="index" item="basketId" separator="," open="(" close=")">
            #{basketId}
        </foreach>
        AND basketType = 1
    </update>

    <select id="getTiemoCardUserLogToDeleteByRefund" resultType="java.lang.Integer">
        SELECT
        cardId
        FROM
        tiemoCardUserLog with(nolock)
        WHERE
        basket_id IN
        <foreach collection="basketIdList" index="index" item="basketId" separator="," open="(" close=")">
            #{basketId}
        </foreach>
    </select>

    <delete id="deleteTiemoCardUserLogByRefund1">
        DELETE
        FROM
        tiemoCardUserLog
        WHERE
        basket_id IN
        <foreach collection="basketIdList" index="index" item="basketId" separator="," open="(" close=")">
            #{basketId}
        </foreach>
    </delete>

    <update id="updateTiemoCardByRefund2">
        UPDATE dbo.tiemoCard
        SET useCount = isnull( useCount, 0 ) - 1
        WHERE
        id IN
        <foreach collection="cardIdList" index="index" item="cardId" separator="," open="(" close=")">
            #{cardId}
        </foreach>
        AND isnull( useCount, 0 ) - 1 >= 0
    </update>

    <select id="getBBSXPUserIdByRefund" resultType="java.lang.Integer">
        SELECT
        u.id
        FROM
        BBSXP_Users u with(nolock)
        LEFT JOIN zitidian z with(nolock) ON u.id= z.userid
        LEFT JOIN sub s with(nolock) ON s.zitidianid= z.id
        WHERE
        z.id> 0
        AND s.delivery= 3
        AND s.sub_id= #{subId}
    </select>

    <select id="getPointsBySubId" resultType="java.lang.Long">
        SELECT
        dbo.getPointsBySubID ( #{subId}, #{price} )
    </select>

    <select id="getAllPriceBySubId" resultType="java.math.BigDecimal">
        SELECT SUM
        ( yingfuM ) AS cost
        FROM
        sub with(nolock)
        WHERE
        ( sub.sub_id = #{subId} OR sub.subPID = #{subId} )
    </select>

    <update id="updateNumberCardByRefund1">
        UPDATE b
        SET b.[State] = 0,
        use_count = use_count - 1,
        b.EndTime= dateadd( DAY, 7, getdate( ) )
        FROM
        dbo.cardLogs a,
        dbo.NumberCard b
        WHERE
        sub_id = #{subId}
        AND a.cardid= b.ID
        AND isnull( b.limit, 0 ) &lt;&gt; 2
        AND b.Isdel= 0
    </update>

    <delete id="deleteNumberCardByRefund1">
        DELETE
        FROM
        NumberCard
        WHERE
        ch999_id IN
        <foreach collection="cardTypeList" index="index" item="cardType" separator="," open="(" close=")">
            #{cardType}
        </foreach>
        EXISTS ( SELECT 1 FROM dbo.cardLogs c with(nolock) WHERE c.cardid= dbo.NumberCard.ID AND c.sub_id= #{subId} )

    </delete>

    <delete id="deleteCardLogsByRefund1">
        DELETE
        FROM
        dbo.cardLogs
        WHERE
        sub_id = #{subId}
    </delete>
    <select id="getBasketIdBySmallproID" resultType="java.lang.String" >
        SELECT sb.basket_id
        from SmallproBill sb with(nolock)
        where sb.smallproID = #{smallproID};
    </select>

    <select id="getYingfuMFromRecoverMarketInfoByRefund" resultType="java.math.BigDecimal">
        SELECT
        yingfuM
        FROM
        dbo.recover_marketInfo with(nolock)
        WHERE
        sub_id = #{subId}
    </select>

    <select id="getPointByRefund1" resultType="java.lang.Long">
        SELECT
        points
        FROM
        dbo.jifen with(nolock)
        WHERE
        sub_number =#{subId}
        AND comment LIKE '%二手良品%'
        AND isinput = 1
        AND price >0
    </select>

    <select id="getBankInstallmentInfoByRefund"
            resultType="com.jiuji.oa.afterservice.smallpro.bo.smallproExchange.SmallproBankInstallmentInfoBO">
        SELECT
        a.otherDsc,
        a.fenQiNum,
        a.midplatform
        FROM
        dbo.alipayInfo a with(nolock)
        JOIN netpay_record n with(nolock) ON CAST ( a.id AS nvarchar ( 20 ) ) = n.trade_no
        JOIN netPayRefundInfo p with(nolock) ON n.id= p.netRecordId
        WHERE
        p.returnid=#{afterREId}
        AND n.payWay= '建行分期'
    </select>

    <select id="selectTiemoCardInfoByPickup"
            resultType="com.jiuji.oa.afterservice.smallpro.bo.filmCard.FilmCardInfoForPickupBO">
        SELECT top 1
        id,
        etime AS eTime,
        useCount AS useCount,
        allCount AS allCount,
        datediff( MONTH, dtime, etime ) monthCount,
        dtime AS dTime,
        areaid AS areaId,
        t.userid userId
        FROM
        tiemoCard t with(nolock)
        WHERE
        isnull( t.isdel, 0 ) = 0
        AND basket_idBind = #{basketId}
        AND useCount &lt; allCount
        order by t.id desc
    </select>

    <update id="updateTiemoCardByPickUp">
        UPDATE dbo.tiemoCard
        SET useCount = isnull( useCount, 0 ) + 1
        <if test="type">
            ,last_count = isnull( last_count, 0 ) + 1
        </if>
        WHERE
        id = #{cardId}
        AND useCount &lt; allCount
        <if test="!type">
        AND etime &gt;= CAST (#{inDate} AS DATE )
        </if>
    </update>
    <update id="updateCodeMsgAndFid">
        UPDATE dbo.Smallpro SET codeMsg=#{codeMsg},fid=#{fid} WHERE id=#{smallproId}
    </update>
    <update id="correctStatus">
        UPDATE Smallpro
        SET stats = 1
        WHERE
            id IN (
        SELECT
            s.id
        FROM
            Smallpro s with(nolock)
            LEFT JOIN shouhou_fanchang sf with(nolock) ON sf.smallproid= s.id
        WHERE
            s.stats= 0
            AND sf.id IS NOT NULL)
    </update>

    <select id="getProductInfoByPpriceId" resultType="com.jiuji.oa.afterservice.smallpro.bo.SmallproProductInfoBO">
        SELECT
        product_name as productName,
        isnull( product_color, '' ) productColor,
        barCode,
        ppriceid ppriceId,
        ppriceid1 ppriceId1,
        memberprice memberPrice
        FROM
        dbo.productinfo with(nolock)
        WHERE
        ppriceid = #{ppriceId}
    </select>

    <select id="getProductInfoForCheck" resultType="com.jiuji.oa.afterservice.smallpro.bo.SmallproProductInfoBO">
        SELECT
        memberprice as memberPrice,
        ppriceid as ppriceId,
        cidFamily
        FROM
        dbo.productinfo with(nolock)
        WHERE
        ppriceid= #{ppriceId}
    </select>

    <select id="getSubUserIdByTuihuanKind" resultType="java.lang.Integer">
        <choose>
            <when test="kind == 6 or kind= 7">
                select userid from dbo.sub with(nolock) where sub_id = #{subId}
            </when>
            <when test="kind == 8">
                select userid from dbo.recover_marketInfo with(nolock) where sub_id = #{subId}
            </when>
            <when test="kind ==3 or kind ==4 or kind == 5 or kind ==11">
                select userid from dbo.shouhou with(nolock) where id = #{subId}
            </when>
            <when test="kind == 10">
                select userid from dbo.Smallpro with(nolock) where id = #{subId}
            </when>
            <!--其他权限显示-->
            <otherwise>
            </otherwise>
        </choose>

    </select>

    <select id="getUserOpenId" resultType="java.lang.String">
        select top 1 openid from dbo.WeixinUser with(nolock) where kinds=1 and userid=#{userId}
    </select>

    <select id="getLastChangePPID" resultType="com.jiuji.oa.afterservice.smallpro.bo.filmCard.FilmCardLastChangePPidBO">
        SELECT TOP
        1 s.id AS smallproId,
        s.changePpriceid AS changePpriceId,
        s.sub_id AS subId,
        s.Indate AS inDate,
        s.Buydate AS buyDate,
        sb.basket_id AS basketId,
        sb.ppriceid AS ppriceId,
        sub.tradeDate1 AS buyTime,
        tcul.cardId,
        tcul.dtime AS dTime,
        tcul.inuser AS cardInuser,
        p.cid AS cId
        FROM
        sub with(nolock)
        LEFT JOIN smallpro s with(nolock) ON s.sub_id= sub.sub_id
        LEFT JOIN SmallproBill sb with(nolock) ON sb.smallproID = s.id
        LEFT JOIN tiemoCardUserLog tcul with(nolock) ON tcul.smallProId= s.id
        LEFT JOIN productinfo p with(nolock) ON  p.ppriceid=sb.ppriceid
        WHERE
        sb.basket_id= #{basketId}
        AND sub.sub_id = #{subId}
        AND s.stats= 1
        AND s.kind = 2
        and tcul.dtime is not null
        ORDER BY
        s.Indate DESC
    </select>

    <select id="getPayInfoBySubId"
            resultType="com.jiuji.oa.afterservice.smallpro.bo.smallproExchange.SmallproPayInfoBO">
        SELECT
        id,
        money - isnull( refundPrice, 0 ) price,
        trade_no AS tradeNo,
        sub_number AS subNumber
        FROM
        dbo.netpay_record with(nolock)
        WHERE
        type = #{type}
        AND CAST ( dtime AS DATE ) &gt;= '2018-03-20'
        AND sub_number IN
        <foreach collection="subIdList" index="index" item="subId" separator="," open="(" close=")">
            #{subId}
        </foreach>
        AND payWay IN
        <foreach collection="payWayList" index="index" item="payWay" separator="," open="(" close=")">
            #{payWay}
        </foreach>
        AND money - isnull( refundPrice, 0 ) &gt;0
    </select>

    <select id="getPayInfoByAfterSaleREId"
            resultType="com.jiuji.oa.afterservice.smallpro.bo.smallproExchange.SmallproPayInfoBO">
        SELECT
        n.id,
        a.price as price,
        n.trade_no as tradeNo,
        n.sub_number as subNumber
        FROM
        dbo.netPayRefundInfo a with(nolock)
        LEFT JOIN dbo.netpay_record n with(nolock) ON a.netRecordId= n.id
        WHERE
        a.returnid=#{afterSaleREId}
    </select>

    <select id="getEffectiveRefundType" resultType="java.lang.String">
        SELECT DISTINCT
        inuser
        FROM
        dbo.shouying with(nolock)
        WHERE
        sub_id IN
        <foreach collection="subIdList" index="index" item="subId" separator="," open="(" close=")">
            #{subId}
        </foreach>
        AND sub_pay03 > 0
        <choose>
            <when test="type == 8">
                AND shouying_type IN ( '订金2', '交易2' )
            </when>
            <otherwise>
                AND shouying_type IN ( '订金', '交易' )
            </otherwise>
        </choose>
    </select>

    <select id="getKcRelatedIdType" resultType="com.jiuji.oa.afterservice.smallpro.bo.SmallproKcRelatedInfoBO">
        SELECT
        basket_id as basketId,
        comment comment
        FROM
        dbo.product_kclogs with(nolock)
        WHERE
        basket_id=#{basketId}
    </select>

    <select id="getCh999UserInfoByOneClickStocking"
            resultType="com.jiuji.oa.afterservice.smallpro.bo.smallproExchange.SmallproOneClickStockUserInfoBO">
        SELECT top 1
        cu.ch999_id as id,
        cu.ch999_name as name,
        cu.mobile as mobile,
        bu.id as bbsxpId
        FROM
        ch999_user cu with(nolock)
        LEFT JOIN BBSXP_Users bu with(nolock) on bu.mobile=cu.mobile
        WHERE
        cu.ch999_id = #{userId}
        and isnull(bu.mobile,0)=cu.mobile
        and cu.iszaizhi=1
        order by cu.ch999_id desc
    </select>

    <select id="getProductInfoByOneClickStocking"
            resultType="com.jiuji.oa.afterservice.smallpro.bo.smallproExchange.SmallproOneClickStockingProductInfoBO">
        SELECT
        memberprice as memberPrice,
        ppriceid as ppriceId,
        productid as productId,
        config as config,
        isnull( costprice, 0 ) AS costprice,
        product_name as productName,
        product_color as productColor,
        ismobile1 as isMobile
        FROM
        productinfo p with(nolock)
        WHERE
        display = 1
        AND isdel = 0
        AND que &lt;&gt; 2
        AND ppriceid IN
        <foreach collection="ppidList" index="index" item="ppid" separator="," open="(" close=")">
            #{ppid}
        </foreach>
        ORDER BY
        viewsWeek DESC,
        viewsWeekr DESC,
        memberprice ASC
    </select>

    <select id="getPpriceIdByBarcodeWithSmallpro" resultType="java.lang.Integer">
        SELECT TOP
        1 ppriceid
        FROM
        productprice with(nolock)
        WHERE
        barCode LIKE CONCAT('%',#{barCode},'%')
    </select>

    <select id="checkTemperedFilm" resultType="java.lang.Integer">
        SELECT
        1
        FROM
        basketBindRecord with(nolock)
        WHERE
        basket_id =#{basketId}
    </select>

    <select id="getImeiFromBasketBindRecord" resultType="java.lang.String">
        SELECT
            imei
        FROM
            basketBindRecord with(nolock)
        WHERE
            basket_id =#{basketId}
    </select>

    <select id="checkTemperedFilmWithImei" resultType="java.lang.Integer">
        SELECT
        1
        FROM
        basketBindRecord with(nolock)
        WHERE
        imei = #{imei}
        AND basket_id= #{basketId}
    </select>
    <select id="getIsChangePre" resultType="java.lang.Integer">
        SELECT TOP 1 s.id
        FROM Smallpro s WITH(NOLOCK)
        INNER JOIN SmallproBill sb WITH(NOLOCK) ON s.id = sb.smallproID AND sb.basket_id = #{basketId}
        WHERE s.sub_id = #{subId}
          AND s.kind = 2
          AND s.stats = 1
          AND ISNULL(s.isdel, 0) = 0
          AND s.id != #{id}
        ORDER BY s.Indate DESC;
    </select>
    <select id="getLastOutPPID" resultType="java.lang.Integer">
         select top 1 ppriceid from product_kclogs p with(nolock)  where p.basket_id=#{smallProId} and p.comment like '%换货%';
    </select>
    <select id="checkBasketBindRecord" resultType="java.lang.Integer">
        SELECT 1 FROM basketBindRecord WITH(NOLOCK) WHERE
        1=1
        <if test="imei != null and imei != ''">
            and imei=#{imei}
        </if>
        <if test="basketId != null">
            and basket_id=#{basketId}
        </if>

    </select>
    <select id="getBasketMessage" resultType="com.jiuji.oa.afterservice.smallpro.bo.BasketBO">
        SELECT
            p.product_name productName,
            s.sub_mobile subMobile,
            s.userid userId,
            s.sub_id subId
        FROM
            dbo.basket b with(nolock)
            INNER JOIN sub s with(nolock) ON s.sub_id= b.sub_id
            INNER JOIN dbo.productinfo p with(nolock) ON p.ppriceid= b.ppriceid
        WHERE
            b.basket_id=#{basketId}
    </select>
    <select id="getWeixinUser" resultType="com.jiuji.oa.afterservice.smallpro.bo.WxBO">
        SELECT
            openid,
            wxid
        FROM
            WeixinUser with(nolock)
        WHERE
            userid = #{userId}
            AND type = 1
            AND kinds = 1
            AND follow =1
    </select>
    <select id="getUseCount" resultType="java.lang.Integer">
        SELECT COUNT
            ( 1 )
        FROM
            dbo.Smallpro s with(nolock)
            LEFT JOIN dbo.SmallproBill b with(nolock) ON s.id = b.smallproID
        WHERE
            isnull( s.isdel, 0 ) = 0
            AND s.Stats != 2
            AND s.Kind = 2
            AND b.basket_id = #{basketId}
    </select>

    <select id="checkOrginSubCommitAndUnDeal" resultType="com.jiuji.oa.afterservice.smallpro.po.Smallpro">
        select * from dbo.Smallpro s with(nolock) where s.sub_id=15784499 and s.Kind=3 and s.Stats=0
        and exists(select * from dbo.SmallproBill b with(nolock) where b.smallproID= s.id
        and b.basket_id in
        <foreach collection="basketId" index="index" item="basketid" separator="," open="(" close=")">
            #{basketid}
        </foreach>

    </select>
    <select id="getSmallproReceivableProductBySubIdHis"
            resultType="com.jiuji.oa.afterservice.smallpro.bo.smallproReceivable.SmallproReceivableProductBO">
        SELECT
                basket_id as basketId,
                b.ppriceid as ppriceId,
                s.userid as userId,
                s.tradedate1 as tradeDate,
                u.username as userName,
                isnull(u.mobile,s.sub_mobile) as mobile,
                u.realname as realName,
                b.ismobile as isMobile,
                p.productid as productId,
                ppriceid1 as targetPpriceId,
                p.product_name as productName,
                p.product_color as productColor,
                basket_count as basketCount,
                p.memberprice as memberprice,
                b.price as price,
                tc.useCount as useCount,
                p.cid,
                p.barCode,
                p.isSn
                FROM
                basket b with(nolock)
                INNER JOIN sub s with(nolock) ON b.sub_id= s.sub_id
                LEFT JOIN productinfo p with(nolock) ON b.ppriceid= p.ppriceid
                LEFT JOIN BBSXP_Users u with(nolock) ON u.id= s.userid
                LEFT JOIN dbo.tiemoCard tc with(nolock) ON tc.basketid = b.basket_id
                WHERE
                s.sub_id= #{subId}
                AND ismobile = 0
                <if test="basketId != null and basketId != 0">
                    and b.basket_id = #{basketId}
                </if>
                AND s.sub_check= 3
                AND isnull( b.isdel, 0 ) = 0
                AND b.ischu= 1
                AND NOT EXISTS (
                SELECT
                smallpro.STATS,
                smallpro.id
                FROM
                smallprobill with(nolock)
                LEFT JOIN smallpro with(nolock) ON smallprobill.smallproid = smallpro.id
                WHERE
                smallpro.stats in (0,3,5,6)
                AND smallpro.kind in (1,2,3)
                AND smallpro.sub_id = s.sub_id
                )
                <if test="isJiujiXtenant != null and isJiujiXtenant == false">
                AND (
                tc.id IS NULL
                OR (
                ( tc.dtime &gt;= #{frameTime} AND isnull( tc.useCount, 0 ) = 0 )
                OR ( tc.dtime &lt; #{frameTime} AND isnull( tc.useCount, 0 ) &lt;= 2 )
                )
                )
                </if>
    </select>

    <select id="getSmallProIdBySubId" resultType="java.lang.Integer">
        select top 1 smallproid from dbo.shouhou_tuihuan with(nolock)
        where sub_id=#{subId} and tuihuan_kind=7 and isnull(isdel,0)=0 and check3 is not null
        and comment=#{comment}
        and isnull(iszengpin,0) = 1
    </select>
    <select id="getSmallproOrderInfoHis"
            resultType="com.jiuji.oa.afterservice.smallpro.bo.smallproInfo.SmallproInfoOrderBO">
        SELECT
        bsk.basket_id AS basketId,
        bsk.price as price,
        isnull(bsk.price_shouhou, bsk.price2) priceReturn,
        p.memberprice AS memberPrice,
        sub.areaid AS areaId
        FROM dbo.sub with(nolock)
            inner join dbo.basket as bsk with(nolock) on dbo.sub.sub_id = bsk.sub_id
            LEFT JOIN productinfo p with(nolock) ON bsk.ppriceid= p.ppriceid
        WHERE (dbo.sub.sub_check != 4) and (isnull(bsk.isdel, 0) = 0)
        and basket_id in
        <foreach collection="basketIdList" index="index" item="basketId" separator="," open="(" close=")">
            #{basketId}
        </foreach>
    </select>
    <select id="getMsgCodeSendSubInfoHis"
            resultType="com.jiuji.oa.afterservice.smallpro.bo.smallproSendMsgCode.SmallproSendMsgCodeSubInfoBO">
        SELECT
        tradeDate1 as tradeDate,
        sub_mobile as subMobile
        FROM
        sub WITH(nolock)
        WHERE
        sub_id =#{subId}
    </select>

    <select id="checkPpid" resultType="java.lang.Integer">
        SELECT
        1
        FROM
        productinfo p WITH(nolock)
        WHERE
        ppriceid in
        <foreach collection="ppId" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        AND (EXISTS ( SELECT 1 FROM f_category_children ( '63' ) f  WHERE f.ID= p.cid )
        or p.cid in ('217','464','218','295','385','219','465','220','662','386','465','386'))
    </select>
    <select id="checkBarcode" resultType="java.lang.Integer">
        SELECT
        1
        FROM
        productinfo p WITH(nolock)
        WHERE
        barCode like CONCAT('%',#{ppId})
        AND (EXISTS ( SELECT 1 FROM f_category_children ( '63' ) f  WHERE f.ID= p.cid )
        or p.cid in ('43','217','218','385','464','501','502','609','614','465','386'))
    </select>
    <select id="getDIYYearCard" resultType="com.jiuji.oa.afterservice.smallpro.bo.smallproSendMsgCode.DIYTimoCardBO">
        select  basket_id basketId,isdel,end_time from DIYTimeCard WITH(nolock)  where purchase_basket_id= #{basketId}
    </select>
    <select id="getDIYYearCardByBasketIds"
            resultType="com.jiuji.oa.afterservice.smallpro.bo.smallproSendMsgCode.DIYTimoCardBO">
        select basket_id basketId,isdel,create_time buyTime from DIYTimeCard WITH(nolock)  where purchase_basket_id
        in
        <foreach collection="basketIds" index="index" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </select>
    <select id="getAddSmallproLogBatch"
            resultType="com.jiuji.oa.afterservice.statistics.bo.smallpro.AddSmallproLogBatchBO">
        SELECT
        s.Inuser,
        s.id,
        p.product_name productName
        FROM
        Smallpro s with(nolock)
        LEFT JOIN SmallproBill ss with(nolock) ON s.id= ss.smallproid
        LEFT JOIN productinfo p with(nolock) ON ss.ppriceid = p.ppriceid
        WHERE
        s.id IN
        <foreach collection="idList" index="index" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </select>
    <select id="getproductIdByPPid" resultType="java.lang.Integer">
        SELECT top 1
        ppriceid
        FROM
        productinfo with(nolock)
        WHERE
        ppriceid in
        <foreach collection="ppids" index="index" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
        AND productid = 45218
    </select>
    <select id="getTradeDateTime" resultType="java.time.LocalDateTime">
        select tradeDate1 from sub with(nolock) where sub_id= #{subId}
    </select>
    <select id="getBaitiaoPrice" resultType="java.math.BigDecimal">
        select top 1 y.sub_pay04 from dbo.shouying y,dbo.shouyin_other o with(nolock) where y.id=o.shouyinid and o.type_=10 and y.sub_id=#{subId} order by  y.sub_pay04 desc
    </select>
    <select id="getKuBaitiaoPrice" resultType="java.math.BigDecimal">
        select sum(money-isnull(refundPrice,0)) leftPrice from dbo.netpay_record with(nolock) where sub_number=#{subId} and type=1 and payWay='库分期'
    </select>
    <select id="getSpotSubData" resultType="java.lang.Integer">
        select top 1 b.sub_id
        FROM sub s with (nolock)
        LEFT JOIN basket b with (nolock)
        ON s.sub_id = b.sub_id
        WHERE 1 = 1
        and s.sub_id = #{subId}
        <if test="ppIds != null and !ppIds.isEmpty()">
            and b.ppriceid in
            <foreach collection="ppIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </select>
    <select id="getReceiptPrinting"
            resultType="com.jiuji.oa.afterservice.smallpro.bo.SmallReceiptPrintingBO">
        select
           a.area area,
           a.company_tel1 phone,
           s.id smallProId,
           s.qujiandate quJiTime,
           s.Username userName,
           s.Mobile userPhone,
           '[' + convert(varchar(20), p.ppriceid) + ']' + ISNULL(p.product_name ,'')+ ' ' + ISNULL(p.product_color,'') productName,
           S.imei snNumber,
           s.Indate sendRepairTime,
           s.Problem problem,
           s.Kind
    from Smallpro s
             left join SmallproBill sb on s.id = sb.smallproID
             left join areainfo a on s.areaid = a.id
             left join productinfo p on p.ppriceid = sb.ppriceid
    where s.id = #{smallProId}
      and isnull(s.isdel, 0) = 0
    </select>
    <select id="getCheckSmallProWuLiu" resultType="com.jiuji.oa.afterservice.smallpro.bo.CheckSmallProWuLiuBO">
        select top 1 s.istoarea AS isToArea,
               s.areaid AS areaId,
               s.id AS id,
               p.product_name+' '+p.product_color AS name,
               a.area AS area,
               aa.area AS toArea,
               b.count
        from Smallpro s with(nolock)
                 left join areainfo a with(nolock) on s.areaid = a.id
                 left join areainfo aa with(nolock) on s.toareaid = aa.id
                 left join SmallproBill b with(nolock) on s.id = b.smallproID
                 left join productinfo p with(nolock) on p.ppriceid = b.ppriceid
        where s.id = #{smallProId}
    </select>

    <sql id="querySmallCategory>">
        <if test="req.brandIds!=null and req.brandIds.size>0">
            and p.brandID in
            <foreach collection="req.brandIds" item="brandId" open="(" separator="," close=")">
                #{brandId}
            </foreach>
        </if>
        <if test="req.ciDs!=null and req.ciDs.size>0">
            and c.id in
            <foreach collection="req.ciDs" item="ciD" open="(" separator="," close=")">
                #{ciD}
            </foreach>
        </if>
        <if test="areaIds!=null and areaIds.size>0">
            and a.id in
            <foreach collection="areaIds" item="areaId" open="(" separator="," close=")">
                #{areaId}
            </foreach>
        </if>
        <if test="null != req.areaLevel and req.areaLevel.size > 0 ">
            and a.level1 in
            <foreach collection="req.areaLevel" item="it" open="(" separator="," close=")">
                #{it}
            </foreach>
        </if>
        <if test="null != req.displayLevel and req.displayLevel.size > 0 ">
            and a.DisplayLevel in
            <foreach collection="req.displayLevel" item="it" open="(" separator="," close=")">
                #{it}
            </foreach>
        </if>
        <if test="null != req.areaKind">
            and a.kind1 = #{req.areaKind}
        </if>
        <if test="null != req.display and req.display">
            and p.display = 1
        </if>
        <if test="null != req.display and !req.display">
            and p.display = 0
        </if>
    </sql>
    <select id="getSmallCategoryData" resultType="com.jiuji.oa.afterservice.smallpro.bo.SmallCategoryBO">
        select
        sum(case when b.ismobile = 1 then 1 else isnull(b.basket_count, 0) end) saleCount,
        c.Name categoryName,
        cast(SUM(b.price2 * (case when b.ismobile = 1 then 1 else isnull(b.basket_count, 0) end)) as decimal(30,
        2)) AS saleAmount,
        cast(
        SUM(
        case
        <choose>
            <when test="xTenant==0">
                when b.ismobile = 1 and z.mkc_id is not null and b.price <![CDATA[ < ]]> isnull(k.staticPrice + isnull(k.fanli,0),b.price2) then 0
                when b.ismobile = 1 then b.price2 - isnull(k.staticPrice + isnull(k.fanli,0), b.price2)
            </when>
            <when test="xTenant==1000">
                when b.ismobile = 1 and z.mkc_id is not null and b.price <![CDATA[ < ]]> isnull(k.staticPrice,b.price2) then 0
                when b.ismobile = 1 then b.price2 - isnull(k.staticPrice, b.price2)
            </when>
            <otherwise>
                when b.ismobile = 1 and z.mkc_id is not null and b.price <![CDATA[ < ]]> isnull(k.staticPrice - isnull(k.fanli,0),b.price2) then 0
                when b.ismobile = 1 then b.price2 - isnull(k.staticPrice - isnull(k.fanli,0), b.price2)
            </otherwise>
        </choose>
        when b.ismobile = 0 and isnull(b.type, 0) in (1, 13, 29, 22) then 0
        when b.ismobile = 0 and p.pLabel in (3, 11) and isnull(b.inprice, b.price2) <![CDATA[ > ]]> b.price2 then 0
        else b.price2 - isnull(b.inprice, b.price2) end
        ) as decimal(30,
        2)) AS profitAmount,
        c.Name cidName,
        c.id cid
        from basket b with (nolock)
        left join sub s with (nolock) on b.sub_id = s.sub_id
        left join productinfo p with (nolock) on p.ppriceid = b.ppriceid
        left join category c with (nolock) on c.id = p.cid
        left join areainfo a with (nolock) on a.id = s.areaid
        left join product_mkc k with(nolock) on b.basket_id = k.basket_id
        left join view_zx_mkc z with(nolock) on z.mkc_id = k.id
        where b.ischu = 1
        AND c.Child = 0
        AND s.subtype <![CDATA[ <> ]]> 10
        AND s.sub_check = 3
        AND isnull(b.isdel, 0) = 0
        <if test="req.timeType==1">
          <if test="null !=req.startTime">
              AND s.tradeDate1 <![CDATA[ >= ]]> #{req.startTime}
          </if>
          <if test="null !=req.endTime">
              AND s.tradeDate1 <![CDATA[ <= ]]> #{req.endTime}
          </if>
        </if>
        <if test="req.timeType==2">
            <if test="null !=req.startTime">
                AND s.tradeDate <![CDATA[ >= ]]> #{req.startTime}
            </if>
            <if test="null !=req.endTime">
                AND s.tradeDate <![CDATA[ <= ]]> #{req.endTime}
            </if>
        </if>
        <include refid="querySmallCategory>"/>
        group by c.Name, c.id
    </select>
    <select id="getSmallCategoryDataInventory"
            resultType="com.jiuji.oa.afterservice.smallpro.bo.SmallCategoryInventoryBO">
        select
               isnull(sum(b.lcount),0) lCount,
               c.id cid,
               c.Name categoryName,
               isnull(sum(b.inprice * b.lcount),0) cost
        from dbo.diaobo_basket b with (nolock)
                 left join dbo.diaobo_sub s with (nolock) on b.sub_id = s.id
                 left join productinfo p with (nolock) on p.ppriceid = b.ppriceid
                 left join category c with (nolock) on c.id = p.cid
                 left join areainfo a with (nolock) on a.id = s.toareaid
        where s.stats in (2, 3)
        AND c.Child = 0
        AND isnull(b.basket_id,0) = 0
        <include refid="querySmallCategory>"/>
        group by c.Name, c.id
    </select>
    <select id="getSmallCategoryDataInventoryOn"
            resultType="com.jiuji.oa.afterservice.smallpro.bo.SmallCategoryInventoryBO">
        select  b1.categoryName,b1.cid,sum(b1.cost) as cost, sum(b1.lCount) as lcount from (
        select c.Name categoryName,
               c.id cid,
               isnull(sum(k.inprice * k.leftCount),0) cost,
               isnull(sum(k.leftCount),0) lCount
        from product_kc k with(nolock)
                 left join productinfo p with (nolock) on p.ppriceid = k.ppriceid
                 left join category c with (nolock) on c.id = p.cid
                 left join areainfo a with (nolock) on a.id = k.areaid
        where 1 = 1
        AND c.Child = 0
        <include refid="querySmallCategory>"/>
        <if test="null != req.noStock and req.noStock">
            and k.lcount >= 0
        </if>
        group by c.Name, c.id
        union all
        select c.Name categoryName, c.id cid,sum(staticPrice) as cost,count(1) as lCount
        from product_mkc k with(nolock)
        left join productinfo p with (nolock) on p.ppriceid = k.ppriceid
        left join category c with (nolock) on c.id = p.cid
        left join areainfo a with (nolock) on a.id = k.areaid
        where k.kc_check in (2,3,10)
        and isnull(k.basket_id,0) = 0
        and isnull(k.mouldFlag,0) != 1
        and not exists(select 1 from dbo.xc_mkc x with(nolock) where x.mkc_id=k.id )
        <include refid="querySmallCategory>"/>
        group by c.Name, c.id
        )b1 group by b1.categoryName,b1.cid
    </select>
    <select id="getBatchPushCh999Ids" resultType="com.jiuji.oa.afterservice.smallpro.bo.BatchPushBO">
        select distinct s.id smallProId,
                        s.Inuser ch999Name,
                        ch999_id ch999Id
        from Smallpro s with (nolock)
        left join ch999_user c999u with (nolock) on s.Inuser = c999u.ch999_name
        where s.id in
        <foreach collection="smallProIds" index="index" item="smallProId" separator="," open="(" close=")">
            #{smallProId}
        </foreach>
    </select>

    <select id="getYuyueIdByMobile" resultType="java.lang.Integer">
         select top 1 yy.id from shouhou_yuyue yy with(nolock)
         where yy.areaid = #{areaId} and yy.mobile = #{mobile}
         and ISNULL(yy.ismobile,0) = 0 and ISNULL(yy.isdel, 0) = 0
         and yy.stype = 1
         and yy.stats not in (3,5,10)
         and yy.cancelKind is null
    </select>

    <select id="getProVimpelComInfo" resultType="com.jiuji.oa.afterservice.smallpro.bo.smallproInfo.SmallProVimpelComBo">
    SELECT top 1 b.ppriceid as ppid,basket_id as basketId,b.sub_id as subId,basket_count-ISNULL(re.count_,0) as basketCount
    ,isnull(ob.offsetMoney,0.00) as offsetMoney
    FROM dbo.basket b with(nolock)
    LEFT JOIN dbo.sub s ON b.sub_id=s.sub_id
    LEFT JOIN dbo.productinfo p ON b.ppriceid=p.ppriceid
    LEFT JOIN (select SUM(BasketCount) count_,BasketID FROM dbo.ReturnsDetail GROUP BY BasketID   ) re
    ON b.basket_id=re.BasketID
    LEFT JOIN (select sum(ob.offsetMoney) offsetMoney,ob.bindBasketId from operatorBasket ob with(nolock)  where isnull( isChecked,0)=1 and isnull(ob.isdel,0)=0 and ob.status = 1 group by ob.bindBasketId) ob
    on ob.bindBasketId=b.basket_id
    WHERE  b.basket_id=#{basketId}
    AND b.ismobile=0 and s.sub_check=3 and isnull(b.isdel,0)=0 and b.ischu=1 and isnull(b.isdel,0)=0
    </select>
    <select id="checkSmallProId" resultType="java.lang.Integer">
        select s.id
            from Smallpro s with(nolock)
                     left join SmallproBill sb with(nolock) on s.id = sb.smallproID
            where isnull(s.isdel, 0) = 0
              and s.id = #{oldId}
            and sb.ppriceid=#{ppId}
            and dateadd( hour , 48, s.Indate ) > getdate()
            union
            select s.id
            from Smallpro s with(nolock)
            where isnull(s.isdel, 0) = 0
              and s.id = #{oldId}
            and s.changePpriceid=#{ppId}
            and dateadd( hour , 48, s.Indate ) > getdate()
    </select>
    <select id="getFilmCountByPpids" resultType="java.lang.Integer">
        select count(1) from productinfo where cid in ('217','464','218','295','385','219','465','220','662','386') and ppriceid in
        <foreach collection="ppids" index="index" item="ppid" open="(" close=")" separator=",">
            #{ppid}
        </foreach>
    </select>

    <select id="getBindImeiBySmallId" resultType="cn.hutool.core.lang.Dict">
        SELECT r.imei,sb.id smallProBillId,sb.ppriceid
        FROM
            dbo.basketBindRecord r WITH ( nolock )
            INNER JOIN dbo.SmallproBill sb WITH ( nolock ) ON r.basket_id = sb.basket_id
            INNER JOIN dbo.Smallpro s WITH ( nolock ) ON s.id = sb.smallproID
        WHERE
            s.id = #{smallId}
            AND s.kind = 2
    </select>

    <select id="getSmallProOldGoodsWaitingForSelect" resultType="com.jiuji.oa.afterservice.smallpro.vo.res.SmallProOldGoodsWaitingForSelectRes">
        SELECT s.id,
	    count(f.id) as number,
        s.areaid as areaId,a1.area,s.toareaid as toAreaId,a2.area as toArea
        from Smallpro s with(nolock)
        inner join shouhou_fanchang f with(nolock) on f.smallproid = s.id and isnull(rstats,0) = 0
        left join areainfo a1 with(nolock) on s.areaid = a1.id
        left join areainfo a2 with(nolock) on s.toareaid = a2.id
        WHERE isnull(s.istoarea,0) = 1
        and s.areaid = #{areaId}
        and s.stats != 2
        and s.toareaid is not null and s.wuliuid is null
        group by s.id, s.areaid, a1.area, s.toareaid, a2.area
        order by s.id asc
    </select>

    <select id="getSmallProListBySubId" resultType="com.jiuji.oa.afterservice.smallpro.po.Smallpro">
        select * from Smallpro with(nolock) where isnull(stats,0) = 0 and isnull(isdel,0) = 0 and kind != 4 and sub_id = #{subId}
        <if test="areaId != null and areaId != 0">
            and areaid = #{areaId}
        </if>
    </select>

    <select id="getAllCategory" resultType="com.jiuji.oa.afterservice.smallpro.bo.CategoryBO">
        select id cid,name,parentid pid,path from category with(nolock)
    </select>

    <select id="getDisplayProductInfoData"
            resultType="com.jiuji.oa.afterservice.smallpro.bo.DisplayProductInfoBO">
        select isnull(sum(count_),0) clCount,
               isnull(sum(count_ * sellprice),0) clCost,
               c.Name categoryName,
               c.id cid
        from dbo.displayProductInfo d with(nolock)
        left join productinfo p with (nolock) on p.ppriceid = d.ppriceid
        left join category c with (nolock) on c.id = p.cid
        left join areainfo a with (nolock) on a.id = d.areaid
        where d.stats_=1
          and d.mkc_id is null
          and c.id is not null
        <if test="req.brandIds!=null and req.brandIds.size>0">
            and p.brandID in
            <foreach collection="req.brandIds" item="brandId" open="(" separator="," close=")">
                #{brandId}
            </foreach>
        </if>
        <if test="req.ciDs!=null and req.ciDs.size>0">
            and c.id in
            <foreach collection="req.ciDs" item="ciD" open="(" separator="," close=")">
                #{ciD}
            </foreach>
        </if>
        <if test="areaIds!=null and areaIds.size>0">
            and a.id in
            <foreach collection="areaIds" item="areaId" open="(" separator="," close=")">
                #{areaId}
            </foreach>
        </if>
        <if test="null != req.areaLevel and req.areaLevel.size > 0 ">
            and a.level1 in
            <foreach collection="req.areaLevel" item="it" open="(" separator="," close=")">
                #{it}
            </foreach>
        </if>
        <if test="null != req.displayLevel and req.displayLevel.size > 0 ">
            and a.DisplayLevel in
            <foreach collection="req.displayLevel" item="it" open="(" separator="," close=")">
                #{it}
            </foreach>
        </if>
        <if test="null != req.areaKind">
            and a.kind1 = #{req.areaKind}
        </if>
        <if test="null != req.display and req.display">
            and p.display = 1
        </if>
        <if test="null != req.display and !req.display">
            and p.display = 0
        </if>
        group by c.Name,c.id
    </select>

    <select id="getProductKcLeftCountByPpidAndAreaId" resultType="java.lang.Integer">
        select leftCount from product_kc with(nolock) where ppriceid = #{ppid} and areaid = #{areaId}
    </select>


    <select id="getIncomingSmallproInfoList"
            resultType="com.jiuji.oa.afterservice.smallpro.bo.smallproExchange.SmallproIncomingInfoBO">
        SELECT
            sf.id as shouhouFanchangId,
            sf.smallproid as smallproId,
            sf.basket_id as basketId,
            sf.ppid,
            sf.ppid1,
            dbo.getPjType ( p.cid ) pjType,
            s.areaid as areaId,
            s.istoarea as isToArea,
            s.toareaid as toAreaId,
            s.wuliuid as wuliuId,
            s.Inuser as inUser,
            case when isnull(bi.inprice,0)&lt;&gt;0 then bi.inprice else isnull(b.inprice,sf.inprice) end tuikuanM
        FROM
            shouhou_fanchang sf with(nolock)
            LEFT JOIN smallpro s with(nolock) ON sf.smallproid = s.id
            left join dbo.SmallproBill bi with(nolock) on bi.id = sf.billId
            LEFT JOIN dbo.basket b with(nolock) ON sf.basket_id = b.basket_id
            LEFT JOIN dbo.productinfo p with(nolock) ON p.ppriceid = sf.ppid1
        WHERE
            sf.zxinuser IS NULL
          AND sf.id in
        <foreach collection="collects" index="index" item="collect" open="(" close=")" separator=",">
            #{collect}
        </foreach>
    </select>


    <select id="getScrapSmallproInfoList"
            resultType="com.jiuji.oa.afterservice.smallpro.bo.smallproExchange.SmallproScrapInfoBO">
        SELECT
            sf.id as shouhouFanchangId,
            s.id AS smallproId,
            s.sub_id AS subId,
            s.wuliuid AS wuliuId,
            <!-- 4 现货 没有订单 从旧件表取  非现货 优先从接件取成本->从旧件表取(本次换出去的商品成本)->订单表成本 -->
            iif(s.Kind = 4,isnull(sf.inprice,sb.inprice),isnull(isnull(sb.inprice,sf.inprice),b.inprice)) AS inPrice,
            ISNULL( s.areaid, s.toareaid ) areaId,
            s.istoarea AS isToArea,
            iif(s.Kind = 4,isnull(sf.inprice,k.inprice),k.inprice) AS kcInprice,
            s.userid AS userId ,
            a.kind1 ,
            a.area as areaCode,
            s.ServiceType serviceType
        FROM
            dbo.shouhou_fanchang sf with(nolock)
            LEFT JOIN dbo.basket b with(nolock) ON b.basket_id = sf.basket_id
            LEFT JOIN dbo.Smallpro s with(nolock) ON s.id = sf.smallproid
            left join dbo.SmallproBill sb with(nolock) on sb.smallproID = s.id
            LEFT JOIN dbo.product_kc k with(nolock) ON ( k.ppriceid = sf.ppid AND k.areaid = ISNULL( s.areaid, s.toareaid))
            LEFT JOIN dbo.areainfo a with(nolock) ON a.id = ISNULL( s.areaid, s.toareaid )
        WHERE
            sf.id in
        <foreach collection="fanChangIdList" index="index" item="fanChangId" open="(" close=")" separator=",">
            #{fanChangId}
        </foreach>
          AND sf.rstats IN ( 0, 1 )
    </select>

    <select id="getSmallProRefundList" resultType="com.jiuji.oa.afterservice.other.po.ShouhouTuihuan">
        select  * from shouhou_tuihuan with(nolock)
        where  isnull(isdel,0) = 0 and (smallproid = #{smallProId}  and tuihuan_kind in(7, 13) or (shouhou_id = #{smallProId} and tuihuan_kind = 10)) ;

    </select>

    <select id="getOperatorBasketByStatus" resultType="java.lang.Integer">
        select basketId from dbo.OperatorBasket where basketId in
        <foreach collection="basketIdList" index="index" item="basketId" open="(" close=")" separator=",">
            #{basketId}
        </foreach>
        AND status != 2 AND isnull(isdel, 0) = 0
    </select>

    <select id="getLastExchangePpidInpriceByBasketId" resultType="java.math.BigDecimal">
        SELECT top 1 sf.inprice from shouhou_fanchang sf with(nolock)
        left join Smallpro s with(nolock) on sf.smallproid = s.id
        where sf.basket_id = #{basketId} and s.stats = 1 and s.kind = 2 ORDER BY s.id desc
    </select>

    <select id="getSmallproCurrentPageCost"
            resultType="com.jiuji.oa.afterservice.smallpro.vo.res.SmallproInfoCostCalculateRes$Cost">
        SELECT
        SUM(round(sp.costPrice,2)) AS costPrice,
        SUM(round(sp.feiyong,2)) AS maintainPrice
        FROM
        smallpro sp with(nolock)
        LEFT JOIN areainfo a with(nolock) ON isnull( sp.toareaid, sp.areaid ) = a.id
        LEFT JOIN areainfo a2 WITH(nolock) ON sp.areaid=a2.id
        WHERE
        1 = 1
        <include refid="queryConditionSql">
            <property name="query" value="#{query}"/>
        </include>
    </select>

    <select id="getBasketTypeById" resultType="java.lang.Integer">
        select basket_id as basketId from dbo.basket where basket_id in
        <foreach collection="basketIdList" index="index" item="basketId" open="(" close=")" separator=",">
            #{basketId}
        </foreach>
        AND isnull(type,0) = 22 AND isnull(isdel, 0) = 0
    </select>

    <select id="getRefundCount" resultType="java.lang.Integer">
        SELECT count(1) from SmallproBill sb with(nolock)
        INNER JOIN Smallpro s with(nolock) on s.id = sb.smallproID
        where sb.basket_id = #{basketId} and s.stats = 1  and s.kind = 3
    </select>

    <select id="getProductIsResale" resultType="com.jiuji.oa.afterservice.smallpro.bo.smallproInfo.NotSoldBackBO">
        select ob.id,ob.bindBasketId,ob.basketId FROM OperatorBasket ob with(nolock) where bindBasketId in
        <foreach collection="basketIdList" index="index" item="basketId" open="(" close=")" separator=",">
            #{basketId}
        </foreach>
        and status = 1 and isdel = 0
    </select>
    <select id="listLastChangeSmallpro"
            resultType="com.jiuji.oa.afterservice.smallpro.bo.smallproInfo.LastExchangeSmallproBo">
        select a.smallproId,a.basketId,a.inprice
        from (SELECT s.id smallproId,sb.basket_id basketId,sf.inprice,ROW_NUMBER() over(partition by sb.basket_id order by s.Indate DESC) rn
            FROM Smallpro s with(nolock)
            LEFT JOIN SmallproBill sb with(nolock) ON s.id = sb.smallproID
            LEFT JOIN shouhou_fanchang sf with(nolock) ON sf.basket_id = sb.basket_id and sf.smallproid=s.id
        WHERE
            s.sub_id= #{subId}
          AND s.kind= 2
          AND s.stats= 1
          and  ISNULL(s.isdel, 0) = 0
          AND sb.basket_id in
          <foreach collection="basketIds" item="basketId" separator="," open="(" close=")">
              #{basketId}
          </foreach>
          AND s.id!= #{smallproId}) a where a.rn = 1
    </select>
    <select id="listLastOutPpriceId"
            resultType="com.jiuji.oa.afterservice.smallpro.bo.smallproInfo.LastExchangeSmallproBo">
        select p.ppriceid,ssb.smallId smallproId
        from product_kclogs p with(nolock)
        inner join (
            select s.id smallId,sb.id billId,s.qujiandate
            from Smallpro s with(nolock)
            left join SmallproBill sb with(nolock) on sb.smallproID = s.id
            where s.id in
                  <foreach collection="smallproIds" separator="," item="smallproId" open="(" close=")">
                    #{smallproId}
                  </foreach>
        ) ssb on p.basket_id in(ssb.smallId, ssb.billId) and p.dtime BETWEEN DATEADD(MINUTE, -10, ssb.qujiandate) and  DATEADD(MINUTE, 10, ssb.qujiandate)
        where p.comment like '%换货%';
    </select>
    <select id="listFilmCardInfomation"
            resultType="com.jiuji.oa.afterservice.smallpro.bo.filmCard.FilmCardInfomationBO">
        select * from (
            SELECT tc.sub_id AS subId,
            tc.areaid AS areaId,
            tc.basketid AS basketId,
            b.basket_id cardBasketTbId,
            tc.basket_idBind AS basketBindId,
            tc.price tcPrice,
            CASE
            <foreach collection="basketIds" item="basketId">WHEN tc.basketid=#{basketId} then b.price
                WHEN tc.
                basket_idBind=#{basketId} then tc.price
            </foreach>
            END price,
            isnull(b.price_shouhou, b.price2) as canRefundPrice,
            tc.useCount ,
            tc.allCount,
            tc.dtime AS buyTime,
            tc.etime AS endTime,
            a.area AS areaCode,
            a.area_name AS areaName,
            tcl.dtime AS lastUseTime,
            tcl.inuser AS lastInuser,
            isnull(s.tradeDate1,tc.dtime) AS tradeDate,
            <!--新增字段startTime-->
            tc.stime startTime,
            <!--新增字段lastCount 年包过期后的使用次数-->
            tc.last_count lastCount,
            tc.type_ type,
            b.ppriceid,
            row_number() over(partition by tc.basket_idBind ORDER BY tc.allCount desc,tc.id desc,tcl.dtime DESC) rn
            FROM
            tiemoCard tc with(nolock)
            LEFT JOIN areainfo a with(nolock) ON tc.areaid = a.id
            LEFT JOIN tiemoCardUserLog tcl with(nolock) ON tc.id= tcl.cardId
            LEFT JOIN sub s with(nolock) ON s.sub_id= tc.sub_id
            LEFT JOIN basket b with(nolock) ON tc.basketid= b.basket_id
            WHERE
                  (basket_idBind in
                    <foreach collection="basketIds" open="(" close=")" item="basketId" separator=",">
                        #{basketId}
                    </foreach>
                    OR basketid in
                    <foreach collection="basketIds" open="(" close=")" item="basketId" separator=",">
                        #{basketId}
                    </foreach>
                  )
            <if test="isNotDel">
                and isnull(tc.isdel,0)=0
            </if>
        ) a where a.rn = 1
    </select>

    <!-- listSmallOrderOnGoing -->
    <select id="listSmallOrderOnGoing" resultType="com.jiuji.oa.afterservice.small.SmallYuyueOrderOnGoingVO">
        SELECT count(DISTINCT s.id) smallOrderCount, sb.basket_id basketId
        FROM Smallpro s with(nolock)
            INNER JOIN SmallproBill sb with(nolock) ON sb.smallproID = s.id
        WHERE s.ServiceType = 4
            AND s.Stats NOT IN(1,2)
            AND isnull(s.isdel,0) = 0
            AND sb.basket_id IN
        <foreach collection="basketIds" open="(" close=")" item="basketId" separator=",">
            #{basketId}
        </foreach>
        <if test="userId != null and userId >0">
            AND s.userid = #{userId}
        </if>
        GROUP BY sb.basket_id
    </select>

    <!-- listYuyueOrderOnGoing -->
    <select id="listYuyueOrderOnGoing" resultType="com.jiuji.oa.afterservice.small.SmallYuyueOrderOnGoingVO">
        SELECT count(DISTINCT sy.id) yuyueOrderCount, sy.basket_id basketId
        FROM shouhou_yuyue sy with(nolock)
        WHERE sy.smallproid is null
            AND sy.stats not in(5,10)
            AND isnull(sy.isdel, 0) = 0
            AND sy.basket_id IN
        <foreach collection="basketIds" open="(" close=")" item="basketId" separator=",">
            #{basketId}
        </foreach>
        <if test="userId != null and userId >0">
            and sy.userid = #{userId}
        </if>
        GROUP BY sy.basket_id
    </select>

    <select id="getKcCount" resultType="java.lang.Integer">
        select
            CASE WHEN ISNULL(k.leftCount,0) > 0 THEN ISNULL(k.leftCount,0) ELSE 0 END AS kcCount
        from
            dbo.product_kc k with(nolock)
        where
            k.ppriceid = #{ppriceid} and k.areaid =#{areaId}
    </select>
    <select id="selectBasketBindRecordImei" resultType="java.lang.String">
        select imei from dbo.basketBindRecord with (nolock )where basket_id=#{basketId} order by binddate;
    </select>
    <select id="selectTiemoCardUserLog" resultType="com.jiuji.oa.afterservice.smallpro.po.TiemoCardUserLog">
        select tcul.*
        from dbo.tiemoCard tc with (nolock)
        left join dbo.tiemoCardUserLog tcul with (nolock)  on tc.id = tcul.cardId
            left join dbo.Smallpro sp with(nolock )  on sp.id=tcul.smallProId
        where  isnull(tc.isdel, 0) = 0
          and (tc.basket_idBind = #{basketId} OR tc.basketid = #{basketId})
          and sp.imei=#{imei}
        order by tcul.dtime desc
     </select>
    <select id="getSubWithPidSubTotalMoney" resultType="java.math.BigDecimal">
        SELECT isnull(sum(a.yifuM),0)+isnull(sum(psub.yifuM),0)+isnull(sum(brsub.yifuM),0)
        from (SELECT s.yifuM,s.subPID from sub s with(nolock) where s.sub_check = 3 and s.sub_id =#{subId}) a
         left join sub psub with(nolock) on psub.sub_id = a.subPID and psub.sub_check = 3
         left join sub brsub with(nolock) on brsub.sub_id = psub.sub_id and brsub.sub_check = 3
    </select>
    <select id="remainingCashMoney" resultType="java.math.BigDecimal">
        select isnull(sum(h.tuikuanM),0) from
            (select h.tuikuanM,h.tui_way from dbo.shouhou s with(nolock) inner join dbo.shouhou_tuihuan h with(nolock) on s.id=h.shouhou_id
            where isnull(h.check3,0)=1 and isnull(h.isdel,0)=0 and h.tuihuan_kind in (3,4) and s.sub_id>0 and s.sub_id= #{subId}
              and isnull(s.ishuishou,0)=0 and h.tui_way='现金'
            union
            select h.tuikuanM,h.tui_way from dbo.Smallpro s with(nolock) inner join dbo.shouhou_tuihuan h with(nolock) on s.id=h.smallproid
            where isnull(h.check3,0)=1 and isnull(h.isdel,0)=0 and h.tuihuan_kind in (7) and s.sub_id>0 and s.sub_id= #{subId} and h.tui_way='现金'
            union
            select h.tuikuanM,h.tui_way from dbo.sub s with(nolock) inner join dbo.shouhou_tuihuan h with(nolock) on s.sub_id=h.sub_id
            where isnull(h.check3,0)=1 and isnull(h.isdel,0)=0 and h.tuihuan_kind in (6)  and (s.sub_id= #{subId} or s.subPID= #{subId}) and h.tui_way='现金') h
    </select>
    <select id="getTotalYearCardUseNcPrices" resultType="java.math.BigDecimal">
        SELECT sum(nc.Total) from sub s with(nolock)
              left join NumberCard nc with(nolock) on nc.userid = s.userid and nc.State = 1 and isnull(nc.Isdel,0) = 0
                                                          and nc.AddTime >= CONVERT(DATE, CONVERT(VARCHAR(10), s.tradeDate1, 112))
        where s.sub_id = #{subId} and nc.GName like '%九机年卡%'
    </select>

    <select id="checkSub" resultType="java.lang.String">
        SELECT DISTINCT s.sub_id FROM sub s with(nolock)
        left join basket b with(nolock) on s.sub_id =b.sub_id
        WHERE s.subtype in ('10','20') and s.sub_check ='3'
          and b.ismobile ='0' and s.sub_id in
        <foreach collection="subIdList" open="(" close=")" item="subId" separator=",">
            #{subId}
        </foreach>
    </select>


    <select id="getHandleCountList" resultType="com.jiuji.oa.afterservice.smallpro.vo.req.HandleCountReq">
        <choose>
            <when test="basketIdList == null || basketIdList.isEmpty()">
                <!-- 现货单统计 -->
                SELECT sb.ppriceid basket_id,sum(sb.count) num from SmallproBill sb with(nolock)
                inner join Smallpro s with(nolock) on s.id = sb.smallproID and s.Stats = 1 and isnull(s.isdel,0) = 0
                where sb.ppriceid in
                <foreach collection="ppids" open="(" close=")" item="ppriceId" separator=",">
                    #{ppriceId}
                </foreach>
                and s.areaid = #{areaId} and s.userid = 76783
                group by sb.ppriceid
            </when>
            <otherwise>
                SELECT sb.basket_id,count(DISTINCT s.id) num from SmallproBill sb with(nolock)
                inner join Smallpro s with(nolock) on s.id = sb.smallproID and s.Stats = 1 and isnull(s.isdel,0) = 0
                where sb.basket_id in
                <foreach collection="basketIdList" open="(" close=")" item="basketId" separator=",">
                    #{basketId}
                </foreach>
                group by sb.basket_id
            </otherwise>
        </choose>
    </select>


    <select id="getShouHouBasketIdList" resultType="com.jiuji.oa.afterservice.smallpro.vo.req.ShouHouBasketReq">
        with a as(
        select st.basket_id newBasketId,sb.basket_id oldBasketId, 1 as recursionLevel from shouhou_tuihuan st with(nolock,index=idx_basket_id)
        left join SmallproBill sb with(nolock) on sb.smallproID = st.smallproid
        where isnull(st.isdel,0)=0 and st.tuihuan_kind = 13 and st.check3 = 1 and st.basket_id in
        <foreach collection="basketIdList" open="(" close=")" item="basketId" separator=",">
            #{basketId}
        </foreach>
        union all
        select a.newBasketId newBasketId,sb.basket_id oldBasketId, a.recursionLevel+1
        from shouhou_tuihuan st with(nolock,index=idx_basket_id),SmallproBill sb with(nolock), a
        where isnull(st.isdel,0)=0 and st.tuihuan_kind = 13 and st.check3 = 1
        and sb.smallproID = st.smallproid and a.oldBasketId=st.basket_id
        )
        select a.newBasketId,a.oldBasketId  from a where a.recursionLevel &lt; 1000

    </select>
    <select id="getXiaojianSub" resultType="com.jiuji.cloud.after.vo.res.XiaojianSubResVO">
        SELECT top 1 s.id subId,
                kind,
               problem,
               outward,
               stats,
               isBaoxiu,
               feiyong,
               costprice,
               yuyueid,
               s.Comment comment,
               s.mobile,
               username,
               CONVERT(VARCHAR, indate, 120) indate,
               CONVERT(VARCHAR, buydate, 120) buydate,
               sub_id buySubId,
               inuser,
               s.areaid,
               a.area_name areaName,
               s.oldid oldId,
               s.old_id_type oldIdType,
               dbo.encrypt(s.id) commentCode,
               (case when e.id is null then 0 else 1 end) as ispj
        FROM dbo.Smallpro s with(nolock)
        left join office.dbo.evaluate e with(nolock) on(e.subid = s.id and e.EvaluateType IN(13, 14))
        left join areainfo a with(nolock) on a.id = s.areaid
        where s.id = #{req.id}
        and (s.userid = #{req.userId} or s.old_user_id = #{req.userId})
    </select>
    <select id="getXiaojianProductList" resultType="com.jiuji.cloud.after.vo.res.XiaojianProductVO">
        SELECT sb.smallproID xjid,
               p.ppriceid,
               p.product_name productName,
               p.product_color productColor,
               p.bpic productUrl,
               sb.count productCount,
               b.price productPrice,
               b.basket_id,
               p.cid
        FROM dbo.SmallproBill sb with(nolock)
        INNER JOIN dbo.Smallpro s with(nolock) ON s.id=sb.smallproID
        LEFT JOIN dbo.productinfo p with(nolock) ON sb.ppriceid=p.ppriceid
        LEFT JOIN dbo.basket b with(nolock) ON b.basket_id=sb.basket_id
        where s.id = #{req.id}
    </select>
    <select id="getTuihuanHistCount" resultType="java.lang.Integer">
        SELECT count(*) FROM smallpro s with(nolock)
        LEFT JOIN shouhou_tuihuan st with(nolock) ON st.smallproid = s.id
        WHERE s.sub_id = #{subId}
        AND s.STATS = 1
    </select>
    <select id="getMaintenanceCostDifference" resultType="java.math.BigDecimal">
        SELECT ISNULL((SELECT SUM(s.hejim)
                       FROM shouying s WITH(NOLOCK)
            WHERE s.shouying_type IN('售后小件') AND s.sub_id = #{smallproId}), 0) -
               ISNULL((SELECT SUM(st.tuikuanM)
                       FROM shouhou_tuihuan st WITH(NOLOCK)
                   WHERE st.shouhou_id = #{smallproId}
                   AND ISNULL(st.isdel, 0) = 0
                   AND st.tuihuan_kind = 10), 0) AS difference
    </select>
    <select id="selectSpecialExchanges" resultType="com.jiuji.oa.afterservice.smallpro.po.Smallpro">
        select * from dbo.Smallpro s with (nolock)
        left join dbo.SmallproBill b with (nolock) on b.smallproID = s.id
        where isnull(s.isdel,0)=0
        and isnull(s.is_special_treatment,0)=#{isSpecialTreatment}
        and s.Stats &lt;&gt; 2
        <if test="basketIdList != null and basketIdList.size > 0">
            and b.basket_id in
            <foreach collection="basketIdList" open="(" close=")" item="id" separator=",">
                #{id}
            </foreach>
       </if>
    </select>
    <select id="selectRecursiveQueriesReq" resultType="com.jiuji.oa.afterservice.smallpro.vo.req.RecursiveQueriesReq">
        WITH RecursiveCTE AS (
          SELECT sb.basket_id     oldBasketId,
                   st.basket_id     newBasketId,
                   st.smallproID AS smallproId
            FROM dbo.shouhou_tuihuan st WITH (NOLOCK)
            inner JOIN
            dbo.SmallproBill sb WITH (NOLOCK) ON sb.smallproID = st.smallproid
        WHERE st.tuihuan_kind = 13
          AND st.basket_id = #{basketId}
          AND st.check3 = 1

        UNION ALL
        SELECT sb2.basket_id     oldBasketId,
               st2.basket_id     newBasketId,
               st2.smallproID AS smallproId
        FROM dbo.shouhou_tuihuan st2 WITH (NOLOCK)
             INNER JOIN
         dbo.SmallproBill sb2 WITH (NOLOCK) ON sb2.smallproID = st2.smallproid
            INNER JOIN
            RecursiveCTE rcte ON st2.basket_id = rcte.oldBasketId
        WHERE st2.tuihuan_kind = 13
          AND st2.check3 = 1)
        SELECT *
        FROM RecursiveCTE
            OPTION (MAXRECURSION 100)
    </select>
    <select id="selectHistoricalProcessing" resultType="com.jiuji.oa.afterservice.smallpro.vo.res.HistoricalProcessingRes">
        select top 10 s.id          as smallproId,
                s.sub_id      as subId,
               area.area     as areaName,
               s.Kind        as kind,
               s.Stats       as stats,
               s.ServiceType as serviceType,
               s.Inuser as inuer,
               s.Indate as indate,
               s.feiyong,
               s.changePpriceid,
               bill.ppriceid
        from dbo.Smallpro s with (nolock)
         left join dbo.areainfo area with (nolock) on area.id = s.areaid
            left join  dbo.SmallproBill bill with (nolock) on bill.smallproID = s.id
       <where>
           1=1  and s.userid &lt;&gt;   76783 and s.Stats &lt;&gt; 2
           <if test="req.userId != null">
               and s.userid = #{req.userId}
           </if>
           <if test="req.inuer != null and req.inuer != '' ">
               and s.Inuser = #{req.inuer}
           </if>
          <if test="req.basketIdList != null and req.basketIdList.size > 0">
               and bill.basket_id in
               <foreach collection="req.basketIdList" open="(" close=")" item="id" separator=",">
                   #{id}
               </foreach>
           </if>
            order by s.id desc
       </where>

    </select>
    <select id="selectServiceProductByBasketId" resultType="com.jiuji.oa.afterservice.smallpro.bo.filmCard.ServiceProductBO">
        SELECT sr.id,1 kind from ServiceRecord sr with(nolock) where sr.basket_id = #{basketId}
        union ALL
        SELECT tc.id,2 kind  from tiemoCard tc with(nolock) where tc.basketid = #{basketId}
        union ALL
        SELECT dc.id,3 kind from DIYTimeCard dc with(nolock) where dc.basket_id = #{basketId}
    </select>
    <select id="findQuantityOfLoss" resultType="java.lang.Integer">
        select sum(b.count)
        from dbo.Smallpro s with (nolock)
         left join dbo.SmallproBill b with (nolock) on b.smallproID = s.id
        where s.old_id_type = #{oldIdType} and s.oldid =#{oldId} and s.userid = 76783 and s.Stats= 1
    </select>
    <select id="getMinBasketIdBySmallProId" resultType="java.lang.Integer">
        select min(b.basket_id)
        from dbo.Smallpro p with (nolock)
         left join dbo.SmallproBill bill with(nolock ) on bill.smallproID = p.id
            left join dbo.basket b  with(nolock ) on b.sub_id = p.oldid and b.ppriceid = bill.ppriceid
        where p.id=#{smallProId} and isnull(b.isdel,0)=0

    </select>
    <select id="selectResistFilmPage" resultType="com.jiuji.oa.afterservice.smallpro.vo.res.SelectResistFilmRes">
        select s.id             as smallProId,
               s.areaid         as areaId,
               a.area,
               s.Stats          as state,
               s.changePpriceid as exchangeGoodsPpid,
               p1.product_name  as exchangeGoods,
               b.ppriceid       as receivedPpid,
               p2.product_name  as receivedGoods,
               s.ServiceType    as serviceType,
               s.feiyong        as supplementAmount,
            <if test="req.lossCountMin != null or req.lossCountMax != null">
                   t.lossCount          as lossCount,
            </if>
               s.Inuser         as recipient,
               u.ch999_id         as recipientUserId,
               s.fid,
               s.is_special_treatment as specialHandle,
               s.Kind as kind,
               isnull(s.oldid,s.sub_id) as oldId,
               isnull(s.old_id_type,1) as oldIdType,
               s.Indate         as createTime
        from dbo.Smallpro s with (nolock)
            left join dbo.ch999_user u with(nolock ) on u.ch999_name=s.Inuser
            left join dbo.productinfo p1 with (nolock ) on p1.ppriceid=s.changePpriceid
            left join dbo.areainfo a with (nolock) on a.id = s.areaid
            left join dbo.SmallproBill b with (nolock) on b.smallproID = s.id
            left join dbo.productinfo p2 with (nolock ) on p2.ppriceid=b.ppriceid
        <if test="req.lossCountMin != null or req.lossCountMax != null">
            left join (select s.oldid, sum(b.count) as lossCount
            from dbo.Smallpro s with (nolock)
            left join dbo.SmallproBill b with (nolock) on b.smallproID = s.id
            where old_id_type = 2
            and userid = 76783 group by s.oldid) t on t.oldid = s.id
        </if>
           <where>
            <if test="req.selectKeyType == 1 and req.selectKeyValue != '' and req.selectKeyValue != null">
                and s.id = #{req.selectKeyValue}
            </if>
            <if test="req.selectKeyType == 2 and req.selectKeyValue != '' and req.selectKeyValue != null">
                and b.ppriceid = #{req.selectKeyValue}
            </if>
            <if test="req.selectKeyType == 3 and req.selectKeyValue != '' and req.selectKeyValue != null">
                and p2.product_name like concat('%',#{req.selectKeyValue},'%')
            </if>
            <if test="req.selectKeyType == 4 and req.selectKeyValue != '' and req.selectKeyValue != null">
                and s.changePpriceid = #{req.selectKeyValue}
            </if>
            <if test="req.selectKeyType == 5 and req.selectKeyValue != '' and req.selectKeyValue != null">
                and p1.product_name like concat('%',#{req.selectKeyValue},'%')
            </if>
            <if test="req.selectKeyType == 6 and req.selectKeyValue != '' and req.selectKeyValue != null">
                and s.Inuser like concat('%',#{req.selectKeyValue},'%')
            </if>
            <if test="req.orderType!=null and req.orderType == 1">
               and s.old_id_type is null
            </if>
            <if test="req.orderType!=null and req.orderType == 2">
               and s.old_id_type = 2
            </if>
            <if test="req.serviceTypeList != null and req.serviceTypeList.size > 0">
                and s.ServiceType in
                <foreach collection="req.serviceTypeList" open="(" close=")" item="id" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="req.status != null and req.status.size > 0">
                and s.Stats in
                <foreach collection="req.status" open="(" close=")" item="id" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="req.cidList != null and req.cidList.size > 0">
                and p2.cid in
                <foreach collection="req.cidList" open="(" close=")" item="id" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="req.areaIdList!= null and req.areaIdList.size>0">
                and s.areaid in
                <foreach collection="req.areaIdList" open="(" close=")" item="id" separator=",">
                 #{id}
                </foreach>
            </if>
            <if test="req.orderTimeStart!= null and req.orderTimeEnd!= null">
                and s.Indate between #{req.orderTimeStart} and #{req.orderTimeEnd}
            </if>
            <if test="req.supplementAmountMin != null">
                and s.feiyong &gt;= #{req.supplementAmountMin}
            </if>
            <if test="req.supplementAmountMax != null">
                and s.feiyong &lt; #{req.supplementAmountMax}
            </if>
            <if test="req.lossCountMin != null">
                and t.lossCount &gt;= #{req.lossCountMin}
            </if>
            <if test="req.lossCountMax != null">
                and t.lossCount &lt; #{req.lossCountMax}
            </if>
        </where>
    </select>
    <select id="selectLossDetails" resultType="com.jiuji.oa.afterservice.smallpro.vo.res.LossDetailsInfo">
        select s.id as smallProId, b.count as lossCount, s.Inuser as createUser, s.Stats as state, s.Indate as submitTime
        from dbo.Smallpro s with (nolock)
         left join dbo.SmallproBill b with (nolock) on b.smallproID = s.id
        where old_id_type = 2 and userid = 76783 and s.oldid=#{req.smallProId}
    </select>
    <select id="getFilmHuanList" resultType="com.jiuji.oa.afterservice.smallpro.bo.filmCard.FilmUseLog">
        select b.basket_id as basketId,
        s.Indate as useDate,
        s.Indate as useDateTime,
        s.areaid as areaId ,
        a.area_name as areaName,
        u.ch999_id AS employeeId,
        u.iszaizhi AS iszaizhi,
        u.ch999_name AS employeeName,
         s.id as smallProId,
        '保护膜30天质保换新' as serverName
        from dbo.Smallpro s with(nolock)
        left join dbo.SmallproBill b with(nolock) on s.id=b.smallproID
        LEFT JOIN ch999_user u with(nolock ) ON u.ch999_name= s.Inuser
        left join dbo.areainfo a with(nolock) on s.areaid=a.id
        where isnull(s.isdel,0)=0 and s.Stats != 2 and s.Kind=2
        and isnull(s.ServiceType,0) &lt;>4
        <if test="basketIds!=null and basketIds.size>0">
            and b.basket_id in
            <foreach collection="basketIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        order by s.Indate desc
    </select>
    <select id="getTieMoCardUse" resultType="com.jiuji.oa.afterservice.smallpro.bo.filmCard.FilmCardInfomationBO">
        SELECT tc.sub_id AS subId,
        tc.areaid AS areaId,
        tc.basketid AS basketId,
        tc.basket_idBind AS basketBindId,
        tc.useCount ,
        tc.allCount,
        tc.dtime AS buyTime,
        tc.etime AS endTime,
        a.area AS areaCode,
        a.area_name AS areaName,
        tcl.dtime AS lastUseTime,
        tcl.inuser AS lastInuser,
        tcl.smallProId as smallProId,
        s.tradeDate1 AS tradeDate,
        tc.id as cardId
        FROM
        tiemoCard tc WITH(NOLOCK)
        LEFT JOIN areainfo a WITH(NOLOCK) ON tc.areaid = a.id
        LEFT JOIN tiemoCardUserLog tcl WITH(NOLOCK) ON tc.id= tcl.cardId
        LEFT JOIN sub s WITH(NOLOCK) ON s.sub_id= tc.sub_id
        LEFT JOIN basket b WITH(NOLOCK) ON tc.basketid= b.basket_id
        <if test="basketIds!=null and basketIds.size>0">
            WHERE basket_idBind in
            <foreach collection="basketIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR basketid in
            <foreach collection="basketIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ORDER BY
        lastUseTime DESC
    </select>
    <select id="selectLossCountSmallProId" resultType="com.jiuji.oa.afterservice.smallpro.vo.res.SelectResistFilmRes">
        select s.oldid as smallProId, sum(b.count) as lossCount
        from dbo.Smallpro s with (nolock)
            left join dbo.SmallproBill b with (nolock) on b.smallproID = s.id
        where old_id_type = 2
            <if test="ids!=null and ids.size>0">
                and s.oldid in
                <foreach collection="ids" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
          and userid = 76783 group by s.oldid
    </select>
    <select id="selectProcessingIdBySubId" resultType="java.lang.Integer">
        select top 1 id from smallpro with(nolock) where isnull(stats,0) = 0 and isnull(isdel,0)=0 and sub_id =#{subId}
    </select>
    <select id="getSmallProRefundNotCheckList" resultType="com.jiuji.oa.afterservice.other.po.ShouhouTuihuan">
        select  * from shouhou_tuihuan with(nolock)
        where  isnull(isdel,0) = 0 and (smallproid = #{smallProId}  and tuihuan_kind = 9) ;

    </select>
    <select id="selectOperatorBasketByBasketId" resultType="com.jiuji.oa.afterservice.smallpro.vo.req.OperatorBasketReq">
        select distinct basketId,offsetMoney,status from dbo.OperatorBasket ob where ob.basketId in
        <foreach collection="basketIds" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND isnull(isdel, 0) = 0 and isChecked = 1
    </select>
    <select id="getOperatorBasketByStatusJiuJi" resultType="java.lang.Integer">
        select basketId from dbo.OperatorBasket where basketId in
        <foreach collection="basketIdList" index="index" item="basketId" open="(" close=")" separator=",">
            #{basketId}
        </foreach>
         AND isnull(isdel, 0) = 0
    </select>
    <select id="getReturnMaintenanceCosts" resultType="com.jiuji.oa.afterservice.other.po.ShouhouTuihuan">
        select  * from shouhou_tuihuan with(nolock)
        where  isnull(isdel,0) = 0 and (shouhou_id = #{smallProId} and tuihuan_kind = 10) ;

    </select>


</mapper>
