<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.statistics.dao.SmallproStatisticsDao">


    <sql id="queryCommon">
        <if test="req.cid!= null">
            AND exists( select 1 from f_category_children(#{req.cid}) f where f.id=p.cid )
        </if>
        <if test="req.cidList != null and req.cidList.size>0">
            and p.cid in
            <foreach collection="req.cidList" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="req.brandIdList != null and req.brandIdList.size>0">
            and p.brandID in
            <foreach collection="req.brandIdList" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="req.key!= null and req.key!= ''">
            <if test="req.keyType!= null">
                <if test="req.keyType== 1">
                    AND p.product_name like concat('%',#{req.key},'%')
                </if>
                <if test="req.keyType== 2">
                    AND p.productid = #{req.key}
                </if>
                <if test="req.keyType== 3">
                    AND p.ppriceid = #{req.key}
                </if>
            </if>
        </if>
    </sql>

    <sql id="areaJoinQuery">
        <if test="(req.areaIdList != null and req.areaIdList.size>0) or 1 == req.areaKind or 2 == req.areaKind or 3 == req.areaKind or null!= req.areaLevel">
            INNER JOIN dbo.areainfo with(nolock) ON areainfo.id=s.areaid
            <if test="req.areaIdList != null and req.areaIdList.size>0">
                AND areainfo.id IN
                <foreach collection="req.areaIdList" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="1 == req.areaKind">
                and areainfo.kind1=1
            </if>
            <if test="2 == req.areaKind">
                and areainfo.kind1!=1 and areainfo.kind1!=3
            </if>
            <if test="3 == req.areaKind">
                and areainfo.kind1=3
            </if>
            <if test="null!= req.areaLevel">
                and areainfo.level1=#{req.areaLevel}
            </if>
        </if>
    </sql>
    <select id="getPieceQuantity"
            parameterType="com.jiuji.oa.afterservice.statistics.vo.req.SmallproStatisticsFilterReq"
            resultType="com.jiuji.oa.afterservice.statistics.bo.smallpro.PieceQuantityBO">
        SELECT
        p.productid AS id,
        p.product_name AS name,
        SUM ( b.count ) pieceQuantity
        FROM dbo.Smallpro s with(nolock)
        INNER JOIN dbo.SmallproBill b with(nolock) ON s.id= b.smallproID
        INNER JOIN dbo.productinfo p with(nolock) ON p.ppriceid= b.ppriceid
        <include refid="areaJoinQuery"></include>
        WHERE
        s.Indate BETWEEN #{req.startTimeStr}
        AND #{req.endTimeStr}
        AND isnull( s.isdel, 0 ) = 0
        and s.Stats<![CDATA[ <> ]]> 2
        AND ISNULL(s.ServiceType,0) NOT IN(1,4)
        <if test="null!= kind">
            and kind=#{kind}
        </if>
        <if test="null!= status and 1==status">
            and stats not in (1,2)
        </if>
        <include refid="queryCommon"></include>
        AND p.cid NOT IN ( 3, 58, 52, 7, 62, 33, 61, 67, 221, 50 )
        GROUP BY
        p.productid,
        p.product_name
    </select>
    <select id="getLossReportedAndCashAmount"
            parameterType="com.jiuji.oa.afterservice.statistics.vo.req.SmallproStatisticsFilterReq"
            resultType="com.jiuji.oa.afterservice.statistics.bo.smallpro.LossReportedAndCashAmountBO">
        SELECT
        p.productid AS id,
        p.product_name AS name,
        SUM ( CASE WHEN fc.rstats= 3 THEN sb.[count] ELSE 0 END ) lossReported,
        SUM ( CASE WHEN fc.rstats= 3 THEN sb.[count] * ISNULL( b.inprice, p.costprice ) ELSE 0 END ) amountReported,
        SUM ( CASE WHEN fc.rstats= 2 THEN sb.[count] ELSE 0 END ) cashAmount,
        SUM ( CASE WHEN fc.rstats= 2 THEN sb.[count] * ISNULL( b.inprice, p.costprice ) ELSE 0 END ) cashTransfer
        FROM dbo.SmallproBill sb with(nolock)
        INNER JOIN dbo.Smallpro s with(nolock) ON s.id= sb.smallproID
        <include refid="areaJoinQuery"></include>
        INNER JOIN dbo.shouhou_fanchang fc with(nolock) ON fc.smallproid= sb.smallproID
        AND fc.ppid= sb.ppriceid
        LEFT JOIN dbo.basket b with(nolock) ON sb.basket_id= b.basket_id
        LEFT JOIN dbo.productinfo p with(nolock) ON p.ppriceid= sb.ppriceid
        WHERE
        fc.rstats IN ( 2, 3 )
        AND p.cid NOT IN ( 3, 58, 52, 7, 62, 33, 61, 67, 221, 50 )
        AND ISNULL( s.isdel, 0 ) = 0
        and s.ServiceType is null
        AND s.Indate BETWEEN #{req.startTimeStr}
        AND #{req.endTimeStr}
        <include refid="queryCommon"></include>
        GROUP BY
        p.productid,
        p.product_name
    </select>
    <select id="getSmallSales" resultType="com.jiuji.oa.afterservice.statistics.bo.smallpro.SmallSalesBO">
        SELECT
        p.productid AS id,
        p.product_name AS name,
        SUM ( b.basket_count ) smallSaleNums ,
        SUM ( b.price * b.basket_count ) smallSales,
        SUM ( ( b.price * b.basket_count ) - b.inprice * b.basket_count ) smallSalesProfit
        FROM sub s with(nolock)
        INNER JOIN dbo.basket b with(nolock) ON s.sub_id= b.sub_id
        AND b.ismobile= 0
        INNER JOIN dbo.productinfo p with(nolock) ON p.ppriceid= b.ppriceid
        AND p.cid NOT IN ( 3, 58, 52, 7, 62, 33, 61, 67, 221, 50 )
        <include refid="areaJoinQuery"></include>
        WHERE
        s.sub_check= 3
        AND ISNULL( b.isdel, 0 ) = 0
        AND s.tradeDate1 BETWEEN #{req.startTimeStr}
        AND #{req.endTimeStr}
        <include refid="queryCommon"></include>
        GROUP BY
        p.productid,
        p.product_name
    </select>
    <select id="getAllDataByCustomer"
            resultType="com.jiuji.oa.afterservice.statistics.bo.smallpro.SmallproSstaticsWithFilterBO">
        SELECT
        s.userid AS id,
        u.UserName,
        SUM ( b.count ) 接件量 INTO #tmp_Smallpro
        FROM dbo.Smallpro s with(nolock)
        INNER JOIN dbo.SmallproBill b with(nolock) ON s.id= b.smallproID
        INNER JOIN dbo.productinfo p with(nolock) ON p.ppriceid= b.ppriceid
        LEFT JOIN dbo.BBSXP_Users u with(nolock) ON s.userid= u.ID
        WHERE
        s.Indate BETWEEN #{req.startTimeStr}
        AND #{req.endTimeStr}
        AND isnull( s.isdel, 0 ) = 0
        and s.ServiceType is null
        <include refid="queryCommon"></include>
        AND p.cid NOT IN ( 3, 58, 52, 7, 62, 33, 61, 67, 221, 50 )
        GROUP BY
        s.userid ,
        u.UserName;
        SELECT
        s.userid AS id,
        u.UserName,
        SUM ( b.count ) 现货量 INTO #tmp_SmallproSpotQuantity
        FROM dbo.Smallpro s with(nolock)
        INNER JOIN dbo.SmallproBill b with(nolock) ON s.id= b.smallproID
        INNER JOIN dbo.productinfo p with(nolock) ON p.ppriceid= b.ppriceid
        LEFT JOIN dbo.BBSXP_Users u with(nolock) ON s.userid= u.ID
        WHERE
        s.Indate BETWEEN #{req.startTimeStr}
        AND #{req.endTimeStr}
        AND isnull( s.isdel, 0 ) = 0
        and s.ServiceType is null
        and s.kind = 4
        <include refid="queryCommon"></include>
        AND p.cid NOT IN ( 3, 58, 52, 7, 62, 33, 61, 67, 221, 50 )
        GROUP BY
        s.userid ,
        u.UserName;
        SELECT
        s.userid AS id,
        u.UserName,
        SUM ( b.count ) 维修量 INTO #tmp_SmallproMaintenance
        FROM dbo.Smallpro s with(nolock)
        INNER JOIN dbo.SmallproBill b with(nolock) ON s.id= b.smallproID
        INNER JOIN dbo.productinfo p with(nolock) ON p.ppriceid= b.ppriceid
        LEFT JOIN dbo.BBSXP_Users u with(nolock) ON s.userid= u.ID
        WHERE
        s.Indate BETWEEN #{req.startTimeStr}
        AND #{req.endTimeStr}
        AND isnull( s.isdel, 0 ) = 0
        and s.ServiceType is null
        and s.kind = 0
        <include refid="queryCommon"></include>
        AND p.cid NOT IN ( 3, 58, 52, 7, 62, 33, 61, 67, 221, 50 )
        GROUP BY
        s.userid ,
        u.UserName;
        SELECT
        s.userid AS id,
        u.UserName,
        SUM ( b.count ) 待处理 INTO #tmp_SmallproProcessed
        FROM dbo.Smallpro s with(nolock)
        INNER JOIN dbo.SmallproBill b with(nolock) ON s.id= b.smallproID
        INNER JOIN dbo.productinfo p with(nolock) ON p.ppriceid= b.ppriceid
        LEFT JOIN dbo.BBSXP_Users u with(nolock) ON s.userid= u.ID
        WHERE
        s.Indate BETWEEN #{req.startTimeStr}
        AND #{req.endTimeStr}
        AND isnull( s.isdel, 0 ) = 0
        and s.ServiceType is null
        and s.stats not in(1,2)
        <include refid="queryCommon"></include>
        AND p.cid NOT IN ( 3, 58, 52, 7, 62, 33, 61, 67, 221, 50 )
        GROUP BY
        s.userid ,
        u.UserName;
        SELECT
        s.userid AS id,
        SUM ( CASE WHEN fc.rstats= 3 THEN sb.[count] ELSE 0 END ) 报损量,
        SUM ( CASE WHEN fc.rstats= 3 THEN sb.[count] * ISNULL( b.inprice, p.costprice ) ELSE 0 END ) 报损金额,
        SUM ( CASE WHEN fc.rstats= 2 THEN sb.[count] ELSE 0 END ) 转现量,
        SUM ( CASE WHEN fc.rstats= 2 THEN sb.[count] * ISNULL( b.inprice, p.costprice ) ELSE 0 END ) 转现金额 INTO
        #tmp_baosun
        FROM dbo.SmallproBill sb with(nolock)
        INNER JOIN dbo.Smallpro s with(nolock) ON s.id= sb.smallproID
        INNER JOIN #tmp_Smallpro tsp ON tsp.id= s.userid
        INNER JOIN dbo.shouhou_fanchang fc with(nolock) ON fc.smallproid= sb.smallproID
        AND fc.ppid= sb.ppriceid
        LEFT JOIN dbo.basket b with(nolock) ON sb.basket_id= b.basket_id
        LEFT JOIN dbo.productinfo p with(nolock) ON p.ppriceid= sb.ppriceid
        WHERE
        fc.rstats IN ( 2, 3 )
        AND p.cid NOT IN ( 3, 58, 52, 7, 62, 33, 61, 67, 221, 50 )
        AND ISNULL( s.isdel, 0 ) = 0
        and s.ServiceType is null
        AND s.Indate BETWEEN #{req.startTimeStr}
        AND #{req.endTimeStr}
        <include refid="queryCommon"></include>
        GROUP BY
        s.userid;
        SELECT
        s.userid AS id,
        SUM ( b.basket_count ) 小件销量 ,
        SUM ( b.price * b.basket_count ) 小件销售额,
        SUM ( ( b.price * b.basket_count ) - b.inprice * b.basket_count ) 小件销售利润 INTO #tmp_pjsale
        FROM sub s with(nolock)
        INNER JOIN dbo.basket b with(nolock) ON s.sub_id= b.sub_id
        AND b.ismobile= 0
        INNER JOIN #tmp_Smallpro tsp with(nolock) ON tsp.id= s.userid
        INNER JOIN dbo.productinfo p with(nolock) ON p.ppriceid= b.ppriceid
        AND p.cid NOT IN ( 3, 58, 52, 7, 62, 33, 61, 67, 221, 50 )
        WHERE
        s.sub_check= 3
        AND ISNULL( b.isdel, 0 ) = 0
        AND s.tradeDate1 BETWEEN #{req.startTimeStr}
        AND #{req.endTimeStr}
        <include refid="queryCommon"></include>
        GROUP BY
        s.userid;
        SELECT
        * INTO #tmp_moresub
        FROM
        (
        SELECT
        *,
        ROW_NUMBER ( ) OVER ( PARTITION BY id ORDER BY num DESC ) r
        FROM
        (
        SELECT
        s.userid id,
        s.sub_id,
        SUM ( b.count ) num
        FROM dbo.Smallpro s with(nolock)
        INNER JOIN dbo.SmallproBill b with(nolock) ON s.id= b.smallproID
        INNER JOIN dbo.productinfo p with(nolock) ON p.ppriceid= b.ppriceid
        WHERE
        s.Indate BETWEEN #{req.startTimeStr}
        AND #{req.endTimeStr}
        AND isnull( s.isdel, 0 ) = 0
        <include refid="queryCommon"></include>
        AND p.cid NOT IN ( 3, 58, 52, 7, 62, 33, 61, 67, 221, 50 )
        GROUP BY
        s.userid,
        s.sub_id
        ) tab1
        ) tab2
        WHERE
        tab2.r= 1;
        SELECT
        a.id,
        a.Username name,
        a.接件量 receiveCount,
        s1.现货量 beforeCount,
        s2.维修量 maintainCount,
        s3.待处理 processedCount,
        isnull(d.报损量,0) damagedCount,
        isnull(d.报损金额,0) damagedAmount,
        isnull(d.转现量,0) convertInventoryCount,
        isnull(d.转现金额,0) convertInventoryAmount,
        isnull(e.小件销量,0) saleCount,
        isnull(e.小件销售额,0) saleAmount,
        isnull(e.小件销售利润,0) saleProfit,
        sub.num maximumNumberOfPickups,
        sub.sub_id moreSubId
        FROM
        #tmp_Smallpro a
        LEFT JOIN #tmp_baosun d ON a.id= d.id
        LEFT JOIN #tmp_SmallproSpotQuantity s1 ON s1.id= a.id
        LEFT JOIN #tmp_SmallproMaintenance s2 ON s2.id= a.id
        LEFT JOIN #tmp_SmallproProcessed s3 ON s3.id= a.id
        LEFT JOIN #tmp_pjsale e ON a.id= e.id
        LEFT JOIN #tmp_moresub sub ON a.id= sub.id;
        DROP TABLE #tmp_Smallpro;
        DROP TABLE #tmp_baosun;
        DROP TABLE #tmp_pjsale;
        DROP TABLE #tmp_moresub;
        DROP TABLE #tmp_SmallproSpotQuantity;
        DROP TABLE #tmp_SmallproMaintenance;
        DROP TABLE #tmp_SmallproProcessed;
    </select>
    <select id="getAllDataByArea"
            resultType="com.jiuji.oa.afterservice.statistics.bo.smallpro.SmallproSstaticsWithFilterBO">
        SELECT
        id,
        area AS area
        FROM dbo.areainfo with(nolock)
        WHERE
        1 =1
        <if test="req.areaIdList != null and req.areaIdList.size>0">
            AND id IN
            <foreach collection="req.areaIdList" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="1 == req.areaKind">
            and kind1=1
        </if>
        <if test="2 == req.areaKind">
            and kind1!=1 and kind1!=3
        </if>
        <if test="3 == req.areaKind">
            and kind1=3
        </if>
        <if test="null!= req.areaLevel">
            and level1=#{req.areaLevel}
        </if>
    </select>
    <select id="getPieceQuantityByArea"
            resultType="com.jiuji.oa.afterservice.statistics.bo.smallpro.PieceQuantityBO">
        SELECT
        s.areaid AS id,
        SUM ( b.count ) pieceQuantity
        FROM dbo.Smallpro s with(nolock)
        INNER JOIN dbo.SmallproBill b with(nolock) ON s.id= b.smallproID
        INNER JOIN dbo.productinfo p with(nolock) ON p.ppriceid= b.ppriceid
        WHERE
        s.Indate BETWEEN #{req.startTimeStr}
        AND #{req.endTimeStr}
        AND isnull( s.isdel, 0 ) = 0
        and s.Stats<![CDATA[ <> ]]> 2
        AND ISNULL(s.ServiceType,0) NOT IN(1,4)
        <if test="null!= kind">
            and kind=#{kind}
        </if>
        <if test="null!= status and 1==status">
            and stats not in (1,2)
        </if>
        <include refid="queryCommon"></include>
        AND p.cid NOT IN ( 3, 58, 52, 7, 62, 33, 61, 67, 221, 50 )
        GROUP BY
        s.areaid
    </select>
    <select id="getLossReportedAndCashAmountByArea"
            resultType="com.jiuji.oa.afterservice.statistics.bo.smallpro.LossReportedAndCashAmountBO">
        SELECT
        s.areaid AS id,
        SUM ( CASE WHEN fc.rstats= 3 THEN sb.[count] ELSE 0 END ) lossReported,
        SUM ( CASE WHEN fc.rstats= 3 THEN sb.[count] * ISNULL( b.inprice, p.costprice ) ELSE 0 END ) amountReported,
        SUM ( CASE WHEN fc.rstats= 2 THEN sb.[count] ELSE 0 END ) cashAmount,
        SUM ( CASE WHEN fc.rstats= 2 THEN sb.[count] * ISNULL( b.inprice, p.costprice ) ELSE 0 END ) cashTransfer
        FROM dbo.SmallproBill sb with(nolock)
        INNER JOIN dbo.Smallpro s with(nolock) ON s.id= sb.smallproID
        INNER JOIN dbo.shouhou_fanchang fc with(nolock) ON fc.smallproid= sb.smallproID
        AND fc.ppid= sb.ppriceid
        LEFT JOIN dbo.basket b with(nolock) ON sb.basket_id= b.basket_id
        LEFT JOIN dbo.productinfo p with(nolock) ON p.ppriceid= sb.ppriceid
        WHERE
        fc.rstats IN ( 2, 3 )
        AND p.cid NOT IN ( 3, 58, 52, 7, 62, 33, 61, 67, 221, 50 )
        AND ISNULL( s.isdel, 0 ) = 0
        and s.ServiceType is null
        AND s.Indate BETWEEN #{req.startTimeStr}
        AND #{req.endTimeStr}
        <include refid="queryCommon"></include>
        GROUP BY
        s.areaid
    </select>
    <select id="getSmallSalesByArea"
            resultType="com.jiuji.oa.afterservice.statistics.bo.smallpro.SmallSalesBO">
        SELECT
        s.areaid AS id,
        SUM ( b.basket_count ) smallSaleNums ,
        SUM ( b.price * b.basket_count ) smallSales,
        SUM ( ( b.price * b.basket_count ) - b.inprice * b.basket_count ) smallSalesProfit
        FROM sub s with(nolock)
        INNER JOIN dbo.basket b with(nolock) ON s.sub_id= b.sub_id
        AND b.ismobile= 0
        INNER JOIN dbo.productinfo p with(nolock) ON p.ppriceid= b.ppriceid
        AND p.cid NOT IN ( 3, 58, 52, 7, 62, 33, 61, 67, 221, 50 )
        WHERE
        s.sub_check= 3
        AND ISNULL( b.isdel, 0 ) = 0
        AND s.tradeDate1 BETWEEN #{req.startTimeStr}
        AND #{req.endTimeStr}
        <include refid="queryCommon"></include>
        GROUP BY
        s.areaid
    </select>
    <select id="getCurUserDefaultKind1" resultType="java.lang.Integer">
        SELECT
            a.kind1
        FROM ch999_user u with(nolock)
            LEFT JOIN areainfo a with(nolock) ON u.area1id= a.id
        WHERE
            u.ch999_id= #{userId}
    </select>
</mapper>
