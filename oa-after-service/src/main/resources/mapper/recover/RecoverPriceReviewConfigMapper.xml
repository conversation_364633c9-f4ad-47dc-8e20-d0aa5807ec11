<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jiuji.oa.afterservice.recover.dao.RecoverPriceReviewConfigMapper">

    <select id="getReviewConfigPrice"
            resultType="com.jiuji.oa.afterservice.recover.vo.res.ReviewConfigPriceVO">
        select top 1 oldPrice, changerPrice
        from recover_SubBasketChangeMoney with(nolock)
        where basket_id = #{basketId}  and checkstate=0
    </select>

    <select id="checkRankExist" resultType="java.lang.Integer">
        select top 1 id from dbo.rank_set with(nolock) where  rank= #{rank}
    </select>

    <select id="getOldPriceByBasketId"
            resultType="com.jiuji.oa.afterservice.recover.vo.res.ReviewConfigPriceVO">
        SELECT top 1 ISNULL(price1,0) + ISNULL(sale_out_lp_add_price,0) as oldPrice, use_precise_price,
               precise_price,
               addCodePrice,
               equity_addCodePrice,
               ppriceid,
               sale_out_lp_add_price
        FROM dbo.recover_basket with(nolock)
        WHERE id = #{basketId}
    </select>

</mapper>