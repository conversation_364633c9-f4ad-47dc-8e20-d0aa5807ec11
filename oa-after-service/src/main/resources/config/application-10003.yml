consul:
  host: ************
  port: 8500
image:
  del:
    url: http://**************:5083
  select:
    url: https://img.gzchang.net/
  upload:
    url: http://**************:9333
instance-zone: 10003
jiuji:
  sys:
    moa: https://moa.gzchang.net
    pc: https://oa.gzchang.net
    xtenant: 10003
  xtenant: 3000
logging:
  config:
    path: classpath:log/log4j2-dev.xml
messages:
  basename: i18n/abstractInfo,i18n/saas
mongodb:
  ch999oa:
    url: ch999oa__10003:of4uFxkADiHgrnnN@************:27017,************:27017,************:27017/ch999oa__10003
  url1: ***************************************************************************
mysql:
  datasource:
    max-pool-size: 20
  manage_training:
    dbname: 
    password: 
    url: :3306
    username: 
  oa_core:
    dbname: oa_core__10003
    password: m%BZ7nh0R$f9Pwlt
    url: master.mysql.serv.iteng.com:3306
    username: oa_core__10003
  oa_nc:
    dbname: oa_nc__10003
    password: ^F0ZYzSp7SzR46oP
    url: master.mysql.serv.iteng.com:3306
    username: oa_nc__10003
office:
  sys:
    xtenant: 10003
rabbitmq:
  master:
    password: qsdQc
    port: 5672
    url: master.rabbitmq.serv.iteng.com
    username: oaAsync__10003
    vhost: oaAsync__10003
  msgcenter:
    password: ch999
    port: 35672
    url: storemq.ch999.cn
    username: msgcenter
    vhost: msgcenter
  oa:
    password: scbnr
    port: 5672
    url: master.rabbitmq.serv.iteng.com
    username: oa__10003
    vhost: oa__10003
  oaAsync:
    password: qsdQc
    port: 5672
    url: master.rabbitmq.serv.iteng.com
    username: oaAsync__10003
    vhost: oaAsync__10003
  printer:
    password: UqPQR
    port: 5672
    url: master.rabbitmq.serv.iteng.com
    username: printer__10003
    vhost: printer__10003
redis:
  oa:
    host: ************
    password: google99
    port: 6392
    url: google99@************:6392
sms:
  send:
    email:
      url: http://sms.gzchang.net/email/email.aspx
    in:
      url: http://office/Handler/api.ashx
  url: http://sms.gzchang.net/?test=
spring:
  cloud:
    consul:
      discovery:
        instance-zone: 10003
sqlserver:
  after_write:
    dbname: ch999oanew__10003
    host: web.sqlserver.serv.iteng.com
    password: "Z*T4ZtxbY7SF"
    port: 1433
    username: ch999oanew__10003
  ch999oanew:
    dbname: ch999oanew__10003
    host: web.sqlserver.serv.iteng.com
    password: "Z*T4ZtxbY7SF"
    port: 1433
    username: ch999oanew__10003
  ch999oanewReport:
    dbname: ch999oanew__10003
    host: web.sqlserver.serv.iteng.com
    password: "Z*T4ZtxbY7SF"
    port: 1433
    username: ch999oanew__10003
  ch999oanewHis:
    dbname: ch999oanew__10003
    host: web.sqlserver.serv.iteng.com
    password: "Z*T4ZtxbY7SF"
    port: 1433
    username: ch999oanew__10003
  datasource:
    max-pool-size: 20
  oaOffice:
    dbname: office__10003
    host: web.sqlserver.serv.iteng.com
    password: aPhHDweCV*ZB
    port: 1433
    username: office__10003
  oanewWrite:
    dbname: ch999oanew__10003
    host: web.sqlserver.serv.iteng.com
    password: "Z*T4ZtxbY7SF"
    port: 1433
    username: ch999oanew__10003
  office:
    dbname: office__10003
    host: web.sqlserver.serv.iteng.com
    password: aPhHDweCV*ZB
    port: 1433
    username: office__10003
  officeWrite:
    dbname: office__10003
    host: web.sqlserver.serv.iteng.com
    password: aPhHDweCV*ZB
    port: 1433
    username: office__10003
  smallpro_write:
    dbname: ch999oanew__10003
    host: web.sqlserver.serv.iteng.com
    password: "Z*T4ZtxbY7SF"
    port: 1433
    username: ch999oanew__10003
  web999:
    dbname: web999__10003
    host: web.sqlserver.serv.iteng.com
    password: sCf_CPlhwKpJ
    port: 1433
    username: web999__10003
url:
  delImgUrl: http://************:5083
  oa-push-info: http://inwcf.gzchang.net/ajax.ashx?act=oaMessagePush&content=%s&ch999ids=%s&link=%s
  selectImgUrl: https://img.gzchang.net/
  source:
      path: i18n/url
  uploadImgUrl: http://************:9333
after:
  lmstfy:
    host: 'lmstfy.service.ch999.cn'
    token: '01GP2BFHSGQESTJ151AFN4TP7J'